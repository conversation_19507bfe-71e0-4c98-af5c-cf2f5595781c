{% liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
%}
<script src="{{ 'announcement-bar.js' | asset_url }}" defer="defer"></script>
{%- if section.settings.announcement_enable and section.blocks.size >= 1 -%}
  {{ 'announcement-bar.css' | asset_url | stylesheet_tag }}
  {{ 'announcement-timer.css' | asset_url | stylesheet_tag }}
  {{ 'component-list-social.css' | asset_url | stylesheet_tag }}
  <link rel="stylesheet" href="{{ 'component-rte.css' | asset_url }}" media="print" onload="this.media='all'">
  <noscript>{{ 'component-rte.css' | asset_url | stylesheet_tag }}</noscript>

  <style>
               .announcement-collapsible-content{
                 display: none;
               }
               .announcement-bar:not(.topbar__timer--bar) {
                   display: flex;
                   align-items: center;
                   justify-content: center;
                   position: relative;
                   gap: 2rem;
               }
               .close__announcement--bar.modal__close-button:not(.announcement--timer-close-btn) {
                 width: auto;
                 height: 42px;
             }
               .announcement__image {
                 line-height: 1;
               }
               #shopify-section-announcement-bar {
                 z-index: 4;
               }
               .announcement__icon--arrow{
                 width: 3.5rem;
               }
               .button__icon--arrow_svg{
                 max-width: 2.5rem;
               }
              {%- for block in section.blocks -%}
                {%- case block.type -%}
                   {%- when 'announcement' -%}
                     .collapsible__content--background-image::before {
                       background-color: rgba(var(--color-background), {{ block.settings.image_overlay_opacity | divided_by: 100.0 }});
                     }
               {%- endcase -%}
             {%- endfor -%}

            .button--announcement {
               border-width: 0.1rem;
               font-size: 1.2rem;
               font-weight: 400;
               gap:  0.5rem;
             }
             @media only screen and (min-width: 992px) {
               .button--announcement {
                 padding: 0.2rem 1rem;
               }
             }
            .announcement__bar--timer .countdown__item > span {
          line-height: 1.2;
        }
        .announcement__bar--timer .countdown__item > span.countdown__text {
      font-size: 1.4rem;
    }
  </style>

  {%- liquid
    assign container = ''
    if section.settings.container == 'container'
      assign container = 'container'
    elsif section.settings.container == 'container-fluid'
      assign container = 'container-fluid'
    else
      assign container = 'container-fluid px-0'
    endif
  -%}

  {% if theme_rtl %}
    {{ 'announcement-bar-rtl.css' | asset_url | stylesheet_tag }}
  {% endif %}

  <div
    class="announcement__area "
    data-section-id="{{ section.id }}"
    data-section-type="announcement-bar"
  >
    {%- for block in section.blocks -%}
      {%- case block.type -%}
        {%- when 'announcement' -%}
          {%- liquid
            assign page = false
            assign grid = false
            assign background_image = false

            unless request.page_type == 'index'
              assign page = block.settings.index
            endunless

            if request.page_type == 'index'
              assign page = block.settings.no_index
            endif

            if block.settings.image != blank and block.settings.background_image == false
              assign grid = true
            endif

            if block.settings.image != blank and block.settings.background_image
              assign background_image = true
            endif

            assign link_button = true
            if block.settings.collapsible_content
              assign link_button = false
            endif
          -%}
          {%- unless page == true -%}
            <div class="announcement__wrapper color-{{ section.settings.color_scheme }} ">
              <div class="announcement-bar-innner {{ container }}  {% unless section.settings.show_offset %}padding--narrow-header{% endunless %}">
                <div class="d-md-none">
                  {% if block.settings.social_media %}
                    {%- render 'social-media', className: 'announment--bar-social-media' -%}
                  {% endif %}
                </div>

                <div
                  class="announcement-bar {% if block.settings.link == blank %}py-1{% endif %}"
                  role="region"
                  aria-label="{{ 'sections.header.announcement' | t }}"
                  {{ block.shopify_attributes }}
                >
                  {%- if block.settings.text != blank -%}
                    <div class="announcement-bar--content">
                      {% if link_button -%}
                        <a href="{{ block.settings.link }}" class="announcment--link-button">
                      {%- endif %}
                      <span class="announcement-bar__message h6 mb-0">
                        {{ block.settings.text | escape }}
                      </span>
                      {% if link_button %}
                        <span class="arrow__link--button">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            class="feather feather-arrow-right"
                          >
                            <line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline>
                          </svg>
                        </span>
                        </a>
                      {% endif %}
                    </div>
                  {%- endif -%}

                  {% if block.settings.collapsible_content %}
                    <button
                      id="announcement-more-info"
                      class="button button--announcement button--secondary d-flex align-items-center"
                      aria-label="Open announcement bar panel"
                    >
                      <span class="d-md-none">{{ block.settings.toggle_button }}</span> {% render 'icon-caret' %}
                    </button>
                  {% endif %}
                </div>

                <div class=" d-flex justify-content-end d-md-none">
                  {% render 'localization-form',
                    form_currency_id: 'headerCountryForm-3',
                    form_language_id: 'headerLanguageForm-3',
                    dropdown_position: 'dropdown__bottom--right-position',
                    enable_country_selector: block.settings.country_selector,
                    enable_language_selector: block.settings.language_selector,
                    place: 'header'
                  %}
                </div>
              </div>

              {% if block.settings.collapsible_content %}
                <div class="announcement-collapsible-content color-{{ block.settings.color_scheme }}">
                  <div class="{{ container }} announcement-collapsible__inner">
                    <div class="announcement-collapsible-content-close">
                      <div class="announcement-collapsible-content-close-inner {{ container }} d-flex justify-content-end align-items-center">
                        <button type="button" class="close__announcement--bar modal__close-button link">
                          {%- render 'icon-close' -%}
                        </button>
                      </div>
                    </div>
                    <div class="collapsible-content--template  {% if grid %} collapsible-content--template-grid {% endif %} {% if background_image %} collapsible__content--background-image {% endif %}">
                      <div class="collapsible-content--template-content {% if background_image %} collapsible--background__media--{{ block.settings.section_height }} {% endif %}">
                        <div class="collapsible-content--template-inner text-{{ block.settings.alignment }} {% if background_image %}background-image--in-collapsible-content{% endif %} {% if block.settings.image == blank %}no-image--in-collapsible-content{% endif %}">
                          {% if block.settings.subheading != blank %}
                            <div class="announcement-collapsible-subheading">{{ block.settings.subheading }}</div>
                          {% endif %}
                          {% if block.settings.heading != blank %}
                            <h2 class="announcement-collapsible-heading mb-0 {{ block.settings.heading_size }}">
                              {{ block.settings.heading }}
                            </h2>
                          {% endif %}

                          {% if block.settings.description != blank %}
                            <div class="announcement-collapsible-text rte">
                              {{ block.settings.description }}
                            </div>
                          {% endif %}
                          <div class="announcement-collapsible-button">
                            {% liquid
                              if block.settings.button_style == 'primary'
                                assign button_class = ''
                              else
                                assign button_class = 'button--secondary'
                              endif
                            %}

                            <a
                              {% if block.settings.button_link == blank %}
                                role="link" aria-disabled="true"
                              {% else %}
                                href="{{ block.settings.button_link }}"
                              {% endif %}
                              class="button button--{{ block.settings.button_size }} {{ button_class }}  {% if block.settings.button_icon %}button--with-icon{% endif %}"
                            >
                              <span class="button--label-with-icon"> {{ block.settings.button_label | escape }}</span>
                              {% if block.settings.button_icon %}
                                <span class="button--icon button--icon-right {% unless block.settings.button_icon_mobile %}d-sm-none{% endunless %}">
                                  {% render 'icon-arrow-right' %}
                                </span>
                              {% endif %}
                            </a>
                          </div>
                        </div>
                      </div>

                      {% if block.settings.image %}
                        <div class="collapsible__content--with-image {% if background_image %} background--image{% endif %}">
                          <div
                            class="{% unless background_image %} image-with-text__media image-with-text__media--{{ block.settings.height }} {% endunless %} media"
                            {% if block.settings.height == 'adapt'
                              and block.settings.image != blank
                              and background_image == false
                            %}
                              style="padding-bottom: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%;"
                            {% endif %}
                          >
                            {%- if block.settings.image != blank -%}
                              {%- capture sizes -%}{% if background_image %} (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 }}px, (min-width: 750px) calc(100vw - 130px) {% else %}(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px, (min-width: 750px) calc((100vw - 130px) / 2){% endif %}, calc(100vw - 50px){%- endcapture -%}
                              {{
                                block.settings.image
                                | image_url: width: 1500
                                | image_tag:
                                  loading: 'lazy',
                                  sizes: sizes,
                                  widths: '165, 360, 535, 750, 1070, 1500, 1700, 2200, 3000'
                              }}
                            {%- endif -%}
                          </div>
                        </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
              {% endif %}
            </div>
          {%- endunless -%}
        {%- when 'countdown_timer' -%}
          {%- liquid
            assign page = false

            unless request.page_type == 'index'
              assign page = block.settings.index
            endunless

            if request.page_type == 'index'
              assign page = block.settings.no_index
            endif
          -%}
          <style>
            {% if block.settings.custom_colors %}
            .custom-colors-{{ section.id }}{
              --color-foreground: {{ block.settings.foreground.red }}, {{ block.settings.foreground.green }}, {{ block.settings.foreground.blue }};
              --color-background: {{ block.settings.background.red }}, {{ block.settings.background.green }}, {{ block.settings.background.blue }};
            }
            {% endif %}
          </style>
          {%- unless page == true -%}
            <announcement-bar class="announcement__bar--container">
              <div
                class="announcement-bar topbar__timer--bar color-{{ block.settings.color_scheme }} {% if block.settings.custom_colors  %} custom-colors-{{ section.id }} {% endif %} {% if block.settings.link == blank %}py-1{% endif %}"
                role="region"
                aria-label="{{ 'sections.header.announcement' | t }}"
                {{ block.shopify_attributes }}
              >
                <div class="announcement__bar--timer-wrapper">
                  <div class="announcement__bar--timer-column text__with--timer">
                    {% if block.settings.text != blank %}
                      <div class="announcement__bar--timer">
                        {{ block.settings.text | escape }}
                      </div>
                    {% endif %}

                    {% if block.settings.timer %}
                      {%- liquid
                        assign countdown_timer = block.settings.countdown_year | append: '-' | append: block.settings.countdown_month | append: '-' | append: block.settings.countdown_days
                        assign countdown_timer_convert = countdown_timer | date: '%s'
                        assign today_date = 'now' | date: '%s'

                        assign timer = false
                        if countdown_timer_convert > today_date
                          assign timer = true
                        endif
                      -%}
                      <countdown-timer>
                        {% if timer %}
                          <div
                            class="announcement__bar--timer d-flex"
                            data-countdown="{{ block.settings.countdown_year }}-{{ block.settings.countdown_month }}-{{ block.settings.countdown_days }}"
                          >
                            <div class="countdown__item Days">
                              <span class="countdown__number">- -</span>
                              <span class="countdown__text">{{ 'products.product.countdown_timer.days' | t }}</span>
                            </div>
                            <div class="countdown__item Hrs">
                              <span class="countdown__number">- -</span>
                              <span class="countdown__text">{{ 'products.product.countdown_timer.hours' | t }}</span>
                            </div>
                            <div class="countdown__item Min">
                              <span class="countdown__number">- -</span>
                              <span class="countdown__text">{{ 'products.product.countdown_timer.minutes' | t }}</span>
                            </div>
                            <div class="countdown__item Sec">
                              <span class="countdown__number">- -</span>
                              <span class="countdown__text">{{ 'products.product.countdown_timer.seconds' | t }}</span>
                            </div>
                          </div>
                        {% else %}
                          <div
                            class="announcement__bar--timer d-flex"
                          >
                            <div class="countdown__item Days">
                              <span class="countdown__number">00</span>
                              <span class="countdown__text">{{ 'products.product.countdown_timer.days' | t }}</span>
                            </div>
                            <div class="countdown__item Hrs">
                              <span class="countdown__number">00</span>
                              <span class="countdown__text">{{ 'products.product.countdown_timer.hours' | t }}</span>
                            </div>
                            <div class="countdown__item Min">
                              <span class="countdown__number">00</span>
                              <span class="countdown__text">{{ 'products.product.countdown_timer.minutes' | t }}</span>
                            </div>
                            <div class="countdown__item Sec">
                              <span class="countdown__number">00</span>
                              <span class="countdown__text">{{ 'products.product.countdown_timer.seconds' | t }}</span>
                            </div>
                          </div>
                        {% endif %}
                      </countdown-timer>
                    {% endif %}
                  </div>

                  {% liquid
                    if block.settings.button_type == 'primary'
                      assign button_class = 'button--primary'
                    else
                      assign button_class = 'button--secondary'
                    endif
                  %}
                  {% if block.settings.link_label != blank %}
                    <div class="announcement__bar--timer-column announcement__button">
                      <a
                        {% if block.settings.link == blank %}
                          role="link" aria-disabled="true"
                        {% else %}
                          href="{{ block.settings.link }}"
                        {% endif %}
                        class="button button--{{ block.settings.button_size }} {{ button_class }}  {% if block.settings.button_icon %}button--with-icon{% endif %}"
                      >
                        <span class="button--label-with-icon"> {{ block.settings.link_label | escape }}</span>
                        {% if block.settings.button_icon %}
                          <span class="button--icon button--icon-right {% unless block.settings.button_icon_mobile %}d-sm-none{% endunless %}">
                            {% render 'icon-arrow-right' %}
                          </span>
                        {% endif %}
                      </a>
                    </div>
                  {%- endif -%}
                </div>
                {%- if block.settings.enable_close_btn -%}
                  <button type="button" class="close__announcement--bar link announcement--timer-close-btn">
                    {%- render 'icon-close' -%}
                  </button>
                {%- endif -%}
              </div>
              <announcement-bar>
          {%- endunless -%}
      {%- endcase -%}
    {%- endfor -%}
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.announcement-bar.name",
  "settings": [
    {
      "type": "checkbox",
      "id": "announcement_enable",
      "label": "Show announcement",
      "default": true
    },
    {
       "type": "select",
       "id": "container",
       "label": "Page width",
       "default": "container",
       "options": [
         {
           "value": "container",
           "label": "Boxed"
         },
         {
           "value": "container-fluid",
           "label": "Full width"
         }
       ]
     },
    {
       "type": "checkbox",
       "id": "show_offset",
       "label": "Enable Offset (left & right)",
       "default": true,
        "info": "It will work if you select the \"Full width\" of the page"
     },
    {
     "type": "header",
     "content": "colors"
   },
    {
        "type": "color_scheme",
        "id": "color_scheme",
        "label": "t:sections.all.colors.label",
        "default": "accent-1"
      }
  ],
  "blocks": [
     {
      "type": "countdown_timer",
      "name": "Countdown timer",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "index",
          "label": "Show on home page only",
          "default": true
        },
		{
          "type": "checkbox",
          "id": "no_index",
          "label": "Hide on home page only",
          "default": false
        },
        {
         "type": "header",
         "content": "Countdown timer"
       },
        {
          "type": "checkbox",
          "id": "timer",
          "label": "Enable",
          "default": true
        },
        {
        "type": "number",
        "id": "countdown_year",
         "label": "Year",
        "info": "Write the year in the following format: 2023",
        "placeholder": "2023"
      },
      {
          "type": "select",
          "id": "countdown_month",
          "label": "Month",
          "options": [
            {
              "value": "01",
              "label": "Jan"
            },
            {
              "value": "02",
              "label": "Feb"
            },
            {
              "value": "03",
              "label": "Mar"
            },
            {
              "value": "04",
              "label": "Apr"
            },
            {
              "value": "05",
              "label": "May"
            },
            {
              "value": "06",
              "label": "Jun"
            },
            {
              "value": "07",
              "label": "Jul"
            },
            {
              "value": "08",
              "label": "Aug"
            },
            {
              "value": "09",
              "label": "Sept"
            },
            {
              "value": "10",
              "label": "Oct"
            },
            {
              "value": "11",
              "label": "Nov"
            },
            {
              "value": "12",
              "label": "Dec"
            }
          ],
          "default": "01"
        },
        {
          "type": "select",
          "id": "countdown_days",
          "label": "Day",
          "options": [
            {
              "value": "01",
              "label": "1"
            },
            {
              "value": "02",
              "label": "2"
            },
            {
              "value": "03",
              "label": "3"
            },
            {
              "value": "04",
              "label": "4"
            },
            {
              "value": "05",
              "label": "5"
            },
            {
              "value": "06",
              "label": "6"
            },
            {
              "value": "07",
              "label": "7"
            },
            {
              "value": "08",
              "label": "8"
            },
            {
              "value": "09",
              "label": "9"
            },
            {
              "value": "10",
              "label": "10"
            },
            {
              "value": "11",
              "label": "11"
            },
            {
              "value": "12",
              "label": "12"
            },
            {
              "value": "13",
              "label": "13"
            },
            {
              "value": "14",
              "label": "14"
            },
            {
              "value": "15",
              "label": "15"
            },
            {
              "value": "16",
              "label": "16"
            },
            {
              "value": "17",
              "label": "17"
            },
            {
              "value": "18",
              "label": "18"
            },
            {
              "value": "19",
              "label": "19"
            },
            {
              "value": "20",
              "label": "20"
            },
            {
              "value": "21",
              "label": "21"
            },
            {
              "value": "22",
              "label": "22"
            },
            {
              "value": "23",
              "label": "23"
            },
            {
              "value": "24",
              "label": "24"
            },
            {
              "value": "25",
              "label": "25"
            },
            {
              "value": "26",
              "label": "26"
            },
            {
              "value": "27",
              "label": "27"
            },
            {
              "value": "28",
              "label": "28"
            },
            {
              "value": "29",
              "label": "29"
            },
            {
              "value": "30",
              "label": "30"
            },
            {
              "value": "31",
              "label": "31"
            }
          ],
          "default": "01"
        },
        {
         "type": "header",
         "content": "Button"
       },
        {
          "type": "text",
          "id": "link_label",
          "label": "Button label"
        },
        {
             "type": "checkbox",
             "id": "button_icon",
             "label": "Use arrow icon",
             "default": false
           },
        {
             "type": "checkbox",
             "id": "button_icon_mobile",
             "label": "Enable arrow icon on mobile",
             "default": false
           },
        {
          "type": "url",
          "id": "link",
          "label": "Button link"
        },
        {
             "type": "select",
             "id": "button_type",
             "label": "Button type",
             "default": "primary",
             "options": [
               {
                 "value": "secondary",
                 "label": "Secondary"
               },
               {
                 "value": "primary",
                 "label": "Primary"
               }
             ]
           },
        {
             "type": "select",
             "id": "button_size",
             "label": "Button size",
             "default": "extra--small",
             "options": [
               {
                 "value": "extra--medium",
                 "label": "Large"
               },
			        {
                 "value": "small",
                 "label": "Medium"
               },
                {
                 "value": "extra--small",
                 "label": "Small"
               }
             ]
           },
        {
         "type": "header",
         "content": "Content"
       },
       {
            "type": "textarea",
            "id": "text",
            "default": "Welcome to our store",
            "label": "t:sections.announcement-bar.blocks.announcement.settings.text.label"
          },
          {
            "type": "checkbox",
            "id": "enable_close_btn",
            "label": "Enable close button",
            "default": true
          },
        {
           "type": "header",
           "content": "Colors"
         },
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:sections.all.colors.label",
          "default": "background-2"
        },
           {
            "type": "checkbox",
            "id": "custom_colors",
            "label": "Replace with your custom colors",
            "default": false
          },
          {
            "type": "color",
            "id": "foreground",
            "default": "#121212",
            "label": "Foreground color"
          },
          {
            "type": "color",
            "id": "background",
            "default": "#F7F8FC",
            "label": "Background color"
          }
      ]
    },
    {
      "type": "announcement",
      "name": "t:sections.announcement-bar.blocks.announcement.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "index",
          "label": "Show on home page only",
          "default": true
        },
		{
          "type": "checkbox",
          "id": "no_index",
          "label": "Hide on home page only",
          "default": false
        },
        {
          "type": "textarea",
          "id": "text",
          "default": "Welcome to our store",
          "label": "t:sections.announcement-bar.blocks.announcement.settings.text.label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.announcement-bar.blocks.announcement.settings.link.label"
        },
        {
       "type": "header",
       "content": "Social media"
     },
      {
       "type": "checkbox",
       "id": "social_media",
       "default": true,
       "label": "Enable"
     },
      {
       "type": "header",
       "content": "t:sections.footer.settings.header__3.content",
       "info": "t:sections.footer.settings.header__4.info"
     },
     {
       "type": "checkbox",
       "id": "country_selector",
       "default": true,
       "label": "Enable"
     },
     {
       "type": "header",
       "content": "t:sections.footer.settings.header__5.content",
       "info": "t:sections.footer.settings.header__6.info"
     },
     {
       "type": "checkbox",
       "id": "language_selector",
       "default": true,
       "label": "Enable"
     },
        {
         "type": "header",
         "content": "Collapsible content"
       },
       {
          "type": "checkbox",
          "id": "collapsible_content",
          "label": "Enable",
          "default": true
        },
        {
          "type": "text",
          "id": "toggle_button",
          "default": "More",
          "label": "Toggle button label"
        },
        {
          "type": "text",
          "id": "subheading",
          "default": "Subheading",
          "label": "Subheading"
        },
        {
          "type": "text",
          "id": "heading",
          "default": "Announcement title",
          "label": "Heading"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "t:sections.rich-text.blocks.heading.settings.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.rich-text.blocks.heading.settings.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.rich-text.blocks.heading.settings.heading_size.options__3.label"
            },
             {
              "value": "hxl",
              "label": "t:sections.rich-text.blocks.heading.settings.heading_size.options__4.label"
            }
          ],
          "default": "h1",
          "label": "t:sections.rich-text.blocks.heading.settings.heading_size.label"
        },
        {
          "type": "richtext",
          "id": "description",
          "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Include information about availability, style, or even a review.</p>",
          "label": "t:sections.rich-text.blocks.text.settings.text.label"
        },
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "t:sections.rich-text.blocks.button.settings.button_label.label"
        },
         {
             "type": "checkbox",
             "id": "button_icon",
             "label": "Use arrow icon",
             "default": false
           },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.rich-text.blocks.button.settings.button_link.label"
        },
		{
          "type": "select",
          "id": "button_style",
          "label": "Button style",
          "default": "primary",
          "options": [
            {
              "value": "secondary",
              "label": "Secondary"
            },
            {
              "value": "primary",
              "label": "Primary"
            }
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Button size",
          "default": "medium",
          "options": [
          {
            "value": "large",
            "label": "Large"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
          {
            "value": "small",
            "label": "Small"
            }
          ]
        },
        {
          "type": "select",
          "id": "alignment",
          "options": [
            {
              "value": "left",
              "label": "t:sections.image-with-text.settings.mobile_content_alignment.options__1.label"
            },
            {
              "value": "center",
              "label": "t:sections.image-with-text.settings.mobile_content_alignment.options__2.label"
            },
            {
              "value": "right",
              "label": "t:sections.image-with-text.settings.mobile_content_alignment.options__3.label"
            }
          ],
          "default": "center",
          "label": "Content alignment"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "select",
          "id": "height",
          "options": [
            {
              "value": "adapt",
              "label": "t:sections.image-with-text.settings.height.options__1.label"
            },
            {
              "value": "small",
              "label": "t:sections.image-with-text.settings.height.options__2.label"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "t:sections.image-with-text.settings.height.options__3.label"
            }
          ],
          "default": "adapt",
          "label": "Image height",
          "info": "It works unless you enable \"Show as a background image\""
        },
        {
          "type": "checkbox",
          "id": "background_image",
          "label": "Show as a background image",
          "default": false
        },
        {
          "type": "range",
          "id": "image_overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "Overlay opacity on image",
          "default": 0,
          "info": "Applicable when the \"show as a background image\" is enabled"
        },
        {
          "type": "select",
          "id": "section_height",
          "options": [
            {
              "value": "small",
              "label": "t:sections.image-with-text.settings.height.options__2.label"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "t:sections.image-with-text.settings.height.options__3.label"
            }
          ],
          "default": "medium",
          "label": "Section height",
          "info": "It works when you enable \"Show as a background image\""
        },
    {
     "type": "header",
     "content": "colors"
   },

        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:sections.all.colors.label",
          "default": "inverse"
        }


      ]
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "announcement"
      }
    ]
  }
}
{% endschema %}
