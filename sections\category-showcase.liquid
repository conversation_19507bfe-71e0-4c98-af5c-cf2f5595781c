{{ 'category-showcase.css' | asset_url | stylesheet_tag }}

<script src="{{ 'category-showcase.js' | asset_url }}" defer="defer"></script>

{%- style -%}
  /* Global overflow prevention - highest priority */
  body {
    overflow-x: hidden !important;
  }
  
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.5 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.5 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  {% if section.settings.custom_colors %}
    .category-showcase .category-card:nth-child(1) { --card-accent: {{ section.settings.color_accent_1 }}; }
    .category-showcase .category-card:nth-child(2) { --card-accent: {{ section.settings.color_accent_2 }}; }
    .category-showcase .category-card:nth-child(3) { --card-accent: {{ section.settings.color_accent_3 }}; }
    .category-showcase .category-card:nth-child(4) { --card-accent: {{ section.settings.color_accent_4 }}; }
    .category-showcase .category-card:nth-child(5) { --card-accent: {{ section.settings.color_accent_5 }}; }
    .category-showcase .category-card:nth-child(6) { --card-accent: {{ section.settings.color_accent_6 }}; }
  {% endif %}

  {%- comment -%} Custom width and gap settings {%- endcomment -%}
  .category-showcase {
    {%- liquid
      case section.settings.gap_size
        when 'small'
          assign gap_vertical = '1rem'
        when 'medium'
          assign gap_vertical = '2rem'
        when 'large'
          assign gap_vertical = '3rem'
        when 'extra-large'
          assign gap_vertical = '4rem'
        else
          assign gap_vertical = '2rem'
      endcase

      case section.settings.gap_horizontal
        when 'small'
          assign gap_horizontal = '1rem'
        when 'medium'
          assign gap_horizontal = '2rem'
        when 'large'
          assign gap_horizontal = '3rem'
        when 'extra-large'
          assign gap_horizontal = '4rem'
        else
          assign gap_horizontal = '2rem'
      endcase
    -%}
    
    --custom-gap-vertical: {{ gap_vertical }};
    --custom-gap-horizontal: {{ gap_horizontal }};
  }

  {%- comment -%} Individual category widths - Desktop only (not for custom-rows) {%- endcomment -%}
  {%- unless section.settings.layout_style == 'custom-rows' -%}
    {%- for block in section.blocks -%}
      {%- if block.settings.category_width_desktop != 'auto' -%}
        {%- liquid
          case block.settings.category_width_desktop
            when '1-2'
              assign category_width = 'calc(50% - var(--custom-gap-horizontal) / 2)'
              assign category_height = block.settings.custom_height | append: 'px'
            when '1-3'
              assign category_width = 'calc(33.333% - var(--custom-gap-horizontal) * 2 / 3)'
              assign category_height = block.settings.custom_height | append: 'px'
            when '1-4'
              assign category_width = 'calc(25% - var(--custom-gap-horizontal) * 3 / 4)'
              assign category_height = block.settings.custom_height | append: 'px'
            when '1-5'
              assign category_width = 'calc(20% - var(--custom-gap-horizontal) * 4 / 5)'
              assign category_height = block.settings.custom_height | append: 'px'
            when '1-6'
              assign category_width = 'calc(16.666% - var(--custom-gap-horizontal) * 5 / 6)'
              assign category_height = block.settings.custom_height | append: 'px'
          endcase
        -%}
        @media screen and (min-width: 750px) {
          .category-showcase .category-card:nth-child({{ forloop.index }}) {
            --custom-category-width: {{ category_width }};
            --custom-category-height: {{ category_height }};
            flex: 0 0 {{ category_width }} !important;
            max-width: min({{ category_width }}, 90vw) !important;
            width: min({{ category_width }}, 90vw) !important;
            height: {{ category_height }} !important;
            box-sizing: border-box !important;
            overflow: hidden !important;
          }
        }
      {%- else -%}
        {%- if block.settings.custom_height != 290 -%}
          @media screen and (min-width: 750px) {
            .category-showcase .category-card:nth-child({{ forloop.index }}) {
              --custom-category-height: {{ block.settings.custom_height }}px;
              height: {{ block.settings.custom_height }}px !important;
            }
          }
        {%- endif -%}
      {%- endif -%}
    {%- endfor -%}
  {%- endunless -%}

  {%- comment -%} Custom Row Layout Logic {%- endcomment -%}
  {%- if section.settings.layout_style == 'custom-rows' and section.settings.custom_row_pattern != blank -%}
    {%- liquid
      assign row_pattern = section.settings.custom_row_pattern | replace: ' ', '' | split: ','
      assign pattern_length = row_pattern.size
      assign total_cards = section.blocks.size
    -%}
    
    {%- for block in section.blocks -%}
      {%- liquid
        assign card_index = forloop.index0
        assign pattern_index = card_index | modulo: pattern_length
        assign cards_in_row = row_pattern[pattern_index] | plus: 0
      -%}
      
      {%- comment -%}Calculate position in current row{%- endcomment -%}
      {%- liquid
        assign cards_before_current_row = 0
        assign current_row_start = 0
        
        for i in (0..card_index)
          assign temp_pattern_index = i | modulo: pattern_length
          if i == card_index
            break
          endif
          if temp_pattern_index == 0 and i > 0
            assign cards_before_current_row = i
          endif
        endfor
        
        assign position_in_row = card_index | minus: cards_before_current_row | plus: 1
      -%}
      
      {%- comment -%}Calculate width percentage{%- endcomment -%}
      {%- liquid
        if cards_in_row == 1
          assign card_width_percent = 100
        elsif cards_in_row == 2
          assign card_width_percent = 50
        elsif cards_in_row == 3
          assign card_width_percent = 33.333
        elsif cards_in_row == 4
          assign card_width_percent = 25
        elsif cards_in_row == 5
          assign card_width_percent = 20
        elsif cards_in_row == 6
          assign card_width_percent = 16.666
        else
          assign card_width_percent = 100 | divided_by: cards_in_row
        endif
      -%}
      
      @media screen and (min-width: 750px) {
        .category-showcase.category-showcase .category-grid--custom-rows .category-card:nth-child({{ forloop.index }}) {
          --custom-row-width: {{ card_width_percent }}%;
          width: calc({{ card_width_percent }}% - var(--custom-gap-horizontal) * {{ cards_in_row | minus: 1 }} / {{ cards_in_row }}) !important;
          max-width: calc({{ card_width_percent }}% - var(--custom-gap-horizontal) * {{ cards_in_row | minus: 1 }} / {{ cards_in_row }}) !important;
          flex: 0 0 calc({{ card_width_percent }}% - var(--custom-gap-horizontal) * {{ cards_in_row | minus: 1 }} / {{ cards_in_row }}) !important;
          height: auto !important;
          box-sizing: border-box !important;
        }
      }
    {%- endfor -%}
  {%- endif -%}

  {%- comment -%} View All Button Styles {%- endcomment -%}
  .section-header__top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 2rem;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    padding: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .section-header__text {
    flex: 1;
    min-width: 0;
    max-width: 75%;
    overflow: hidden;
    word-wrap: break-word;
  }

  .section-header__action {
    flex-shrink: 0;
    white-space: nowrap;
    max-width: 25%;
    overflow: hidden;
  }

  /* When there's no header content, align button to the right */
  .section-header__top.button-only {
    justify-content: flex-end;
  }

  .view-all-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    background: transparent;
    border: none;
    color: rgb(var(--color-foreground));
    text-decoration: underline;
    text-underline-offset: 4px;
    text-decoration-thickness: 1px;
    font-weight: 400;
    font-size: 16px;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    transition: none;
  }

  .view-all-button:hover {
    color: rgb(var(--color-foreground));
    text-decoration: underline;
    text-underline-offset: 4px;
    text-decoration-thickness: 1px;
    transform: none;
    transition: none;
  }

  @media screen and (max-width: 750px) {
    .section-header__top {
      flex-direction: column;
      align-items: stretch;
      gap: 0.5rem;
      width: 100%;
      padding: 0;
      margin: 0;
    }

    .section-header__text {
      max-width: 100%;
    }

    .section-header__action {
      align-self: flex-end;
      width: auto;
      max-width: 100%;
    }

    .view-all-button {
      padding: 0.4rem 0;
      font-size: 14px;
      display: inline-flex;
      width: auto;
    }

    /* Mobile: when no header content, center the button */
    .section-header__top.button-only {
      align-items: center;
      justify-content: center;
      padding: 0;
      margin: 0;
    }
    
    /* Mobile specific card image styling */
    .category-card__media {
      background-size: cover !important;
      background-position: center !important;
      background-repeat: no-repeat !important;
    }
    
    /* Mobile specific card content layout */
    .category-card__text {
      display: flex !important;
      flex-direction: row !important;
      align-items: center !important;
      justify-content: space-between !important;
      width: 100% !important;
    }
    
    .category-card__title {
      flex: 1 !important;
      margin: 0 !important;
    }
    
    .category-card__arrow {
      margin-left: 10px !important;
      flex-shrink: 0 !important;
    }
  }

  /* Prevent grid overflow */
  .category-grid {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
    margin: 0 !important;
}
@media screen and (max-width: 750px) {
    .category-grid{

        padding: 10px;
    }

  }


  /* Ensure no individual card causes overflow */
  .category-card {
    max-width: calc(100vw - 30px) !important;
    box-sizing: border-box !important;
  }

  /* Global overflow prevention */
  .category-showcase-wrapper,
  .category-showcase-wrapper *,
  .category-showcase,
  .category-showcase * {
    box-sizing: border-box !important;
    max-width: 100% !important;
  }

  .category-showcase-wrapper {
    overflow-x: hidden !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .page-width {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow-x: hidden !important;
  }
{%- endstyle -%}

{%- liquid
  assign columns_desktop = section.settings.columns_desktop | default: 3
  assign columns_mobile = section.settings.columns_mobile | default: 1
  assign layout_style = section.settings.layout_style | default: 'hexagonal'
-%}

<div class="category-showcase-wrapper gradient" style="overflow-x: hidden; width: 100%; max-width: 100vw; box-sizing: border-box;">
  <div class="category-showcase section-{{ section.id }}-padding isolate" style="overflow-x: hidden; width: 100%; max-width: 100%; box-sizing: border-box;">
    <div class="page-width" style="overflow-x: hidden; max-width: 100%; box-sizing: border-box; margin: 0; padding: 0;">{%- assign has_header_content = false -%}
      {%- if section.settings.heading != blank or section.settings.subheading != blank or section.settings.description != blank -%}
        {%- assign has_header_content = true -%}
      {%- endif -%}
      
      {%- if has_header_content or section.settings.show_view_all_button and section.settings.view_all_url != blank -%}
        <div class="section-header">
          <div class="section-header__top{% unless has_header_content %} button-only{% endunless %}">
            {%- if has_header_content -%}
              <div class="section-header__text">
                {%- if section.settings.subheading != blank -%}
                  <p class="section-subheading animate-fade-up" data-delay="100">
                    {{ section.settings.subheading | escape }}
                  </p>
                {%- endif -%}
                
                {%- if section.settings.heading != blank -%}
                  <h2 class="section-heading {{ section.settings.heading_size }} animate-fade-up" data-delay="200">
                    {{ section.settings.heading | escape }}
                  </h2>
                {%- endif -%}
                
                {%- if section.settings.description != blank -%}
                  <div class="section-description animate-fade-up" data-delay="300">
                    {{ section.settings.description }}
                  </div>
                {%- endif -%}
              </div>
            {%- endif -%}
            
            {%- if section.settings.show_view_all_button and section.settings.view_all_url != blank -%}
              <div class="section-header__action">
                <a href="{{ section.settings.view_all_url }}" class="view-all-button">
                  <span>{{ section.settings.view_all_text | default: 'View All' }}</span>
                </a>
              </div>
            {%- endif -%}
          </div>
        </div>
      {%- endif -%}

      <div class="category-grid category-grid--{{ layout_style }} category-grid--{{ columns_desktop }}-col-desktop category-grid--{{ columns_mobile }}-col-mobile" 
           data-layout="{{ layout_style }}"
           data-columns-desktop="{{ columns_desktop }}"
           data-columns-mobile="{{ columns_mobile }}"
           {% if section.settings.custom_row_pattern != blank %}data-custom-pattern="{{ section.settings.custom_row_pattern }}"{% endif %}>
        
        {%- for block in section.blocks -%}
          {%- liquid
            assign collection = block.settings.collection
            assign custom_image = block.settings.custom_image
            assign featured = block.settings.featured
            assign card_size = section.settings.card_size | default: 'medium'
            assign card_image = custom_image | default: collection.featured_image
            assign card_title = block.settings.custom_title | default: collection.title
            assign card_description = block.settings.description
            assign button_text = block.settings.button_text | default: 'Explore Collection'
            assign product_count = collection.products_count | default: 0
          -%}
          
          {%- comment -%}Add custom row attributes for custom-rows layout{%- endcomment -%}
          {%- liquid
            assign row_data_attrs = ''
            if section.settings.layout_style == 'custom-rows' and section.settings.custom_row_pattern != blank
              assign row_pattern = section.settings.custom_row_pattern | replace: ' ', '' | split: ','
              assign pattern_length = row_pattern.size
              assign card_index = forloop.index0
              assign pattern_index = card_index | modulo: pattern_length
              assign cards_in_row = row_pattern[pattern_index] | plus: 0
              assign row_data_attrs = 'data-row-cards="' | append: cards_in_row | append: '" data-card-index="' | append: forloop.index | append: '"'
            endif
          -%}
          
          <div class="category-card category-card--{{ card_size }}{% if featured %} category-card--featured{% endif %} animate-slide-up" 
               data-delay="{{ forloop.index | times: 100 | plus: 400 }}"
               data-card-index="{{ forloop.index }}"
               {{ row_data_attrs }}
               {% if collection.url != blank %}onclick="window.location.href='{{ collection.url }}'"{% endif %}
               {{ block.shopify_attributes }}>
            
            <div class="category-card__inner">
              
              {%- if card_image -%}
                <div class="category-card__media" style="background-image: url('{{ card_image | image_url: width: 800 }}'); background-size: cover; background-position: center; background-repeat: no-repeat;">
                  <img src="{{ card_image | image_url: width: 800 }}" alt="{{ card_title | escape }}" class="category-card__image" loading="lazy" style="width: 100%; height: 100%; object-fit: cover; object-position: center;">
                  {%- if section.settings.show_overlay -%}
                    <div class="category-card__overlay"></div>
                  {%- endif -%}
                  
                  {%- if section.settings.show_trending and block.settings.category_width_desktop == '1-3' -%}
                    <div class="category-card__badge">
                      <span class="badge badge--trending">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon icon-trending">
                          <path d="M7 14L12 9L16 13L21 8M21 8H16M21 8V13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Trending
                      </span>
                    </div>
                  {%- endif -%}
                </div>
              {%- else -%}
                <div class="category-card__media category-card__media--placeholder">
                  {{ 'collection-1' | placeholder_svg_tag: 'category-card__image placeholder-svg' }}
                  {%- if section.settings.show_overlay -%}
                    <div class="category-card__overlay"></div>
                  {%- endif -%}
                </div>
              {%- endif -%}
              
              <div class="category-card__content">
                <div class="category-card__text">
                  <h3 class="category-card__title">
                    {% if collection.url != blank %}
                      <a href="{{ collection.url }}" class="category-card__link">
                        {{ card_title | escape }}
                      </a>
                    {% else %}
                      {{ card_title | escape }}
                    {% endif %}
                  </h3>
                  
                  {%- if card_description != blank -%}
                    <p class="category-card__description">
                      {{ card_description | escape }}
                    </p>
                  {%- endif -%}
                </div>
                
                <!-- Small arrow icon in bottom right -->
                <div class="category-card__arrow">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon icon-arrow-small">
                    <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
              </div>
              
              {%- if section.settings.enable_3d_effect -%}
                <div class="category-card__shine"></div>
              {%- endif -%}
            </div>
          </div>
        {%- endfor -%}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Category Showcase",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "heading",
      "default": "Explore from our Categories",
      "label": "Heading"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "View All Button"
    },
    {
      "type": "checkbox",
      "id": "show_view_all_button",
      "label": "Show view all button",
      "default": false
    },
    {
      "type": "text",
      "id": "view_all_text",
      "label": "View all button text",
      "default": "View All"
    },
    {
      "type": "url",
      "id": "view_all_url",
      "label": "View all button URL"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "layout_style",
      "label": "Layout style",
      "options": [
        {
          "value": "grid",
          "label": "Classic Grid"
        },
        {
          "value": "hexagonal",
          "label": "Hexagonal"
        },
        {
          "value": "masonry",
          "label": "Masonry"
        },
        {
          "value": "floating",
          "label": "Floating Cards"
        },
        {
          "value": "custom-rows",
          "label": "Custom Row Layout"
        }
      ],
      "default": "hexagonal"
    },
    {
      "type": "text",
      "id": "custom_row_pattern",
      "label": "Custom row pattern",
      "info": "Define categories per row (e.g., '2,3' = 2 in row 1, 3 in row 2, then repeat). Examples: '1,2,3' for pyramid layout, '3,2' for inverted pyramid, '2,2,2' for consistent pairs. Works with ALL layout styles when specified.",
      "placeholder": "2,3"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 2,
      "max": 6,
      "step": 1,
      "default": 3,
      "label": "Number of columns on desktop"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Number of columns on mobile",
      "options": [
        {
          "value": "1",
          "label": "1 column"
        },
        {
          "value": "2",
          "label": "2 columns"
        }
      ],
      "default": "1"
    },
    {
      "type": "select",
      "id": "card_size",
      "label": "Card size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "medium"
    },
    {
      "type": "select",
      "id": "gap_size",
      "label": "Gap between categories (vertical)",
      "options": [
        {
          "value": "small",
          "label": "Small (1rem)"
        },
        {
          "value": "medium",
          "label": "Medium (2rem)"
        },
        {
          "value": "large",
          "label": "Large (3rem)"
        },
        {
          "value": "extra-large",
          "label": "Extra Large (4rem)"
        }
      ],
      "default": "medium"
    },
    {
      "type": "select",
      "id": "gap_horizontal",
      "label": "Gap between categories (horizontal)",
      "options": [
        {
          "value": "small",
          "label": "Small (1rem)"
        },
        {
          "value": "medium",
          "label": "Medium (2rem)"
        },
        {
          "value": "large",
          "label": "Large (3rem)"
        },
        {
          "value": "extra-large",
          "label": "Extra Large (4rem)"
        }
      ],
      "default": "medium"
    },
    {
      "type": "header",
      "content": "Visual Settings"
    },
    {
      "type": "checkbox",
      "id": "show_overlay",
      "default": true,
      "label": "Show image overlay"
    },
    {
      "type": "checkbox",
      "id": "enable_3d_effect",
      "default": true,
      "label": "Enable 3D hover effects"
    },
    {
      "type": "checkbox",
      "id": "show_trending",
      "default": true,
      "label": "Show trending badges"
    },
    {
      "type": "header",
      "content": "Custom Colors"
    },
    {
      "type": "checkbox",
      "id": "custom_colors",
      "label": "Enable custom accent colors",
      "default": false
    },
    {
      "type": "color",
      "id": "color_accent_1",
      "label": "Card 1 accent color",
      "default": "#667eea"
    },
    {
      "type": "color",
      "id": "color_accent_2",
      "label": "Card 2 accent color", 
      "default": "#764ba2"
    },
    {
      "type": "color",
      "id": "color_accent_3",
      "label": "Card 3 accent color",
      "default": "#f093fb"
    },
    {
      "type": "color",
      "id": "color_accent_4",
      "label": "Card 4 accent color",
      "default": "#4facfe"
    },
    {
      "type": "color",
      "id": "color_accent_5",
      "label": "Card 5 accent color",
      "default": "#43e97b"
    },
    {
      "type": "color",
      "id": "color_accent_6",
      "label": "Card 6 accent color",
      "default": "#fa709a"
    },
    {
      "type": "header",
      "content": "Section Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "category",
      "name": "Category",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "Collection"
        },
        {
          "type": "image_picker",
          "id": "custom_image",
          "label": "Custom image (optional)"
        },
        {
          "type": "text",
          "id": "custom_title",
          "label": "Custom title (optional)"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Description"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button text",
          "default": "Explore Collection"
        },
        {
          "type": "checkbox",
          "id": "featured",
          "label": "Featured category",
          "info": "Makes this category larger and more prominent",
          "default": false
        },
        {
          "type": "select",
          "id": "category_width_desktop",
          "label": "Category width on desktop",
          "options": [
            {
              "value": "auto",
              "label": "Auto (Based on layout)"
            },
            {
              "value": "1-2",
              "label": "1/2 width (50%)"
            },
            {
              "value": "1-3",
              "label": "1/3 width (638px × 290px)"
            },
            {
              "value": "1-4",
              "label": "1/4 width (309px × 290px)"
            },
            {
              "value": "1-5",
              "label": "1/5 width (20%)"
            },
            {
              "value": "1-6",
              "label": "1/6 width (17%)"
            }
          ],
          "default": "auto"
        },
        {
          "type": "range",
          "id": "custom_height",
          "min": 200,
          "max": 600,
          "step": 10,
          "unit": "px",
          "label": "Custom height",
          "default": 290,
          "info": "Override default height for this category"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Category Showcase",
      "blocks": [
        {
          "type": "category"
        },
        {
          "type": "category"
        },
        {
          "type": "category"
        },
        {
          "type": "category"
        },
        {
          "type": "category"
        },
        {
          "type": "category"
        }
      ]
    }
  ]
}
{% endschema %}
