{%- liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
-%}
{{ 'section-product-tabs.css' | asset_url | stylesheet_tag }}
{{ 'component-rating.css' | asset_url | stylesheet_tag }}
{{ 'countdown-timer.css' | asset_url | stylesheet_tag }}
{{ 'stock-countdwon.css' | asset_url | stylesheet_tag }}
{{ 'product-tooltip.css' | asset_url | stylesheet_tag }}
{{ 'section-title.css' | asset_url | stylesheet_tag }}
{{ 'product-card.css' | asset_url | stylesheet_tag }}

{%- comment -%}
  {{ 'component-price.css' | asset_url | stylesheet_tag }}
{%- endcomment -%}

<style>
  .rating {
    display: inline-block;
    margin: 0;
  }

  .product .rating-star {
    --letter-spacing: 0.8;
    --font-size: 2;
  }

  .product__card .rating-star {
    --letter-spacing: 0.7;
    --font-size: 2;
  }

  .rating-star {
    --percent: calc(
      (
          var(--rating) / var(--rating-max) + var(--rating-decimal) *
            var(--font-size) /
            (var(--rating-max) * (var(--letter-spacing) + var(--font-size)))
        ) * 100%
    );
    letter-spacing: calc(var(--letter-spacing) * 1rem);
    font-size: calc(var(--font-size) * 1rem);
    line-height: 1;
    display: inline-block;
    font-family: Times;
    margin: 0;
  }

  .rating-star::before {
    content: '★★★★★';
    background: linear-gradient(
      90deg,
      var(--color-icon) var(--percent),
      rgba(var(--color-foreground), 0.15) var(--percent)
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .rating-text {
    display: none;
  }

  .rating-count {
    display: inline-block;
    margin: 0;
  }

  @media (forced-colors: active) {
    .rating {
      display: none;
    }

    .rating-text {
      display: block;
    }
  }
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.mobile_padding_top }}px;
      padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
    }
    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top }}px;
        padding-bottom: {{ section.settings.padding_bottom }}px;
      }
    }
.tab_sec_title_tab_title .section-heading.text-space-between {
    margin: 0;
}

.tab_sec_title_tab_title.text-space-between_area {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}
  
</style>

{% if theme_rtl %}
  {{ 'section-product-tabs-rtl.css' | asset_url | stylesheet_tag }}
  {{ 'product-card-rtl.css' | asset_url | stylesheet_tag }}
{% endif %}

{%- liquid
  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif

  assign flex_align = 'justify-content-center'
  if section.settings.text_center == 'left'
    assign flex_align = 'justify-content-start'
  elsif section.settings.text_center == 'right'
    assign flex_align = 'justify-content-end'
  endif
-%}

<div
  class="product__section section-{{ section.id }}-padding section-products--tabs nrbtabs_area"
  data-section-id="{{ section.id }}"
  data-section-type="product-tab"
>
  <div class="{{ container }}">
    <div class="tab_sec_title_tab_title {% if section.settings.text_center =="space-between"%} text-space-between_area {%endif%}  ">
      {% if section.settings.heading != blank or section.settings.subtitle != blank %}
        <div class="section-heading text-{{ section.settings.text_center }}  {% if section.settings.border_line or section.settings.show_navigation_topbar %} mb-50 {% else %} mb-30{% endif %}">
          {% if section.settings.heading_position == 'bottom' and section.settings.subtitle != blank %}
            <span class="section-heading__sub_title">{{- section.settings.subtitle -}}</span>
          {% endif %}
          <h2 class="section-heading__title {% if section.settings.heading_position == "top" and section.settings.subtitle != blank and section.settings.border_line %}heading__space--bottom{% endif %} ">
            {% if section.settings.border_line %}<span>{% endif %}
            {{- section.settings.heading -}}
            {% if section.settings.border_line %}</span>{% endif %}
          </h2>
          {% if section.settings.heading_position == 'top' and section.settings.subtitle != blank %}
            <span class="section-heading__sub_title">{{- section.settings.subtitle -}}</span>
          {% endif %}
        </div>
      {% endif %}
  
      <ul class="product__tab--one product__tab--btn d-flex {{ flex_align }} mb-35">
        {%- for block in section.blocks -%}
          {%- case block.type -%}
            {%- when 'product_tab' -%}
              <li
                class="product__tab--btn__list {% if forloop.first == true %}active{% endif %}"
                data-toggle="tab"
                data-target="#tab__target--{{ block.id }}"
                {{ block.shopify_attributes }}
              >
                {{ block.settings.title }}
              </li>
          {%- endcase -%}
        {%- endfor -%}
      </ul>
    </div>  
    <div class="tab_content">
      {%- for block in section.blocks -%}
        {%- case block.type -%}
          {%- when 'product_tab' -%}
            {%- liquid
              assign featured_product_limit = block.settings.products_to_show
              assign collection = collections[block.settings.collection]

              assign more_in_collection = false
              if collection.all_products_count > featured_product_limit
                assign more_in_collection = true
              endif
            -%}
            <div
              id="tab__target--{{ block.id }}"
              class="tab_pane {% if forloop.first == true %}active show{% endif %}"
              {{ block.shopify_attributes }}
            >
              <div class="product__section--inner">
                <div class="row row-cols-xxl-{{ section.settings.product_column }} row-cols-xl-{{ section.settings.product_column }} row-cols-lg-{{ section.settings.product_column_laptop }} row-cols-md-{{ section.settings.product_column_tablet }} row-cols-{{ section.settings.product_column_mobile }}">
                  {%- for product in collection.products limit: featured_product_limit -%}
                    <div class="col mb-30">
                      {% render 'product-card',
                        product_card_product: product,
                        media_size: section.settings.image_ratio,
                        show_secondary_image: section.settings.show_secondary_image,
                        show_vendor: section.settings.show_vendor,
                        show_badge: section.settings.show_badges,
                        show_cart_button: section.settings.show_cart_button,
                        show_preorder_button: section.settings.show_preorder_button,
                        show_quick_view: section.settings.show_quick_view_button,
                        show_quick_compare: section.settings.show_compare_view_button,
                        show_wishlist: section.settings.show_wishlist_button,
                        show_countdown: section.settings.show_countdown,
                        show_title: section.settings.show_title,
                        show_price: section.settings.show_price,
                        show_rating: section.settings.show_product_rating,
                        card_style: section.settings.card_style,
                        color_swatches: section.settings.color_swatches,
                        color_scheme: section.settings.product_card_color_scheme,
                        spacing: section.settings.product_card_spacing,
                        corner_radius: section.settings.product_card_radius,
                        inventory_status: section.settings.inventory_status
                      %}
                    </div>
                  {%- else -%}
                    {%- assign a = 1 -%}
                    {%- for i in (1..featured_product_limit) -%}
                      {%- liquid
                        assign product_item = 'product-' | append: a
                        assign a = a | plus: 1
                      -%}
                      {%- render 'product-card-placeholder', product_item: product_item, productItem: 'col mb-30' -%}
                      {%- liquid
                        if a == 7
                          assign a = 1
                        endif
                      -%}
                    {%- endfor -%}
                  {%- endfor -%}
                </div>

                {% liquid
                  if section.block.button_style == 'primary'
                    assign button_class = ''
                  else
                    assign button_class = 'button--secondary'
                  endif
                %}

                {%- if block.settings.show_view_all and more_in_collection and block.settings.button_label != blank -%}
                  <div class="collection--button text-center mt-20">
                    <a
                      href="{{ collection.url }}"
                      class="button button--{{ block.settings.button_size }} {{ button_class }}  {% if block.settings.button_icon %} button--with-icon{% endif %}"
                    >
                      {{- block.settings.button_label -}}

                      {% if block.settings.button_icon %}
                        <span class="button--icon button--icon-right"> {% render 'icon-arrow-right' %} </span>
                      {% endif %}
                    </a>
                  </div>
                {%- endif -%}
              </div>
            </div>
        {%- endcase -%}
      {%- endfor -%}
    </div>
  </div>
</div>

{% schema %}
 {
   "name": "Collection tabs",
   "settings": [
{
     "type": "header",
     "content": "General"
   },
   {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
  {
      "type": "header",
      "content": "Section header"
    },
     {
      "type": "inline_richtext",
      "id": "subtitle",
      "default": "Subtitle",
      "label": "Subheading"
    },
    {
      "type": "text",
      "id": "heading",
      "default": "Product Tabs",
      "label": "t:sections.featured-collection.settings.title.label"
    },
    {
      "type": "select",
      "id": "heading_position",
      "options": [
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        }
      ],
      "default": "bottom",
      "label": "Heading position"
    },
    {
      "type": "select",
      "id": "text_center",
      "options": [
        {
          "value": "right",
          "label": "Right"
        },
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "space-between",
          "label": "Space Between"
        }
      ],
      "default": "center",
      "label": "Alignment"
    },
    {
       "type": "checkbox",
       "id": "border_line",
       "label": "Enable border line",
       "default": true
     },
     {
     "type": "header",
     "content": "Layout"
   },
     {
        "type": "select",
        "id": "product_column",
        "label": "Number of columns on desktop",
        "options": [
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          },
          {
            "value": "5",
            "label": "5"
          }
        ],
        "default": "5"
      },
      {
        "type": "select",
        "id": "product_column_laptop",
        "label": "Number of columns on laptop",
        "options": [
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          }
        ],
        "default": "4"
      },
      {
        "type": "select",
        "id": "product_column_tablet",
        "label": "Number of columns on tablet",
        "options": [
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          }
        ],
        "default": "3"
      },
     {
        "type": "select",
        "id": "product_column_mobile",
        "label": "Number of columns on mobile",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          }
        ],
        "default": "2"
      },
    {
     "type": "header",
     "content": "Product card"
   },
     {
        "type": "select",
        "id": "card_style",
        "label": "Card style",
        "options": [
          {
            "value": "style_1",
            "label": "Style 1"
          },
          {
            "value": "style_2",
            "label": "Style 2"
          }
        ],
        "default": "style_2"
      },
    {
       "type": "select",
       "id": "image_ratio",
       "options": [
         {
           "value": "adapt",
           "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__1.label"
         },
         {
           "value": "portrait",
           "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__2.label"
         },
         {
           "value": "square",
           "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__3.label"
         },
          {
           "value": "landscape",
           "label": "Landscape"
         }
       ],
       "default": "adapt",
       "label": "t:sections.main-collection-product-grid.settings.image_ratio.label"
     },
     {
       "type": "checkbox",
       "id": "show_secondary_image",
       "default": false,
       "label": "t:sections.main-collection-product-grid.settings.show_secondary_image.label"
     },
      {
        "type": "checkbox",
        "id": "color_swatches",
        "default": true,
        "label": "Enable color swatches",
        "info": "To display color swatches, you need to enable it. [Learn more](https:\/\/team90degree.com\/suruchi-theme\/theme-settings\/color-swatches)."
      },
     {
       "type": "checkbox",
       "id": "show_badges",
       "default": true,
       "label": "Show badges"
     },
     {
       "type": "checkbox",
       "id": "show_cart_button",
       "default": true,
       "label": "Show cart button"
     },
     {
        "type": "checkbox",
        "id": "show_preorder_button",
        "default": true,
        "label": "Show pre-order button",
        "info": "You may want to allow customers to purchase out-of-stock items. [Learn more](https://team90degree.com/suruchi-theme/general-topics-faq/how-to-enable-the-pre-order-product-to-your-store)"
      },
     {
       "type": "checkbox",
       "id": "show_quick_view_button",
       "default": true,
       "label": "Show quick view"
     },
     {
       "type": "checkbox",
       "id": "show_compare_view_button",
       "default": true,
       "label": "Show compare button"
     },
     {
       "type": "checkbox",
       "id": "show_wishlist_button",
       "default": true,
       "label": "Show wishlist button"
     },
     {
       "type": "checkbox",
       "id": "show_title",
       "default": true,
       "label": "Show title"
     },
     {
       "type": "checkbox",
       "id": "show_price",
       "default": true,
       "label": "Show price"
     },
     {
       "type": "checkbox",
       "id": "show_vendor",
       "default": false,
       "label": "t:sections.main-collection-product-grid.settings.show_vendor.label"
     },
      {
       "type": "checkbox",
       "id": "show_countdown",
       "default": false,
       "label": "Show countdown",
       "info": "To display countdown timer, you need to enable it. [Learn more](https://team90degree.com/suruchi-theme/collections-and-products/products/add-a-countdown-timer-to-the-product)."
   },

   {
     "type": "checkbox",
     "id": "show_product_rating",
     "default": false,
     "label": "Show product rating"
   },
      {
        "type": "checkbox",
        "id": "inventory_status",
        "label": "Show inventory status",
        "default": false
    },
   {
      "type": "checkbox",
      "id": "product_card_radius",
      "label": "Round corner",
      "default": false
    }, 
    {
      "type": "checkbox",
      "id": "product_card_spacing",
      "label": "Card spacing",
      "default": false
    }, 
   {
      "type": "color_scheme",
      "id": "product_card_color_scheme",
      "label": "Product card color scheme",
      "default": "background-1"
    },
   {
     "type": "header",
     "content": "Section padding"
   },
   {
         "type": "paragraph",
         "content": "Desktop"
       },
       {
         "type": "range",
         "id": "padding_top",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding top",
         "default": 0
       },
       {
         "type": "range",
         "id": "padding_bottom",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding bottom",
         "default": 0
       },
	{
         "type": "paragraph",
         "content": "Mobile"
       },
	{
         "type": "range",
         "id": "mobile_padding_top",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding top",
         "default": 0
       },
       {
         "type": "range",
         "id": "mobile_padding_bottom",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding bottom",
         "default": 0
       }
],
"blocks": [
      {
         "type": "product_tab",
         "name": "Collection",
         "settings": [
		{
             "type": "text",
             "id": "title",
             "default": "Tab",
             "label": "Heading"
           },
           {
             "type": "collection",
             "id": "collection",
             "label": "Collection"
           },
           {
             "type": "range",
             "id": "products_to_show",
             "min": 2,
             "max": 50,
             "step": 1,
             "default": 8,
             "label": "t:sections.featured-collection.settings.products_to_show.label"
           },
           {
      "type": "header",
      "content": "Button settings"
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "default": true,
      "label": "t:sections.featured-collection.settings.show_view_all.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Button label",
      "default": "View all"
    },
           {
             "type": "checkbox",
             "id": "button_icon",
             "label": "Use arrow icon",
             "default": true
           },
	{
        "type": "select",
        "id": "button_style",
        "label": "Button style",
        "default": "primary",
        "options": [
          {
            "value": "secondary",
            "label": "Secondary"
          },
          {
            "value": "primary",
            "label": "Primary"
          }
        ]
      },
      {
        "type": "select",
        "id": "button_size",
        "label": "Button size",
        "default": "medium",
        "options": [
          {
            "value": "large",
            "label": "Large"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
                  {
            "value": "small",
            "label": "Small"
          }
        ]
      }
   ]
 }
],
"presets": [
     {
       "name": "Collection tabs",
	"blocks":[
			{
                 "type": "product_tab",
			   "settings":{
                       "title":"Tab 1"
                   }
               },
			{
                 "type": "product_tab",
				"settings":{
                       "title":"Tab 2"
                   }
               }
		]
     }
   ],
   "disabled_on": {
    "groups": ["header","footer"]
  }
 }
{% endschema %}