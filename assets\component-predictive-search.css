.predictive-search:not(.predictive-search-drawer-result) {
    display: none;
    position: absolute;
    top: calc(100% + 0.4rem);
    width: calc(100% + .1rem);
    left: -.1rem;
    border: .1rem solid rgba(var(--color-foreground),.2);
    background-color: #fff;
    z-index: 9;
    border-radius: 1rem;
    overflow-y: auto;
}
.predictive-search__results-groups-wrapper {
  display: flex;
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
  padding-bottom: 1rem;
}
.predictive-search--search-template {
  z-index: 9;
}
@media screen and (max-width: 749px) {
  .predictive-search--header {
    right: 0;
    left: 0;
    top: 100%;
  }
}

@media screen and (max-width: 989px) {
  .predictive-search {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

@media screen and (min-width: 750px) {
  .predictive-search {
    border-top: none;
  }

  .header predictive-search {
    position: relative;
  }
}

predictive-search[open] .predictive-search,
predictive-search[loading] .predictive-search {
  display: block;
}

.predictive-search__heading {
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
  font-size: 1.2rem;
  text-transform: uppercase;
  margin: 0 auto;
  padding: 15px 0 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: calc(100% - 4rem);
  color: rgba(var(--color-foreground), 0.7);
}

predictive-search .spinner {
  width: 1.5rem;
  height: 1.5rem;
  line-height: 0;
}

.predictive-search__heading .spinner {
  margin: 0 0.2rem 0 2rem;
}

predictive-search[loading] .predictive-search__loading-state {
  display: flex;
  justify-content: center;
  padding: 1rem;
}

predictive-search[loading]
  .predictive-search__heading
  ~ .predictive-search__loading-state,
predictive-search[loading] .predictive-search__results-list:first-child {
  display: none;
}

.predictive-search__list-item[aria-selected="true"] > *,
.predictive-search__list-item:hover > * {
  color: rgb(var(--color-foreground));
  background-color: rgba(var(--color-foreground), 0.04);
}
button.predictive-search__item--term {
  align-items: center;
  word-break: break-all;
  line-height: 1.8;
  display: block;
  width: 100%;
  padding: 15px 20px;
  text-align: left;
  display: flex;
  justify-content: space-between;
  font-size: 1.6rem;
}
.predictive-search__item--term .icon-arrow {
  width: 17px;
  height: 17px;
  flex-shrink: 0;
  margin-left: 15px;
  color: rgb(var(--color-link));
}
.predictive-search__image {
  object-fit: contain;
  font-family: "object-fit: contain";
}

/* Custom css */
a.predictive-search__item {
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  text-align: left;
  text-decoration: none;
  width: 100%;
}

h3.predictive-search__item-heading {
  margin: 0;
}
.predictive-search__item-heading {
  margin: 0;
}
.predictive-search__item-content {
  padding-left: 15px;
}
.search__content_inner:not(.predictive--search-drawer-inner)
  .search__input_field
  .input__field {
  padding: 0 8rem 0 20px;
}
.search__input_field .input__field {
  border-radius: 0;
}
.predictive-search__result-group {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  gap: 2rem;
}
@media screen and (min-width: 750px) {
  .predictive-search__result-group:first-child {
    flex: 0 0 26.4rem;
  }
  .predictive-search__results-groups-wrapper--no-products
    .predictive-search__result-group:nth-child(2),
  .predictive-search__result-group:last-child
    .predictive-search__pages-wrapper {
    display: none;
  }
}
@media screen and (max-width: 749px) {
  .predictive-search__results-groups-wrapper {
    flex-direction: column;
  }
}
.logo__center--menu-bottom .reset__button,
.logo__left--menu-bottom .reset__button {
  right: 5rem;
  top: 0.3rem;
}

/* Predictive search in header */
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .predictive--search--in-header .predictive-search__result-group:first-child {
    flex: 0 0 3rem;
    gap: 1rem;
  }
  .predictive--search--in-header .predictive-search__results-groups-wrapper {
    flex-direction: column;
  }
}
@media screen and (min-width: 992px) {
  .predictive--search--in-header.predictive--search-narrow-width
    .predictive-search__result-group:first-child {
    flex: 0 0 3rem;
    gap: 1rem;
  }
  .predictive--search--in-header.predictive--search-narrow-width
    .predictive-search__results-groups-wrapper {
    flex-direction: column;
  }
}
/* Predtive search drawer  */
.predictive-search-drawer-result .predictive-search__result-group:first-child {
  flex: 0 0 3rem;
  gap: 1rem;
}
.predictive-search-drawer-result .predictive-search__results-groups-wrapper {
  flex-direction: column;
}
.category__search--box,
.predictive__search--drawer--form-inner {
  display: grid;
  grid-template-columns: auto 4rem;
  gap: 1rem;
  align-items: center;
  padding: 2rem 0 2rem 2rem;
}
.predictive__search--drawer--form-inner .input__field_form_button {
  right: auto;
  left: 0;
  padding: 0;
}
.predictive__search--drawer--form-inner button.reset__button {
  right: 0;
  top: 0.3rem;
}
.predictive__search--drawer--form-inner .search__input_field .input__field {
  padding: 0 2rem 0 4rem;
  border-radius: 0;
  border-top-color: transparent;
  border-left: transparent;
  border-right-color: transparent;
}
.predictive__search--drawer--form-inner
  .search__input_field
  .input__field:focus,
.predictive__search--drawer--form-inner
  .search__input_field
  .input__field:focus-visible {
  outline: none;
  border-bottom: 0.2rem solid rgba(var(--color-foreground));
  box-shadow: none;
}
.predictive-search.predictive-search--header.predictive-search-drawer-result,
.popular__search--history {
  overflow-y: auto;
  flex-grow: 1;
  width: 100%;
  max-height: 100% !important;
  overflow-x: hidden;
}
.predictive--search-drawer-inner .search.search-modal__form {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.predictive--search-drawer-inner .search-modal__form {
  height: 100%;
}
.predictive--search-drawer-inner {
  height: 100%;
}
.predictive-search.predictive-search-drawer-result {
  display: none;
}

predictive-search[open] .predictive-search.predictive-search-drawer-result {
  display: block;
}
predictive-search[open] .popular__search--history {
  display: none;
}
.predictive-search__result-group:only-child {
  flex-grow: 1;
}

.search__input_field .input__field {
    border-radius: 1rem;
}












