{{ 'welcome-video.css' | asset_url | stylesheet_tag }}

<style>
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
</style>

{%- liquid
  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif
-%}

<div
  class="about__section section-{{ section.id }}-padding"
  data-section-id="{{ section.id }}"
  data-section-type="popup-video"
>
  <div class="{{ container }}">
    <div class="row welcome__video--grid {% if section.settings.image_layout == "image_first" %} welcome__image--first{% endif %}">
      <div class="col-lg-6 welcome__video--content-child">
        <div class="about__content welcome__video--content">
          {%- for block in section.blocks -%}
            {% case block.type %}
              {%- when 'heading' -%}
                <h2
                  class="about__content--maintitle {{ block.settings.heading_size }} mb-0"
                  {{ block.shopify_attributes }}
                >
                  {{ block.settings.heading }}
                </h2>
              {%- when 'subheading' -%}
                <span class="about__content--subtitle color-foreground-accent-1 " {{ block.shopify_attributes }}>
                  {{ block.settings.subheading -}}
                </span>
              {%- when 'text' -%}
                <div class="about__content--desc" {{ block.shopify_attributes }}>
                  {{ block.settings.text }}
                </div>
              {%- when 'signature' -%}
                <div class="about__author position__relative" {{ block.shopify_attributes }}>
                  <div class="d-flex align-items-center sign__author--header">
                    <div class="sign__author--heaidng">
                      <h3 class="about__author--name h4 mb-0">{{ block.settings.heading }}</h3>
                      <span class="about__author--rank">{{ block.settings.subheading }}</span>
                    </div>

                    {%- if block.settings.image != blank -%}
                      <div class="sign--image">
                        <img
                          srcset="
                            {%- if block.settings.settings.image.width >= 165 -%}{{ block.settings.image | image_url: width: 165 }} 165w,{%- endif -%}
                            {%- if block.settings.image.width >= 360 -%}{{ block.settings.image | image_url: width: 360 }} 360w,{%- endif -%}
                            {%- if block.settings.image.width >= 535 -%}{{ block.settings.image | image_url: width: 535 }} 535w,{%- endif -%}
                            {%- if block.settings.image.width >= 750 -%}{{ block.settings.image | image_url: width: 750 }} 750w,{%- endif -%}
                            {%- if block.settings.image.width >= 1070 -%}{{ block.settings.image | image_url: width: 1070 }} 1070w,{%- endif -%}
                            {%- if block.settings.image.width >= 1500 -%}{{ block.settings.image | image_url: width: 1500 }} 1500w,{%- endif -%}
                            {{ block.settings.image | image_url }} {{ block.settings.image.width }}w
                          "
                          src="{{ block.settings.image | image_url: width: 1500 }}"
                          sizes="200px"
                          alt="{{ block.settings.image.alt | escape }}"
                          loading="lazy"
                          width="{{ block.settings.image.width }}"
                          height="{{ block.settings.image.height }}"
                        >
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              {%- when 'button' -%}
                {% liquid
                  if block.settings.button_style == 'primary'
                    assign button_class = ''
                  else
                    assign button_class = 'button--secondary'
                  endif
                %}

                {%- if block.settings.button_label != blank -%}
                  <div class="button__wrapper">
                    <a
                      {% if block.settings.button_link == blank %}
                        role="link" aria-disabled="true"
                      {% else %}
                        href="{{ block.settings.button_link }}"
                      {% endif %}
                      class="button button--{{ block.settings.button_size }} {{ button_class }} {% if block.settings.button_icon %} button--with-icon{% endif %}"
                      {{ block.shopify_attributes }}
                    >
                      {{ block.settings.button_label | escape }}
                      {% if block.settings.button_icon %}
                        <span class="button--icon button--icon-right"> {% render 'icon-arrow-right' %} </span>
                      {% endif %}
                    </a>
                  </div>
                {%- endif -%}
            {%- endcase -%}
          {%- endfor -%}
        </div>
      </div>
      <div class="col-lg-6">
        <div class="about__thumb d-flex">
          <div class="about__thumb--items">
            <div
              class="welcome__video--media welcome__video--media--{{ section.settings.height }} {% if section.settings.image != blank %}media{% else %} placeholder{% endif %}"
              {% if section.settings.height == 'adapt' and section.settings.image != blank %}
                style="padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%;"
              {% endif %}
            >
              {%- if section.settings.image != blank -%}
                {%- capture sizes -%}
                  (min-width: {{ settings.container_lg_width }}px) {{ settings.container_lg_width | minus: 100 | divided_by: 2 }}px,
                  (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)
                {%- endcapture -%}
                {{
                  section.settings.image
                  | image_url: width: 1500
                  | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1500'
                }}
              {%- else -%}
                {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
              {%- endif -%}
            </div>
          </div>
          <div class="about__thumb--items position__relative">
            <div
              class="welcome__video--media welcome__video--media--{{ section.settings.height }} {% if section.settings.image_2 != blank %}media{% else %} placeholder{% endif %}"
              {% if section.settings.height == 'adapt' and section.settings.image_2 != blank %}
                style="padding-bottom: {{ 1 | divided_by: section.settings.image_2.aspect_ratio | times: 100 }}%;"
              {% endif %}
            >
              {%- if section.settings.image_2 != blank -%}
                {%- capture sizes -%}
                  (min-width: {{ settings.container_lg_width }}px) {{ settings.container_lg_width | minus: 100 | divided_by: 2 }}px,
                  (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)
                {%- endcapture -%}
                {{
                  section.settings.image_2
                  | image_url: width: 1500
                  | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1500'
                }}

              {%- else -%}
                {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
              {%- endif -%}
              <div class="banner__bideo--play about__thumb--play deferred-media__poster-button">
                <a
                  class="banner__bideo--play__icon about__thumb--play__icon glightbox"
                  href="{%- if section.settings.video_url.type == 'youtube' -%}https://www.youtube.com/watch?v={{ section.settings.video_url.id }}{%- else -%}https://vimeo.com/{{ section.settings.video_url.id }}{%- endif -%}"
                  data-gallery="video"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="23" viewBox="0 0 31 37">
                    <path id="Polygon_1" data-name="Polygon 1" d="M16.783,2.878a2,2,0,0,1,3.435,0l14.977,25.1A2,2,0,0,1,33.477,31H3.523a2,2,0,0,1-1.717-3.025Z" transform="translate(31) rotate(90)" fill="currentColor"></path>
                  </svg>
                  <span class="visually-hidden">video play</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
 {
   "name": "Welcome video",
   "settings": [
     {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
     {
     "type": "header",
     "content": "Image settings"
   },
	{
         "type": "image_picker",
         "id": "image",
         "label": "First image"
       },
	{
         "type": "image_picker",
         "id": "image_2",
         "label": "Second image"
      },
	 {
       "type": "select",
       "id": "height",
       "options": [
         {
           "value": "adapt",
           "label": "t:sections.image-with-text.settings.height.options__1.label"
         },
         {
           "value": "small",
           "label": "t:sections.image-with-text.settings.height.options__2.label"
         },
	  {
           "value": "medium",
           "label": "Medium"
         },
         {
           "value": "large",
           "label": "t:sections.image-with-text.settings.height.options__3.label"
         }
       ],
       "default": "adapt",
       "label": "t:sections.image-with-text.settings.height.label"
     },
     {
      "type": "select",
      "id": "image_layout",
      "options": [
        {
          "value": "image_first",
          "label": "Image first"
        },
        {
          "value": "text_first",
          "label": "Text first"
        }
      ],
      "default": "text_first",
      "label": "Image position"
    },
     {
     "type": "header",
     "content": "Video settings"
   },
	{
       "type": "video_url",
       "id": "video_url",
       "accept": [
         "youtube",
         "vimeo"
       ],
       "default": "https:\/\/www.youtube.com\/watch?v=_9VUPq3SxOc",
       "label": "t:sections.video.settings.video_url.label",
       "placeholder": "t:sections.video.settings.video_url.placeholder",
       "info": "t:sections.video.settings.video_url.info"
     },
	{
     "type": "header",
     "content": "Section padding"
   },
   {
         "type": "paragraph",
         "content": "Desktop"
       },
       {
         "type": "range",
         "id": "padding_top",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding top",
         "default": 0
       },
       {
         "type": "range",
         "id": "padding_bottom",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding bottom",
         "default": 0
       },
	{
         "type": "paragraph",
         "content": "Mobile"
       },
	{
         "type": "range",
         "id": "mobile_padding_top",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding top",
         "default": 0
       },
       {
         "type": "range",
         "id": "mobile_padding_bottom",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding bottom",
         "default": 0
       }
],
"blocks": [
   {
     "type": "heading",
     "name": "t:sections.image-with-text.blocks.heading.name",
     "limit": 1,
     "settings": [
       {
         "type": "textarea",
         "id": "heading",
         "default": "Heading",
         "label": "t:sections.image-with-text.blocks.heading.settings.heading.label"
       },
       {
         "type": "select",
         "id": "heading_size",
         "options": [
           {
             "value": "h2",
             "label": "t:sections.all.heading_size.options__1.label"
           },
           {
             "value": "h1",
             "label": "t:sections.all.heading_size.options__2.label"
           },
           {
             "value": "h0",
             "label": "t:sections.all.heading_size.options__3.label"
           }
         ],
         "default": "h2",
         "label": "t:sections.all.heading_size.label"
       }
     ]
   },
{
     "type": "subheading",
     "name": "Subheading",
     "limit": 1,
     "settings": [
       {
         "type": "textarea",
         "id": "subheading",
         "default": "Subheading",
         "label": "Subheading"
       }
     ]
   },
{
     "type": "signature",
     "name": "Signature",
     "limit": 1,
     "settings": [
	{
         "type": "text",
         "id": "heading",
         "default": "Name",
         "label": "t:sections.image-with-text.blocks.heading.settings.heading.label"
       },
       {
         "type": "text",
         "id": "subheading",
         "default": "sub title",
         "label": "Subheading"
       },
	{
         "type": "image_picker",
         "id": "image",
         "label": "Sign image"
       }
     ]
   },
   {
     "type": "text",
     "name": "t:sections.image-with-text.blocks.text.name",
     "limit": 1,
     "settings": [
       {
         "type": "richtext",
         "id": "text",
         "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
         "label": "t:sections.image-with-text.blocks.text.settings.text.label"
       }
     ]
   },
   {
     "type": "button",
     "name": "t:sections.image-with-text.blocks.button.name",
     "limit": 1,
     "settings": [
       {
         "type": "text",
         "id": "button_label",
         "default": "Button label",
         "label": "t:sections.image-with-text.blocks.button.settings.button_label.label",
         "info": "t:sections.image-with-text.blocks.button.settings.button_label.info"
       },
        {
             "type": "checkbox",
             "id": "button_icon",
             "label": "Use arrow icon",
             "default": true
           },
       {
         "type": "url",
         "id": "button_link",
         "label": "t:sections.image-with-text.blocks.button.settings.button_link.label"
       },
    	{
             "type": "select",
             "id": "button_style",
             "label": "Button style",
             "default": "primary",
             "options": [
               {
                 "value": "secondary",
                 "label": "Secondary"
               },
               {
                 "value": "primary",
                 "label": "Primary"
               }
             ]
           },
		{
             "type": "select",
             "id": "button_size",
             "label": "Button size",
             "default": "small",
             "options": [
               {
                 "value": "large",
                 "label": "Large"
               },
               {
                 "value": "medium",
                 "label": "Medium"
               },
			        {
                 "value": "small",
                 "label": "Small"
               }
             ]
           }
     ]
   }
 ],
"presets": [
     {
       "name": "Welcome video",
	"blocks": [
         {
           "type": "heading"
         },
	  {
           "type": "subheading"
         },
         {
           "type": "text"
         },
         {
           "type": "signature"
         }
       ]
     }
   ],
   "disabled_on": {
    "groups": ["header","footer"]
  }
 }
{% endschema %}
