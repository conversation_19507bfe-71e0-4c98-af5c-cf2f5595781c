{%- liquid
if settings.heading_base_size != blank
assign heading_size = settings.heading_base_size | times: section.settings.heading_size | times: 0.000225
else
assign heading_size = section.settings.heading_size | times: 100 | times: 0.0004
endif
if settings.heading_scale
assign heading_size = heading_size | times: settings.heading_scale | times: 0.01
endif


assign max_columns = section.settings.max_columns
assign min_columns_mobile = section.settings.min_columns_mobile | times: 1
assign rounded_corner = true
if section.settings.full_width and section.settings.padding_full_width == false
  assign rounded_corner = false
endif
assign swiper_on_mobile = false
if section.settings.swiper_on_mobile and section.blocks.size > 1
  assign swiper_on_mobile = true
endif

assign row_height_mobile = section.settings.height_mobile
-%}

{%- style -%}
{%- capture page_width_var -%}
  {%- if settings.page_width == blank -%}
    1440
  {%- elsif settings.page_width contains 'narrow' -%}
    1440
  {%- elsif settings.page_width contains 'normal' -%}
    1920
  {%- elsif settings.page_width contains 'wide' -%}
    2400
  {%- else -%}
    {{ settings.page_width }}
  {%- endif -%}
{%- endcapture -%}
:root {
    --page-width-var: {{ page_width_var }};
}
#shopify-section-{{ section.id }} {
  background-color: {{ section.settings.background }};
  {% if section.settings.text_color.alpha != 0.0 %}
  --colors-text: {{ section.settings.text_color.red }},{{ section.settings.text_color.green }},{{ section.settings.text_color.blue }};
  {% endif %}
}
#shopify-section-{{ section.id }} .highlight.hl-font {
  color: var(--color-highlight);
}
#shopify-section-{{ section.id }} .otsb-image-hover {
    {% if section.settings.disable_zoom == true %}
      --otsb-image-zoom: 1;
    {% endif %}
  }
#shopify-section-{{ section.id }} .content-{{ block.id }} .highlight.hl-font {
  color: var(--color-highlight);
}
#shopify-section-{{ section.id }} .highlight.hl-underline {
  color: var(--color-highlight);
}
#shopify-section-{{ section.id }} .content-{{ block.id }} .highlight.hl-underline {
  color: var(--color-highlight);
}
.otsb__root .heading-{{ section.id }} {
  {% if section.settings.heading_title.alpha != 0 %}
  color: {{ section.settings.heading_title }};
  {% else %}
  color: currentColor;
  {% endif %}
}
#shopify-section-{{ section.id }} .text-content-top a {
  {% if section.settings.text_link.alpha != 0.0 %}
  color: {{ section.settings.text_link }};
  {% else %}
  color: rgba(var(--color-link), var(--alpha-link));
  
  {% endif %}
}
#shopify-section-{{ section.id }} .text-content-top a:hover {
  text-decoration: underline;
  {% if section.settings.text_link.alpha != 0.0 %}
  color: {{ section.settings.text_link }};
  {% else %}
  color: rgba(var(--color-link), var(--alpha-link));
  {% endif %}
}
#shopify-section-{{ section.id }} .divider {
  {% if section.settings.section_divider_color.alpha != 0.0 %}
  border-color: {{ section.settings.section_divider_color }};
  {% else %}
  border-color: rgba(var(--color-foreground), 0.75);
  {% endif %}
}
#shopify-section-{{ section.id }} .corner_radius {
    border-radius: {{ section.settings.corner_image }}px;
  }
  .cell-{{ section.id }}-1 {
    min-height: {{ row_height_mobile }}px;
  }
  {% if swiper_on_mobile %}
    .preload-slide-{{ section.id }} {
      margin-right: {{ section.settings.spacing_mobile }}px;
    }
    .cell-{{ section.id }}-2 {
      min-height: {{ section.settings.height_mobile }}px;
    }
    .cell-{{ section.id }}-3 {
      min-height: {{ section.settings.height_mobile }}px;
    }
  {% else %}
    .gap-grid--{{ section.id }} {
      gap: {{ section.settings.spacing_mobile }}px;
    } 
    .cell-{{ section.id }}-2 {
      min-height: {{ row_height_mobile | times: 2 }}px;
    }
    .cell-{{ section.id }}-3 {
      min-height: {{ row_height_mobile | times: 2 }}px;
    }
  {% endif %}
  #shopify-section-{{ section.id }} .animate_transition_card__image {
    transform: scale(1.1);
    opacity: 0;
    transition-property: transform, opacity;
    transition-timing-function: ease-in;
    transition-duration: 0.6s;
  }
  #shopify-section-{{ section.id }} .active.animate_transition_card__image {
    transform: scale(1);
    opacity: 1;
    will-change: transform, opacity;
  }
  #shopify-section-{{ section.id }} .otsb-button-outline .otsb-button-text,
  #shopify-section-{{ section.id }} .otsb-btn__text-link .otsb-button-text {
    transform: translate(0px);
  }
  {% if section.settings.full_width_mobile == true %}
  #shopify-section-{{ section.id }} .section {
     --full-page-grid-margin: 0;
     --full-page-grid-central-column-width: auto;
  }
  {% endif %}
  @media screen and (min-width: 1024px) {
    #shopify-section-{{ section.id }} .otsb-btn__text-link:hover .btn-text-link-effect-1 {
      transform: translatey(-100%);
    }
  }
  @media screen and (min-width: 768px) {
    {% if swiper_on_mobile %}
      .preload-slide-{{ section.id }} {
        margin-right: 0;
      }
    {% endif %}
    {% if section.settings.keep_ratio_on_different_width %}
      .cell-{{ section.id }}-1 {
        height: calc(({{ section.settings.height_desktop }} / (var(--page-width-var) + 40)) * 100vw);
        min-height: auto;
      }
      .cell-{{ section.id }}-2 {
        height: calc((({{ section.settings.height_desktop | times: 2 }} / (var(--page-width-var) + 40)) * 100vw) + {{ section.settings.spacing }});
        min-height: auto;
      }
      .cell-{{ section.id }}-3 {
        height: calc((({{ section.settings.height_desktop | times: 3 }} / (var(--page-width-var) + 40)) * 100vw) + {{ section.settings.spacing | times: 2 }});
        min-height: auto;
      }
    {% else %}
      .cell-{{ section.id }}-1 {
        min-height: {{ section.settings.height_desktop }}px;
      }
      .cell-{{ section.id }}-2 {
        min-height: {{ section.settings.height_desktop | times: 2 }}px;
      }
      .cell-{{ section.id }}-3 {
        min-height: {{ section.settings.height_desktop | times: 3 }}px;
      }
    {% endif %}
    .gap-grid--{{ section.id }} {
      gap: {{ section.settings.spacing }}px
    }
    {% if section.settings.full_width_mobile == true and section.settings.full_width == false %}
      #shopify-section-{{ section.id }} .section {
         --full-page-grid-margin: minmax(var(--page-margin), 1fr);
         --full-page-grid-central-column-width: min(var(--page-width) - var(--page-margin) * 2, calc(100% - var(--page-margin) * 2));
      }
    {% endif %}
  }
    {% if section.settings.keep_ratio_on_different_width and section.settings.full_width == false %}
    @media screen and (min-width: calc(var(--page-width-var) + 40px)) {
      .cell-{{ section.id }}-1 {
        max-height: {{ section.settings.height_desktop }}px;
      }
      .cell-{{ section.id }}-2 {
        max-height: calc({{ section.settings.height_desktop | times: 2 }}px + {{ section.settings.spacing }}px);
      }
      .cell-{{ section.id }}-3 {
        max-height: calc({{ section.settings.height_desktop | times: 3 }}px + {{ section.settings.spacing | times: 2 }}px);
      }
    }
  {% endif %}
{%- endstyle -%}
{% if request.design_mode %}
  <style>
    .otsb_nope {
      display: none !important;
      height: 0 !important;
      overflow: hidden !important;
      visibility: hidden !important;
      width: 0 !important;
      opacity: 0 !important;
    }
    ._otsb_warning {
      position: relative;
      box-shadow: 0rem 0.1875rem 0.0625rem -0.0625rem rgba(26, 26, 26, 0.07);
      border-radius: 1rem;
    }
    ._otsb_warning::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 0;
      box-shadow: 0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset, -0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset, 0rem -0.0625rem 0rem 0rem rgba(0, 0, 0, 0.17) inset, 0rem 0.0625rem 0rem 0rem rgba(204, 204, 204, 0.5) inset;
      border-radius: 1rem;
      pointer-events: none;
      mix-blend-mode: luminosity;
    }
    .otsb_warning_root {
      margin-top:36px;
      margin-bottom:36px;
    }
    .otsb_warning_root ._otsb_warning_1 {border-top-left-radius:1rem;border-top-right-radius:1rem;border:1px solid #fcaf0a;background:#fcb827;padding:1rem}
    .otsb_warning_root ._otsb_warning_2 {align-items:center;gap:8px;display:flex;flex-direction:row;flex-wrap:nowrap;justify-content:space-between}
    .otsb_warning_root ._otsb_warning_3 {display:flex;gap:4px;flex-direction:row;flex-wrap:nowrap;justify-content:space-between}
    .otsb_warning_root ._otsb_warning ._otsb_warning__icon {display:block;height:20px;width:20px;max-height:100%;max-width:100%;margin:auto}
    .otsb_warning_root h2 {overflow-wrap:anywhere;word-break:normal;font-size:100%;font-weight:650;line-height:1.25;color:rgb(37,26,0)}
    .otsb_warning_root * {
      margin: 0;
      padding: 0;
      font-family: var(--font-body-family);
      line-height: 1.375;
    }
    .otsb_warning_root ul {
      list-style-type: disc;
    }
    .otsb_warning_root a {
      color: rgb(0, 0, 238);
      text-decoration: underline;
    }
    .otsb_warning_root .otsb_warning_message_container {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      padding:1rem;
      color:rgb(37,26,0);
    }
    .otsb_warning_root .otsb_warning_message_container ul {
      padding-inline-start:3rem;
    }
  </style>
  <div x-data="otsb_script_require" class="page-width otsb_warning_root">
    <div class="_otsb_warning">
      <div class="_otsb_warning_1">
        <div class="_otsb_warning_2">
          <div class="_otsb_warning_3">
            <span class="_otsb_warning__icon">
              <svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M10 6.75a.75.75 0 0 1 .75.75v3.5a.75.75 0 1 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75Z"></path><path d="M11 13.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path><path fill-rule="evenodd" d="M10 3.5c-1.045 0-1.784.702-2.152 1.447a449.26 449.26 0 0 1-2.005 3.847l-.028.052a403.426 403.426 0 0 0-2.008 3.856c-.372.752-.478 1.75.093 2.614.57.863 1.542 1.184 2.464 1.184h7.272c.922 0 1.895-.32 2.464-1.184.57-.864.465-1.862.093-2.614-.21-.424-1.113-2.147-2.004-3.847l-.032-.061a429.497 429.497 0 0 1-2.005-3.847c-.368-.745-1.107-1.447-2.152-1.447Zm-.808 2.112c.404-.816 1.212-.816 1.616 0 .202.409 1.112 2.145 2.022 3.88a418.904 418.904 0 0 1 2.018 3.875c.404.817 0 1.633-1.212 1.633h-7.272c-1.212 0-1.617-.816-1.212-1.633.202-.408 1.113-2.147 2.023-3.883a421.932 421.932 0 0 0 2.017-3.872Z"></path></svg>
            </span>
            <h2>App Embeds Are Disabled</h2>
          </div>
        </div>
      </div>
      <div class="otsb_warning_message_container">
        <p>To use this section, the app embeds of OT: Theme Sections must be enabled in the theme editor. Please follow these steps to activate them:</p>
        <ul>
          <li>In the left panel, click the last icon that says <b>“App embeds”</b>.</li>
          <li>Enter <b>“OT”</b> on the search bar to quickly find and embed the apps from OT: Theme Sections.</li>
          <li>Turn on the Toggle buttons of "Section Builder Script" and "Section Builder Style", then click <b>Save</b>.</li>
        </ul>
        <p>Please refer to the User Guide <a href="https://support.omnithemes.com/blogs/ot-theme-sections-get-started/1-embed-app-to-shopify-theme" target="_blank">here</a></p>
        <p>For further support: feel free to contact us at <a href="mailto:<EMAIL>"><EMAIL></a>!</p>
      </div>
    </div>
  </div>
{% endif %}
<style>#shopify-section-{{section.id}} .otsb_trademark_root {user-select:none;color:#999;font-size:.75em;text-align:right;margin-top:2.5rem;}#shopify-section-{{section.id}} .otsb_trademark_root a {color:#999;background:none;text-decoration: none;}</style>
<div class="otsb_nope {% if section.settings.full_width == false %}section section--page-width page-width {% endif %} {% if section.settings.full_width_mobile == false and section.settings.full_width == true %} pl-4 pr-4 {% endif %} {% if section.settings.full_width == true %} {% if section.settings.padding_full_width == true %} md:pl-5 md:pr-5 {% else %} md:pl-0 md:pr-0 {% endif %}{% endif %}" x-data="otsb_script_1">
  {% render 'otsb-section-divider' %}
  <div class="w-full pt-[{{ section.settings.padding_top_mobile }}px] md:pt-[{{ section.settings.padding_top }}px] pb-[{{ section.settings.padding_bottom_mobile }}px] md:pb-[{{ section.settings.padding_bottom }}px] ">
    <div class="otsb_media_gallery otsb-content-wrapper ">
    {%- if section.settings.heading != blank or section.settings.text != blank or section.settings.subheading != blank -%}
      {%- style -%} 
        .otsb__root.otsb-v2 .heading-{{ section.id }} { font-size: {{ heading_size | times: 0.6 }}rem; }
        @media screen and (min-width: 768px) {
          .otsb__root.otsb-v2 .heading-{{ section.id }}{ font-size: {{ heading_size }}rem; }
        }
      {%- endstyle -%}
      <div class="text-{{ section.settings.heading_alignment }} mb-6">
        {%- if section.settings.subheading != blank -%}
          <p class="rte mb-1.5 text-[rgba(var(--colors-text))]">{{ section.settings.subheading }}</p>
        {% endif %}
        {%- if section.settings.heading != blank -%}
          <{{ section.settings.heading_tag }} class="heading-{{ section.id }} h1 block py-2 leading-tight">
            {% render 'otsb-heading-highlight',
              headingId: section.id,
              heading: section.settings.heading,
              highlight_type: section.settings.highlight_type,
              color_heading_highlight_light: section.settings.marker,
              color_text: section.settings.text_color,
            %}
          </{{ section.settings.heading_tag }}>
        {%- endif -%}
        {%- if section.settings.text != blank -%}
          <div class="text-content-top rte mt-2 mb-6 lg:mb-12 text-[rgba(var(--colors-text))]">{{ section.settings.text }}</div>
        {% endif %}
      </div>
    {%- endif -%}
    <div 
      id="x-slide-mobile-{{ section.id }}"
      class="{% if swiper_on_mobile %}x-splide splide visible relative{% else %} otsb-grid grid-cols-{{ min_columns_mobile }} md:grid-cols-{{ max_columns }} gap-grid--{{ section.id }}{% endif %}"
      {% if swiper_on_mobile %}
        x-intersect.once.margin.200px='$store.xSplide.load($el, {
          "autoWidth": true,
          "height": "{{ section.settings.height_mobile }}px",
          "focus": "center",
          "type": "slide",
          "arrows": false,
          "mediaQuery": "min",
          "gap": "{{ section.settings.spacing_mobile }}px",
          "progressBar": {{ section.blocks.size }},
          "breakpoints": {
            768: {
              "destroy": true
            }
          },
          "classes": {
            "pagination": "hidden"
          }
        })'
      {% endif %}
    >
      {% if swiper_on_mobile %}
        <div class="splide__track overflow-hidden w-full h-full">
          <div class="w-full h-full splide__list md:grid md:grid-cols-{{ max_columns }} gap-grid--{{ section.id }}">
      {%- endif -%}
        {%- for block in section.blocks -%}
          {%- liquid 
            assign columns_desktop = block.settings.number_of_columns
            assign columns_mobile = block.settings.number_of_columns
            if settings.heading_base_size != blank
              assign title_size = settings.heading_base_size | times: section.settings.heading_size | times: 0.000225
            else
              assign title_size = block.settings.title_size | times: 100 | times: 0.0004
            endif
            if settings.heading_scale
              assign title_size = title_size | times: settings.heading_scale | times: 0.01
            endif
            assign text_size = block.settings.text_size | times: 0.01
            if settings.body_scale
              assign text_size = text_size | times: settings.body_scale | times: 0.01
            endif
            if columns_desktop > max_columns
              assign columns_desktop = max_columns  
            endif
            if columns_mobile > min_columns_mobile
              assign columns_mobile = min_columns_mobile  
            endif
            assign video_type = false
            assign video_alt = block.settings.video_alt_text
            if block.settings.video_url.type == 'youtube'
              assign video_type = 'youtube'
              assign video_id = block.settings.video_url.id
            endif
            if block.settings.video_url.type == 'vimeo'
              assign video_type = 'vimeo'
              assign video_id = block.settings.video_url.id
            endif
          
            if block.settings.video != null 
              assign video_type = 'video_select'
            endif
          -%}
          {% style %}
            #shopify-section-{{ section.id }} .text-{{ block.id }} a {
              {% if block.settings.colors_text_link.alpha != 0.0 %}
              color: {{ block.settings.colors_text_link }};
              {% elsif section.settings.text_link.alpha != 0.0 %}
              color: {{ section.settings.text_link }};
              {% else %}
              color: rgba(var(--color-link), var(--alpha-link));
              {% endif %}
            }
            #shopify-section-{{ section.id }} .text-{{ block.id }} a:hover {
              text-decoration: underline;
            }
            .media-{{ block.id }}, .media-{{ block.id }} *:before { 
              {% if block.settings.color_overlay.alpha != 0 %}
                --image-treatment-overlay: {{ block.settings.color_overlay.red }}, {{ block.settings.color_overlay.green }}, {{ block.settings.color_overlay.blue }};
              {% endif %}
              {% if block.settings.color_button.alpha != 0 %}
                --color-button-text: {{ block.settings.color_button.red }}, {{ block.settings.color_button.green }}, {{ block.settings.color_button.blue }};
              {% endif %}
              {% if block.settings.background_button.alpha != 0 %}
                --color-button: {{ block.settings.background_button.red }}, {{ block.settings.background_button.green }}, {{ block.settings.background_button.blue }};
              {% endif %}
              {% if block.settings.color_button_hover.alpha != 0 %}
                --colors-button-text-hover: {{ block.settings.color_button_hover.red }}, {{ block.settings.color_button_hover.green }}, {{ block.settings.color_button_hover.blue }};
              {% endif %}
              {% if block.settings.background_button_hover.alpha != 0 %}
                --colors-button-hover: {{ block.settings.background_button_hover.red }}, {{ block.settings.background_button_hover.green }}, {{ block.settings.background_button_hover.blue }};
              {% elsif block.settings.background_button.alpha != 0 %}
                --colors-button-hover: {{ block.settings.background_button.red }}, {{ block.settings.background_button.green }}, {{ block.settings.background_button.blue }};
              {% endif %}
              {% if block.settings.button_color_mobile == 'color'%}
                {% if block.settings.color_button.alpha != 0 %}
                  --color-button-text-mobile: rgb({{ block.settings.color_button.red }}, {{ block.settings.color_button.green }}, {{ block.settings.color_button.blue }});
                {% else %}
                 --color-button-text-mobile:  rgb(var(--color-button-text));
                {% endif %}
                {% if block.settings.background_button.alpha != 0 %}
                  --color-button-mobile: {{ block.settings.background_button.red }}, {{ block.settings.background_button.green }}, {{ block.settings.background_button.blue }};
                {% else %}
                  --color-button-mobile: var(--color-button);
                {% endif %}
              {% else %}
              {% if block.settings.color_button_hover.alpha != 0 %}
                --color-button-text-mobile: rgb({{ block.settings.color_button_hover.red }}, {{ block.settings.color_button_hover.green }}, {{ block.settings.color_button_hover.blue }});
              {% endif %}
              {% if block.settings.background_button_hover.alpha != 0 %}
                --color-button-mobile: {{ block.settings.background_button_hover.red }}, {{ block.settings.background_button_hover.green }}, {{ block.settings.background_button_hover.blue }};
              {% elsif block.settings.background_button.alpha != 0 %}
                --color-button-mobile: {{ block.settings.background_button.red }}, {{ block.settings.background_button.green }}, {{ block.settings.background_button.blue }};
              {% endif %}
              {% endif %}
            }
            .button--{{ block.id }}.otsb-button-outline {
              {%- if block.settings.secondary_button_text.alpha != 0.0 -%} 
                --colors-secondary-button: {{ block.settings.secondary_button_text.red }}, {{ block.settings.secondary_button_text.green }}, {{ block.settings.secondary_button_text.blue }}; 
                --colors-line-secondary-button: {{ block.settings.secondary_button_text.red }}, {{ block.settings.secondary_button_text.green }}, {{ block.settings.secondary_button_text.blue }}; 
                --background-secondary-button: transparent;
              {% endif %}
              {%- if block.settings.color_button_secondary.alpha != 0.0 -%} 
                --background-secondary-button: {{ block.settings.color_button_secondary.red }}, {{ block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }}; 
                --colors-line-secondary-button: {{ block.settings.color_button_secondary.red }}, {{ block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }}; 
              {% endif %}
            }
            .button--{{ block.id }}.otsb-btn__text-link, .button--{{ block.id }}.otsb-btn__text-link::after, .button--{{ block.id }}.otsb-btn__text-link::before {
                {% if block.settings.colors_text_link.alpha != 0.0 %}
                --colors-text-link: {{ block.settings.colors_text_link.red }}, {{ block.settings.colors_text_link.green }}, {{ block.settings.colors_text_link.blue }};
                {% else %}
                --colors-text-link: var(--color-link);
              {% endif %}
            }
          
          {% endstyle %}
          <div 
            x-intersect.once.margin.-20px.0px.-20px.0px="$el.querySelector('.animate_transition_card__image')?.classList.add('active')"
            class="z-0 media-{{ block.id }} flex{% if block.settings.media_position == 'top' %} flex-col{% else %} flex-col-reverse{% endif %} group overflow-hidden relative {% if swiper_on_mobile %} splide__slide x-splide-slide preload-slide-{{ section.id }} w-full{% else %}col-span-{{ columns_mobile }}{% endif %} md:col-span-{{ columns_desktop }} cell-{{ section.id }}-{{ block.settings.number_of_row }} row-span-{{ block.settings.number_of_row }}" 
            {{ block.shopify_attributes }}
          >
            <div class="flex grow relative h-full w-full z-10 overflow-hidden corner_radius">
              {% if block.settings.image_link %} 
                <a 
                  aria-label="{{ 'accessibility.image_link' | t }}" 
                  href="{{ block.settings.image_link }}"{% if block.settings.open_new_window %} target="_blank"{% endif %} 
                  class="animate_transition_card__image flex absolute top-0 left-0 h-full w-full disable-effect overflow-hidden"
                >
              {%- else -%}
                <div class="{% if block.settings.image != blank or video_type or block.settings.image_mobile != blank %}animate_transition_card__image {% endif %} flex absolute top-0 left-0 h-full w-full disable-effect overflow-hidden">
              {% endif %} 
                {%- capture sizes -%}
                  {%- if section.settings.full_width -%}
                    (min-width: 768px) {{ columns_desktop | times: 100 | divided_by: max_columns }}vw, {{ columns_mobile | times: 100 | divided_by: min_columns_mobile }}vw
                  {%- else -%}
                    (min-width: {{ settings.page_width }}px) {{ settings.page_width | times: columns_desktop | divided_by: max_columns }}px, {{ columns_mobile | times: 100 | divided_by: min_columns_mobile }}vw
                  {%- endif -%}
                {%- endcapture -%}
                {% assign img_mobile = false %}
                {%- if block.settings.image != blank and video_type == false -%}
                  {% assign img_mobile = true %}
                  {% if block.settings.image_mobile == blank %}
                    {{ block.settings.image | image_url: width: 3840 | image_tag: loading: "lazy", sizes: sizes, widths: '375, 450, 750, 900, 1100, 1500, 1780, 2000, 3000, 3840', class: 'w-full object-cover z-0 h-full otsb-image-hover absolute top-0 left-0 block' }}
                  {% else %}
                    {{ block.settings.image | image_url: width: 3840 | image_tag: loading: "lazy", sizes: sizes, widths: '375, 450, 750, 900, 1100, 1500, 1780, 2000, 3000, 3840', class: 'w-full object-cover z-0 h-full otsb-image-hover otsb-hidden md:block absolute top-0 left-0 block' }}
                  {% endif %}
                  {%- elsif video_type -%}
                    {% assign img_mobile = true %}
                    <div x-intersect="$nextTick(() => { $store.xVideo.load($el) })" 
                      class="video-block-{{ block.id }} absolute top-0 left-0 w-full h-full flex items-center"> 
                      <div class="relative otsb-external-video h-full w-full rounded-none overflow-hidden{% if block.settings.enable_video_autoplay %} otsb-video-hero{% endif %}" 
                        x-intersect:leave="$store.xVideo.pause($el)"
                        {% if block.settings.enable_video_autoplay %}
                          @click.stop="$store.xVideo.togglePlay($el)"
                          {% if video_type == 'video_select' %}x-intersect="$store.xVideo.play($el)"{% endif %}
                          {% if video_type == 'youtube' or video_type == 'vimeo' %}
                            x-intersect.once="$store.xVideo.externalLoad($el, '{{ video_type }}', '{{ video_id }}', false, '{{ video_alt }}')"
                            x-intersect="$store.xVideo.play($el)"
                          {% endif %}
                        {% else %}
                          {% if video_type == 'video_select' %}@click.prevent="$store.xVideo.mp4Thumbnail($el)"{% endif %}
                          {% if video_type == 'youtube' or video_type == 'vimeo' %}@click.prevent="$store.xVideo.externalLoad($el, '{{ video_type }}', '{{ video_id }}', false, '{{ video_alt }}')"{% endif %}
                        {% endif %}
                      >
                        {%- render 'otsb-media-video', 
                          enable_video_autoplay: block.settings.enable_video_autoplay,
                          video_type: video_type,
                          video_id: video_id,
                          video_alt: video_alt,
                          video: block.settings.video,
                          cover_image: block.settings.image,
                          columns_desktop: columns_desktop,
                          show_sound_control: block.settings.show_sound_control
                        -%}
                      </div>
                    </div>
                {%- else -%}
                  {{ 'image' | placeholder_svg_tag: 'bg-[#c9c9c9] text-[#acacac] otsb-hidden md:block absolute top-0 left-0 w-full h-full object-cover' }}
                {%- endif -%}
                {% if block.settings.image_mobile != blank %}
                  {{ block.settings.image_mobile | image_url: width: 3840 | image_tag: loading: "lazy", sizes: sizes, widths: '375, 450, 750, 900, 1100, 1500, 1780, 2000, 3000, 3840', class: 'w-full md:hidden object-cover z-0 h-full otsb-image-hover absolute top-0 left-0 block' }}
                {% elsif img_mobile == false %}
                  {{ 'image' | placeholder_svg_tag: 'bg-[#c9c9c9] text-[#acacac] block md:otsb-hidden absolute top-0 left-0 w-full h-full object-cover' }}
                {% endif %}
                {% if block.settings.enable_content_overlay %}
                  <div class="absolute top-0 left-0 bottom-0 right-0 z-10 otsb-image-treatment-overlay pointer-events-none opacity-{{ block.settings.overlay_opacity }} block"></div>
                {% endif %}
              {% if block.settings.image_link %}
                </a>
              {%- else -%}
                </div>
              {% endif %} 
            </div>
            {%- style -%}
              {% if block.settings.text_color.alpha != 0 %} 
                .content-{{ block.id }} { 
                  --colors-text: {{ block.settings.text_color.red }}, {{ block.settings.text_color.green }}, {{ block.settings.text_color.blue }};
                }
              {% endif %}
              .content-{{ block.id }} { 
                {% if block.settings.title_color.alpha != 0 %}
                  --colors-heading: {{ block.settings.title_color.red }}, {{ block.settings.title_color.green }}, {{ block.settings.title_color.blue }};
                {% else %}
                 --color-heading: var(--colors-heading);
                {% endif %}
              }
            
              .heading-{{ block.id }} { 
                font-size: {{ title_size | times: 0.6 }}rem;
              }
              .text-{{ block.id }} { 
                font-size: calc({{ text_size }}*1.5rem);
              }
              .video-block-{{ block.id }}  {
                {% if  section.settings.image_treatment_text.alpha != 0 %}   
                  --image-treatment-text: {{ section.settings.image_treatment_text.red }}, {{ section.settings.image_treatment_text.green }}, {{ section.settings.image_treatment_text.blue }};
                {% else %}
                  --image-treatment-text: 255,255,255;
                {% endif %}
              }
              .video-block-{{ block.id }} .image-treatment-text svg {
                color: rgba(var(--image-treatment-text));
              }
              {%- if block.settings.icon != 'none' -%}
                .icon--{{ block.id }} {
                  width: {{ block.settings.icon_size | times: 0.7 }}px;
                  height: {{ block.settings.icon_size | times: 0.7 }}px;
                }
              {% endif %}
              @media screen and (min-width: 1024px) {
                .heading-{{ block.id }} { 
                  font-size: {{ title_size }}rem;
                }
                .text-{{ block.id }} { 
                  font-size: calc({{ text_size }}*1.6rem);
                }
                {%- if block.settings.icon != 'none' -%}
                  .icon--{{ block.id }} {
                    width: {{ block.settings.icon_size }}px;
                    height: {{ block.settings.icon_size }}px;
                  }
                {% endif %}
              }
              .text-content-image-{{ block.id }} .heading-{{ block.id }} {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
              }
              
            {%- endstyle -%}
            {% liquid
            assign main_button_classes = ''
            case block.settings.button_type
              when 'square'
                assign main_button_classes = main_button_classes | append: ' otsb-btn-square'
              when 'rounded'
                assign main_button_classes = main_button_classes | append: ' otsb-btn-rounded'
              when 'rounded_corners'
                assign main_button_classes = main_button_classes | append: ' otsb-btn-rounded-corners'
              when 'mixed'
                assign main_button_classes = main_button_classes | append: ' otsb-btn-mixed'
            endcase
            case block.settings.button_animation
              when 'slide'
                assign main_button_classes = main_button_classes | append: ' otsb-btn__slide'
              when 'fill_up'
                assign main_button_classes = main_button_classes | append: ' otsb-btn__fill_up'
              when 'underline'
                assign main_button_classes = main_button_classes | append: ' otsb-btn__underline'
              when 'sliced'
                assign main_button_classes = main_button_classes | append: ' otsb-btn__sliced'
            endcase
            if section.settings.content_position == 'top-left'
              assign main_button_classes = main_button_classes | append: ' md:w-full'
            endif
          %}
          {% liquid
            assign main_button_classes_accept = ''
            if block.settings.button_primary_accept
              assign main_button_classes_accept = main_button_classes_accept | append: ' otsb-btn__solid'
            else
              assign main_button_classes_accept = main_button_classes_accept | append: ' otsb-button-outline'
            endif

            case block.settings.button_type
              when 'square'
                assign main_button_classes_accept = main_button_classes_accept | append: ' otsb-btn-square'
              when 'rounded'
                assign main_button_classes_accept = main_button_classes_accept | append: ' otsb-btn-rounded'
              when 'rounded_corners'
                assign main_button_classes_accept = main_button_classes_accept | append: ' otsb-btn-rounded-corners'
              when 'mixed'
                assign main_button_classes_accept = main_button_classes_accept | append: ' otsb-btn-mixed'
            endcase
            case block.settings.button_animation
              when 'slide'
                assign main_button_classes_accept = main_button_classes_accept | append: ' otsb-btn__slide'
              when 'fill_up'
                assign main_button_classes_accept = main_button_classes_accept | append: ' otsb-btn__fill_up'
              when 'underline'
                assign main_button_classes_accept = main_button_classes_accept | append: ' otsb-btn__underline'
              when 'sliced'
                assign main_button_classes_accept = main_button_classes_accept | append: ' otsb-btn__sliced'
            endcase
            if block.settings.marker.alpha != 0
            assign color_heightlight = block.settings.marker
            else
            assign color_heightlight = section.settings.marker
            endif
          %}
            {% if block.settings.icon_content_inline %}
              <div class="content-{{ block.id }} text-{{ block.settings.text_align }} pointer-events-none pr-5 pl-5 lg:pr-8 lg:pl-8 xl:pr-11 xl:pl-11{% unless block.settings.enable_content_overlay %} 2xl:pl-16 2xl:pr-16{% if block.settings.media_position == 'top' %} pt-4 lg:pt-2{% else %} pb-4 lg:pb-2{% endif %}{% else %} pt-5 pb-5 lg:pt-9 lg:pb-9 2xl:pt-12 2xl:pb-12 2xl:pl-12 2xl:pr-12 z-10 absolute left-0 right-0{% if block.settings.content_position == 'top' %} top-0{% elsif block.settings.content_position == 'center' %} top-1/2 -translate-y-1/2{% else %} bottom-0{% endif %}{% endunless %}">
                <div class="grid md:flex justify-center justify-items-center gap-8 md:gap-12 grid-cols-3 items-center">
                  <div class="icon--{{ block.id }} relative w-40 h-40 md:w-56 md:h-56 col-span-1">
                    {% if block.settings.icon_image != blank %}
                      {{ block.settings.icon_image | image_url: width: 3840 | image_tag: loading: "lazy", sizes: sizes, widths: '375, 450, 750, 900, 1100, 1500, 1780, 2000, 3000, 3840', class: 'w-full object-cover z-0 h-full otsb-image-hover absolute top-0 left-0 block rounded-full' }}
                    {%- else -%}
                      {{ 'image' | placeholder_svg_tag: 'bg-[#c9c9c9] text-[#acacac] absolute top-0 left-0 w-full h-full object-cover rounded-full' }}
                    {%- endif -%}
                  </div>
                  <div class="text-left col-span-2 text-content-image-{{ block.id }} max-w-sm"> 
                    {% if block.settings.sub_title != blank %}
                      <p class="empty:otsb-hidden leading-tight pointer-events-auto p-break-words text-{{ block.id }} text-[rgba(var(--colors-heading))]">
                        {{ block.settings.sub_title }}
                      </p>
                    {% endif %}
                    <p class="text-[rgba(var(--colors-heading))] heading-{{ block.id }} empty:otsb-hidden overflow-hidden leading-tight pointer-events-auto p-break-words h2{% if block.settings.text != blank %} mb-1.5{% endif %}">
                      {% render 'otsb-heading-highlight',
                        headingId: block.id,
                        heading: block.settings.title,
                        highlight_type: block.settings.highlight_type,
                        color_heading_highlight_light: color_heightlight,
                        color_text: block.settings.text_color,
                      %}
                    </p>
                    <div class="leading-tight pointer-events-auto p-break-words pl0-ul rte text-{{ block.id }} text-[rgb(var(--colors-text))] ">
                      {{ block.settings.text }} 
                    </div>
                    
                      {% comment %} End button design {% endcomment %}
                    {%- if block.settings.button_label != blank -%}
                      <a{% if block.settings.button_link != blank %} href="{{ block.settings.button_link }}"{% if block.settings.open_new_window_button %} target="_blank"{% endif %}{% else %} role="link" aria-disabled="true"{% endif %} class="rounded-md otsb-button{% if block.settings.show_button_style == 'secondary' %} otsb-button-outline mt-4 lg:mt-6{% elsif block.settings.show_button_style == 'text-link' %} otsb-btn__text-link mt-2 lg:mt-3{% else %} otsb-btn__solid mt-4 lg:mt-6{% endif %} button--{{ block.id }} p-break-words border inline-flex text-center justify-center items-center empty:otsb-hidden pl-4 pr-4 lg:pl-10 lg:pr-10 pt-2.5 pb-2.5 leading-normal md:pt-3 md:pb-3 cursor-pointer pointer-events-auto{% if block.settings.button_link == blank %} hover:cursor-not-allowed opacity-70{%- endif -%} {{ main_button_classes }}"> 
                        {% render 'otsb-button-label',
                        button_label: block.settings.button_label, 
                        show_button_style: block.settings.show_button_style,
                        button_animation: block.settings.button_animation,
                        custom_icon_button: block.settings.custom_icon_button
                         %} 
                      </a>
                    {% endif %}
                  </div>
                </div>
              </div>
            {% else %}
              <div class="content-{{ block.id }} text-{{ block.settings.text_align }} pointer-events-none pr-5 pl-5 lg:pr-8 lg:pl-8 xl:pr-11 xl:pl-11{% unless block.settings.enable_content_overlay %} 2xl:pl-16 2xl:pr-16{% if block.settings.media_position == 'top' %} pt-4 lg:pt-7{% else %} pb-4 lg:pb-7{% endif %}{% else %} pt-5 pb-5 lg:pt-9 lg:pb-9 2xl:pt-12 2xl:pb-12 2xl:pl-12 2xl:pr-12 z-10 absolute left-0 right-0{% if block.settings.content_position == 'top' %} top-0{% elsif block.settings.content_position == 'center' %} top-1/2 -translate-y-1/2{% else %} bottom-0{% endif %}{% endunless %}">
                {%- if block.settings.icon != 'none' -%}
                  {% unless block.settings.custom_icon == blank and block.settings.another_icon == blank and block.settings.icon == 'another_icon' %}
                    <div class="inline-flex items-center mb-3 md:mb-6 justify-{{ block.settings.text_align }} text-[rgba(var(--colors-text))]">
                      <span class="inline-block icon--{{ block.id }}">
                        {% if block.settings.custom_icon == blank %}
                          {% if block.settings.another_icon != blank and block.settings.icon == 'another_icon' %}
                            {% render 'otsb-icon-new-alls', icon: block.settings.another_icon %}
                          {% else %}
                            {% render 'otsb-icon-labels-bags-new', icon: block.settings.icon %}
                          {% endif %}
                        {% else %}
                          {{ block.settings.custom_icon }}
                        {% endif %}
                      </span>
                    </div>
                  {% endunless %}
                {% endif %}
                 {% if block.settings.sub_title != blank %}
                    <p class="empty:otsb-hidden leading-tight pointer-events-auto p-break-words text-{{ block.id }} text-[rgb(var(--colors-text))]">
                      {{ block.settings.sub_title }}
                    </p>
                  {% endif %}
                  <p class="text-[rgba(var(--colors-heading))] heading-{{ block.id }} empty:otsb-hidden leading-tight pointer-events-auto p-break-words h2{% if block.settings.text != blank %} mb-1.5{% endif %}">
                    {% render 'otsb-heading-highlight',
                      headingId: block.id,
                      heading: block.settings.title,
                      highlight_type: block.settings.highlight_type,
                      color_heading_highlight_light: color_heightlight,
                      color_text: block.settings.text_color,
                    %}
                  </p>
                  <div class="leading-tight pointer-events-auto p-break-words pl0-ul rte text-{{ block.id }} text-[rgb(var(--colors-text))]">
                    {{ block.settings.text }} 
                  </div>
                  {%- if block.settings.button_label != blank -%}
                    <a{% if block.settings.button_link != blank %} href="{{ block.settings.button_link }}"{% if block.settings.open_new_window_button %} target="_blank"{% endif %}{% else %} role="link" aria-disabled="true"{% endif %} class="rounded-md otsb-button{% if block.settings.show_button_style == 'secondary' %} otsb-button-outline mt-4 lg:mt-6{% elsif block.settings.show_button_style == 'text-link' %} otsb-btn__text-link mt-2 lg:mt-3{% else %} otsb-btn__solid mt-4 lg:mt-6{% endif %} button--{{ block.id }} p-break-words border inline-flex text-center justify-center items-center empty:otsb-hidden pl-4 pr-4 lg:pl-6 lg:pr-6 pt-2.5 pb-2.5 leading-normal md:pt-3 md:pb-3 cursor-pointer pointer-events-auto{% if block.settings.button_link == blank %} hover:cursor-not-allowed opacity-70{%- endif -%} {{ main_button_classes }}"> 
                      {% render 'otsb-button-label',
                       button_label: block.settings.button_label, 
                       show_button_style: block.settings.show_button_style,
                       button_animation: block.settings.button_animation,
                       custom_icon_button: block.settings.custom_icon_button
                      %} 
                    </a>
                  {% endif %}
              </div>
            {% endif %}
          </div>
        {%- endfor -%}
      {% if swiper_on_mobile %}
          </div>
        </div>
        <div class="items-center flex w-full mt-2.5 md:otsb-hidden{% if section.settings.full_width_mobile %} pl-6 pr-6{% endif %}">
          <div class="splide-progress-{{ section.id }} inline-block grow rounded-md">
            {% liquid
              assign my_float = columns_desktop | times: 1.0
              assign width_bar = my_float | divided_by: section.blocks.size
              assign mobile_width_bar = 1 | times: 1.0 | divided_by: section.blocks.size 
            %} 
            {%- style -%}
              .splide-progress-{{ section.id }} > div:empty {
                display: block;
              }
              .splide-progress-bar-{{ section.id }} {
                width: {{ mobile_width_bar | times: 100 }}%;
              }
              @media (min-width: 768px) {
                .splide-progress-bar-{{ section.id }} {
                  width: {{ width_bar | times: 100 }}%;
                }
              }
            {%- endstyle -%}
            <div class="splide-progress-bar-{{ section.id }} splide-progress-bar rounded-md"></div>
          </div>
        </div>
      {%- endif -%}
    </div>
  </div>
</div>
</div>