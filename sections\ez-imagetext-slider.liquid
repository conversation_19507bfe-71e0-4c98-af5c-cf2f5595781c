{% liquid
  assign container_size = section.settings.container_size
  assign container_size_full = section.settings.container_size_full
  assign heading_fonts = section.settings.heading_fonts
  assign heading_custom = section.settings.heading_custom
  assign caption_fonts = section.settings.caption_fonts
  assign caption_custom = section.settings.caption_custom
  assign description_fonts = section.settings.description_fonts
  assign description_custom = section.settings.description_custom
  assign button_fonts = section.settings.button_fonts
  assign button_custom = section.settings.button_custom
%}

{% style %}
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }
  {% if container_size_full %}
    .section-{{ section.id }} .ez-container{
      width: 100%;
      max-width: 100%;
       padding:0 20px;
    }
  {% else container_size %}
    #ez-section-{{ section.id }} .ez-container{
      margin-left: auto;
      margin-right: auto;
      max-width: {{container_size}}px;
      padding:0 20px;
    }
  {% endif %}

  #ez-section-{{ section.id }}  {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 0.75 | round: 0 }}px;
    {% if section.settings.background_type != 'none' %}
      {% if section.settings.background_type == 'gradient' %}
        {% if section.settings.section_background_gradient != blank %}
          background-image: {{ section.settings.section_background_gradient  }};
        {% endif %}
      {% endif %}
      {% if section.settings.background_type == 'color' %}
        {% if section.settings.background-color != blank %}
          background-color: {{ section.settings.background-color }};
        {% endif %}
      {% endif %}
    {% endif %}
  }

  @media screen and (min-width: 768px) {
    #ez-section-{{ section.id }}  {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }
  #ez-section-{{ section.id }} .ez_image_and_text .swiper-slide {
    height: auto;
  }
  #ez-section-{{ section.id }} .ez_image_and_text{
    display: flex;
    gap: 20px;
    height: 100%;
    width:100%;
  }
  #ez-section-{{ section.id }} .ez_image_first {
    flex-direction: row;
  }
  #ez-section-{{ section.id }} .ez_text_first {
    flex-direction: row-reverse;
  }
  #ez-section-{{ section.id }} .ez_image {
    width: calc(50% - 20px / 2);
    flex: 0 0 auto;
    position: relative;
    border-radius: {{section.settings.image_border_radius}}px;
    border: {{section.settings.image_border_radius}}px solid {{image_border_color}};
    overflow: hidden;
  }
  #ez-section-{{ section.id }} .ez_image svg {
    width: 100%;
    height: 100%;
    background-color: #ccc;
  }
  #ez-section-{{ section.id }} .ez_image img {
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    object-fit: cover;
  }
  #ez-section-{{ section.id }} .ez_text {
    width: calc(50% - 20px / 2);
    flex: 0 0 auto;
    padding: 0px 20px;
    padding-top: {{ section.settings.text_padding_top }}px;
    padding-bottom: {{ section.settings.text_padding_bottom }}px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  #ez-section-{{ section.id }} .ez_heading{
    margin: {{ section.settings.heading_margin_top}}px 0 0 0;
  }
  #ez-section-{{ section.id }} .ez_description{
    margin: {{ section.settings.description_margin_top}}px 0 0 0;
  }
  #ez-section-{{ section.id }} .ez_description * {
    margin: 0;
  }
  #ez-section-{{ section.id }} .ez_description * + * {
    margin: 10px 0 0 0;
  }
  #ez-section-{{ section.id }} .ez_text_button {
    display: block;
    padding:{{ section.settings.fast_button_padding_top_and_bottom }}px {{ section.settings.fast_button_padding_left_and_right }}px;
    text-decoration: none;
    margin: {{ section.settings.button_margin_top }}px 0 0 0;
  }
  #ez-section-{{ section.id }} .swiper-pagination {
    position: absolute;
    left: 73%;
    bottom: 5%;
    width: auto;
  }
  span.swiper-pagination-bullet {
    width: 8px !important;
    height: 8px !important;
    background-color: {{section.settings.dots_color}};
    opacity: 0.5;
  }
  span.swiper-pagination-bullet.swiper-pagination-bullet-active {
    width: 32px !important;
    border-radius: 4.0px;
    opacity: 1;
  }

  {% if caption_custom %}
    #ez-section-{{ section.id }} .ez_caption span{
      font-family: {{ caption_fonts.family }}, {{ caption_fonts.fallback_families }};
      font-weight: {{ caption_fonts.weight }};
      font-style: {{ caption_fonts.style }};
    }
  {% endif %}
  {% if heading_custom %}
    #ez-section-{{ section.id }} .ez_heading h2{
      font-family: {{ heading_fonts.family }}, {{ heading_fonts.fallback_families }};
      font-weight: {{ heading_fonts.weight }};
      font-style: {{ heading_fonts.style }};
    }
  {% endif %}

  {% if description_custom %}
    #ez-section-{{ section.id }} .ez_description{
      font-family: {{ description_fonts.family }}, {{ description_fonts.fallback_families }};
      font-weight: {{ description_fonts.weight }};
      font-style: {{ description_fonts.style }};
    }
  {% endif %}

  {% if button_custom %}
    #ez-section-{{ section.id }} .ez_text_button{
      font-family: {{ button_fonts.family }}, {{ button_fonts.fallback_families }};
      font-weight: {{ button_fonts.weight }};
      font-style: {{ button_fonts.style }};
    }
  {% endif %}
  @media (max-width: 767px) {
    #ez-section-{{ section.id }} .ez_image_and_text{
      flex-direction: column;
    }
    #ez-section-{{ section.id }} .ez_image_and_text.ez-img-btm{
      flex-direction: column-reverse;
    }
    #ez-section-{{ section.id }} .ez_image {
      width: 100%;
      padding-top: var(--padding-top);
      padding-top: {{section.settings.mob_height}}px;
    }
    #ez-section-{{ section.id }} .ez_image svg{
      width: 100%;
      height: 100%;
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      object-fit: cover;
    }
    #ez-section-{{ section.id }} .ez_text {
      width: auto;
      padding-top: {{ section.settings.text_padding_top_mobile }}px;
      padding-bottom: {{ section.settings.text_padding_bottom_mobile }}px;
    }
    #ez-section-{{ section.id }} .ez_heading{
      margin: {{ section.settings.mobile_button_margin_top }}px 0 0 0;
    }
    #ez-section-{{ section.id }} .ez_description{
      margin: {{ section.settings.mobile_description_margin_top }}px 0 0 0;
    }
    #ez-section-{{ section.id }} .ez_text_button {
      margin: {{ section.settings.mobile_button_margin_top }}px 0 0 0;
    }
    #ez-section-{{ section.id }} .swiper-pagination {
      position: absolute;
      left: 50%;
      bottom: 50%;
      width: auto;
      transform: translate(-50%, -50%);
    }
    #ez-section-{{ section.id }} .ezmob-ez-img-top .swiper-pagination{
      bottom: unset;
      top: calc({{section.settings.mob_height}}px - 30px);
    }
    #ez-section-{{ section.id }} .ezmob-ez-img-btm .swiper-pagination{
      bottom: 15px;
      transform: translate(-50%, 0%);
    }
  }
{% endstyle %}

<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css">

<div
  id="ez-section-{{ section.id }}"
  class="ez_image_and_text_section "
>
  <div class="ez-container">
    <div class="ez_image_and_text-{{ section.id }} ez_image_and_text swiper ezmob-{{ section.settings.layout_mobile }}">
      <div class="swiper-wrapper">
        {%- for block in section.blocks -%}
          {% liquid
            assign boxBgColor = block.settings.text_box_background_color
            assign container_alignment = section.settings.container_alignment
            assign text_box_border_radius = section.settings.text_box_border_radius
            assign text_box_border = section.settings.text_box_border
            assign text_box_border_color = block.settings.text_box_border_color

            assign image_box_border = section.settings.image_box_border
            assign image_border_color = block.settings.image_border_color

            assign heading_color = block.settings.heading_color
            assign heading_font_size = section.settings.heading_font_size

            assign caption_color = block.settings.caption_color
            assign caption_font_size = section.settings.caption_font_size

            assign description_color = block.settings.description_color
            assign description_font_size = section.settings.description_font_size

            assign button_text_color = block.settings.button_text_color
            assign button_background_color = block.settings.button_background_color
            assign button_border_color = block.settings.button_border_color
            assign button_font_size = section.settings.button_font_size
            assign button_border = section.settings.button_border
            assign button_border_radius = section.settings.button_border_radius
            assign button_hover_text_color = block.settings.button_hover_text_color
            assign button_hover_background_color = block.settings.button_hover_background_color
            assign button_hover_border_color = block.settings.button_hover_border_color
          %}
          {% style %}
            #ez-section-{{ section.id }} .ez_image {
              border: {{image_box_border}}px solid {{image_border_color}};
            }
            #slide__{{ block.id }} .ez_text {
              background-color: {{ boxBgColor }};
              text-align: {{ container_alignment }};
              align-items: {{ container_alignment }};
              border-radius: {{ text_box_border_radius }}px;
              border: {{text_box_border}}px solid {{text_box_border_color}};
            }
            #slide__{{ block.id }} .ez_heading h2{
              color:{{ heading_color }};
              font-size:{{ heading_font_size }}px;
              margin: 0;
            }
            #slide__{{ block.id }} .ez_caption span{
              color:{{ caption_color }};
              font-size:{{ caption_font_size }}px;
            }
            #slide__{{ block.id }} .ez_description p{
              color:{{ description_color }};
              font-size:{{ description_font_size }}px;
            }
            #slide__{{ block.id }} .ez_button .ez_text_button{
              color:{{ button_text_color }};
              background-color: {{ button_background_color }};
              border: solid {{ button_border }}px {{ button_border_color }};
              font-size:{{section.settings.button_font_size}}px ;
              border-radius:{{section.settings.button_border_radius }}px;
            }
            #slide__{{ block.id }} .ez_button .ez_text_button:hover {
              color:{{ button_hover_text_color }};
              background-color: {{ button_hover_background_color }};
              border: solid {{text_box_border}}px {{ button_hover_border_color }};
            }
            @media (max-width: 767px) {
              #slide__{{ block.id }} .ez_heading h2{
                font-size:{{section.settings.mobile_heading_font_size}}px ;
              }
              #slide__{{ block.id }} .ez_caption span{
                font-size:{{section.settings.mobile_caption_font_size}}px ;
              }
              #slide__{{ block.id }} .ez_description p{
                font-size:{{section.settings.mobile_description_font_size}}px ;
              }
               #slide__{{ block.id }} .ez_button .ez_text_button{
                font-size:{{section.settings.mobile_button_font_size}}px ;
              }
            }
          {% endstyle %}
          <div class="swiper-slide">
            <div
              id="slide__{{ block.id }}"
              class="ez_image_and_text {{ section.settings.layout }} {{ section.settings.layout_mobile }}"
            >
              <div
                class="ez_image "
                style="--padding-top: {{ 1 | divided_by: section.blocks[0].settings.image.aspect_ratio | times: 100 }}%;"
              >
                {% if block.settings.image != blank %}
                  <img src="{{ block.settings.image |img_url:''}}">
                {% else %}
                  {{ 'image' | placeholder_svg_tag: 'test2_svg' }}
                {% endif %}
              </div>
              <div class="ez_text">
                {% if block.settings.caption != blank %}
                  <div class="ez_caption">
                    <span>
                      {{- block.settings.caption -}}
                    </span>
                  </div>
                {% endif %}
                {% if block.settings.heading != blank %}
                  <div class="ez_heading">
                    <h2>
                      {{ block.settings.heading }}
                    </h2>
                  </div>
                {% endif %}
                {% if block.settings.description != blank %}
                  <div class="ez_description">
                    {{ block.settings.description }}
                  </div>
                {% endif %}
                {% if block.settings.button_label != blank %}
                  <div class="ez_button">
                    <a
                      class="ez_text_button"
                      href="{{ block.settings.button_link }}"
                    >
                      {{ block.settings.button_label }}
                    </a>
                  </div>
                {% endif %}
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
      <div class="swiper-pagination"></div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
<script>
  var testimonialSwiper = new Swiper(".ez_image_and_text-{{ section.id }}", {
    spaceBetween: 1,
    effect: "fade",
    pagination: {
        el: ".swiper-pagination",
        clickable: true,
      }
  });
</script>

{% schema %}
{
  "name": "EZ - ImageText Slider",
  "class": "ez-sections ez-imagetext-slider",
  "settings": [
    {
      "type": "header",
      "content": "Content Box Settings"
    },
    {
      "type": "select",
      "id": "container_alignment",
      "label": "Content Alignment",
      "default": "center",
      "options": [
        {
          "value": "start",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "end",
          "label": "Right"
        }
      ]
    },
    {
      "type": "range",
      "id": "text_box_border",
      "label": "Border Thickness",
      "default": 1,
      "min": 0,
      "max": 10,
      "step": 1
    },
    {
      "type": "range",
      "id": "text_box_border_radius",
      "label": "Border Radius",
      "default": 10,
      "min": 0,
      "max": 100,
      "step": 1
    },
    {
      "type": "range",
      "id": "text_padding_top",
      "min": 0,
      "max": 300,
      "step": 10,
      "unit": "px",
      "label": "Padding Top",
      "default": 120
    },
    {
      "type": "range",
      "id": "text_padding_top_mobile",
      "min": 0,
      "max": 300,
      "step": 10,
      "unit": "px",
      "label": "Padding Top - Mobile",
      "default": 20
    },
    {
      "type": "range",
      "id": "text_padding_bottom",
      "min": 0,
      "max": 300,
      "step": 10,
      "unit": "px",
      "label": "Padding Bottom",
      "default": 120
    },
    {
      "type": "range",
      "id": "text_padding_bottom_mobile",
      "min": 0,
      "max": 300,
      "step": 10,
      "unit": "px",
      "label": "Padding Bottom - Mobile",
      "default": 20
    },
    {
      "type": "header",
      "content": "Image Box Settings"
    },
    {
      "type": "select",
      "id": "layout",
      "options": [
        {
          "value": "ez_image_first",
          "label": "Left"
        },
        {
          "value": "ez_text_first",
          "label": "Right"
        }
      ],
      "default": "ez_text_first",
      "label": "Image Placement"
    },
    {
      "type": "select",
      "id": "layout_mobile",
      "options": [
        {
          "value": "ez-img-top",
          "label": "Top"
        },
        {
          "value": "ez-img-btm",
          "label": "Bottom"
        }
      ],
      "default": "ez-img-top",
      "label": "Image Placement - Mobile"
    },
    {
      "type": "range",
      "id": "mob_height",
      "min": 0,
      "max": 500,
      "step": 10,
      "unit": "px",
      "label": "Image height - Mobile",
      "default": 400
    },
    {
      "type": "range",
      "id": "image_box_border",
      "label": "Border Thickness",
      "default": 1,
      "min": 0,
      "max": 10,
      "step": 1
    },
    {
      "type": "range",
      "id": "image_border_radius",
      "label": "Border Radius",
      "default": 10,
      "min": 0,
      "max": 100,
      "step": 1
    },
    {
      "type": "header",
      "content": "Caption Settings"
    },
    {
      "type": "checkbox",
      "id": "caption_custom",
      "label": "Use custom font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "caption_fonts",
      "label": "Font family",
      "default": "josefin_sans_n4",
      "visible_if": "{{ section.settings.caption_custom == true }}"
    },
    {
      "type": "range",
      "id": "caption_font_size",
      "label": "Font Size",
      "default": 12,
      "min": 8,
      "max": 30,
      "step": 2
    },
    {
      "type": "range",
      "id": "mobile_caption_font_size",
      "label": "Font Size - Mobile",
      "default": 12,
      "min": 0,
      "max": 30,
      "step": 2
    },
    {
      "type": "header",
      "content": "Heading Settings"
    },
    {
      "type": "checkbox",
      "id": "heading_custom",
      "label": "Use custom font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "heading_fonts",
      "label": "Font family",
      "default": "josefin_sans_n4",
      "visible_if": "{{ section.settings.heading_custom == true }}"
    },
    {
      "type": "range",
      "id": "heading_font_size",
      "label": "Font Size",
      "default": 38,
      "min": 20,
      "max": 60,
      "step": 2
    },
    {
      "type": "range",
      "id": "mobile_heading_font_size",
      "label": "Font Size - Mobile",
      "default": 26,
      "min": 14,
      "max": 32,
      "step": 2
    },
    {
      "type": "range",
      "id": "heading_margin_top",
      "label": "Margin Top",
      "default": 10,
      "min": 0,
      "max": 100,
      "step": 1
    },
    {
      "type": "range",
      "id": "mobile_heading_margin_top",
      "label": "Margin Top - Mobile",
      "default": 10,
      "min": 0,
      "max": 50,
      "step": 1
    },
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "checkbox",
      "id": "description_custom",
      "label": "Use custom font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "description_fonts",
      "label": "Font family",
      "default": "josefin_sans_n4",
      "visible_if": "{{ section.settings.description_custom == true }}"
    },
    {
      "type": "range",
      "id": "description_font_size",
      "label": "Font Size",
      "default": 16,
      "min": 8,
      "max": 40,
      "step": 2
    },
    {
      "type": "range",
      "id": "mobile_description_font_size",
      "label": "Font Size - Mobile",
      "default": 12,
      "min": 0,
      "max": 40,
      "step": 2
    },
    {
      "type": "range",
      "id": "description_margin_top",
      "label": "Margin Top",
      "default": 30,
      "min": 0,
      "max": 100,
      "step": 1
    },
    {
      "type": "range",
      "id": "mobile_description_margin_top",
      "label": "Margin Top - Mobile",
      "default": 10,
      "min": 0,
      "max": 50,
      "step": 1
    },
    {
      "type": "header",
      "content": "Button Settings"
    },
    {
      "type": "checkbox",
      "id": "button_custom",
      "label": "Use custom font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "button_fonts",
      "label": "Font family",
      "default": "josefin_sans_n4",
      "visible_if": "{{ section.settings.button_custom == true }}"
    },
    {
      "type": "range",
      "id": "button_font_size",
      "label": "Font Size",
      "default": 14,
      "min": 8,
      "max": 40,
      "step": 2
    },
    {
      "type": "range",
      "id": "mobile_button_font_size",
      "label": "Font Size - Mobile",
      "default": 12,
      "min": 0,
      "max": 40,
      "step": 2
    },
    {
      "type": "range",
      "id": "button_border",
      "label": "Border Thickness",
      "default": 0,
      "min": 0,
      "max": 10,
      "step": 1
    },
    {
      "type": "range",
      "id": "button_border_radius",
      "label": "Border Radius",
      "default": 10,
      "min": 0,
      "max": 100,
      "step": 1
    },
    {
      "type": "range",
      "id": "fast_button_padding_top_and_bottom",
      "min": 0,
      "max": 50,
      "step": 1,
      "label": "Padding Top/Bottom",
      "default": 10
    },
    {
      "type": "range",
      "id": "fast_button_padding_left_and_right",
      "min": 0,
      "max": 200,
      "step": 2,
      "label": "Padding Left/Right",
      "default": 20
    },
    {
      "type": "range",
      "id": "button_margin_top",
      "label": "Margin Top",
      "default": 10,
      "min": 0,
      "max": 100,
      "step": 1
    },
    {
      "type": "range",
      "id": "mobile_button_margin_top",
      "label": "Margin Top - Mobile",
      "default": 10,
      "min": 0,
      "max": 50,
      "step": 1
    },
    {
      "type": "header",
      "content": "Dots Settings"
    },
    {
      "type": "color",
      "id": "dots_color",
      "label": "Dots Color",
      "default": "#fff"
    },
    {
      "type": "header",
      "content": "Section Width"
    },
    {
      "type": "checkbox",
      "id": "container_size_full",
      "label": "Container Size Full",
      "default": false,
      "info": "Custom container size will not work if this 'TRUE' "
    },
    {
      "type": "range",
      "id": "container_size",
      "label": "Container Size",
      "step": 100,
      "min": 800,
      "max": 1500,
      "default": 1000,
      "visible_if": "{{ section.settings.container_size_full == false }}"
    },
    {
      "type": "header",
      "content": "Section Color"
    },
    {
      "type": "select",
      "id": "background_type",
      "label": "Background Type",
      "default": "none",
      "options": [
        {
          "label": "Color",
          "value": "color"
        },
        {
          "label": "Gradient",
          "value": "gradient"
        },
        {
          "label": "None",
          "value": "none"
        }
      ]
    },
    {
      "type": "color",
      "id": "background-color",
      "label": "Background Color",
      "default":"#FFEFDE",
      "visible_if": "{{ section.settings.background_type == 'color' }}"
    },
    {
      "type": "color_background",
      "id": "section_background_gradient",
      "label": "Background Gradient Color",
      "visible_if": "{{ section.settings.background_type == 'gradient' }}"
    },
    {
      "type": "header",
      "content": "Section Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding Top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding Bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Section Margin"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Margin top",
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Margin bottom",
      "default": 0
    },
  ],
  "blocks": [
    {
      "type": "slide",
      "name": "Slide",
      "settings": [
        {
          "type": "header",
          "content": "Content Settings"
        },
        {
          "type": "text",
          "id": "caption",
          "label": "Caption",
          "default": "Surf proof!"
        },
        {
          "type": "inline_richtext",
          "id": "heading",
          "label": "Heading",
          "default": "Swim tops"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "Description",
          "default": "<p>The perfect swim bottoms combine both comfort and style, offering a secure fit for swimming and water activities while also showcasing a fashionable design</p>"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Button Label",
          "default": "Shop Now"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "Button Link"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "header",
          "content": "Image Colors Settings"
        },
        {
          "type": "color",
          "id": "image_border_color",
          "label": "Border Color",
          "default": "#000"
        },
        {
          "type": "header",
          "content": "Content Colors Settings"
        },
        {
          "type": "color",
          "id": "heading_color",
          "label": "Heading Color",
          "default": "#000"
        },
        {
          "type": "color",
          "id": "caption_color",
          "label": "Caption Color",
          "default": "#000"
        },
        {
          "type": "color",
          "id": "description_color",
          "label": "Description Color",
          "default": "#000"
        },
        {
          "type": "color",
          "id": "text_box_background_color",
          "label": "Background Color",
          "default": "#f0f8ff"
        },
        {
          "type": "color",
          "id": "text_box_border_color",
          "label": "Border Color",
          "default": "#000"
        },
        {
          "type": "header",
          "content": "Button Colors Settings"
        },
        {
          "type": "color",
          "id": "button_text_color",
          "label": "Color",
          "default": "#fff"
        },
        {
          "type": "color",
          "id": "button_background_color",
          "label": "Background Color",
          "default": "#000"
        },
        {
          "type": "color",
          "id": "button_border_color",
          "label": "Border Color",
          "default": "#000"
        },
        {
          "type": "color",
          "id": "button_hover_text_color",
          "label": "Color - Hover",
          "default": "#fff"
        },
        {
          "type": "color",
          "id": "button_hover_background_color",
          "label": "Background Color - Hover",
          "default": "#000"
        },
        {
          "type": "color",
          "id": "button_hover_border_color",
          "label": "Border Color - Hover",
          "default": "#000"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "EZ - ImageText Slider",
      "category": "Image",
      "settings": {
        "container_alignment": "center",
        "text_box_border": 1,
        "text_box_border_radius": 12,
        "text_padding_top": 120,
        "text_padding_top_mobile": 40,
        "text_padding_bottom": 120,
        "text_padding_bottom_mobile": 40,
        "layout": "ez_text_first",
        "layout_mobile": "ez-img-top",
        "image_box_border": 0,
        "image_border_radius": 12,
        "caption_custom": false,
        "caption_font_size": 14,
        "mobile_caption_font_size": 12,
        "heading_custom": false,
        "heading_font_size": 42,
        "mobile_heading_font_size": 26,
        "heading_margin_top": 10,
        "mobile_heading_margin_top": 10,
        "description_custom": false,
        "description_font_size": 18,
        "mobile_description_font_size": 14,
        "description_margin_top": 24,
        "mobile_description_margin_top": 16,
        "button_custom": false,
        "button_font_size": 16,
        "mobile_button_font_size": 14,
        "button_border": 0,
        "button_border_radius": 8,
        "fast_button_padding_top_and_bottom": 12,
        "fast_button_padding_left_and_right": 28,
        "button_margin_top": 20,
        "mobile_button_margin_top": 16,
        "dots_color": "#444",
        "container_size_full": false,
        "container_size": 1200,
        "background_type": "gradient",
        "section_background_gradient": "linear-gradient(135deg, #FFF6E5, #FFE4EC)",
        "padding_top": 48,
        "padding_bottom": 48,
        "margin_top": 0,
        "margin_bottom": 0
      },
      "blocks": [
        {
          "type": "slide",
          "settings": {
            "caption": "Summer Sale",
            "heading": "Breezy Dresses",
            "description": "<p>Discover our light and airy summer dresses – perfect for sunny days and warm nights.</p>",
            "button_label": "Shop Dresses",
            "button_link": "#",
            "image_border_color": "#ffffff",
            "heading_color": "#222222",
            "caption_color": "#E67E22",
            "description_color": "#444444",
            "text_box_background_color": "#FFF9F2",
            "text_box_border_color": "#FFD8A9",
            "button_text_color": "#ffffff",
            "button_background_color": "#E67E22",
            "button_border_color": "#E67E22",
            "button_hover_text_color": "#E67E22",
            "button_hover_background_color": "#ffffff",
            "button_hover_border_color": "#E67E22"
          }
        },
        {
          "type": "slide",
          "settings": {
            "caption": "New Arrivals",
            "heading": "Swim Collection",
            "description": "<p>Dive into the latest swimwear styles made for comfort, style, and performance.</p>",
            "button_label": "Explore Swim",
            "button_link": "#",
            "image_border_color": "#ffffff",
            "heading_color": "#0E374E",
            "caption_color": "#20C997",
            "description_color": "#2D3E50",
            "text_box_background_color": "#EAFDFC",
            "text_box_border_color": "#B7F8DB",
            "button_text_color": "#ffffff",
            "button_background_color": "#20C997",
            "button_border_color": "#20C997",
            "button_hover_text_color": "#20C997",
            "button_hover_background_color": "#ffffff",
            "button_hover_border_color": "#20C997"
          }
        },
        {
          "type": "slide",
          "settings": {
            "caption": "Style Essentials",
            "heading": "Everyday Tops",
            "description": "<p>Find everyday essentials you’ll reach for again and again — soft, stylish, and versatile.</p>",
            "button_label": "Browse Tops",
            "button_link": "#",
            "image_border_color": "#ffffff",
            "heading_color": "#1F2937",
            "caption_color": "#6366F1",
            "description_color": "#4B5563",
            "text_box_background_color": "#F3F4F6",
            "text_box_border_color": "#C7D2FE",
            "button_text_color": "#ffffff",
            "button_background_color": "#6366F1",
            "button_border_color": "#6366F1",
            "button_hover_text_color": "#6366F1",
            "button_hover_background_color": "#ffffff",
            "button_hover_border_color": "#6366F1"
          }
        }
      ]
    }
  ]
}
{% endschema %}