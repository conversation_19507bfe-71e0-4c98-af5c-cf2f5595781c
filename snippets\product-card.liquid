{%- comment -%}
  {% render 'product-card',
            product_card_product: product,
            media_size: settings.image_ratio,
            show_secondary_image: settings.show_secondary_image,
            show_vendor: settings.show_vendor,
            show_badge: settings.show_badges,
            show_cart_button: settings.show_cart_button,
            show_quick_view: settings.show_quick_view_button,
            show_quick_compare: settings.show_compare_view_button,
            show_wishlist: settings.show_wishlist_button,
            show_countdown: settings.show_countdown,
            show_title: settings.show_title,
            show_price: settings.show_price,
            show_product_description: settings.show_product_description,
            show_rating: settings.show_product_rating
  %}
{%- endcomment -%}

{%- liquid
  assign variant = product_card_product.selected_or_first_available_variant

  assign productCountdown = product_card_product.metafields.metaname.countdown_timer.value
  assign todayDate = 'now' | date: '%s'
  assign countDownDate = productCountdown | date: '%s'

  assign first_variant_featured_media_check = false
  if variant.featured_media == null
    assign first_variant_featured_media_check = true
  endif

  assign second_img_position = 1

  unless show_secondary_image
    assign second_image_class = 'second--image__hide'
  endunless

  case settings.product_content_alignment
    when 'left'
      assign price_class = 'product__card__price justify-content-start'
    when 'right'
      assign price_class = 'product__card__price justify-content-end'
    else
      assign price_class = 'product__card__price justify-content-center'
  endcase

  for variant in product_card_product.variants
    assign current_total_stock = current_total_stock | plus: variant.inventory_quantity
  endfor
-%}

<div class="product__card {{ className }} product__card--{{ card_style }} {% if box_shadow %} product__shadow {% endif %} {% if corner_radius %} product--corner-radius-true{% endif %} color-{{ color_scheme }}">
  <div class="product__card__thumbnail card--client-height {{ second_image_class }}">
    {%- if show_badge -%}
      {%- render 'product-badge', product: product_card_product -%}
    {%- endif -%}

    {%- if product_card_product.featured_media -%}
      {%- liquid
        assign featured_media_aspect_ratio = product_card_product.featured_media.aspect_ratio

        if product_card_product.featured_media.aspect_ratio == null
          assign featured_media_aspect_ratio = 1
        endif
      -%}
      <a
        href="{{ product_card_product.url | default: '#' }}"
        class="d-block product__media_thumbnail product__card--link"
      >
        {%- render 'product-card-media',
          product_card_product: product_card_product,
          variant: variant,
          media_size: media_size,
          featured_media_aspect_ratio: featured_media_aspect_ratio,
          second_img_position: second_img_position,
          show_secondary_image: show_secondary_image,
          first_variant_featured_media_check: first_variant_featured_media_check
        -%}
      </a>

    {%- else -%}
      <div class="card__content">
        <a href="{{ product_card_product.url | default: '#' }}" class="d-block">
          <div class="placeholder placeholder_svg_parent" style="padding-bottom: 100%">
            {{ 'product-1' | placeholder_svg_tag: 'placeholder-svg-2' }}
          </div>
        </a>
      </div>
    {%- endif -%}

    {%- if show_countdown -%}
      {%- if todayDate < countDownDate -%}
        <countdown-timer class="product__grid_timer">
          <div class="deals__product--countdown d-flex" data-countdown="{{ productCountdown }}"></div>
        </countdown-timer>
      {%- endif -%}
    {%- endif -%}

    {% if card_style == 'style_1' and show_wishlist %}
      {%- render 'button-wishlist',
        product: product_card_product,
        className: 'product__card--wishlist-btn',
        tooltip: true,
        tooltip_position: 'tooltip--left'
      -%}
    {%- endif -%}

    {% if card_style == 'style_2' and show_wishlist %}
      <div class="d-md-only-visible">
        {%- render 'button-wishlist',
          product: product_card_product,
          className: 'product__card--wishlist-btn',
          tooltip: true,
          tooltip_position: 'tooltip--left'
        -%}
      </div>
    {%- endif -%}

    {% if card_style == 'style_3' and show_wishlist %}
      <div class="d-md-only-visible">
        {%- render 'button-wishlist',
          product: product_card_product,
          className: 'product__card--wishlist-btn',
          tooltip: true,
          tooltip_position: 'tooltip--left'
        -%}
      </div>
    {%- endif -%}

    {% comment %} Add To cart button style 2 desktop {% endcomment %}
    {%- if show_cart_button -%}
      {% if card_style == 'style_2' %}
        {% render 'add-to-cart-form',
          product_card_product: product_card_product,
          variant: variant,
          custom_class: 'product__card--style2 d-md-none',
          button_class: 'button product__card--style2',
          show_preorder_button: show_preorder_button
        %}
      {%- endif -%}
    {%- endif -%}
    {% comment %} Add To cart button style 2 desktop ./ {% endcomment %}

    {% comment %} Add To cart button style 3 desktop {% endcomment %}
    {%- if show_cart_button -%}
      {% if card_style == 'style_3' %}
        {% render 'add-to-cart-form',
          product_card_product: product_card_product,
          variant: variant,
          custom_class: 'product__card--style3 d-md-none',
          button_class: 'button product__card--style3',
          show_preorder_button: show_preorder_button
        %}
      {%- endif -%}
    {%- endif -%}
    {% comment %} Add To cart button style 3 desktop ./ {% endcomment %}

    <!-- Product action button style 2 -->
    {% if card_style == 'style_2' %}
      <div class="product-card-action-buttons-style2 d-md-none">
        {%- if show_wishlist -%}
          {%- render 'button-wishlist',
            product: product_card_product,
            tooltip: true,
            tooltip_position: 'tooltip--left',
            className: 'product__card-style2--action-btn'
          -%}
        {%- endif -%}

        {%- if show_quick_compare -%}
          {%- render 'button-compare',
            product: product_card_product,
            tooltip: true,
            tooltip_position: 'tooltip--left',
            className: 'product__card-style2--action-btn'
          -%}
        {%- endif -%}

        {%- if show_quick_view -%}
          {%- render 'quick-view',
            product_card_product: product_card_product,
            variant: variant,
            type: 'quick_view',
            tooltip: true,
            tooltip_position: 'tooltip--left',
            className: 'product__card-style2--action-btn'
          -%}
        {%- endif -%}
      </div>
    {%- endif -%}
    <!-- Product action button style 2 ./ -->

    <!-- Product action button style 3 -->
    {% if card_style == 'style_3' %}
      <div class="product-card-action-buttons-style3 d-md-none">
        {%- if show_wishlist -%}
          {%- render 'button-wishlist',
            product: product_card_product,
            tooltip: true,
            tooltip_position: 'tooltip--left',
            className: 'product__card-style3--action-btn'
          -%}
        {%- endif -%}

        {%- if show_quick_compare -%}
          {%- render 'button-compare',
            product: product_card_product,
            tooltip: true,
            tooltip_position: 'tooltip--left',
            className: 'product__card-style3--action-btn'
          -%}
        {%- endif -%}

        {%- if show_quick_view -%}
          {%- render 'quick-view',
            product_card_product: product_card_product,
            variant: variant,
            type: 'quick_view',
            tooltip: true,
            tooltip_position: 'tooltip--left',
            className: 'product__card-style3--action-btn'
          -%}
        {%- endif -%}
      </div>
    {%- endif -%}
    <!-- Product action button style 3 ./ -->
  </div>

  <div class="product__card__content text-{{ settings.product_content_alignment }} {% if spacing %} product--card-spacing-true{% endif %}">
    {%- if show_vendor -%}
      <span class="visually-hidden">{{ 'accessibility.vendor' | t }}</span>
      <div class="product__vendor">{{ product_card_product.vendor }}</div>
    {%- endif -%}

    {%- if show_title -%}
      <h3 class="product__card__title {{ settings.product_title_size }}">
        <a class="product__card-title--link" href="{{ product_card_product.url | default: '#' }}">
          {{- product_card_product.title -}}
        </a>
      </h3>
    {%- endif -%}
    
    {%- if show_rating and product_card_product.metafields.reviews.rating.value != blank -%}

      
<div class="trustshop-collection-rating--item" rating-value="{{ product_card_product.metafields.reviews.rating.value }}"
  rating-count="{{ product_card_product.metafields.reviews.rating_count }}">
</div>

      
    {%- endif -%}
    
    {%- if show_price -%}
      {% render 'price', product: product_card_product, price_class: price_class %}
    {%- endif -%}

    {% comment %} Style 3 Customizable Button {% endcomment %}
    {% if card_style == 'style_3' %}
      {%- if settings.style3_button_enable -%}
        <div class="product__card--style3-custom-button-wrapper">
          <a
            href="{{ product_card_product.url | default: '#' }}#scroll-target"
            class="product__card--style3-custom-button button"
            style="
              {% if settings.style3_button_bg_color != blank %}background-color: {{ settings.style3_button_bg_color }};{% endif %}
              {% if settings.style3_button_text_color != blank %}color: {{ settings.style3_button_text_color }};{% endif %}
              {% if settings.style3_button_border_color != blank %}border-color: {{ settings.style3_button_border_color }};{% endif %}
            "
          >
            {{ settings.style3_button_text | default: 'Customize' }}
          </a>
        </div>
      {%- endif -%}
    {% endif %}
    {% comment %} Style 3 Customizable Button ./ {% endcomment %}

       {% if settings.color_swatches %}
      {% if color_swatches %}
        {% render 'product-card-color-swatch', product: product_card_product, current_variant: variant %}
      {% endif %}
    {% endif %}



    {% if inventory_status %}
     <div class="product--inventory-stock">
        {% if current_total_stock > 0 %}
          <span class="product--inventory-stocky-label product--card-stock-badge badge-stock badge-stock-sucess">
            <span class="badge-stock-dot"></span>
            {{ 'products.product.inventory_status.product_card_in_stock' | t }} {{ current_total_stock }}
            {% if current_total_stock > 1 %}{{ 'products.product.inventory_status.in_stock_label_many' | t }}{% else %}{{ 'products.product.inventory_status.in_stock_label_single' | t }}{% endif %}
              
          </span>
        {% else %}
          <span class="product--inventory-stocky-label product--card-stock-badge badge-stock badge-stock-warning">
            <span class="badge-stock-dot"></span>
            {{ 'products.product.out_of_stock' | t }}
          </span>
        {% endif %}
      </div>
     {% endif %}

    <!-- Product action button -->
    <div class="product-card-action-buttons xxv {% if card_style == 'style_2' or card_style == 'style_3' %}d-md-only-visible{% endif %}">
      {% comment %} Add To cart button style 1 {% endcomment %}
      {%- if show_cart_button -%}
        {% render 'add-to-cart-form',
          product_card_product: product_card_product,
          variant: variant,
          tooltip: true,
          tooltip_position: 'tooltip--top',
          tooltip_desktop: false,
          show_preorder_button: show_preorder_button
        %}
      {%- endif -%}
      {% comment %} Add To cart button style 1 ./ {% endcomment %}
 {% comment %}
      {%- if show_quick_compare -%}
        {%- render 'button-compare',
          product: product_card_product,
          className: 'product__card--action-btn product__card--action-btn-icon',
          tooltip: true,
          tooltip_position: 'tooltip--top'
        -%}
      {%- endif -%}

      {%- if show_quick_view -%}
        {%- render 'quick-view',
          product_card_product: product_card_product,
          variant: variant,
          type: 'quick_view',
          className: 'product__card--action-btn product__card--action-btn-icon',
          tooltip: true,
          tooltip_position: 'tooltip--top'
        -%}
      {%- endif -%}
       {% endcomment %}
    </div>
    <!-- Product action button ./ -->

  </div>
</div>
