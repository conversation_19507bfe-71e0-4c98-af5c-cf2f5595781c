{"settings_schema": {"colors": {"name": "Couleurs", "settings": {"colors_solid_button_labels": {"label": "Texte de bouton plein", "info": "Utilisé comme couleur de premier plan sur les couleurs d'accentuation."}, "colors_accent_1": {"label": "Accentuation 1", "info": "Utilisé pour l'arrière-plan de bouton uni."}, "colors_accent_2": {"label": "Accentuation 2"}, "header__1": {"content": "Couleurs primaires"}, "header__2": {"content": "Couleurs secondaires"}, "colors_text": {"label": "Texte", "info": "Utilisé comme couleur de premier plan sur les couleurs d'arrière-plan."}, "colors_outline_button_labels": {"label": "Bouton en relief", "info": "Utilisé également pour les liens de texte."}, "colors_background_1": {"label": "Arrière-plan 1"}, "colors_background_2": {"label": "Arrière-plan 2"}}}, "typography": {"name": "Typographie", "settings": {"type_header_font": {"label": "Police", "info": "La sélection d'une police différente peut influencer la vitesse de votre boutique. [En savoir plus sur les polices système.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)"}, "header__1": {"content": "Titres"}, "header__2": {"content": "Corps"}, "type_body_font": {"label": "Police", "info": "La sélection d'une police différente peut influencer la vitesse de votre boutique. [En savoir plus sur les polices système.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)"}}}, "styles": {"name": "Styles", "settings": {"sold_out_badge_color_scheme": {"options__1": {"label": "Arrière-plan 1"}, "options__2": {"label": "Inverser"}, "label": "Schéma de couleurs des badges épuisés"}, "header__1": {"content": "Badges"}, "header__2": {"content": "Éléments décoratifs"}, "sale_badge_color_scheme": {"options__1": {"label": "Arrière-plan 2"}, "options__2": {"label": "Accentuation 1"}, "options__3": {"label": "Accentuation 2"}, "label": "Schéma de couleurs des badges de vente"}, "accent_icons": {"options__1": {"label": "Accentuation 1"}, "options__2": {"label": "Accentuation 2"}, "options__3": {"label": "Bouton en relief"}, "options__4": {"label": "Texte"}, "label": "Icônes d'accent"}}}, "social-media": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"share_facebook": {"label": "Partager sur Facebook"}, "share_twitter": {"label": "Tweeter sur Twitter"}, "share_pinterest": {"label": "<PERSON><PERSON><PERSON> sur Pinterest"}, "header__1": {"content": "Options de partage social"}, "header__2": {"content": "Co<PERSON><PERSON> sociaux"}, "social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://vimeo.com/shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "http://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "Co<PERSON><PERSON> sociaux"}}}, "currency_format": {"name": "Format de devise", "settings": {"content": "Codes de devise", "currency_code_enabled": {"label": "Afficher les codes de devise"}, "paragraph": "Le panier et les prix au moment du paiement indiquent toujours les codes de devise. Exemple : 1 EUR."}}, "favicon": {"name": "Favicon", "settings": {"favicon": {"label": "Image favicon", "info": "Sera réduite à 32 x 32 px"}}}, "layout": {"name": "Mise en page", "settings": {"page_width": {"label": "Largeur maximum", "options__1": {"label": "1 200 pixels"}, "options__2": {"label": "1 600 pixels"}}}}, "search_input": {"name": "Rechercher une entrée", "settings": {"header": {"content": "Suggestions de produits"}, "predictive_search_enabled": {"label": "Activer les suggestions de produits"}, "predictive_search_show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le distributeur", "info": "Visible lorsque les suggestions de produits sont activées."}, "predictive_search_show_price": {"label": "<PERSON>ff<PERSON>r le prix", "info": "Visible lorsque les suggestions de produits sont activées."}}}}, "sections": {"announcement-bar": {"name": "Barre d'annonces", "blocks": {"announcement": {"name": "<PERSON><PERSON><PERSON>", "settings": {"text": {"label": "Texte"}, "color_scheme": {"label": "Combinaison de couleurs", "options__1": {"label": "Arrière-plan 1"}, "options__2": {"label": "Arrière-plan 2"}, "options__3": {"label": "Inverser"}, "options__4": {"label": "Accentuation 1"}, "options__5": {"label": "Accentuation 2"}}, "link": {"label": "<PERSON><PERSON>"}}}}}, "collage": {"name": "Collage", "settings": {"heading": {"label": "Titre"}, "desktop_layout": {"label": "Mise en page du bureau", "options__1": {"label": "Grand bloc gauche"}, "options__2": {"label": "Grand bloc droit"}}, "mobile_layout": {"label": "Mise en page pour téléphone portable", "options__1": {"label": "Collage"}, "options__2": {"label": "Colonne"}}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Image"}, "image_padding": {"label": "Ajouter une marge intérieure à l'image", "info": "Sélectionnez une marge intérieure pour éviter que votre image ne soit rognée."}, "color_scheme": {"options__1": {"label": "Accentuation 1"}, "options__2": {"label": "Accentuation 2"}, "options__3": {"label": "Arrière-plan 1"}, "options__4": {"label": "Arrière-plan 2"}, "options__5": {"label": "Inverser"}, "label": "Combinaison de couleurs", "info": "Sé<PERSON><PERSON>ner une marge intérieure pour l'image afin de rendre la couleur visible."}}}, "product": {"name": "Produit", "settings": {"product": {"label": "Produit"}, "secondary_background": {"label": "Afficher l'arrière-plan secondaire"}, "second_image": {"label": "Afficher la deuxième image en survol"}, "image_padding": {"label": "Ajouter une marge intérieure à l'image", "info": "Sélectionnez une marge intérieure pour éviter que vos images soient rognées."}}}, "collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}, "image_padding": {"label": "Ajouter une marge intérieure à l'image", "info": "Sélectionnez une marge intérieure pour éviter que votre image ne soit rognée."}, "color_scheme": {"options__1": {"label": "Accentuation 1"}, "options__2": {"label": "Accentuation 2"}, "options__3": {"label": "Arrière-plan 1"}, "options__4": {"label": "Arrière-plan 2"}, "options__5": {"label": "Inverser"}, "label": "Combinaison de couleurs"}}}, "video": {"name": "Vidéo", "settings": {"cover_image": {"label": "Image de couverture"}, "video_url": {"label": "URL", "info": "Si la section contient d'autres blocs, la vidéo est lue dans une fenêtre pop-up.", "placeholder": "Utiliser une URL YouTube ou Vimeo"}, "image_padding": {"label": "Ajouter une marge intérieure à l'image", "info": "Sélectionnez une marge intérieure pour éviter que votre image de couverture ne soit rognée."}, "description": {"label": "Texte alternatif de la vidéo", "info": "Décrivez la vidéo pour la rendre accessible aux clients utilisant des lecteurs d'écran."}}}}, "presets": {"name": "Collage"}}, "collection-list": {"name": "Liste des collections", "settings": {"title": {"label": "Titre"}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "info": "Pour ajouter des images, modifiez vos collections. [En savoir plus](https://help.shopify.com/en/manual/products/collections)"}, "color_scheme": {"options__1": {"label": "Accentuation 1"}, "options__2": {"label": "Accentuation 2"}, "options__3": {"label": "Arrière-plan 1"}, "options__4": {"label": "Arrière-plan 2"}, "options__5": {"label": "Inverser"}, "label": "Combinaison de couleurs"}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}, "image_padding": {"label": "Ajouter une marge intérieure à l'image"}, "show_view_all": {"label": "Activer le bouton « Tout afficher » si la liste comprend plus de collections que celles affichées"}}, "blocks": {"featured_collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}}}}, "presets": {"name": "Liste des collections"}}, "contact-form": {"name": "Formulaire de contact", "presets": {"name": "Formulaire de contact"}}, "custom-liquid": {"name": "Liquid personnalisé", "settings": {"custom_liquid": {"label": "Liquid personnalisé", "info": "Ajoutez des extraits d'application ou autre code Liquid pour créer des personnalisations avancées."}}, "presets": {"name": "Liquid personnalisé"}}, "featured-blog": {"name": "Articles de blog", "settings": {"heading": {"label": "Titre"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Articles de blog"}, "show_view_all": {"label": "<PERSON>r le bouton « Tout afficher » si le blog comprend plus d'articles que ceux affichés"}, "show_image": {"label": "Afficher les images vedettes", "info": "Pour optimiser vos résultats, utilisez une image ayant un rapport d'aspect de 2:3. [En savoir plus](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "soft_background": {"label": "Afficher l'arrière-plan secondaire"}, "show_date": {"label": "Affiche<PERSON> la date"}, "show_author": {"label": "Afficher l'auteur"}}, "blocks": {"title": {"name": "Titre", "settings": {"show_date": {"label": "Affiche<PERSON> la date"}, "show_author": {"label": "Afficher l'auteur"}}}, "summary": {"name": "Extrait"}, "link": {"name": "<PERSON><PERSON>"}}, "presets": {"name": "Articles de blog"}}, "featured-collection": {"name": "Collection en vedette", "settings": {"title": {"label": "Titre"}, "collection": {"label": "Collection"}, "products_to_show": {"label": "Quantité maximale de produits à afficher"}, "show_view_all": {"label": "<PERSON><PERSON> le bouton « Tout afficher » si la collection comprend plus de produits que ceux affichés"}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}, "header": {"content": "Carte de produit"}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Afficher la deuxième image en survol"}, "add_image_padding": {"label": "Ajouter une marge intérieure à l'image"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le distributeur"}, "show_image_outline": {"label": "Afficher la bordure de l'image"}}, "presets": {"name": "Collection en vedette"}}, "footer": {"name": "Pied de page", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON>-tête", "info": "Un titre est nécessaire pour afficher le menu."}, "menu": {"label": "<PERSON><PERSON>", "info": "Affiche uniquement les éléments de menu de niveau supérieur."}}}, "text": {"name": "Texte", "settings": {"heading": {"label": "<PERSON>-tête"}, "subtext": {"label": "Sous-texte"}}}}, "settings": {"color_scheme": {"options__1": {"label": "Accentuation 1"}, "options__2": {"label": "Accentuation 2"}, "options__3": {"label": "Arrière-plan 1"}, "options__4": {"label": "Arrière-plan 2"}, "options__5": {"label": "Inverser"}, "label": "Combinaison de couleurs"}, "newsletter_enable": {"label": "Afficher l'inscription à la liste de diffusion"}, "newsletter_heading": {"label": "<PERSON>-tête"}, "header__1": {"content": "Inscription à la liste de diffusion", "info": "Abonnés automatiquement ajoutés à votre liste de clients « marketing accepté ». [En savoir plus](https://help.shopify.com/en/manual/customers/manage-customers)"}, "header__2": {"content": "Icônes de médias sociaux", "info": "Pour afficher vos comptes de réseaux sociaux, associez-les dans les paramètres de votre thème."}, "show_social": {"label": "Afficher les icônes des médias sociaux"}, "header__3": {"content": "Sélecteur de pays/région"}, "header__4": {"info": "Pour ajouter un pays/une région, allez à vos [paramètres de paiement.](/admin/settings/payments)"}, "enable_country_selector": {"label": "Activer le sélecteur de pays/région"}, "header__5": {"content": "<PERSON><PERSON><PERSON><PERSON> de langue"}, "header__6": {"info": "Pour ajouter une langue, allez à vos [paramètres de langue.](/admin/settings/languages)"}, "enable_language_selector": {"label": "<PERSON><PERSON> le sélecteur de langue"}, "header__7": {"content": "Moyens de paiement"}, "payment_enable": {"label": "Afficher les icônes de paiement"}}}, "header": {"name": "<PERSON>-tête", "settings": {"logo": {"label": "Image du logo"}, "logo_width": {"unit": "px", "label": "Largeur personnalisée du logo"}, "logo_position": {"label": "Position du logo sur les grands écrans", "options__1": {"label": "Centré à gauche"}, "options__2": {"label": "En haut à gauche"}, "options__3": {"label": "En haut au centre"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "Afficher la ligne de séparation"}, "enable_sticky_header": {"label": "<PERSON><PERSON> l'en-tête collé", "info": "L'en-tête s'affiche à l'écran lorsque les clients font défiler vers le haut."}}}, "image-banner": {"name": "Bannière avec image", "settings": {"image": {"label": "Première image"}, "image_2": {"label": "Deuxième image"}, "desktop_text_box_position": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "Bas"}, "label": "Position du texte sur le bureau"}, "color_scheme": {"options__1": {"label": "Accentuation 1"}, "options__2": {"label": "Accentuation 2"}, "options__3": {"label": "Arrière-plan 1"}, "options__4": {"label": "Arrière-plan 2"}, "options__5": {"label": "Inverser"}, "label": "Combinaison de couleurs", "info": "Visible lorsque la zone de texte est affichée"}, "stack_images_on_mobile": {"label": "Empiler des images sur un mobile"}, "adapt_height_first_image": {"label": "Adapter la hauteur de la section à la taille de la première image"}, "show_text_box": {"label": "Afficher la zone de texte sur le bureau"}, "image_overlay_opacity": {"label": "Opacité de la superposition d'images"}, "header": {"content": "Mise en page mobile"}, "show_text_below": {"label": "Afficher le texte sous les images"}}, "blocks": {"heading": {"name": "Titre", "settings": {"heading": {"label": "Titre"}, "heading_size": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Grand"}, "label": "Taille de la police du titre"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Description"}}}, "buttons": {"name": "Boutons", "settings": {"button_label_1": {"label": "Texte du premier bouton", "info": "Laisser le texte vide pour masquer le bouton."}, "button_link_1": {"label": "Lien du premier bouton"}, "button_style_secondary_1": {"label": "Utiliser le style du bouton en relief"}, "button_label_2": {"label": "Texte du deuxième bouton", "info": "Laisser le texte vide pour masquer le bouton."}, "button_link_2": {"label": "Lien du deuxième bouton"}, "button_style_secondary_2": {"label": "Utiliser le style du bouton en relief"}}}}, "presets": {"name": "Bannière avec image"}}, "image-with-text": {"name": "Image avec texte", "settings": {"image": {"label": "Image"}, "height": {"options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "Grand"}, "label": "Rapport d'image"}, "color_scheme": {"options__1": {"label": "Arrière-plan 1"}, "options__2": {"label": "Arrière-plan 2"}, "options__3": {"label": "Inverser"}, "options__4": {"label": "Accentuation 1"}, "options__5": {"label": "Accentuation 2"}, "label": "Combinaison de couleurs"}, "layout": {"options__1": {"label": "L'image en premier"}, "options__2": {"label": "Le texte en premier"}, "label": "Mise en page du bureau", "info": "La mise en page par défaut pour les appareils mobiles est « Image en premier »."}}, "blocks": {"heading": {"name": "Titre", "settings": {"heading": {"label": "Titre"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Description"}}}, "button": {"name": "Bouton", "settings": {"button_label": {"label": "Texte du bouton", "info": "Laisser le texte vide pour masquer le bouton."}, "button_link": {"label": "<PERSON>n du bouton"}}}}, "presets": {"name": "Image avec texte"}}, "main-article": {"name": "Article de blog", "blocks": {"featured_image": {"name": "Image vedette", "settings": {"image_height": {"label": "<PERSON>ur de l'image vedette", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}, "info": "Pour optimiser vos résultats, utilisez une image ayant un rapport d'aspect de 16:9. [En savoir plus](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "title": {"name": "Titre", "settings": {"blog_show_date": {"label": "Affiche<PERSON> la date"}, "blog_show_author": {"label": "Afficher l'auteur"}}}, "content": {"name": "Contenu"}, "social_sharing": {"name": "Boutons de partage social"}, "share": {"name": "Partager", "settings": {"featured_image_info": {"content": "Si vous incluez un lien dans les publications sur les réseaux sociaux, l'image vedette de la page sera affichée comme image d'aperçu. [En savoir plus](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Un titre et une description de la boutique sont inclus avec l'image d'aperçu. [En savoir plus](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "Texte"}}}}}, "main-blog": {"name": "Articles de blog", "settings": {"header": {"content": "Carte d'article de blog"}, "show_image": {"label": "Afficher les images vedettes", "info": "Pour optimiser vos résultats, utilisez une image ayant un rapport d'aspect de 2:3. [En savoir plus](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "paragraph": {"content": "Pour changer les extraits, modifiez vos articles de blog. [En savoir plus](https://help.shopify.com/en/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "show_date": {"label": "Affiche<PERSON> la date"}, "show_author": {"label": "Afficher l'auteur"}}, "blocks": {"title": {"name": "Titre", "settings": {"show_date": {"label": "Affiche<PERSON> la date"}, "show_author": {"label": "Afficher l'auteur"}}}, "summary": {"name": "Extrait"}, "link": {"name": "<PERSON><PERSON>"}}}, "main-cart-footer": {"name": "Sous-total", "settings": {"show_cart_note": {"label": "Activer la note de panier"}}, "blocks": {"subtotal": {"name": "Sous-total du prix"}, "buttons": {"name": "Bouton de paiement"}}}, "main-cart-items": {"name": "Articles", "settings": {"show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le distributeur"}}}, "main-collection-banner": {"name": "Bannière de collection", "settings": {"paragraph": {"content": "Pour ajouter une description ou une image, modifiez votre collection. [En savoir plus](https://help.shopify.com/en/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Afficher la description de la collection"}, "show_collection_image": {"label": "Afficher l'image de la collection", "info": "Pour optimiser vos résultats, utilisez une image ayant un rapport d'aspect de 16:9. [En savoir plus](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-collection-product-grid": {"name": "Grille de produit", "settings": {"products_per_page": {"label": "Produits par page"}, "enable_filtering": {"label": "<PERSON>r le filtrage", "info": "Personnaliser les [filtres](/admin/menus)"}, "enable_sorting": {"label": "<PERSON><PERSON> le tri"}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Afficher la deuxième image en survol"}, "add_image_padding": {"label": "Ajouter une marge intérieure à l'image"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le distributeur"}, "header__1": {"content": "Filtrage et tri"}, "header__3": {"content": "Carte de produit"}, "enable_tags": {"label": "<PERSON>r le filtrage", "info": "[Personnaliser les filtres](/admin/menus)"}, "show_image_outline": {"label": "Afficher la bordure de l'image"}, "enable_sort": {"label": "<PERSON><PERSON> le tri"}, "collapse_on_larger_devices": {"label": "Réduire sur les écrans plus grands"}}}, "main-list-collections": {"name": "Page de liste des collections", "settings": {"title": {"label": "Titre"}, "sort": {"label": "Trier les collections par :", "options__1": {"label": "Alphabétique, de A à Z"}, "options__2": {"label": "Alphabétique, de Z à A"}, "options__3": {"label": "Date, de la plus récente à la plus ancienne"}, "options__4": {"label": "Date, de la plus ancienne à la plus récente"}, "options__5": {"label": "Nombre de produits, par ordre décroissant"}, "options__6": {"label": "Nombre de produits, par ordre croissant"}}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "info": "Pour ajouter des images, modifiez vos collections. [En savoir plus](https://help.shopify.com/en/manual/products/collections)"}, "color_scheme": {"options__1": {"label": "Accentuation 1"}, "options__2": {"label": "Accentuation 2"}, "options__3": {"label": "Arrière-plan 1"}, "options__4": {"label": "Arrière-plan 2"}, "options__5": {"label": "Inverser"}, "label": "Combinaison de couleurs"}, "image_padding": {"label": "Ajouter une marge intérieure à l'image"}}}, "main-page": {"name": "Page"}, "main-password-footer": {"name": "Pied de page du mot de passe", "settings": {"color_scheme": {"options__1": {"label": "Accentuation 1"}, "options__2": {"label": "Accentuation 2"}, "options__3": {"label": "Arrière-plan 1"}, "options__4": {"label": "Arrière-plan 2"}, "options__5": {"label": "Inverser"}, "label": "Combinaison de couleurs"}}}, "main-password-header": {"name": "En-tête du mot de passe", "settings": {"logo": {"label": "Image du logo"}, "logo_max_width": {"label": "Largeur personnalisée du logo", "unit": "px"}, "color_scheme": {"options__1": {"label": "Accentuation 1"}, "options__2": {"label": "Accentuation 2"}, "options__3": {"label": "Arrière-plan 1"}, "options__4": {"label": "Arrière-plan 2"}, "options__5": {"label": "Inverser"}, "label": "Combinaison de couleurs"}}}, "main-product": {"blocks": {"text": {"name": "Texte", "settings": {"text": {"label": "Texte"}, "text_style": {"label": "Style de texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "Titre"}, "price": {"name": "Prix"}, "quantity_selector": {"name": "Sélecteur de quantité"}, "variant_picker": {"name": "Sélecteur de variante", "settings": {"picker_type": {"label": "Type", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Bouton"}}}}, "buy_buttons": {"name": "Boutons d'achat", "settings": {"show_dynamic_checkout": {"label": "Afficher les boutons de paiement dynamique", "info": "En utilisant les méthodes de paiement disponibles sur votre boutique, les clients voient leur option préférée, comme PayPal ou Apple Pay. [En savoir plus](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "pickup_availability": {"name": "Disponibilité du service de retrait"}, "description": {"name": "Description"}, "share": {"name": "Partager", "settings": {"featured_image_info": {"content": "Si vous incluez un lien dans les publications sur les réseaux sociaux, l'image vedette de la page sera affichée comme image d'aperçu. [En savoir plus](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Un titre et une description de la boutique sont inclus avec l'image d'aperçu. [En savoir plus](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "Texte"}}}, "collapsible_tab": {"name": "Onglet réductible", "settings": {"heading": {"info": "Incluez un titre qui explique le contenu.", "label": "Titre"}, "content": {"label": "Contenu de l'onglet"}, "page": {"label": "Contenu de l'onglet de la page"}, "icon": {"options__1": {"label": "Aucune"}, "options__2": {"label": "postale"}, "options__3": {"label": "Bulle de chat"}, "options__4": {"label": "Coche"}, "options__5": {"label": "Séchoir"}, "options__6": {"label": "Œil"}, "options__7": {"label": "<PERSON><PERSON><PERSON>"}, "options__8": {"label": "Fer"}, "options__9": {"label": "<PERSON><PERSON><PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON>"}, "options__11": {"label": "Cadenas"}, "options__12": {"label": "Épingle sur la carte"}, "options__13": {"label": "Pantalons"}, "options__14": {"label": "Avion"}, "options__15": {"label": "Balise de prix"}, "options__16": {"label": "Point d'interrogation"}, "options__17": {"label": "Retour"}, "options__18": {"label": "<PERSON><PERSON><PERSON>"}, "options__19": {"label": "Chemise"}, "options__20": {"label": "<PERSON><PERSON><PERSON>"}, "options__21": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__22": {"label": "<PERSON><PERSON><PERSON>"}, "options__23": {"label": "Camion"}, "options__24": {"label": "Lavage"}, "label": "Icône"}}}, "popup": {"name": "Pop-up", "settings": {"link_label": {"label": "Étiquette de lien"}, "page": {"label": "Page"}}}, "custom_liquid": {"name": "Liquid personnalisé", "settings": {"custom_liquid": {"label": "Liquid personnalisé", "info": "Ajoutez des extraits d'application ou autre code Liquid pour créer des personnalisations avancées."}}}}, "settings": {"header": {"content": "Support multimédia", "info": "En savoir plus sur les [types de supports multimédia.](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "<PERSON><PERSON> le bouclage de la vidéo"}, "enable_sticky_info": {"label": "Activer les informations produits collées sur de grands écrans"}, "hide_variants": {"label": "Masquer les supports multimédias des autres variantes après la sélection d'une variante"}}, "name": "Informations produits"}, "main-search": {"name": "Résultats de la recherche", "settings": {"image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Afficher la deuxième image en survol"}, "add_image_padding": {"label": "Ajouter de l'espace au pourtour de l'image"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le distributeur"}, "header__1": {"content": "Carte de produit"}, "header__2": {"content": "<PERSON>te de <PERSON>"}, "article_show_date": {"label": "Affiche<PERSON> la date"}, "article_show_author": {"label": "Afficher l'auteur"}, "show_image_outline": {"label": "Afficher la bordure de l'image"}}}, "multicolumn": {"name": "Multicolonne", "settings": {"title": {"label": "Titre"}, "image_width": {"label": "Largeur d'image", "options__1": {"label": "Un tiers de largeur de la colonne"}, "options__2": {"label": "Demi-<PERSON>ur de colonne"}, "options__3": {"label": "Largeur complè<PERSON> de colonne"}}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "options__4": {"label": "Cercle"}}, "column_alignment": {"label": "Alignement de colonne", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}}, "background_style": {"label": "Arrière-plan secondaire", "options__1": {"label": "Aucune"}, "options__2": {"label": "Afficher comme arrière-plan de la colonne"}, "options__3": {"label": "Afficher comme arrière-plan de la section"}}, "button_label": {"label": "Texte du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}}, "blocks": {"column": {"name": "Colonne", "settings": {"image": {"label": "Image"}, "title": {"label": "Titre"}, "text": {"label": "Description"}}}}, "presets": {"name": "Multicolonne"}}, "newsletter": {"name": "Inscription à la liste de diffusion", "settings": {"color_scheme": {"label": "Combinaison de couleurs", "options__1": {"label": "Accentuation 1"}, "options__2": {"label": "Accentuation 2"}, "options__3": {"label": "Arrière-plan 1"}, "options__4": {"label": "Arrière-plan 2"}, "options__5": {"label": "Inverser"}}, "full_width": {"label": "Rendre la section pleine largeur"}, "paragraph": {"content": "Chaque abonnement aux e-mails entraîne la création d'un compte client. [En savoir plus](https://help.shopify.com/en/manual/customers)"}}, "blocks": {"heading": {"name": "Titre", "settings": {"heading": {"label": "Titre"}}}, "paragraph": {"name": "Sous-titre", "settings": {"paragraph": {"label": "Description"}}}, "email_form": {"name": "Formulaire électronique"}}, "presets": {"name": "Inscription à la liste de diffusion"}}, "page": {"name": "Page", "settings": {"page": {"label": "Page"}}, "presets": {"name": "Page"}}, "product-recommendations": {"name": "Recommandations de produits", "settings": {"heading": {"label": "Titre"}, "header__1": {"content": "Recommandations de produits"}, "header__2": {"content": "Carte de produit"}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Afficher la deuxième image en survol"}, "add_image_padding": {"label": "Ajouter une marge intérieure à l'image"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le distributeur"}, "paragraph__1": {"content": "Les recommandations dynamiques utilisent les informations sur les commandes et les produits pour changer et s'améliorer au fil du temps. [En savoir plus](https://help.shopify.com/en/themes/development/recommended-products)"}, "show_image_outline": {"label": "Afficher la bordure de l'image"}}}, "rich-text": {"name": "Texte enrichi", "settings": {"color_scheme": {"options__1": {"label": "Accentuation 1"}, "options__2": {"label": "Accentuation 2"}, "options__3": {"label": "Arrière-plan 1"}, "options__4": {"label": "Arrière-plan 2"}, "options__5": {"label": "Inverser"}, "label": "Combinaison de couleurs"}, "full_width": {"label": "Rendre la section pleine largeur"}}, "blocks": {"heading": {"name": "Titre", "settings": {"heading": {"label": "Titre"}, "heading_size": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Taille de la police du titre", "options__3": {"label": "Grand"}}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Description"}}}, "button": {"name": "Bouton", "settings": {"button_label": {"label": "Texte du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}, "button_style_secondary": {"label": "Utiliser le style du bouton en relief"}}}}, "presets": {"name": "Texte enrichi"}}, "apps": {"name": "Applications", "settings": {"include_margins": {"label": "Rendre les marges des sections identiques à celles du thème"}}, "presets": {"name": "Applications"}}, "video": {"name": "Vidéo", "settings": {"heading": {"label": "<PERSON>-tête"}, "cover_image": {"label": "Image de couverture"}, "video_url": {"label": "URL", "placeholder": "Utiliser une URL YouTube ou Vimeo", "info": "La vidéo est lue sur la page."}, "description": {"label": "Texte alternatif de la vidéo", "info": "Décrivez la vidéo pour que les client(e)s utilisant des lecteurs d'écran puissent la regarder."}, "image_padding": {"label": "Ajouter une marge intérieure à l'image", "info": "Sélectionnez une marge intérieure pour éviter que votre image de couverture soit rognée."}, "full_width": {"label": "Rendre la section pleine largeur"}}, "presets": {"name": "Vidéo"}}, "featured-product": {"name": "Produit en vedette", "blocks": {"text": {"name": "Texte", "settings": {"text": {"label": "Texte"}, "text_style": {"label": "Style de texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "Titre"}, "price": {"name": "Prix"}, "quantity_selector": {"name": "Sélecteur de quantité"}, "variant_picker": {"name": "Sélecteur de variante", "settings": {"picker_type": {"label": "Type", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Bouton"}}}}, "buy_buttons": {"name": "Boutons d'achat", "settings": {"show_dynamic_checkout": {"label": "Afficher les boutons de paiement dynamique", "info": "En utilisant les moyens de paiement disponibles sur votre boutique, les clients voient leur option préférée, comme PayPal ou Apple Pay. [En savoir plus](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Description"}, "share": {"name": "Partager", "settings": {"featured_image_info": {"content": "Si vous incluez un lien dans des publications sur les médias sociaux, l'image vedette de la page sera affichée comme image d'aperçu. [En savoir plus](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Un titre et une description de la boutique sont inclus avec l'image d'aperçu. [En savoir plus](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Texte"}}}, "custom_liquid": {"name": "Liquid personnalisé", "settings": {"custom_liquid": {"label": "Liquid personnalisé"}}}}, "settings": {"product": {"label": "Produit"}, "secondary_background": {"label": "Afficher l'arrière-plan secondaire"}, "header": {"content": "Support multimédia", "info": "En savoir plus sur les [types de supports multimédias](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "<PERSON>r la vidéo en boucle"}}, "presets": {"name": "Produit en vedette"}}}}