
{% render 'otsb-image-comparison-base' %}
{% schema %}
{
  "name": "OT: Before After Image #4",
  "class": "otsb__root section section-image-comparison x-section",
  "tag": "section",
  "disabled_on": {
    "groups": [
      "header",
      "footer",
      "aside"
    ]
  },
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "default": "Before & After",
      "label": "Heading"
    },
    {
      "type": "range",
      "id": "heading_base_size",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Heading size",
      "default": 100
    },
    {
      "type": "select",
      "id": "heading_tag",
      "default": "h2",
      "label": "Heading tag",
      "options": [
        {
          "value": "h1",
          "label": "H1"
        },
        {
          "value": "h2",
          "label": "H2"
        },
        {
          "value": "h3",
          "label": "H3"
        },
        {
          "value": "h4",
          "label": "H4"
        },
        {
          "value": "h5",
          "label": "H5"
        },
        {
          "value": "h6",
          "label": "H6"
        },
        {
          "value": "p",
          "label": "p"
        }
      ]
    },
    {
      "type": "select",
      "id": "layout_comparison",
      "options": [
        {
          "value": "horizontal",
          "label": "Horizontal"
        },
        {
          "value": "vertical",
          "label": "Vertical"
        }
      ],
      "default": "horizontal",
      "label": "Layout"
    },
    {
      "type": "select",
      "id": "desktop_height",
      "options": [
        {
          "value": "450px",
          "label": "450 px"
        },
        {
          "value": "550px",
          "label": "550 px"
        },
        {
          "value": "650px",
          "label": "650 px"
        },
        {
          "value": "750px",
          "label": "750 px"
        },
        {
          "value": "natural",
          "label": "Adapt to first image"
        },
        {
          "value": "100vh",
          "label": "Fullscreen"
        }
      ],
      "default": "550px",
      "label": "Desktop image height"
    },
    {
      "type": "select",
      "id": "mobile_height",
      "options": [
        {
          "value": "250px",
          "label": "250 px"
        },
        {
          "value": "300px",
          "label": "300 px"
        },
        {
          "value": "400px",
          "label": "400 px"
        },
        {
          "value": "500px",
          "label": "500 px"
        },
        {
          "value": "natural",
          "label": "Adapt to first image"
        },
        {
          "value": "100vh",
          "label": "Fullscreen"
        }
      ],
      "default": "400px",
      "label": "Mobile image height"
    },
    {
      "type": "range",
      "id": "image_corner_radius",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Corner radius",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "disable_parallax_effect",
      "default": true,
      "label": "Disable parallax effect"
    },
    {
      "type": "header",
      "content": "Before image"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Desktop image"
    },
    {
      "type": "image_picker",
      "id": "image_mobile",
      "label": "Mobile image"
    },
    {
      "type": "text",
      "id": "heading_before_image",
      "label": "Heading",
      "default": "Before"
    },
    {
      "type": "range",
      "id": "heading_base_size_before_image",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Heading size",
      "default": 100
    },
    {
      "type": "select",
      "id": "heading_before_image_position",
      "options": [
        {
          "value": "top",
          "label": "Start"
        },
        {
          "value": "center",
          "label": "Middle"
        },
        {
          "value": "bottom",
          "label": "End"
        }
      ],
      "default": "top",
      "label": "Heading image position"
    },
    {
      "type": "header",
      "content": "After image"
    },
    {
      "type": "image_picker",
      "id": "image_after",
      "label": "Desktop image"
    },
    {
      "type": "image_picker",
      "id": "image_mobile_after",
      "label": "Mobile image"
    },
    {
      "type": "text",
      "id": "heading_after_image",
      "label": "Heading",
      "default": "After"
    },
    {
      "type": "range",
      "id": "heading_base_size_after_image",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Heading size",
      "default": 100
    },
    {
      "type": "select",
      "id": "heading_after_image_position",
      "options": [
        {
          "value": "top",
          "label": "Start"
        },
        {
          "value": "center",
          "label": "Middle"
        },
        {
          "value": "bottom",
          "label": "End"
        }
      ],
      "default": "top",
      "label": "Heading image position"
    },
    {
      "type": "range",
      "id": "default_scroll_position",
      "min": 10,
      "max": 90,
      "step": 1,
      "unit": "%",
      "label": "Default scroll position",
      "default": 50
    },
    {
      "type": "select",
      "id": "text_position",
      "options": [
        {
          "value": "start",
          "label": "Top"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "end",
          "label": "Bottom"
        }
      ],
      "default": "center",
      "label": "Text position",
      "info": "Only applies to desktop display."
    },
    {
      "type": "select",
      "id": "text_alignment",
      "options": [
        {
          "value": "start",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "end",
          "label": "Right"
        }
      ],
      "default": "start",
      "label": "Text alignment"
    },
    {
      "type": "header",
      "content": "Styles"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#212020"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Heading background color",
      "default": "#fff"
    },
    {
      "type": "color",
      "id": "text_background_light",
      "default": "#DC6D5C",
      "label": "Background color"
    },
    {
      "type": "color",
      "id": "color_line",
      "default": "#DC6D5C",
      "label": "Line and borders"
    },
    {
      "type": "header",
      "content": "Desktop layout"
    },
    {
      "type": "select",
      "id": "image_alignment",
      "options": [
        {
          "value": "md:flex-row",
          "label": "Left"
        },
        {
          "value": "md:flex-row-reverse",
          "label": "Right"
        }
      ],
      "default": "md:flex-row",
      "label": "Image alignment"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "default": true,
      "label": "Make section full width"
    },
    {
      "type": "checkbox",
      "id": "padding_full_width",
      "default": true,
      "label": "Enable side padding",
      "info": "Add left and right padding when section is full-width."
    },
    {
      "type": "checkbox",
      "id": "show_section_divider",
      "default": false,
      "label": "Show section divider"
    },
    {
      "type": "range",
      "id": "top_padding",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 0
    },
    {
      "type": "range",
      "id": "bottom_padding",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 0
    },
    {
      "type": "header",
      "content": "Mobile layout"
    },
    {
      "type": "select",
      "id": "image_alignment_mobile",
      "options": [
        {
          "value": "flex-col",
          "label": "Image first"
        },
        {
          "value": "flex-col-reverse",
          "label": "Image second"
        }
      ],
      "default": "flex-col",
      "label": "Image alignment"
    },
    {
      "type": "checkbox",
      "id": "full_width_mobile",
      "default": false,
      "label": "Make section full width"
    },
    {
      "type": "checkbox",
      "id": "show_section_divider_mobile",
      "default": false,
      "label": "Show section divider"
    },
    {
      "type": "range",
      "id": "top_padding_mobile",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 0
    },
    {
      "type": "range",
      "id": "bottom_padding_mobile",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "Heading",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "subheading",
          "default": "Example subheading",
          "label": "Subheading"
        },
        {
          "type": "text",
          "id": "heading",
          "default": "Before & After",
          "label": "Heading"
        },
        {
          "type": "range",
          "id": "heading_base_size",
          "min": 50,
          "max": 200,
          "step": 10,
          "unit": "%",
          "label": "Heading size",
          "default": 100
        },
        {
          "type": "select",
          "id": "heading_tag",
          "default": "h2",
          "label": "Heading tag",
          "options": [
            {
              "value": "h1",
              "label": "H1"
            },
            {
              "value": "h2",
              "label": "H2"
            },
            {
              "value": "h3",
              "label": "H3"
            },
            {
              "value": "h4",
              "label": "H4"
            },
            {
              "value": "h5",
              "label": "H5"
            },
            {
              "value": "h6",
              "label": "H6"
            },
            {
              "value": "p",
              "label": "p"
            }
          ]
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
          "label": "Text"
        },
        {
          "type": "color",
          "id": "heading_light",
          "label": "Heading color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "text_light",
          "label": "Text color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "color_text_link",
          "label": "Text link color",
          "default": "rgba(0,0,0,0)"
        }
      ]
    },
    {
      "type": "button",
      "name": "Button",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "Button label"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "Button link"
        },
        {
          "type": "checkbox",
          "id": "open_new_window",
          "default": false,
          "label": "Open this link in a new window"
        },
        {
          "type": "checkbox",
          "id": "button_primary",
          "default": true,
          "label": "Show as primary button"
        },
        {
          "type": "header",
          "content": "Button design"
        },
        {
          "type": "select",
          "id": "button_type",
          "options": [
            {
              "value": "square",
              "label": "Square"
            },
            {
              "value": "rounded",
              "label": "Rounded"
            },
            {
              "value": "rounded_corners",
              "label": "Rounded Corners"
            },
            {
              "value": "mixed",
              "label": "Mixed"
            }
          ],
          "label": "Button style",
          "default": "square"
        },
        {
          "type": "select",
          "id": "button_color_mobile",
          "options": [
            {
              "value": "hover",
              "label": "Use button hover color"
            },
            {
              "value": "color",
              "label": "Use button color"
            }
          ],
          "label": "Mobile primary button style",
          "default": "color"
        },
        {
          "type": "select",
          "id": "button_animation",
          "options": [
            {
              "value": "slide",
              "label": "Slide"
            },
            {
              "value": "fill_up",
              "label": "Fill up"
            },
            {
              "value": "sliced",
              "label": "Sliced with icon"
            },
            {
              "value": "underline",
              "label": "Underline"
            }
          ],
          "label": "Primary button hover animation",
          "default": "fill_up"
        },
        {
          "type": "html",
          "id": "custom_icon_button",
          "label": "Custom icon (SVG code)",
          "info": "Applies to 'Sliced with icon' animation type. For assistance with custom SVG code or fixing issues that arise from custom SVG code, please [contact our support](https://support.bsscommerce.com/support/tickets/new)."
        },
        {
          "type": "color",
          "id": "button_light",
          "label": "Primary button",
          "default": "#FD8E16"
        },
        {
          "type": "color",
          "id": "button_text_light",
          "label": "Primary button text",
          "default": "#FFFFFF"
        },
        {
          "type": "color",
          "id": "button_hover_light",
          "label": "Primary button hover",
          "default": "#FF6600"
        },
        {
          "type": "color",
          "id": "button_text_hover_light",
          "label": "Primary button hover text",
          "default": "#000000"
        },
        {
          "type": "color",
          "id": "secondary_button_light",
          "label": "Secondary button color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "secondary_button_text_light",
          "label": "Secondary button text color",
          "default": "#000000"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "OT: Before After Image #4",
      "blocks": [
        {
          "type": "heading",
          "settings": {
            "subheading": "Example subheading",
            "heading": "Stunning Transformations: Before & After",
            "text": "<p>Improving images through digital adjustments to color, tone, and detail.</p>",
            "heading_light": "#fff",
            "text_light": "#fff"
          }
        }
      ]
    }
  ]
}
{% endschema %}
