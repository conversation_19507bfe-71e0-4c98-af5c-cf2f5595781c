<a
  {% if collection.all_products_count > 0 %}
    href="{{ collection.url }}"
  {% endif %}
  class="{% if image != blank %} card--media{% else %}{% if media_aspect_ratio != 'adapt' %} card--stretch{% endif %}{% endif %} collection__card"
>
  {%- if image != blank -%}
    <div class="collection-card--media__wrapper {{ image_width }}">
      <div
        class="media media--{{ media_aspect_ratio }} media--hover-effect overflow-hidden"
        {% if media_aspect_ratio == 'adapt' %}
          style="padding-bottom: {{ 1 | divided_by: image.aspect_ratio | times: 100 }}%;"
        {% endif %}
      >
        <img
          srcset="
            {%- if image.width >= 165 -%}{{ image | img_url: '165x' }} 165w,{%- endif -%}
            {%- if image.width >= 360 -%}{{ image | img_url: '360x' }} 360w,{%- endif -%}
            {%- if image.width >= 535 -%}{{ image | img_url: '535x' }} 535w,{%- endif -%}
            {%- if image.width >= 750 -%}{{ image | img_url: '750x' }} 750w,{%- endif -%}
            {%- if image.width >= 1000 -%}{{ image | img_url: '1000x' }} 1000w,{%- endif -%}
            {%- if image.width >= 1500 -%}{{ image | img_url: '1500x' }} 1500w,{%- endif -%}
          "
          src="{{ image | img_url: '1500x' }}"
          sizes="(min-width: {{ settings.container_lg_width }}px) {{ settings.container_lg_width | minus: 100 | divided_by: 3 }}px, (min-width: 750px) calc((100vw - 130px) / 3), calc(100vw - 30px)"
          alt="{{ collection.title | escape }}"
          height="{{ image.height }}"
          width="{{ image.width }}"
          loading="lazy"
        >
      </div>
    </div>
  {%- else -%}
    <div class="placeholder_svg_parent" style="padding-bottom: 100%">
      {% capture current %}{% cycle 1, 2, 3, 4, 5, 6 %}{% endcapture %}
      {{ 'collection-' | append: current | placeholder_svg_tag: 'placeholder-svg-2' }}
    </div>
  {%- endif -%}
  <div class="product__categories--content">
    <div class="product__categories--content__left">
      {%- if collection != blank -%}
        <h3 class="product__categories--content__maintitle h5 mb-0">{{ collection.title }}</h3>
      {%- else -%}
        <h3 class="product__categories--content__maintitle h5 mb-0">
          {{ 'sections.collection_list.default_title' | t }}
        </h3>
      {%- endif -%}
      {%- if show_product_items -%}
        <span class="product__categories--content__subtitle">
          {{- collection.products_count }}
          {{ 'sections.collection_list.items' | t -}}
        </span>
      {%- endif -%}
    </div>

    {%- if show_icon -%}
      <div class="product__categories--icon">
        <span class="product__categories--icon__link">
          <svg xmlns="http://www.w3.org/2000/svg" width="15.995" height="10.979" viewBox="0 0 15.995 10.979">
            <path  d="M212.706,299.839a.425.425,0,0,0,0-.6l-3.458-3.458a.425.425,0,0,1,0-.6l1.008-1.008a.425.425,0,0,1,.6,0l5.065,5.065a.425.425,0,0,1,0,.6l-5.065,5.066a.425.425,0,0,1-.6,0l-1.008-1.008a.425.425,0,0,1,0-.6Zm-6.305-.3a2.215,2.215,0,1,0,2.216-2.216A2.215,2.215,0,0,0,206.4,299.541Zm-3.634,0a1.6,1.6,0,1,0,1.6-1.605A1.6,1.6,0,0,0,202.767,299.541Zm-2.717,0a1.154,1.154,0,1,0,1.154-1.154A1.155,1.155,0,0,0,200.05,299.541Z" transform="translate(-200.05 -294.05)" fill="currentColor"/>
          </svg>
        </span>
      </div>
    {%- endif -%}
  </div>
</a>
