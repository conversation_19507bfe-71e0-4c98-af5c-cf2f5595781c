{"current": {"favicon": "shopify://shop_images/favicon_b7566afb-62e9-4198-bbbe-0f101066fd8c.png", "currency_code_enabled": false, "enable_rtl": false, "langauges_rtl": "", "container_lg_width": 1530, "container_fluid_offset": 190, "placeholder_background": "#fff4f8", "placeholder_foreground": "#ee2761", "type_body_font": "quicksand_n4", "use_custom_body_font": false, "custom_body_font": "https://cdn.shopify.com/s/files/1/0561/2742/2636/files/Jost-Regular.ttf?v=1618297125@400&https://cdn.shopify.com/s/files/1/0561/2742/2636/files/Jost-Medium.ttf?v=1618297125@500&https://cdn.shopify.com/s/files/1/0561/2742/2636/files/Jost-SemiBold.ttf?v=1618297125@600", "body_font_size": 100, "type_header_font": "cormorant_n4", "use_custom_heading_font": false, "custom_heading_font": "@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300..700;1,300..700&display=swap');", "heading_font_weight": "700", "heading_font_size": 100, "button_border_radius": 31, "product_content_alignment": "center", "sale_percentage_show": true, "sale_badge_color_scheme": "scheme-6faef5ad-45c9-491a-a1c5-63d982ced7a6", "new_badge_color_scheme": "scheme-930e7e6c-61d9-4bba-9a30-8c655b499bb7", "sold_out_badge_color_scheme": "background-2", "recent_viewed_proudct_card": "style_2", "show_compare_view_button": false, "recent_viewd_product_rating": true, "product_card_radius": true, "product_card_spacing": true, "product_card_color_scheme": "scheme-b80a3a8f-5d6e-49d3-9647-c794f19aadda", "color_swatches": true, "max_swatches_show": 3, "quick_shop_type": "popup", "varian_picker": "button", "color_option_style": "image", "wishlist_show_wishlist_button": false, "social_twitter_link": "", "social_facebook_link": "https://www.facebook.com/people/Giftparty-Studio/61564869546819/?mibextid=ZbWKwL", "social_pinterest_link": "", "social_instagram_link": "https://www.instagram.com/giftpartystudio.in/?igsh=dXJ1emNsczB1YnFl#", "social_tiktok_link": "", "social_snapchat_link": "", "cart_type": "drawer", "shipping_calc_enable": true, "continue_shopping_link": "shopify://collections/all", "free_shipping_amount": 199, "shipping_rate_low": "#f44b88", "shipping_rate_medium": "#f88e0f", "shipping_rate_high": "#00bbae", "predictive_search_enabled": true, "search_type": "tag", "search_filter_tags": "", "show_collection": true, "show_article": true, "show_page": true, "most_searced_products_collection": "new-arrivals", "most_searced_products_limit": 6, "popular_search_queries": "", "account_page_breadcrumb": true, "customer_color_scheme": "background-2", "colors_accent_1": "#ee2761", "gradient_accent_1": "", "colors_solid_button_labels": "#ffffff", "colors_text": "#131313", "colors_text_link_hover": "#ee2761", "colors_accent_2": "#131313", "colors_outline_button_labels": "#131313", "colors_background_1": "#ffffff", "gradient_background_1": "", "colors_background_2": "#f7f8fc", "image_ratio": "adapt", "show_secondary_image": true, "show_product_rating": true, "show_variant_options": false, "pcard_option_display": "1_option", "pcard_option_design": "color", "page_width": "1200", "show_rating": true, "sections": {"main-password": {"type": "main-password", "settings": {"color_scheme": "background-1"}}, "password-footer": {"type": "password-footer", "settings": {"color_scheme": "scheme-b2546f5a-df99-42a8-bed6-b937817ffaca"}}, "color-swatches": {"type": "color-swatches", "settings": {}}}, "content_for_index": [], "blocks": {"8170924994765517213": {"type": "shopify://apps/tepo-product-options/blocks/app-embed/906ef57c-14e4-45d8-8b53-3ba7349d56a6", "disabled": false, "settings": {}}, "17985673643287205206": {"type": "shopify://apps/c-edd-estimated-delivery-date/blocks/app_setting/4a0685bc-c234-45b2-8382-5ad6a8e3e3fd", "disabled": false, "settings": {}}, "12016176150940211849": {"type": "shopify://apps/ot-theme-sections/blocks/otsb-script/45c0d634-d78b-458c-8bca-17086e7d65aa", "disabled": false, "settings": {}}, "11119278427475967885": {"type": "shopify://apps/ot-theme-sections/blocks/otsb-style/45c0d634-d78b-458c-8bca-17086e7d65aa", "disabled": false, "settings": {}}, "2975745137944656958": {"type": "shopify://apps/bl-custom-html-css-js-liquid/blocks/site-wide-code/96fc73ef-2727-4174-82ec-12cfce5b3426", "disabled": false, "settings": {"html_code": "", "css_code": "", "js_code": "/**\n * Enhanced trackpad (two-finger) + mousewheel swipe helper for Shopify product sliders.\n * Now includes individual product card panning support.\n * \n * What it does:\n *  - Main slider functionality (existing)\n *  - Individual product card carousels (.product-card-carousel within slides)\n *  - Smart scroll delegation: determines whether to scroll main slider or individual card\n *  - Prevents double-initialization and listens for newly injected sliders\n *\n * Expected HTML structure:\n * <div class=\"productSlider swiper\">\n *   <div class=\"swiper-wrapper\">\n *     <div class=\"swiper-slide\">\n *       <div class=\"product-card-carousel swiper\">\n *         <div class=\"swiper-wrapper\">\n *           <div class=\"swiper-slide\"><!-- product image/content --></div>\n *           <div class=\"swiper-slide\"><!-- product image/content --></div>\n *         </div>\n *       </div>\n *     </div>\n *   </div>\n * </div>\n */\n(function () {\n  'use strict';\n\n  // CDN fallback if theme doesn't include Swiper\n  var SWIPER_CDN = 'https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js';\n  var CDN_LOAD_TIMEOUT = 8000; // ms\n\n  // Small helper to inject CSS to make touch behavior nicer for sliders\n  function injectSliderCSS() {\n    if (document.getElementById('trackpad-swiper-helper-css')) return;\n    var css = '\\\n.productSlider.swiper, \\\n.productSlider.swiper .swiper-wrapper, \\\n.product-card-carousel.swiper, \\\n.product-card-carousel.swiper .swiper-wrapper { \\\n  touch-action: pan-y; /* allow vertical page scroll, horizontal gestures handled by swiper */ \\\n} \\\n.productSlider.swiper .swiper-slide, \\\n.product-card-carousel.swiper .swiper-slide { \\\n  -webkit-user-drag: none; \\\n  user-select: none; \\\n} \\\n.product-card-carousel { \\\n  position: relative; \\\n  overflow: hidden; \\\n} \\\n.product-card-carousel .swiper-slide { \\\n  width: auto; \\\n  flex-shrink: 0; \\\n}';\n    var s = document.createElement('style');\n    s.id = 'trackpad-swiper-helper-css';\n    s.appendChild(document.createTextNode(css));\n    document.head.appendChild(s);\n  }\n\n  // Load external script helper (returns Promise)\n  function loadScript(src, timeout) {\n    timeout = timeout || CDN_LOAD_TIMEOUT;\n    return new Promise(function (resolve, reject) {\n      if (window.Swiper) return resolve(window.Swiper);\n      var existing = document.querySelector('script[src=\"' + src + '\"]');\n      if (existing) {\n        var to = setInterval(function () {\n          if (window.Swiper) {\n            clearInterval(to);\n            resolve(window.Swiper);\n          }\n        }, 50);\n        setTimeout(function () {\n          clearInterval(to);\n          reject(new Error('Timed out waiting for Swiper to load'));\n        }, timeout);\n        return;\n      }\n      var s = document.createElement('script');\n      s.src = src;\n      s.async = true;\n      s.onload = function () {\n        if (window.Swiper) return resolve(window.Swiper);\n        setTimeout(function () {\n          if (window.Swiper) resolve(window.Swiper);\n          else reject(new Error('Swiper loaded but not available on window'));\n        }, 50);\n      };\n      s.onerror = function (e) {\n        reject(e || new Error('Failed to load script: ' + src));\n      };\n      document.body.appendChild(s);\n    });\n  }\n\n  // Ensure Swiper exists; if not, attempt to load from CDN\n  function ensureSwiper() {\n    return new Promise(function (resolve, reject) {\n      if (window.Swiper) return resolve(window.Swiper);\n      loadScript(SWIPER_CDN).then(resolve).catch(function (err) {\n        console.warn('Trackpad helper: could not load Swiper from CDN', err);\n        reject(err);\n      });\n    });\n  }\n\n  // Find the closest swiper instance by traversing up the DOM\n  function findClosestSwiper(element) {\n    var current = element;\n    while (current && current !== document) {\n      if (current.swiper || current.__swiper__ || current.__swiperInstance) {\n        return current.swiper || current.__swiper__ || current.__swiperInstance;\n      }\n      if (current.classList && current.classList.contains('swiper')) {\n        return current.swiper || current.__swiper__ || current.__swiperInstance;\n      }\n      current = current.parentElement;\n    }\n    return null;\n  }\n\n  // Smart wheel handler that decides which swiper to control\n  function attachSmartWheelHandler(mainSliderEl, mainSwiperInstance) {\n    try {\n      if (mainSliderEl.__smart_wheel_handler) return;\n\n      var accumulated = 0;\n      var lastEventTime = 0;\n      var THRESHOLD = 60;\n      var RESET_MS = 300;\n      var lastTargetSwiper = null;\n\n      var handler = function (ev) {\n        var dx = ev.deltaX || 0;\n        var dy = ev.deltaY || 0;\n\n        // If vertical intent is stronger, let page scroll\n        if (Math.abs(dy) > Math.abs(dx) && Math.abs(dy) > 5) return;\n\n        // Find the target element and determine which swiper should handle this\n        var targetEl = ev.target;\n        var targetSwiper = null;\n        \n        // First, check if we're over a product card carousel\n        var cardCarousel = targetEl.closest('.product-card-carousel.swiper');\n        if (cardCarousel && (cardCarousel.swiper || cardCarousel.__swiper__ || cardCarousel.__swiperInstance)) {\n          targetSwiper = cardCarousel.swiper || cardCarousel.__swiper__ || cardCarousel.__swiperInstance;\n          \n          // Check if this card carousel can actually scroll in the intended direction\n          if (targetSwiper) {\n            var canScrollLeft = !targetSwiper.isBeginning;\n            var canScrollRight = !targetSwiper.isEnd;\n            var scrollingLeft = dx < 0;\n            var scrollingRight = dx > 0;\n            \n            // If card can't scroll in intended direction, fall back to main slider\n            if ((scrollingLeft && !canScrollLeft) || (scrollingRight && !canScrollRight)) {\n              targetSwiper = mainSwiperInstance;\n            }\n          }\n        } else {\n          // Default to main slider\n          targetSwiper = mainSwiperInstance;\n        }\n\n        if (!targetSwiper) return;\n\n        ev.preventDefault();\n\n        // Reset accumulator if target changed or too much time passed\n        var now = Date.now();\n        if (now - lastEventTime > RESET_MS || lastTargetSwiper !== targetSwiper) {\n          accumulated = 0;\n        }\n        lastEventTime = now;\n        lastTargetSwiper = targetSwiper;\n        \n        accumulated += dx;\n\n        if (Math.abs(accumulated) >= THRESHOLD) {\n          if (accumulated > 0) {\n            if (targetSwiper && typeof targetSwiper.slideNext === 'function') {\n              targetSwiper.slideNext();\n            }\n          } else {\n            if (targetSwiper && typeof targetSwiper.slidePrev === 'function') {\n              targetSwiper.slidePrev();\n            }\n          }\n          accumulated = 0;\n        }\n      };\n\n      mainSliderEl.addEventListener('wheel', handler, { passive: false });\n      mainSliderEl.__smart_wheel_handler = handler;\n    } catch (e) {\n      console.warn('Trackpad helper: failed to attach smart wheel handler', e);\n    }\n  }\n\n  // Initialize product card carousels within a main slider\n  function initProductCardCarousels(mainSliderEl) {\n    try {\n      var cardCarousels = mainSliderEl.querySelectorAll('.product-card-carousel.swiper');\n      \n      cardCarousels.forEach(function (cardEl) {\n        if (cardEl.__card_carousel_inited) return;\n        cardEl.__card_carousel_inited = true;\n\n        var SwiperLib = window.Swiper;\n        if (!SwiperLib) return;\n\n        // Check if already has swiper instance\n        var existing = cardEl.swiper || cardEl.__swiper__ || cardEl.__swiperInstance;\n        if (existing) return;\n\n        // Find pagination/nav elements for this card\n        function findCardEl(selector) {\n          var el = cardEl.querySelector(selector);\n          if (!el && cardEl.parentElement) {\n            el = cardEl.parentElement.querySelector(selector);\n          }\n          return el || null;\n        }\n\n        var cardPaginationEl = findCardEl('.swiper-pagination');\n        var cardNextEl = findCardEl('.swiper-button-next');\n        var cardPrevEl = findCardEl('.swiper-button-prev');\n\n        var cardConfig = {\n          slidesPerView: 1,\n          spaceBetween: 0,\n          loop: cardEl.dataset && cardEl.dataset.loop === 'true',\n          simulateTouch: true,\n          grabCursor: true,\n          effect: 'slide',\n          speed: 300,\n          // Enable mousewheel for individual cards too\n          mousewheel: {\n            enabled: true,\n            forceToAxis: true,\n            sensitivity: 0.1,\n            releaseOnEdges: true\n          },\n          pagination: cardPaginationEl ? { \n            el: cardPaginationEl, \n            clickable: true,\n            dynamicBullets: true\n          } : undefined,\n          navigation: (cardNextEl && cardPrevEl) ? { \n            nextEl: cardNextEl, \n            prevEl: cardPrevEl \n          } : undefined,\n          watchSlidesProgress: true,\n          // Prevent interference with main slider\n          nested: true,\n          resistanceRatio: 0\n        };\n\n        try {\n          var cardInstance = new SwiperLib(cardEl, cardConfig);\n          console.info('Trackpad helper: initialized product card carousel for', cardEl);\n        } catch (err) {\n          console.error('Trackpad helper: Card carousel init failed', err);\n          cardEl.__card_carousel_inited = false;\n        }\n      });\n    } catch (e) {\n      console.error('Trackpad helper: error in initProductCardCarousels', e);\n    }\n  }\n\n  // Initialize or patch a single main slider element\n  function initOrPatchSlider(sliderEl) {\n    try {\n      if (sliderEl.__trackpad_inited) return;\n      sliderEl.__trackpad_inited = true;\n\n      var SwiperLib = window.Swiper;\n      if (!SwiperLib) {\n        sliderEl.__trackpad_inited = false;\n        return;\n      }\n\n      var existing = sliderEl.swiper || sliderEl.__swiper__ || sliderEl.__swiperInstance || null;\n\n      function findEl(selector) {\n        var el = sliderEl.querySelector(selector);\n        if (!el && sliderEl.parentElement) el = sliderEl.parentElement.querySelector(selector);\n        return el || null;\n      }\n\n      var paginationEl = findEl('.swiper-pagination');\n      var nextEl = findEl('.swiper-button-next');\n      var prevEl = findEl('.swiper-button-prev');\n\n      if (existing) {\n        try {\n          existing.params = existing.params || {};\n          existing.params.mousewheel = existing.params.mousewheel || {};\n          Object.assign(existing.params.mousewheel, {\n            enabled: true,\n            forceToAxis: true,\n            sensitivity: 1,\n            releaseOnEdges: true\n          });\n          if (existing.mousewheel && typeof existing.mousewheel.enable === 'function') {\n            existing.mousewheel.enable();\n          }\n          existing.params.simulateTouch = true;\n          existing.params.grabCursor = true;\n          existing.update && existing.update();\n          \n          // Initialize card carousels and smart wheel handler\n          initProductCardCarousels(sliderEl);\n          attachSmartWheelHandler(sliderEl, existing);\n          \n          console.info('Trackpad helper: patched existing main Swiper for', sliderEl);\n        } catch (e) {\n          console.warn('Trackpad helper: could not patch existing Swiper instance', e);\n        }\n        return;\n      }\n\n      // Create new main slider instance\n      var cfg = {\n        slidesPerView: 'auto',\n        spaceBetween: 20,\n        loop: sliderEl.dataset && sliderEl.dataset.loop === 'true',\n        simulateTouch: true,\n        grabCursor: true,\n        mousewheel: {\n          enabled: true,\n          forceToAxis: true,\n          sensitivity: 1,\n          releaseOnEdges: true\n        },\n        pagination: paginationEl ? { el: paginationEl, clickable: true } : undefined,\n        navigation: (nextEl && prevEl) ? { nextEl: nextEl, prevEl: prevEl } : undefined,\n        watchSlidesProgress: true,\n        // Allow nested swipers\n        nested: false,\n        resistanceRatio: 0.85\n      };\n\n      var instance = null;\n      try {\n        instance = new SwiperLib(sliderEl, cfg);\n        \n        // Initialize card carousels after main slider is ready\n        initProductCardCarousels(sliderEl);\n        attachSmartWheelHandler(sliderEl, instance);\n        \n        console.info('Trackpad helper: initialized main Swiper for', sliderEl);\n      } catch (err) {\n        console.error('Trackpad helper: Main Swiper init failed', err);\n        sliderEl.__trackpad_inited = false;\n        return;\n      }\n    } catch (e) {\n      console.error('Trackpad helper: unexpected error in initOrPatchSlider', e);\n    }\n  }\n\n  // Find and init all sliders currently in the DOM\n  function initAllSliders() {\n    try {\n      injectSliderCSS();\n      var nodes = document.querySelectorAll('.productSlider.swiper');\n      if (!nodes || nodes.length === 0) {\n        return;\n      }\n      nodes.forEach(function (el) {\n        initOrPatchSlider(el);\n      });\n    } catch (e) {\n      console.error('Trackpad helper: initAllSliders error', e);\n    }\n  }\n\n  // Use MutationObserver to catch sliders added later\n  function watchForNewSliders() {\n    try {\n      if (window.__trackpad_helper_observer) return;\n      var observer = new MutationObserver(function (mutations) {\n        var foundMain = false;\n        var foundCard = false;\n        \n        mutations.forEach(function (m) {\n          if (m.addedNodes && m.addedNodes.length) {\n            m.addedNodes.forEach(function (n) {\n              try {\n                if (n.nodeType !== 1) return;\n                \n                // Check for main sliders\n                if (n.matches && n.matches('.productSlider.swiper')) {\n                  initOrPatchSlider(n);\n                  foundMain = true;\n                } else {\n                  var insideMain = n.querySelector && n.querySelector('.productSlider.swiper');\n                  if (insideMain) {\n                    initOrPatchSlider(insideMain);\n                    foundMain = true;\n                  }\n                }\n                \n                // Check for card carousels that might be added dynamically\n                if (n.matches && n.matches('.product-card-carousel.swiper')) {\n                  var parentSlider = n.closest('.productSlider.swiper');\n                  if (parentSlider) {\n                    initProductCardCarousels(parentSlider);\n                    foundCard = true;\n                  }\n                } else {\n                  var insideCard = n.querySelector && n.querySelector('.product-card-carousel.swiper');\n                  if (insideCard) {\n                    var parentSlider = insideCard.closest('.productSlider.swiper');\n                    if (parentSlider) {\n                      initProductCardCarousels(parentSlider);\n                      foundCard = true;\n                    }\n                  }\n                }\n              } catch (e) { /* ignore individual node errors */ }\n            });\n          }\n        });\n      });\n      observer.observe(document.documentElement || document.body, { childList: true, subtree: true });\n      window.__trackpad_helper_observer = observer;\n    } catch (e) {\n      console.warn('Trackpad helper: MutationObserver unavailable', e);\n    }\n  }\n\n  // Main runner\n  function runHelper() {\n    if (window.Swiper) {\n      initAllSliders();\n    } else {\n      ensureSwiper().then(function () {\n        initAllSliders();\n      }).catch(function () {\n        initAllSliders();\n      });\n    }\n    watchForNewSliders();\n    window.addEventListener('load', function () {\n      setTimeout(initAllSliders, 300);\n    });\n  }\n\n  // Start on DOMContentLoaded\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', runHelper);\n  } else {\n    runHelper();\n  }\n})();", "liquid_code": ""}}}, "color_schemes": {"background-1": {"settings": {"background": "#f1f3f7", "background_gradient": "", "text": "#111111", "button": "#121212", "button_label": "#ffffff", "primary_button_hover_bg": "#f44b88", "primary_button_hover_text": "#ffffff", "secondary_button_label": "#121212", "secondary_button_hover_bg": "#121212", "secondary_button_hover_text": "#ffffff", "card_success_color": "#337239", "card_warning_color": "#922c2c", "tex_link_hover_color": "#161880"}}, "background-2": {"settings": {"background": "#ffffff", "background_gradient": "", "text": "#111111", "button": "#a35741", "button_label": "#f3f3f3", "primary_button_hover_bg": "#121212", "primary_button_hover_text": "#ffffff", "secondary_button_label": "#121212", "secondary_button_hover_bg": "#121212", "secondary_button_hover_text": "#ffffff", "card_success_color": "#008000", "card_warning_color": "#922c2c", "tex_link_hover_color": "#161880"}}, "inverse": {"settings": {"background": "#242833", "background_gradient": "", "text": "#ffffff", "button": "#ffffff", "button_label": "#111111", "primary_button_hover_bg": "#121212", "primary_button_hover_text": "#ffffff", "secondary_button_label": "#ffffff", "secondary_button_hover_bg": "#121212", "secondary_button_hover_text": "#ffffff", "card_success_color": "#337239", "card_warning_color": "#922c2c", "tex_link_hover_color": "#121212"}}, "accent-1": {"settings": {"background": "#2567b3", "background_gradient": "", "text": "#ffffff", "button": "#ffffff", "button_label": "#121212", "primary_button_hover_bg": "#121212", "primary_button_hover_text": "#ffffff", "secondary_button_label": "#ffffff", "secondary_button_hover_bg": "#121212", "secondary_button_hover_text": "#ffffff", "card_success_color": "#337239", "card_warning_color": "#922c2c", "tex_link_hover_color": "#be1515"}}, "accent-2": {"settings": {"background": "#131313", "background_gradient": "", "text": "#ffffff", "button": "#ffffff", "button_label": "#121212", "primary_button_hover_bg": "#121212", "primary_button_hover_text": "#ffffff", "secondary_button_label": "#ffffff", "secondary_button_hover_bg": "#121212", "secondary_button_hover_text": "#ffffff", "card_success_color": "#337239", "card_warning_color": "#922c2c", "tex_link_hover_color": "#121212"}}, "scheme-930e7e6c-61d9-4bba-9a30-8c655b499bb7": {"settings": {"background": "#ddf0df", "background_gradient": "", "text": "#337239", "button": "#f44b88", "button_label": "#ffffff", "primary_button_hover_bg": "#121212", "primary_button_hover_text": "#ffffff", "secondary_button_label": "#121212", "secondary_button_hover_bg": "#121212", "secondary_button_hover_text": "#ffffff", "card_success_color": "#337239", "card_warning_color": "#922c2c", "tex_link_hover_color": "#0c52ba"}}, "scheme-7409ae75-7200-41c9-953d-b1d134c8d4a3": {"settings": {"background": "#121212", "background_gradient": "", "text": "#ffffff", "button": "#ffffff", "button_label": "#121212", "primary_button_hover_bg": "#f44b88", "primary_button_hover_text": "#ffffff", "secondary_button_label": "#121212", "secondary_button_hover_bg": "#121212", "secondary_button_hover_text": "#ffffff", "card_success_color": "#337239", "card_warning_color": "#922c2c", "tex_link_hover_color": "#0c52ba"}}, "scheme-b2546f5a-df99-42a8-bed6-b937817ffaca": {"settings": {"background": "#f7f5f2", "background_gradient": "", "text": "#111111", "button": "#a35741", "button_label": "#ffffff", "primary_button_hover_bg": "#111111", "primary_button_hover_text": "#ffffff", "secondary_button_label": "#121212", "secondary_button_hover_bg": "#121212", "secondary_button_hover_text": "#ffffff", "card_success_color": "#337239", "card_warning_color": "#922c2c", "tex_link_hover_color": "#161880"}}, "scheme-b80a3a8f-5d6e-49d3-9647-c794f19aadda": {"settings": {"background": "#ffffff", "background_gradient": "", "text": "#111111", "button": "#121212", "button_label": "#ffffff", "primary_button_hover_bg": "#f44b88", "primary_button_hover_text": "#ffffff", "secondary_button_label": "#121212", "secondary_button_hover_bg": "#121212", "secondary_button_hover_text": "#ffffff", "card_success_color": "#337239", "card_warning_color": "#922c2c", "tex_link_hover_color": "#161880"}}, "scheme-6faef5ad-45c9-491a-a1c5-63d982ced7a6": {"settings": {"background": "#ffffff", "background_gradient": "", "text": "#111111", "button": "#121212", "button_label": "#ffffff", "primary_button_hover_bg": "#a35741", "primary_button_hover_text": "#ffffff", "secondary_button_label": "#121212", "secondary_button_hover_bg": "#121212", "secondary_button_hover_text": "#ffffff", "card_success_color": "#2e7d32", "card_warning_color": "#c62828", "tex_link_hover_color": "#d4af37"}}, "scheme-293eb39b-6a6a-44ef-9af3-95c5b00b8394": {"settings": {"background": "#a35741", "background_gradient": "", "text": "#f3f3f3", "button": "#121212", "button_label": "#ffffff", "primary_button_hover_bg": "#1c2a44", "primary_button_hover_text": "#ffffff", "secondary_button_label": "#121212", "secondary_button_hover_bg": "#121212", "secondary_button_hover_text": "#ffffff", "card_success_color": "#337239", "card_warning_color": "#922c2c", "tex_link_hover_color": "#161880"}}, "scheme-e7fd24c7-3de4-4c66-bfd9-110b45b5d6ce": {"settings": {"background": "#a35741", "background_gradient": "", "text": "#ffffff", "button": "#121212", "button_label": "#ffffff", "primary_button_hover_bg": "#f44b88", "primary_button_hover_text": "#ffffff", "secondary_button_label": "#121212", "secondary_button_hover_bg": "#121212", "secondary_button_hover_text": "#ffffff", "card_success_color": "#337239", "card_warning_color": "#922c2c", "tex_link_hover_color": "#161880"}}}}, "presets": {"Default": {"colors_solid_button_labels": "#FFFFFF", "colors_accent_1": "#121212", "colors_accent_2": "#334FB4", "colors_text": "#121212", "colors_outline_button_labels": "#121212", "colors_background_1": "#FFFFFF", "colors_background_2": "#F3F3F3", "type_header_font": "assistant_n4", "type_body_font": "assistant_n4", "sections": {"announcement-bar": {"type": "announcement-bar", "blocks": {"announcement-bar-0": {"type": "announcement", "settings": {"color_scheme": "background-1"}}}, "block_order": ["announcement-bar-0"], "settings": {}}}}}, "platform_customizations": {"custom_css": [".custom-product-grid li[data-val=\"1\"],.custom-product-grid li[data-val=\"2\"] {display: none !important;}", ".otsb__root .otsb-rte p,.otsb__root .otsb-rte h2 {max-width: 900px; margin: 0 auto; text-align: center; padding: 0 15px;}", ".view-all-top {margin-right: 17px !important; margin-left: auto; text-decoration: underline;}", " /* Target image inside Description tab */.dt-sc-tabs-content[data-id=\"tab-description\"] img {max-width: 520px; /* set your desired size */ height: auto; /* keep aspect ratio */ margin: 0 auto 20px; /* center + spacing below */ display: block; /* ensure centering works */}", " /* Optional responsive */@media screen and (max-width: 768px) {.dt-sc-tabs-content[data-id=\"tab-description\"] img {max-width: 120px; }}", " /* Resize main product image */.main-product_info .product_media-wrapper img {max-width: 450px; /* set custom width */ height: auto; /* keep ratio */ margin: 0 auto; /* center */ display: block;}", " /* Responsive for mobile */@media screen and (max-width: 768px) {.main-product_info .product_media-wrapper img {max-width: 90%; /* scale down */ }}"]}}