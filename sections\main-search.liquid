{% liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
%}

{{ 'template-collection.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-rating.css' | asset_url | stylesheet_tag }}
{{ 'product-tooltip.css' | asset_url | stylesheet_tag }}
{{ 'product-card.css' | asset_url | stylesheet_tag }}

<link rel="stylesheet" href="{{ 'component-search.css' | asset_url }}" media="print" onload="this.media='all'">

<noscript>{{ 'component-search.css' | asset_url | stylesheet_tag }}</noscript>

{%- if section.settings.enable_filtering or section.settings.enable_sorting -%}
  {{ 'component-facets.css' | asset_url | stylesheet_tag }}
  <script src="{{ 'facets.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

<script src="{{ 'menu-drawer.js' | asset_url }}" defer="defer"></script>

<script src="{{ 'offcanvas-filter-active.js' | asset_url }}" defer="defer"></script>

<script src="{{ 'main-search.js' | asset_url }}" defer="defer"></script>

<style>
     .section-{{ section.id }}-padding {
      margin-top: {{ section.settings.mobile_padding_top }}px;
      margin-bottom: {{ section.settings.mobile_padding_bottom }}px;
    }
    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-padding {
        margin-top: {{ section.settings.padding_top }}px;
        margin-bottom: {{ section.settings.padding_bottom }}px;
      }
    }
    .price__widget{
      display: flex;
      align-items: center;
    }
    .price__widget > div {
      flex-grow: 1;
    }
    .price__divider {
      padding: 0 10px;
      margin-top: 27px;
      text-align: center;
    }

    .input__field.price__filter_input {
      padding-left: 20px;
    }
    .widget__action_display {
      border-bottom: 1px solid rgba(var(--color-foreground),.2);
        padding-bottom: 10px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        }
    .single__widget {
      width: 100%;
    }

    .single__widget details[open]>summary .icon-caret {
      transform: rotate(180deg);
    }
    .single__widget summary .icon-caret {
      right: 0;
      transition: transform .3s ease;
    }
    input.price__filter_input::-webkit-outer-spin-button,
    input.price__filter_input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    /* Firefox */
    input.price__filter_input[type=number] {
      -moz-appearance: textfield;
    }
    input.price__filter_input::-webkit-input-placeholder { /* Edge */
      color: #ccc;
    }

    input.price__filter_input:-ms-input-placeholder { /* Internet Explorer 10-11 */
      color: #ccc;
    }

    input.price__filter_input::placeholder {
      color: #ccc;
    }
    .price__widget .field-currency {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-55%);
      font-size: 16px;
    }
    span.active-facets__button-inner:hover {
      background: #000;
      color: #fff;
    }
    span.active-facets__button-inner:hover svg path {
      stroke: #fff;
    }
    .collection-filters__field .select svg {
      right: 15px;
    }
    .gird__column_icon svg {
      height: 15px;
    }
    a.gird__column_icon {
      line-height: 1;
    }
    .product__grid_column_buttons {
      line-height: 1;
      flex-shrink: 0;
    }
    button.gird__column_icon {
      background: #f5f5f5;
      border: none;
      padding: 8px;
      border-radius: 3px;
    }

    button.gird__column_icon + button.gird__column_icon {
      margin-left: 5px;
    }
    button.gird__column_icon:hover {
      background: #000;
      color: #fff;
    }
    button.gird__column_icon.active {
      background: #000;
      color: #fff;
    }
    @media (min-width: 992px) {
      .grid-col-1 .row-cols-lg-4 > *, .grid-col-1 .row-cols-lg-3 > *, .grid-col-1 .row-cols-lg-2 > * {
        flex: 0 0 auto;
        width: 100%;
      }
      .grid-col-2 .row-cols-lg-4 > *, .grid-col-2 .row-cols-lg-3 > *, .grid-col-2 .row-cols-lg-2 > * {
        flex: 0 0 auto;
        width: 50%;
      }
      .grid-col-3 .row-cols-lg-4 > *, .grid-col-3 .row-cols-lg-3 > *, .grid-col-3 .row-cols-lg-2 > * {
        flex: 0 0 auto;
        width: 33.3333333333%;
      }
      .grid-col-4 .row-cols-lg-4 > *, .grid-col-4 .row-cols-lg-3 > *, .grid-col-4 .row-cols-lg-2 > * {
        flex: 0 0 auto;
        width: 25%;
      }
      .grid-col-5 .row-cols-lg-4 > *, .grid-col-5 .row-cols-lg-3 > *, .grid-col-5 .row-cols-lg-2 > * {
        flex: 0 0 auto;
        width: 20%;
      }
    }

    @media only screen and (min-width: 768px) and (max-width: 991px) {
      .grid-col-1 .row-cols-md-3 > *{
        flex: 0 0 auto;
        width: 100%;
      }
      .grid-col-1 .row-cols-md-3 > * {
        flex: 0 0 auto;
        width: 100%;
      }
      .grid-col-2 .row-cols-md-3 > *{
        flex: 0 0 auto;
        width: 50%;
      }
      .grid-col-3 .row-cols-md-3 > *{
        flex: 0 0 auto;
        width: 33.3333333333%;
      }
      .grid-col-4 .row-cols-md-3 > * {
        flex: 0 0 auto;
        width: 25%;
      }
    }

    @media only screen and (max-width: 767px) {
      .grid-col-1 .row-cols-sm-2 > * {
        flex: 0 0 auto;
        width: 100%;
      }
      .grid-col-2 .row-cols-sm-2 > *{
        flex: 0 0 auto;
        width: 50%;
      }
      .grid-col-1 .row-cols-md-3 > *{
        flex: 0 0 auto;
        width: 100%;
      }
    }
  .offcanvas-filter-sidebar {
      max-width: 350px;
      display: block;
      position: fixed;
      top: 0;
      background: #fff;
      left: 0;
      padding: 30px 20px;
      overflow: auto;
      height: 100%;
      transition: all 0.3s ease 0s;
      z-index: 99;
      transform: translateX(-100%);
      visibility: hidden;
      opacity: 0;
    }
    .offcanvas-filter-sidebar.active{
    	transform: translateX(0);
      visibility: visible;
      opacity: 1;
    }

    .grid-col-1 .product-grid-item {
      display: flex;
    }
    .grid-col-1  .product-grid-item__thumbnail {
      width: 300px;
    }
    .grid-col-1 .product-grid-item__content {
      padding-left: 20px;
      width: calc(100% - 300px);
    }
    .grid-col-1  .product-grid-item {
      text-align: left;
    }
    .grid-col-1 .product-grid-item__price,.grid-col-1 .product__grid_timer .product__countdown {
      justify-content: flex-start;
     }
    @media only screen and (min-width: 575px) and (max-width: 767px) {
      .grid-col-1 .product-grid-item__content {
        width: calc(100% - 245px);
      }
      .grid-col-1  .product-grid-item__thumbnail {
        width: 245px;
      }
    }
    @media only screen and (max-width: 575px) {
      .grid-col-1 .product-grid-item {
        flex-direction: column;
      }
      .grid-col-1 .product-grid-item__content {
        padding-left: 0;
        width: 100%;
      }
      .grid-col-1  .product-grid-item__thumbnail {
        width: 100%;
      }
    }
    .collection_filter_sidebar{
      margin-top: 20px;
      display: block;
    }

    /*  Search filter css  */
    .template-search__header {
      margin-bottom: 3rem;
    }

    .template-search__search {
      margin: 0 auto 3.5rem;
      max-width: 70rem;
    }

    .template-search__search .search {
      margin-top: 3rem;
    }

    .template-search--empty {
      padding-bottom: 18rem;
    }

    @media screen and (min-width: 750px) {
      .template-search__header {
        margin-bottom: 5rem;
      }
    }

    .search__button .icon {
      height: 1.8rem;
    }
    .search_result_page {
      position: relative;
      width: 100%;
      display: block;
    }
    .search_result_bar {
      margin-top: 30px;
    }
    article.article-card {
      position: relative;
    }

    .meta__info--item + .meta__info--item {
      padding-left: 2rem;
      margin-left: 2rem;
      position: relative;
    }
    .meta__info--item + .meta__info--item::before {
      position: absolute;
      width: 5px;
      height: 5px;
      background: rgba(var(--color-foreground));
        content: "";
        border-radius: 100%;
        left: 0;
        margin-left: -5px;
        top: 50%;
        transform: translateY(-50%);
        }
    .article-card__info {
      margin-top: 2rem;
    }
    .ratio_page {
      display: flex;
      position: relative;
      justify-content: center;
      align-items: center;
    }
    .ratio_page:before {
      content: "";
      width: 0;
      height: 0;
      padding-bottom: var(--ratio-percent);
  }
</style>

{%- liquid
  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif

  assign product_columns = section.settings.product_column
  assign product_column_view = ''

  case product_columns
    when '5'
      assign product_column_view = '5'
    when '4'
      assign product_column_view = '4'
    when '3'
      assign product_column_view = '3'
    when '2'
      assign product_column_view = '2'
    else
      assign product_column_view = '1'
  endcase

  assign sort_by = search.sort_by | default: search.default_sort_by
  assign terms = search.terms | escape
  assign search_url = '?q=' | append: terms | append: '&options%5Bprefix%5D=last&sort_by=' | append: sort_by
-%}
{%- if theme_rtl -%}
  {{ 'product-card-rtl.css' | asset_url | stylesheet_tag }}
  {{ 'template-collection-rtl.css' | asset_url | stylesheet_tag }}
  {{ 'component-search-rtl.css' | asset_url | stylesheet_tag }}
{%- endif -%}

{% liquid
  assign predictive_result_type = 'product'

  if settings.show_collection
    assign predictive_result_type = predictive_result_type | append: ',collection'
  endif

  if settings.show_article
    assign predictive_result_type = predictive_result_type | append: ',article'
  endif

  if settings.show_page
    assign predictive_result_type = predictive_result_type | append: ',page'
  endif

  assign my_array = settings.search_filter_tags

  assign new_my_array = my_array | split: ','
  assign result = new_my_array | uniq | sort_natural | join: ', '
  assign product_tags = result | split: ', '
%}

<div
  class="template-search{% unless search.performed and search.results_count > 0 %} template-search--empty{% endunless %} section-{{ section.id }}-padding"
  data-section-id="{{ section.id }}"
  data-section-type="collection-product"
  data-design="{{ request.design_mode }}"
  data-column="{{ section.settings.product_column }}"
>
  <div class="template-search__header {{ container }} center">
    {%- if search.performed -%}
      <h1 class="h2">{{ 'templates.search.title' | t }}</h1>
    {%- else -%}
      <h1 class="h2">{{ 'general.search.search' | t }}</h1>
    {%- endif -%}
    <div class="template-search__search">
      {%- if settings.predictive_search_enabled -%}
        <predictive-search
          class="search_result_page"
          data-loading-text="{{ 'accessibility.loading' | t }}"
          data-limit="{{ settings.number_of_search_result }}"
          data-unavailable="{{ settings.show_unavailable_product }}"
          data-types="{{ predictive_result_type }}"
        >
      {%- endif -%}
      <main-search>
        <form action="{{ routes.search_url }}" method="get" role="search" class="search search-modal__form">
          {% unless settings.search_type == 'none' %}
            <div class="category__search--box-wrapper">
            {% comment %}
              <div class="select-custom header-global-search-select">
                <div class="select__field_form">
                  <main-select-search>
                    <label class="visually-hidden" for="category__serch--form">
                      {{ 'customer.addresses.country' | t }}
                    </label>
                    <select
                      id="search-filter"
                      id="category__serch--form"
                      name="search-filter"
                      data-type="{{ settings.search_type }}"
                      class="header-global-search-categories"
                    >
                      {% if settings.search_type == 'vendor' %}
                        <option value="" selected="selected">{{ 'templates.search.search_filter.vendor' | t }}</option>
                        {% for vendor in shop.vendors %}
                          <option value="{{ vendor }}">{{ vendor }}</option>
                        {%- endfor -%}
                      {% elsif settings.search_type == 'tag' %}
                        <option value="" selected="selected">{{ 'templates.search.search_filter.tag' | t }}</option>
                        {% for tag in product_tags %}
                          <option value="{{ tag | strip }}">{{ tag | strip }}</option>
                        {% endfor %}
                      {% else %}
                        <option value="" selected="selected">{{ 'templates.search.search_filter.type' | t }}</option>
                        {% for type in shop.types %}
                          {% if type != blank %}
                            <option value="{{ type }}">{{ type }}</option>
                          {% endif %}
                        {%- endfor -%}
                      {% endif %}
                    </select>
                    <svg aria-hidden="true" focusable="false" viewBox="0 0 10 6">
                      <use href="#icon-caret" />
                    </svg>
                  </main-select-search>
                </div>
              </div>
            {% endcomment %}
          {% endunless %}

          <div class="search__input_field input__field_form">
            <input
              class="search__input input__field"
              id="Search-In-Template"
              type="search"
              name="q"
              value="{{ search.terms | escape }}"
              placeholder="{{ 'general.search.search' | t }}"
              {%- if settings.predictive_search_enabled -%}
                role="combobox"
                aria-expanded="false"
                aria-owns="predictive-search-results-list"
                aria-controls="predictive-search-results-list"
                aria-haspopup="listbox"
                aria-autocomplete="list"
                autocorrect="off"
                autocomplete="off"
                autocapitalize="off"
                spellcheck="false"
              {%- endif -%}
            >
            <input type="hidden" name="options[prefix]" value="last">
            <button
              type="reset"
              class="reset__button field__button{% if search.terms == blank %} hidden{% endif %}"
              aria-label="{{ 'general.search.reset' | t }}"
            >
              <svg class="icon icon-close" aria-hidden="true" focusable="false">
                <use xlink:href="#icon-reset">
              </svg>
            </button>

            <button class="search__button input__field_form_button" aria-label="{{ 'general.search.search' | t }}">
              <svg class="icon icon-search" aria-hidden="true" focusable="false" role="presentation">
                <use href="#icon-search">
              </svg>
            </button>
          </div>

          {% unless settings.search_type == 'none' %}
            </div>
          {% endunless %}
          {%- if settings.predictive_search_enabled -%}
            <div class="predictive-search predictive-search--header" tabindex="-1" data-predictive-search>
              <div class="predictive-search__loading-state">
                <svg
                  aria-hidden="true"
                  focusable="false"
                  role="presentation"
                  class="spinner"
                  viewBox="0 0 66 66"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                </svg>
              </div>
            </div>

            <span class="predictive-search-status visually-hidden" role="status" aria-hidden="true"></span>
          {%- endif -%}
        </form>
      </main-search>
      {%- if settings.predictive_search_enabled -%}
        </predictive-search>
      {%- endif -%}
    </div>
    {%- if search.performed -%}
      {%- unless section.settings.enable_filtering or section.settings.enable_sorting -%}
        {%- if search.results_count > 0 -%}
          <div class="search_result_bar" role="status">
            <p>
              {{ 'templates.search.results_with_count_and_term' | t: terms: search.terms, count: search.results_count }}
            </p>
          </div>
        {%- endif -%}
      {%- endunless -%}
      {%- if search.results_count == 0 and search.filters == empty -%}
        <div class="search_result_bar" role="status">
          <p role="status">{{ 'templates.search.no_results' | t: terms: search.terms }}</p>
        </div>
      {%- endif -%}
    {%- endif -%}
  </div>

  {%- if search.performed -%}
    {%- if section.settings.enable_sorting
      and section.settings.filter_type == 'vertical'
      and search.filters != empty
    -%}
      <div class="{{ container }} small-hide mb-30">
        <div class="d-flex align-items-center">
          {%- if section.settings.product_view_switcher -%}
            <div class="product__grid_column_buttons d-sm-none">
              <button
                class="gird__column_icon product_col_two {% if product_column_view == "2" %}active{% endif %}"
                aria-label="Product column button"
              >
                <span>
                  <svg fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 5.5 12.5">
                    <defs/><defs><style>.cls-1{fill-rule:evenodd}</style></defs><g id="Layer_2" data-name="Layer 2"><g id="Layer_1-2" data-name="Layer 1"><g id="shop_page" data-name="shop page"><g id="Group-10"><path id="Rectangle" d="M.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 01.75 0z" class="cls-1"/><path id="Rectangle-2" d="M4.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 014.75 0z" class="cls-1" data-name="Rectangle"/></g></g></g></g>
                  </svg>
                </span>
              </button>

              <button
                class="gird__column_icon product_col_three {% if product_column_view == "3" %}active{% endif %}"
                aria-label="Product column button"
              >
                <span>
                  <svg fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 9.5 12.5">
                    <defs/><defs><style>.cls-1{fill-rule:evenodd}</style></defs><g id="Layer_2" data-name="Layer 2"><g id="Layer_1-2" data-name="Layer 1"><g id="shop_page" data-name="shop page"><g id="Group-16"><path id="Rectangle" d="M.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 01.75 0z" class="cls-1"/><path id="Rectangle-2" d="M4.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 014.75 0z" class="cls-1" data-name="Rectangle"/><path id="Rectangle-3" d="M8.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 018.75 0z" class="cls-1" data-name="Rectangle"/></g></g></g></g>
                  </svg>
                </span>
              </button>

              <button
                class="gird__column_icon product_col_four {% if product_column_view == "4" %}active{% endif %}"
                aria-label="Product column button"
              >
                <span>
                  <svg fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13.5 12.5">
                    <defs/><defs><style>.cls-1{fill-rule:evenodd}</style></defs><g id="Layer_2" data-name="Layer 2"><g id="Layer_1-2" data-name="Layer 1"><g id="shop_page" data-name="shop page"><g id="_4_col" data-name="4_col"><path id="Rectangle" d="M.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 01.75 0z" class="cls-1"/><path id="Rectangle-2" d="M4.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 014.75 0z" class="cls-1" data-name="Rectangle"/><path id="Rectangle-3" d="M8.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 018.75 0z" class="cls-1" data-name="Rectangle"/><path id="Rectangle-4" d="M12.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11a.76.76 0 01.75-.75z" class="cls-1" data-name="Rectangle"/></g></g></g></g>
                  </svg>
                </span>
              </button>
            </div>
          {%- endif -%}

          <facet-filters-form class="facets-vertical-sort no-js-hidden flex-grow-1">
            <form class="facets-vertical-form" id="FacetSortForm">
              <div class="facet-filters sorting caption">
                <div class="facet-filters__field">
                  <h2 class="facet-filters__label caption-large text-body">
                    <label for="SortBy">{{ 'products.facets.sort_by_label' | t }}</label>
                  </h2>
                  <div class="select">
                    {%- assign sort_by = search.sort_by | default: search.default_sort_by -%}
                    <select
                      name="sort_by"
                      class="facet-filters__sort select__select caption-large"
                      id="SortBy"
                      aria-describedby="a11y-refresh-page-message"
                    >
                      {%- for option in search.sort_options -%}
                        <option
                          value="{{ option.value | escape }}"
                          {% if option.value == sort_by %}
                            selected="selected"
                          {% endif %}
                        >
                          {{ option.name | escape }}
                        </option>
                      {%- endfor -%}
                    </select>
                    {% render 'icon-caret' %}
                  </div>
                </div>
                <noscript>
                  <button type="submit" class="facets__button-no-js button button--secondary">
                    {{ 'products.facets.sort_button' | t }}
                  </button>
                </noscript>
              </div>

              <div class="product-count-vertical light" role="status">
                <h2 class="product-count__text text-body">
                  <span id="ProductCountDesktop">
                    {%- if search.results_count -%}
                      {{ 'templates.search.results_with_count' | t: terms: search.terms, count: search.results_count }}
                    {%- elsif search.products_count == search.all_products_count -%}
                      {{ 'products.facets.product_count_simple' | t: count: search.products_count }}
                    {%- else -%}
                      {{
                        'products.facets.product_count'
                        | t: product_count: search.products_count, count: search.all_products_count
                      }}
                    {%- endif -%}
                  </span>
                </h2>
                <div class="loading-overlay__spinner">
                  <svg
                    aria-hidden="true"
                    focusable="false"
                    role="presentation"
                    class="spinner"
                    viewBox="0 0 66 66"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                  </svg>
                </div>
              </div>
            </form>
          </facet-filters-form>
        </div>
      </div>
    {%- endif -%}

    <div
      {% if section.settings.filter_type == 'vertical' %}
        class="facets-vertical {{ container }}"
      {% endif %}
    >
      {%- if search.filters != empty -%}
        {%- if section.settings.enable_filtering or section.settings.enable_sorting -%}
          <aside
            aria-labelledby="verticalTitle"
            class="facets-wrapper{% unless section.settings.enable_filtering %} facets-wrapper--no-filters{% endunless %}{% if section.settings.filter_type != 'vertical' %} {% if section.settings.full_width %}container-fluid{% else %} container {% endif %}{% endif %} {% unless section.settings.filter_type == 'vertical' %} mb-30 {% endunless %}"
            id="main-search-filters"
            data-id="{{ section.id }}"
          >
            {% render 'facets',
              results: search,
              enable_filtering: section.settings.enable_filtering,
              enable_sorting: section.settings.enable_sorting,
              filter_type: section.settings.filter_type
            %}
          </aside>
        {%- endif -%}
      {%- endif -%}
      <div class="product-grid-container">
        <div id="ProductGridContainer">
          {%- if search.results.size == 0 and search.filters != empty -%}
            <div
              class="template-search__results collection collection--empty{% if section.settings.filter_type != 'vertical' %} {{ container }}{% endif %}"
              id="product-grid"
              data-id="{{ section.id }}"
            >
              <div class="loading-overlay"></div>
              <div class="title-wrapper center">
                <h2 class="title title--primary">
                  {{ 'sections.collection_template.empty' | t -}}
                  <br>
                  {{
                    'sections.collection_template.use_fewer_filters_html'
                    | t: link: search_url, class: 'underlined-link link'
                  }}
                </h2>
              </div>
            </div>
          {%- else -%}
            {% paginate search.results by section.settings.products_per_page %}
              <div
                class="template-search__results collection{% if section.settings.filter_type != 'vertical' %} {{ container }}{% endif %}"
                id="product-grid"
                data-id="{{ section.id }}"
              >
                <div class="loading-overlay"></div>
                <div
                  class="row row-cols-lg-{{ product_column_view }} row-cols-md-3 {% if section.settings.product_column_on_mobile %} row-cols-1 {% else %} row-cols-2 {% endif %}"
                  data-product-column
                >
                  {%- for item in search.results -%}
                    <div class="col">
                      {%- case item.object_type -%}
                        {%- when 'product' -%}
                          {%- capture product_settings -%}{%- if section.settings.product_show_vendor -%}vendor,{%- endif -%}title,price{%- endcapture -%}
                          {% render 'product-card',
                            className: 'mb-30',
                            product_card_product: item,
                            media_size: section.settings.image_ratio,
                            show_secondary_image: section.settings.show_secondary_image,
                            show_vendor: section.settings.show_vendor,
                            show_badge: section.settings.show_badges,
                            show_cart_button: section.settings.show_cart_button,
                            show_quick_view: section.settings.show_quick_view_button,
                            show_quick_compare: section.settings.show_compare_view_button,
                            show_wishlist: section.settings.show_wishlist_button,
                            show_countdown: section.settings.show_countdown,
                            show_title: section.settings.show_title,
                            show_price: section.settings.show_price,
                            show_rating: section.settings.show_product_rating,
                            card_style: section.settings.card_style
                          %}

                        {%- when 'article' -%}
                          {% render 'article-card',
                            className: 'mb-30',
                            article: item,
                            show_image: true,
                            show_date: section.settings.show_date,
                            show_author: section.settings.show_author,
                            show_comment: section.settings.show_comment,
                            media_height: 'adapt',
                            media_aspect_ratio: 1,
                            show_badge: true
                          %}

                        {%- when 'page' -%}
                          <div class="card-wrapper underline-links-hover mb-30">
                            <div
                              class="card card--card card--text ratio_page color-background-2"
                              style="--ratio-percent: 100%;"
                            >
                              <div class="card__content">
                                <div class="card__information">
                                  <h3 class="card__heading">
                                    <a href="{{ item.url }}" class="full-unstyled-link">
                                      {{ item.title | truncate: 50 | escape }}
                                    </a>
                                  </h3>
                                </div>
                                <div class="product-grid-item__badges">
                                  <span class="badge color-background-1">{{ 'templates.search.page' | t }}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                      {%- endcase -%}
                    </div>
                  {%- endfor -%}
                </div>
                {%- if paginate.pages > 1 -%}
                  {% render 'pagination', paginate: paginate, anchor: '' %}
                {%- endif -%}
              </div>
            {% endpaginate %}
          {%- endif -%}
        </div>
      </div>
    </div>
  {%- endif -%}
</div>

<script>
    class MainSearchSelect extends HTMLElement {
      constructor() {
        super();
        this.init();
      }

      init(){
        const queryString = window.location.search;
         const UrlsearchParams = new URLSearchParams(queryString);
         const categorySearchParamValue = UrlsearchParams.get("search-filter");
         let selectElement = this.querySelector('select');

          if(UrlsearchParams.has("search-filter") && selectElement){
           let optionValues = [...selectElement.options].map(option => {
             if(option.value === categorySearchParamValue){
               option.defaultSelected = true;;
             }
           })
        }
      }

    }

  customElements.define('main-select-search', MainSearchSelect);
</script>

{% schema %}
{
  "name": "t:sections.main-search.name",
  "class": "collection-grid-section",
  "tag": "section",
  "settings": [
  {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
  	  {
        "type": "range",
        "id": "products_per_page",
        "min": 8,
        "max": 24,
        "step": 1,
        "default": 16,
        "label": "Item per page"
      },
      {
        "type": "select",
        "id": "product_column",
        "label": "Number of columns on desktop",
        "options": [
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          }
        ],
        "default": "3"
      },
    {
      "type": "checkbox",
      "id": "product_column_on_mobile",
      "default": false,
      "label": "Display one column on mobile"
    },
    {
      "type": "checkbox",
      "id": "product_view_switcher",
      "default": true,
      "label": "Enable layout switcher"
    },
  	{
      "type": "header",
      "content": "t:sections.main-collection-product-grid.settings.header__1.content"
    },
    {
      "type": "checkbox",
      "id": "enable_filtering",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_filtering.label",
      "info": "t:sections.main-collection-product-grid.settings.enable_filtering.info"
    },
    {
      "type": "select",
      "id": "filter_type",
      "options": [
        {
          "value": "horizontal",
          "label": "Horizontal"
        },
        {
          "value": "vertical",
          "label": "Vertical"
        },
        {
          "value": "drawer",
          "label": "Drawer"
        }
      ],
      "default": "vertical",
      "label": "Desktop filter layout",
      "info": "Drawer is the default mobile layout."
    },
    {
      "type": "checkbox",
      "id": "enable_sorting",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_sorting.label"
    },
    {
      "type": "select",
      "id": "price_filter_type",
      "options": [
        {
          "value": "input",
          "label": "Input field"
        },
        {
          "value": "range",
          "label": "Range slider"
        }
      ],
      "default": "range",
      "label": "Price filter layout"
    },
    {
      "type": "header",
      "content": "Product card"
    },
  	{
        "type": "select",
        "id": "card_style",
        "label": "Card style",
        "options": [
          {
            "value": "style_1",
            "label": "Style 1"
          },
          {
            "value": "style_2",
            "label": "Style 2"
          }
        ],
        "default": "style_1"
      },
      {
         "type": "select",
         "id": "image_ratio",
         "options": [
           {
             "value": "adapt",
             "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__1.label"
           },
           {
             "value": "portrait",
             "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__2.label"
           },
           {
             "value": "square",
             "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__3.label"
           },
            {
             "value": "landscape",
             "label": "Landscape"
           }
         ],
         "default": "adapt",
         "label": "t:sections.main-collection-product-grid.settings.image_ratio.label"
       },
       {
         "type": "checkbox",
         "id": "show_secondary_image",
         "default": false,
         "label": "t:sections.main-collection-product-grid.settings.show_secondary_image.label"
       },
       {
         "type": "checkbox",
         "id": "show_badges",
         "default": true,
         "label": "Show badges"
       },
       {
         "type": "checkbox",
         "id": "show_cart_button",
         "default": true,
         "label": "Show cart button"
       },
       {
         "type": "checkbox",
         "id": "show_quick_view_button",
         "default": true,
         "label": "Show quick view"
       },
       {
         "type": "checkbox",
         "id": "show_compare_view_button",
         "default": true,
         "label": "Show compare button"
       },
       {
         "type": "checkbox",
         "id": "show_wishlist_button",
         "default": true,
         "label": "Show wishlist button"
       },
       {
         "type": "checkbox",
         "id": "show_title",
         "default": true,
         "label": "Show title"
       },
       {
         "type": "checkbox",
         "id": "show_price",
         "default": true,
         "label": "Show price"
       },
       {
         "type": "checkbox",
         "id": "show_vendor",
         "default": false,
         "label": "t:sections.main-collection-product-grid.settings.show_vendor.label"
       },

   {
         "type": "checkbox",
         "id": "show_countdown",
         "default": false,
         "label": "Show countdown"
     },

     {
       "type": "checkbox",
       "id": "show_product_rating",
       "default": false,
       "label": "Show product rating"
     },
      {
        "type": "header",
        "content": "t:sections.main-search.settings.header__2.content"
      },
      {
        "type": "checkbox",
        "id": "show_date",
        "default": false,
        "label": "t:sections.featured-blog.settings.show_date.label"
      },
      {
        "type": "checkbox",
        "id": "show_author",
        "default": false,
        "label": "t:sections.featured-blog.settings.show_author.label"
      },
      {
        "type": "checkbox",
        "id": "show_comment",
        "default": false,
        "label": "Show comment"
      },

    {
      "type": "header",
      "content": "Section padding"
    },
    {
          "type": "paragraph",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        },
		{
          "type": "paragraph",
          "content": "Mobile"
        },
		{
          "type": "range",
          "id": "mobile_padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "mobile_padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        }
  ]
}
{% endschema %}
