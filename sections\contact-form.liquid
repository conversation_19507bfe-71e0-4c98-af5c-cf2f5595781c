{%- liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
-%}
<link rel="stylesheet" href="{{ 'component-rte.css' | asset_url }}" media="print" onload="this.media='all'">
{{ 'section-contact-form.css' | asset_url | stylesheet_tag }}
{{ 'section-title.css' | asset_url | stylesheet_tag }}

<noscript>{{ 'component-rte.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
  }

  @media screen and (min-width: 991px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

{% if theme_rtl %}
  {{ 'section-contact-form-rtl.css' | asset_url | stylesheet_tag }}
{% endif %}

{% liquid
  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif
%}

<div class="contact__section section-{{ section.id }}-padding">
  <div class="{{ container }}">
    {% if section.settings.heading != blank or section.settings.subtitle != blank %}
      <div class="section-heading text-{{ section.settings.text_center }}  {% if section.settings.border_line %} mb-70 {% else %} mb-50{% endif %}">
        {% if section.settings.heading_position == 'bottom' and section.settings.subtitle != blank %}
          <span class="section-heading__sub_title">{{- section.settings.subtitle -}}</span>
        {% endif %}
        <h2 class="section-heading__title {% if section.settings.heading_position == "top" and section.settings.subtitle != blank and section.settings.border_line %}heading__space--bottom{% endif %} ">
          {% if section.settings.border_line %}<span>{% endif %}
          {{- section.settings.heading -}}
          {% if section.settings.border_line %}</span>{% endif %}
        </h2>
        {% if section.settings.heading_position == 'top' and section.settings.subtitle != blank %}
          <span class="section-heading__sub_title">{{- section.settings.subtitle -}}</span>
        {% endif %}
      </div>
    {% endif %}

    <div class="main__contact--area contact__form--grid">






      
      <div class="contact__form">
        {%- form 'contact', id: 'ContactForm' -%}
          {%- if form.posted_successfully? -%}
            <div class="form-status form-status-list form__message" tabindex="-1" autofocus>
              {% render 'icon-success' %}
              {{ 'templates.contact.form.post_success' | t }}
            </div>
          {%- elsif form.errors -%}
            <div class="form__message">
              <h2 class="form-status caption-large" role="alert" tabindex="-1" autofocus>
                {% render 'icon-error' %}
                {{ 'templates.contact.form.error_heading' | t }}
              </h2>
            </div>
            <ul class="form-status-list caption-large" role="list">
              <li>
                <a href="#ContactForm-email" class="link">
                  {{ form.errors.translated_fields.email | capitalize }}
                  {{ form.errors.messages.email }}
                </a>
              </li>
            </ul>
          {%- endif -%}

          <div class="row">
            <div class="col-lg-6 col-md-6">
              <div class="input__field_form  mb-20">
                <label for="ContactForm-name">
                  {{- 'templates.contact.form.name' | t }}
                  <span aria-hidden="true">*</span>
                </label>
                <input
                  class="input__field"
                  autocomplete="name"
                  type="text"
                  id="ContactForm-name"
                  name="contact[{{ 'templates.contact.form.name' | t }}]"
                  value="{% if form.name %}{{ form.name }}{% elsif customer %}{{ customer.name }}{% endif %}"
                  placeholder="{{ 'templates.contact.form.name' | t }}"
                >
              </div>
            </div>
            <div class="col-lg-6 col-md-6">
              <div class="input__field_form field--with-error mb-20">
                <label for="ContactForm-email">
                  {{- 'templates.contact.form.email' | t }}
                  <span aria-hidden="true">*</span></label
                >
                <input
                  autocomplete="email"
                  type="email"
                  id="ContactForm-email"
                  class="input__field"
                  name="contact[email]"
                  spellcheck="false"
                  autocapitalize="off"
                  value="{% if form.email %}{{ form.email }}{% elsif customer %}{{ customer.email }}{% endif %}"
                  aria-required="true"
                  {% if form.errors contains 'email' %}
                    aria-invalid="true"
                    aria-describedby="ContactForm-email-error"
                  {% endif %}
                  placeholder="{{ 'templates.contact.form.email' | t }}"
                >
                {%- if form.errors contains 'email' -%}
                  <small class="contact__field-error" id="ContactForm-email-error">
                    <span class="visually-hidden">{{ 'accessibility.error' | t }}</span>
                    <span class="form__message">
                      {%- render 'icon-error' -%}
                      {{- form.errors.translated_fields.email | capitalize }}
                      {{ form.errors.messages.email -}}
                    </span>
                  </small>
                {%- endif -%}
              </div>
            </div>
            <div class="col-lg-12 col-md-12">
              <div class="input__field_form mb-20">
                <label for="ContactForm-phone">
                  {{- 'templates.contact.form.phone' | t }}
                  <span aria-hidden="true">*</span>
                </label>
                <input
                  type="tel"
                  id="ContactForm-phone"
                  class="input__field"
                  autocomplete="tel"
                  name="contact[{{ 'templates.contact.form.phone' | t }}]"
                  pattern="[0-9\-]*"
                  value="{% if form.phone %}{{ form.phone }}{% elsif customer %}{{ customer.phone }}{% endif %}"
                  placeholder="{{ 'templates.contact.form.phone' | t }}"
                >
              </div>
            </div>

            <div class="col-12">
              <div class="input__field_form contact--form-textarea">
                <label for="ContactForm-body">
                  {{- 'templates.contact.form.comment' | t }}
                  <span aria-hidden="true">*</span>
                </label>
                <textarea
                  rows="10"
                  id="ContactForm-body"
                  class="text-area"
                  name="contact[{{ 'templates.contact.form.comment' | t }}]"
                  placeholder="{{ 'templates.contact.form.comment' | t }}"
                >
                {{- form.body -}}
              </textarea>
              </div>
            </div>
          </div>

          <div class="contact__button">
            <button type="submit" class="button button--primary button--large">
              {{ 'templates.contact.form.send' | t }}
            </button>
          </div>
        {%- endform -%}
      </div>

      
      {%- if section.blocks.size > 0 -%}
        <div class="contact__info border-radius-5 color-{{ section.settings.color_scheme }}">
          {%- for block in section.blocks -%}
            {%- case block.type -%}
              {%- when 'contact_list' -%}
                <div class="contact__info--items" {{ block.shopify_attributes }}>
                  <div class="contact__info--items__inner d-flex">
                    {%- if block.settings.icon != 'none' -%}
                      <div class="contact__info--icon">
                        {% render 'contact-item-icon', icon: block.settings.icon %}
                      </div>
                    {%- endif -%}
                    <div class="contact__info--content">
                      {%- if block.settings.text_title != ' ' -%}
                      <p>{{ block.settings.text_title }}</p>
                      {% endif %}
                      {{ block.settings.text }}
                    </div>
                  </div>
                </div>
              {%- when 'scoial_media' -%}
                <div class="contact__info--items" {{ block.shopify_attributes }}>
                  <h3 class="contact__info--content__title text-white mb-15">{{ block.settings.heading }}</h3>
                  <ul class="contact__info--social d-flex">
                    {%- if settings.social_twitter_link != blank -%}
                      <li class="contact__info--social__list">
                        <a
                          href="{{ settings.social_twitter_link }}"
                          class="contact__info--social__icon"
                          target="_blank"
                          aria-describedby="a11y-external-message"
                        >
                          {%- render 'icon-twitter' -%}
                          <span class="visually-hidden">{{ 'general.social.links.twitter' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}

                    {%- if settings.social_facebook_link != blank -%}
                      <li class="contact__info--social__list">
                        <a
                          href="{{ settings.social_facebook_link }}"
                          class="contact__info--social__icon"
                          target="_blank"
                          aria-describedby="a11y-external-message"
                        >
                          {%- render 'icon-facebook' -%}
                          <span class="visually-hidden">{{ 'general.social.links.facebook' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}

                    {%- if settings.social_pinterest_link != blank -%}
                      <li class="contact__info--social__list">
                        <a
                          href="{{ settings.social_pinterest_link }}"
                          class="contact__info--social__icon"
                          target="_blank"
                          aria-describedby="a11y-external-message"
                        >
                          {%- render 'icon-pinterest' -%}
                          <span class="visually-hidden">{{ 'general.social.links.pinterest' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}

                    {%- if settings.social_instagram_link != blank -%}
                      <li class="contact__info--social__list">
                        <a
                          href="{{ settings.social_instagram_link }}"
                          class="contact__info--social__icon"
                          target="_blank"
                          aria-describedby="a11y-external-message"
                        >
                          {%- render 'icon-instagram' -%}
                          <span class="visually-hidden">{{ 'general.social.links.instagram' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}

                    {%- if settings.social_tiktok_link != blank -%}
                      <li class="contact__info--social__list">
                        <a
                          href="{{ settings.social_tiktok_link }}"
                          class="contact__info--social__icon"
                          target="_blank"
                          aria-describedby="a11y-external-message"
                        >
                          {%- render 'icon-tiktok' -%}
                          <span class="visually-hidden">{{ 'general.social.links.tiktok' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}

                    {%- if settings.social_tumblr_link != blank -%}
                      <li class="contact__info--social__list">
                        <a
                          href="{{ settings.social_tumblr_link }}"
                          class="contact__info--social__icon"
                          target="_blank"
                          aria-describedby="a11y-external-message"
                        >
                          {%- render 'icon-tumblr' -%}
                          <span class="visually-hidden">{{ 'general.social.links.tumblr' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}

                    {%- if settings.social_snapchat_link != blank -%}
                      <li class="contact__info--social__list">
                        <a
                          href="{{ settings.social_snapchat_link }}"
                          class="contact__info--social__icon"
                          target="_blank"
                          aria-describedby="a11y-external-message"
                        >
                          {%- render 'icon-snapchat' -%}
                          <span class="visually-hidden">{{ 'general.social.links.snapchat' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}

                    {%- if settings.social_youtube_link != blank -%}
                      <li class="contact__info--social__list">
                        <a
                          href="{{ settings.social_youtube_link }}"
                          class="contact__info--social__icon"
                          target="_blank"
                          aria-describedby="a11y-external-message"
                        >
                          {%- render 'icon-youtube' -%}
                          <span class="visually-hidden">{{ 'general.social.links.youtube' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}

                    {%- if settings.social_vimeo_link != blank -%}
                      <li class="contact__info--social__list">
                        <a
                          href="{{ settings.social_vimeo_link }}"
                          class="contact__info--social__icon"
                          target="_blank"
                          aria-describedby="a11y-external-message"
                        >
                          {%- render 'icon-vimeo' -%}
                          <span class="visually-hidden">{{ 'general.social.links.vimeo' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}
                  </ul>
                </div>
            {%- endcase -%}
          {%- endfor -%}
        </div>
      {%- endif -%}

      
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.contact-form.name",
  "tag": "section",
  "class": "spaced-section",
  "settings": [
    {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
    {
     "type": "header",
     "content": "Section header"
   },
    {
      "type": "inline_richtext",
      "id": "subtitle",
      "label": "Subheading"
    },
    {
      "type": "text",
      "id": "heading",
      "default": "Get in touch",
      "label": "t:sections.featured-collection.settings.title.label"
    },
    {
      "type": "select",
      "id": "heading_position",
      "options": [
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        }
      ],
      "default": "bottom",
      "label": "Heading position"
    },
    {
      "type": "select",
      "id": "text_center",
      "options": [
        {
          "value": "right",
          "label": "Right"
        },
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        }
      ],
      "default": "center",
      "label": "Alignment"
    },
    {
       "type": "checkbox",
       "id": "border_line",
       "label": "Enable border line",
       "default": true
     },

        {
            "type": "color_scheme",
            "id": "color_scheme",
            "label": "t:sections.all.colors.label",
            "default": "accent-1"
          },

		{
          "type": "header",
          "content": "Section padding"
        },
		{
          "type": "paragraph",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        },
		{
          "type": "paragraph",
          "content": "Mobile"
        },
		{
          "type": "range",
          "id": "mobile_padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "mobile_padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        }
  ],
  "blocks": [
        {
          "type": "contact_list",
          "name": "Contact item",
          "settings": [
			{
              "type": "select",
              "id": "icon",
              "options": [
                {
                  "value": "none",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
                },
                {
                  "value": "email",
                  "label": "Email"
                },
                {
                  "value": "phone",
                  "label": "Phone"
                },
                {
                  "value": "address",
                  "label": "Adress"
                }
              ],
              "default": "email",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.label"
            },
			{
              "type": "text",
              "id": "text_title",
              "default": "Title",
              "label": "Title"
            },
			{
              "type": "richtext",
              "id": "text",
              "default": "<p> <strong> (************* </strong> </br> (*************</p>",
              "label": "t:sections.rich-text.blocks.text.settings.text.label"
            }
          ]
        },
		{
          "type": "scoial_media",
          "name": "Social media",
          "limit": 1,
          "settings": [
			 {
              "type": "text",
              "id": "heading",
              "default": "Follow us",
              "label": "Heading",
              "info": "To display your social media accounts, link them in your theme settings under “Social media”"
            }
          ]
        }
    ],
  "presets": [
    {
      "name": "t:sections.contact-form.presets.name",
		"blocks":[
          {
          	"type": "contact_list"
          },
          {
          	"type": "scoial_media"
          }
        ]
    }
  ],
  "disabled_on": {
    "groups": ["header","footer"]
  }
}
{% endschema %}
