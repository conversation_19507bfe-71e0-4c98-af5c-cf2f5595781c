{%- liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
-%}

{{ 'component-grid.css' | asset_url | stylesheet_tag }}
{{ 'product-card.css' | asset_url | stylesheet_tag }}
{{ 'component-rating.css' | asset_url | stylesheet_tag }}
{{ 'countdown-timer.css' | asset_url | stylesheet_tag }}
{{ 'shop-the-look.css' | asset_url | stylesheet_tag }}
<link rel="stylesheet" href="{{ 'component-price.css' | asset_url }}" media="print" onload="this.media='all'">
<noscript>{{ 'component-price.css' | asset_url | stylesheet_tag }}</noscript>

<link rel="stylesheet" href="{{ 'component-rte.css' | asset_url }}" media="print" onload="this.media='all'">
<noscript>{{ 'component-rte.css' | asset_url | stylesheet_tag }}</noscript>

{{ 'lookbook-slider.css' | asset_url | stylesheet_tag }}
{{ 'slideshow-controls.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
    --padding-top: {{ section.settings.mobile_padding_top }}px;
        --grid-desktop-vertical-spacing: 25px;
      --grid-desktop-horizontal-spacing: 25px;
      --grid-mobile-vertical-spacing: 25px;
      --grid-mobile-horizontal-spacing: 25px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
      --padding-top: {{ section.settings.padding_top }}px;
    }
  }

  .placeholder .lookbook__shop--product-wrapper {
      --color-background: 255, 255, 255;
  }

    {% if section.settings.hotspot_active_color != blank %}
    .lookbook--product-hotspot.active::after {
      --hotspot-background-2-gradient: {{ section.settings.hotspot_active_color | default: "radial-gradient(rgba(121, 128, 252, 1), rgba(215, 204, 250, 1) 100%, rgba(247, 197, 204, 1) 100%)" }};
    }
    {% endif %}
    .lookbook--slider-wrapper {
      position: relative;
  }
{%- endstyle -%}
{% if theme_rtl %}
  {{ 'product-card-rtl.css' | asset_url | stylesheet_tag }}
{% endif %}
{%- liquid
  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif
-%}

<div
  class="image-with-text section--top-space-{{ section.id }} section-{{ section.id }}-padding color-{{ section.settings.color_scheme }}"
  data-section-id="{{ section.id }}"
  data-section-type="lookbook"
  id="Slider-{{ section.id }}"
  data-slider-autoplay="{{ section.settings.autorotate }}"
  data-slider-delay="{{ section.settings.autorotate_speed }}000"
  data-slider-loop="{%- if section.blocks.size > 1 -%}true {%- else -%} false {%- endif -%}"
  data-pagination="{{ pagination }}"
>
  <div class="{{ container }}">
    <div class="section-heading text-{{ section.settings.text_center }}    {% if section.settings.heading != blank or section.settings.subtitle != blank %}{% if section.settings.border_line %} mb-70 {% else %} mb-50{% endif %}{% endif %}">
      {% if section.settings.heading_position == 'bottom' and section.settings.subtitle != blank %}
        <span class="section-heading__sub_title">{{- section.settings.subtitle -}}</span>
      {% endif %}
      <h2 class="section-heading__title {% if section.settings.heading_position == "top" and section.settings.subtitle != blank and section.settings.border_line %}heading__space--bottom{% endif %} ">
        {% if section.settings.border_line %}<span>{% endif %}
        {{- section.settings.heading -}}
        {% if section.settings.border_line %}</span>{% endif %}
      </h2>
      {% if section.settings.heading_position == 'top' and section.settings.subtitle != blank %}
        <span class="section-heading__sub_title">{{- section.settings.subtitle -}}</span>
      {% endif %}
    </div>

    <div class="grid grid--2-col-desktop grid--1-col-tablet-down {% if section.settings.layout == "text_first" %}desktop-row-reverse {% endif %} d-flex flex-wrap {{ column_class_reverse }}">
      <div class="grid__item image-with-text__media-item">
        <div
          class="image-with-text__media {% if section.settings.round_corner %} rounded--image {% endif %} shop__the--look image-with-text__media--{{ section.settings.height }} global-media-settings {% if section.settings.image != blank %}media{% else %}image-with-text__media--placeholder placeholder{% endif %} {{ column_class_reverse }}"
          {% if section.settings.height == 'adapt' and section.settings.image != blank %}
            style="padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%;"
          {% endif %}
        >
          {%- if section.settings.image != blank -%}
            {%- capture sizes -%}(min-width: {{ settings.container_lg_width }}px) {{ settings.container_lg_width | minus: 60 | divided_by: 2 }}px, (min-width: 750px) calc((100vw - 130px) / 2), calc(100vw - 30px){%- endcapture -%}
            {{
              section.settings.image
              | image_url: width: 1500
              | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1500'
            }}
          {%- else -%}
            {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
          {%- endif -%}

          {%- for block in section.blocks -%}
            {% case block.type %}
              {%- when 'look' -%}
                {% assign product = block.settings.product %}
                <div
                  class="lookbook__shop--product-wrapper"
                  style="--hotspot-x: {{ block.settings.hotspot_x }}%; --hotspot-y: {{ block.settings.hotspot_y }}%;"
                >
                  <button
                    type="button"
                    data-index="{{ forloop.index0 }}"
                    class="look__hotspot lookbook--product-hotspot {% if block.settings.hotspot_y > 70 %} lookbook__shop--bottom {% endif %} {% if forloop.first == true %}active{% endif %}"
                    aria-label="{{ 'products.product.select_options' | t }}"
                    style="--hotspot-background-1: {{ block.settings.background_1 }}; --hotspot-background-2-gradient: {{ block.settings.background_2_gradient | default: "radial-gradient(50% 50% at 50% 50%,#764F6A 0%,#1F222F 100%)" }}"
                    {{ block.shopify_attributes }}
                  >
                    <span class="look__hotspot--arrow"></span>
                  </button>

                  {% if product != blank %}
                    <div class="lookbook__shop--product {% if block.settings.hotspot_y > 70 %} lookbook__shop--product-bottom {% endif %} {% if block.settings.hotspot_x > 85 %} lookbook__shop--product-right {% endif %} {% if block.settings.hotspot_x < 15 %} lookbook__shop--product-left{% endif %}">
                      <div class="lookbook__shop--product__title h6">
                        {% if block.settings.quick_shop == false -%}
                          <a href="{{ product.url | default: '#' }}">
                        {% endif %}
                        {{ product.title }}
                        {% if block.settings.quick_shop == false %}</a>{% endif %}
                      </div>
                      {% render 'price', product: product, price_class: 'lookbook__shop--product__price' %}
                    </div>
                  {% else %}
                    <div class="lookbook__shop--product {% if block.settings.hotspot_y > 70 %} lookbook__shop--product-bottom {% endif %} {% if block.settings.hotspot_x > 85 %} lookbook__shop--product-right {% endif %} {% if block.settings.hotspot_x < 15 %} lookbook__shop--product-left{% endif %}">
                      <div class="lookbook__shop--product__title h6">Example product title {{ forloop.index }}</div>
                      <span class="price lookbook__shop--product__price">$99.99</span>
                    </div>
                  {% endif %}
                </div>
            {%- endcase -%}
          {%- endfor -%}
        </div>
      </div>
      <div class="grid__item lookbook--slider-wrapper">
        <div class="lookbook--slider-inner">
          <div class="swiper lookbook--slider">
            <div class="swiper-wrapper">
              {%- for block in section.blocks -%}
                {% case block.type %}
                  {%- when 'look' -%}
                    <div class="swiper-slide" tabindex="-1">
                      {% assign product = block.settings.product %}
                      <div class="lookbook__product--card" {{ block.shopify_attributes }}>
                        {% render 'lookbook-product-card',
                          product_card_product: product,
                          media_size: section.settings.image_ratio,
                          show_secondary_image: section.settings.show_secondary_image,
                          show_vendor: section.settings.show_vendor,
                          show_badge: section.settings.show_badges,
                          show_cart_button: section.settings.show_cart_button,
                          show_quick_view: section.settings.show_quick_view_button,
                          show_quick_compare: section.settings.show_compare_view_button,
                          show_wishlist: section.settings.show_wishlist_button,
                          show_countdown: section.settings.show_countdown,
                          show_title: section.settings.show_title,
                          show_price: section.settings.show_price,
                          show_rating: section.settings.show_product_rating,
                          card_style: section.settings.card_style,
                          color_swatches: section.settings.color_swatches,
                          index: forloop.index
                        %}
                      </div>
                    </div>
                {%- endcase -%}
              {%- endfor -%}
            </div>
          </div>

          {% comment %} Slider controls {% endcomment %}
          <div class="complementary-slideshow--slider lookbook-slideshow--slider-navigate">
            <div class="slidershow--controls--button d-flex align-items-center" id="section__{{ section.id }}">
              <div class="swiper-pagination slideshow--bullet-button pagination--dots"></div>
            </div>
          </div>
          {% comment %} Slider controls .\ {% endcomment %}
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Shop the look slider",
  "disabled_on": {
      "groups": ["header","footer"]
    },
  "class": "section",
  "settings": [
    {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
    {
      "type": "header",
      "content": "Section header"
    },
     {
      "type": "inline_richtext",
      "id": "subtitle",
      "label": "Subheading"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.featured-collection.settings.title.label"
    },
    {
      "type": "select",
      "id": "heading_position",
      "options": [
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        }
      ],
      "default": "bottom",
      "label": "Heading position"
    },
    {
      "type": "select",
      "id": "text_center",
      "options": [
        {
          "value": "right",
          "label": "Right"
        },
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        }
      ],
      "default": "center",
      "label": "Alignment"
    },
    {
       "type": "checkbox",
       "id": "border_line",
       "label": "Enable border line",
       "default": false
     },
    {
        "type": "header",
        "content": "Layout"
      },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.image-with-text.settings.image.label"
    },
    {
      "type": "select",
      "id": "height",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.image-with-text.settings.height.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.image-with-text.settings.height.options__2.label"
        },
		{
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "t:sections.image-with-text.settings.height.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.image-with-text.settings.height.label"
    },
    {
      "type": "select",
      "id": "desktop_image_width",
      "options": [
        {
          "value": "small",
          "label": "t:sections.image-with-text.settings.desktop_image_width.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.image-with-text.settings.desktop_image_width.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.image-with-text.settings.desktop_image_width.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.image-with-text.settings.desktop_image_width.label",
      "info": "t:sections.image-with-text.settings.desktop_image_width.info"
    },
    {
      "type": "select",
      "id": "layout",
      "options": [
        {
          "value": "image_first",
          "label": "t:sections.image-with-text.settings.layout.options__1.label"
        },
        {
          "value": "text_first",
          "label": "t:sections.image-with-text.settings.layout.options__2.label"
        }
      ],
      "default": "image_first",
      "label": "t:sections.image-with-text.settings.layout.label",
      "info": "t:sections.image-with-text.settings.layout.info"
    },
    {
      "type": "checkbox",
      "id": "round_corner",
      "label": "Round corner",
      "default": true
    },
	 {
     "type": "header",
     "content": "Product card"
   },
     {
        "type": "select",
        "id": "card_style",
        "label": "Card style",
        "options": [
          {
            "value": "style_1",
            "label": "Style 1"
          },
          {
            "value": "style_2",
            "label": "Style 2"
          },
          {
            "value": "style_3",
            "label": "Style 3"
          }
        ],
        "default": "style_2"
      },
    {
       "type": "select",
       "id": "image_ratio",
       "options": [
         {
           "value": "adapt",
           "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__1.label"
         },
         {
           "value": "portrait",
           "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__2.label"
         },
         {
           "value": "square",
           "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__3.label"
         },
          {
           "value": "landscape",
           "label": "Landscape"
         }
       ],
       "default": "adapt",
       "label": "t:sections.main-collection-product-grid.settings.image_ratio.label"
     },
     {
       "type": "checkbox",
       "id": "show_secondary_image",
       "default": false,
       "label": "t:sections.main-collection-product-grid.settings.show_secondary_image.label"
     },
     {
        "type": "checkbox",
        "id": "color_swatches",
        "default": true,
        "label": "Enable color swatches",
        "info": "To display color swatches, you need to enable it. [Learn more](https:\/\/help.team90degree.com\/docs\/grocee-meatfields\/)."
      },
     {
       "type": "checkbox",
       "id": "show_badges",
       "default": true,
       "label": "Show badges"
     },
     {
       "type": "checkbox",
       "id": "show_cart_button",
       "default": true,
       "label": "Show cart button"
     },
     {
       "type": "checkbox",
       "id": "show_quick_view_button",
       "default": true,
       "label": "Show quick view"
     },
     {
       "type": "checkbox",
       "id": "show_compare_view_button",
       "default": true,
       "label": "Show compare button"
     },
     {
       "type": "checkbox",
       "id": "show_wishlist_button",
       "default": true,
       "label": "Show wishlist button"
     },
     {
       "type": "checkbox",
       "id": "show_title",
       "default": true,
       "label": "Show title"
     },
     {
       "type": "checkbox",
       "id": "show_price",
       "default": true,
       "label": "Show price"
     },
     {
       "type": "checkbox",
       "id": "show_vendor",
       "default": false,
       "label": "t:sections.main-collection-product-grid.settings.show_vendor.label"
     },

 {
       "type": "checkbox",
       "id": "show_countdown",
       "default": false,
       "label": "Show countdown"
   },

   {
     "type": "checkbox",
     "id": "show_product_rating",
     "default": false,
     "label": "Show product rating"
   },
    {
      "type": "header",
      "content": "Colors"
    },

    {
        "type": "color_scheme",
        "id": "color_scheme",
        "label": "t:sections.all.colors.label",
        "default": "background-1"
      },

    {
      "type": "color_background",
      "id": "hotspot_active_color",
      "label": "Hotspot bullet active color"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
          "type": "paragraph",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        },
		{
          "type": "paragraph",
          "content": "Mobile"
        },
		{
          "type": "range",
          "id": "mobile_padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "mobile_padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        }
  ],
  "blocks": [
    {
      "type": "look",
      "name": "Look",
      "limit": 6,
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        },
        {
          "type": "checkbox",
          "id": "quick_shop",
          "label": "Enable quick shop",
          "default": true
        },
        {
          "type": "range",
          "id": "hotspot_x",
          "min": 5,
          "max": 95,
          "step": 1,
          "default": 15,
          "unit": "%",
          "label": "Horizontal position"
        },
        {
          "type": "range",
          "id": "hotspot_y",
          "min": 5,
          "max": 95,
          "step": 1,
          "default": 15,
          "unit": "%",
          "label": "Vertical  position"
        },
        {
          "type": "color",
          "id": "background_1",
          "label": "Background 1",
          "default": "#ffffff"
        },
        {
          "type": "color_background",
          "id": "background_2_gradient",
          "label": "Background 2 gradient"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Shop the look slider",
      "blocks": [
         {
            "type": "look",
            "settings": {
              "hotspot_x": 39,
              "hotspot_y": 28
            }
         },
        {
            "type": "look",
            "settings": {
              "hotspot_x": 58,
              "hotspot_y": 54
            }
         }
      ]
    }
  ]
}
{% endschema %}
