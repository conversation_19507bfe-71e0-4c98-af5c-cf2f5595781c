{%- liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
-%}
{{ 'section-countdown-banner.css' | asset_url | stylesheet_tag }}

{%- liquid
  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif
-%}

<style>
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
  {% if section.settings.custom_colors %}
  .custom--colors-{{ section.id }}{
    --color-foreground: {{ section.settings.foreground.red }}, {{ section.settings.foreground.green }}, {{ section.settings.foreground.blue }};
    --color-background: {{ section.settings.background.red }}, {{ section.settings.background.green }}, {{ section.settings.background.blue }};
    --color-button: {{ section.settings.accent_1_bg.red }}, {{ section.settings.accent_1_bg.green }}, {{ section.settings.accent_1_bg.blue }};
    --color-button-text: {{ section.settings.accent_1_text.red }}, {{ section.settings.accent_1_text.green }}, {{ section.settings.accent_1_text.blue }};
  }
  {% endif %}
</style>

{% if theme_rtl %}
  {{ 'section-countdown-banner-rtl.css' | asset_url | stylesheet_tag }}
{% endif %}
<div
  class="deals__banner--section {% if section.settings.full_width %} section-{{ section.id }}-padding color-{{ section.settings.color_scheme }} {% if section.settings.custom_colors %} custom--colors-{{ section.id }}{% endif %} {% endif %}"
  data-section-id="{{ section.id }}"
  data-section-type="popup-video"
>
  {% if section.settings.full_width %}
    {% render 'countdown-banner-1', container: container %}
  {% else %}
    {% render 'countdown-banner-2', container: container %}
  {% endif %}
</div>

{% schema %}
 {
   "name": "Countdown banner",
   "settings": [
	{
         "type": "header",
         "content": "General"
       },
     {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
	{
         "type": "image_picker",
         "id": "image",
         "label": "Background image"
       },
       {
         "type": "range",
         "id": "image_overlay_opacity",
         "min": 0,
         "max": 100,
         "step": 10,
         "unit": "%",
         "label": "Image overlay opacity",
         "default": 0
       },
     {
      "type": "checkbox",
      "id": "full_width",
      "label": "Make section full width",
      "default": false
    },
     {
         "type": "header",
         "content": "Video settings"
       },
     {
      "type": "checkbox",
      "id": "video_enable",
      "label": "Enable video",
      "default": true
    },
     {
        "type": "video_url",
        "id": "video_url",
        "accept": [
          "youtube",
          "vimeo"
        ],
        "default": "https:\/\/www.youtube.com\/watch?v=_9VUPq3SxOc",
        "label": "t:sections.video.settings.video_url.label",
        "placeholder": "t:sections.video.settings.video_url.placeholder",
        "info": "t:sections.video.settings.video_url.info"
      },
     {
         "type": "header",
         "content": "Countdown timer"
       },
        {
          "type": "checkbox",
          "id": "countdown_timer",
          "label": "Enable",
          "default": true
        },
        {
        "type": "number",
        "id": "countdown_year",
         "label": "Year",
        "info": "Write the year in the following format: 2023",
        "placeholder": "2023"
      },
      {
          "type": "select",
          "id": "countdown_month",
          "label": "Month",
          "options": [
            {
              "value": "01",
              "label": "Jan"
            },
            {
              "value": "02",
              "label": "Feb"
            },
            {
              "value": "03",
              "label": "Mar"
            },
            {
              "value": "04",
              "label": "Apr"
            },
            {
              "value": "05",
              "label": "May"
            },
            {
              "value": "06",
              "label": "Jun"
            },
            {
              "value": "07",
              "label": "Jul"
            },
            {
              "value": "08",
              "label": "Aug"
            },
            {
              "value": "09",
              "label": "Sept"
            },
            {
              "value": "10",
              "label": "Oct"
            },
            {
              "value": "11",
              "label": "Nov"
            },
            {
              "value": "12",
              "label": "Dec"
            }
          ],
          "default": "01"
        },
        {
          "type": "select",
          "id": "countdown_days",
          "label": "Day",
          "options": [
            {
              "value": "01",
              "label": "1"
            },
            {
              "value": "02",
              "label": "2"
            },
            {
              "value": "03",
              "label": "3"
            },
            {
              "value": "04",
              "label": "4"
            },
            {
              "value": "05",
              "label": "5"
            },
            {
              "value": "06",
              "label": "6"
            },
            {
              "value": "07",
              "label": "7"
            },
            {
              "value": "08",
              "label": "8"
            },
            {
              "value": "09",
              "label": "9"
            },
            {
              "value": "10",
              "label": "10"
            },
            {
              "value": "11",
              "label": "11"
            },
            {
              "value": "12",
              "label": "12"
            },
            {
              "value": "13",
              "label": "13"
            },
            {
              "value": "14",
              "label": "14"
            },
            {
              "value": "15",
              "label": "15"
            },
            {
              "value": "16",
              "label": "16"
            },
            {
              "value": "17",
              "label": "17"
            },
            {
              "value": "18",
              "label": "18"
            },
            {
              "value": "19",
              "label": "19"
            },
            {
              "value": "20",
              "label": "20"
            },
            {
              "value": "21",
              "label": "21"
            },
            {
              "value": "22",
              "label": "22"
            },
            {
              "value": "23",
              "label": "23"
            },
            {
              "value": "24",
              "label": "24"
            },
            {
              "value": "25",
              "label": "25"
            },
            {
              "value": "26",
              "label": "26"
            },
            {
              "value": "27",
              "label": "27"
            },
            {
              "value": "28",
              "label": "28"
            },
            {
              "value": "29",
              "label": "29"
            },
            {
              "value": "30",
              "label": "30"
            },
            {
              "value": "31",
              "label": "31"
            }
          ],
          "default": "01"
        },
	{
         "type": "header",
         "content": "Content settings"
       },
	{
         "type": "text",
         "id": "heading",
         "default": "Deals Of The Day",
         "label": "Heading"
       },
	{
         "type": "text",
         "id": "subheading",
         "default": "Subtitle",
         "label": "Subheading"
       },
      {
        "type": "richtext",
        "id": "text",
        "label": "Text",
        "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post.</p>"
      },
	{
             "type": "paragraph",
             "content": "Button"
           },
		{
             "type": "text",
             "id": "button_label",
             "label": "Button label",
		   "default": "Shop now"
           },
     {
             "type": "checkbox",
             "id": "button_icon",
             "label": "Use arrow icon",
             "default": true
           },
		{
             "type": "url",
             "id": "button_link",
             "label": "Button url"
           },
	    {
             "type": "select",
             "id": "button_type",
             "label": "Button type",
             "default": "primary",
             "options": [
               {
                 "value": "secondary",
                 "label": "Secondary"
               },
               {
                 "value": "primary",
                 "label": "Primary"
               }
             ]
           },
		{
             "type": "select",
             "id": "button_size",
             "label": "Button size",
             "default": "large",
             "options": [
               {
                 "value": "large",
                 "label": "Large"
               },
               {
                 "value": "medium",
                 "label": "Medium"
               },
			        {
                 "value": "small",
                 "label": "Small"
               }
             ]
           },
	{
             "type": "header",
             "content": "Section padding"
           },
           {
                 "type": "paragraph",
                 "content": "Desktop"
               },
               {
                 "type": "range",
                 "id": "padding_top",
                 "min": 0,
                 "max": 150,
                 "step": 5,
                 "unit": "px",
                 "label": "Padding top",
                 "default": 50
               },
               {
                 "type": "range",
                 "id": "padding_bottom",
                 "min": 0,
                 "max": 150,
                 "step": 5,
                 "unit": "px",
                 "label": "Padding bottom",
                 "default": 50
               },
               {
                 "type": "paragraph",
                 "content": "Mobile"
               },
               {
                 "type": "range",
                 "id": "mobile_padding_top",
                 "min": 0,
                 "max": 150,
                 "step": 5,
                 "unit": "px",
                 "label": "Padding top",
                 "default": 50
               },
               {
                 "type": "range",
                 "id": "mobile_padding_bottom",
                 "min": 0,
                 "max": 150,
                 "step": 5,
                 "unit": "px",
                 "label": "Padding bottom",
                 "default": 50
               },
         {
              "type": "header",
              "content": "Colors"
            },

         {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:sections.all.colors.label",
          "default": "background-2"
        },
           {
          "type": "checkbox",
          "id": "custom_colors",
          "label": "Replace with your custom colors",
          "default": true
        },
        {
          "type": "color",
          "id": "foreground",
          "default": "#121212",
          "label": "Foreground color"
        },
        {
          "type": "color",
          "id": "background",
          "default": "#F7F8FC",
          "label": "Background color"
        },
       {
          "type": "color",
          "id": "accent_1_bg",
          "default": "#EE2761",
          "label": "Accent 1 background"
        },
        {
          "type": "color",
          "id": "accent_1_text",
          "default": "#fff",
          "label": "Accent 1 foreground"
        }
],
"presets": [
     {
       "name": "Countdown banner"
     }
 ],
   "disabled_on": {
    "groups": ["header","footer"]
  }
 }
{% endschema %}
