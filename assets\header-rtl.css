.predictive--search-drawer-inner .header-global-search-select svg {
  right: auto !important;
  left: 2rem !important;
}
.predictive-search__item-content {
  padding-left: 0;
  padding-right: 15px;
  text-align: right;
}
.category__search--box,
.predictive__search--drawer--form-inner {
  padding: 2rem 2rem 2rem 0;
}
.predictive__search--drawer--form-inner .search__input_field .input__field {
  padding: 0 4rem 0 2rem;
}
.predictive__search--drawer--form-inner .input__field_form_button {
  right: 0;
  left: auto;
}
.cart-notification-product__image {
  margin-right: 0;
  margin-left: 1.5rem;
}
.cart-notification-product__name {
  padding-right: 0;
  padding-left: 15px;
}
.checkbox-facet-check {
  margin-right: 0;
  margin-left: 1rem;
}
.facet-checkbox .icon-checkmark {
  left: auto;
  right: 0.3rem;
  margin-right: 0;
  margin-left: 1.2rem;
}
ul.header__sub--children__menu {
  left: auto;
  right: 100%;
}
.header__sub--has-children--icon > svg {
  transform: rotate(180deg);
}
.suport__contact > svg {
  margin-left: 7px;
  margin-right: 0;
}
.predictive__search--drawer--form-inner button.reset__button {
  right: auto;
  left: 0;
}
.predictive-search__item--term .icon-arrow {
  transform: rotate(180deg);
}
.header__right--info {
  margin-right: auto;
  margin-left: unset;
}
@media only screen and (min-width: 1200px) {
  .categories__menu {
    padding-right: 0;
    padding-left: 11rem;
  }
}
.categories__menu {
  padding-right: 0;
  padding-left: 10rem;
  margin-right: 0;
  margin-left: 3.2rem;
}
.categories__menu:before {
  border-left: 0.1rem solid rgba(var(--color-foreground), 0.15);
  right: auto;
  left: 0;
  border-right: none;
}
.categories__menu--text {
  margin-left: 0;
  margin-right: 1rem;
}
