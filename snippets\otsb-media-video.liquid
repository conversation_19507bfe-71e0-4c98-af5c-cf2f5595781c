{% comment %}
  Renders product media gallery

  Accepts:
  - video: {Object} Video object (optional)
  - enable_video_autoplay: {<PERSON>olean} Enable autoplay video. Default: false (optional)
  - video_id: {String} Video ID
  - video_alt: {<PERSON>ole<PERSON>} Video alt text.
  - columns_desktop: {Integer} Number of columns on desktop.

  Usage:
  {% render 'media-video', lazy_load: false %}
{% endcomment %}

{%- if enable_video_autoplay -%}
  {%- capture button_play -%}
    <div class="button-play absolute top-1/2 left-1/2 hidden">
      <div class="cursor-pointer absolute z-20 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[60px] h-[60px] md:w-[60px] md:h-[60px] rounded-full p-5 bg-[rgba(var(--image-treatment-text),0.06)] hover:bg-[rgba(var(--image-treatment-text),0.1)] bg-opacity-30 disabled:cursor-not-allowed">
        <span class="pointer-events-none duration-200 bg-button-play absolute w-[16px] h-[20px] top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 text-[rgba(var(--image-treatment-text))]">
          {% render 'otsb-icon-media', icon: 'icon-play', class: 'w-full h-full' %}
        </span>
      </div>
    </div>
  {%- endcapture -%}
  {% if video_type == 'video_select' %}
    {% liquid
      assign class_video = "w-full h-full absolute top-0 left-0 otsb-video video object-cover"
    %}
    <div class="min-h-[1px]">
      {{ video | video_tag: autoplay: enable_video_autoplay, loop: true, loading: 'lazy', class: class_video, poster: '', controls: false | replace: '<img ', '<img loading="lazy" hidden alt="" ' }}
      {% if show_sound_control %}
        <button x-data="{ muted: true }" class="button-sound-control bg-[rgba(var(--image-treatment-text),0.06)] hover:bg-[rgba(var(--image-treatment-text),0.1)] bg-opacity-30 rounded-[30px] flex items-center justify-center absolute right-2 rtl:left-2 rtl:right-auto cursor-pointer w-[35px] h-[35px] md:w-[34px] md:h-[34px]{% if section.settings.product_card_position == 'on' and section.settings.video_layout == 'aspect-[9/16]' %} top-2 {% else %} bottom-2 {% endif %}" @click.stop="$store.xVideo.toggleMute($el); muted=!muted" aria-label="button sound control">
          <span class="w-[18px] h-[18px] lg:w-[18px] lg:h-[18px] text-[rgba(var(--image-treatment-text))]" x-show="muted">
            {% render 'otsb-icon-media', icon: 'icon-unmute' %}
          </span>
           <span class="w-[18px] h-[18px] lg:w-[18px] lg:h-[18px] text-[rgba(var(--image-treatment-text))]" x-show="!muted" x-cloak>
            {% render 'otsb-icon-media', icon: 'icon-mute' %}
          </span>
        </button>
      {% endif %}
      {{ button_play }}
    </div>
  {% endif %}
{%- else -%}
  {%- capture button_play -%}
    <div class="button-play absolute top-1/2 left-1/2">
      <div class="cursor-pointer absolute z-20 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[60px] h-[60px] md:w-[60px] md:h-[60px] rounded-full p-5 bg-[rgba(var(--image-treatment-text),0.06)] hover:bg-[rgba(var(--image-treatment-text),0.1)] bg-opacity-30 disabled:cursor-not-allowed">
        <span class="pointer-events-none duration-200 bg-button-play absolute w-[16px] h-[20px] top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 text-[rgba(var(--image-treatment-text))]">
          {% render 'otsb-icon-media', icon: 'icon-play', class: 'w-full h-full' %}
        </span>
      </div>
    </div>
  {%- endcapture -%}
  {%- liquid
    if video_type == 'video_select' and cover_image == blank
      assign cover_image = video.preview_image
    endif
    if video_type == 'video_select' and image_mobile == blank
      if cover_image == blank
        assign image_mobile = video.preview_image
      else 
        assign image_mobile = cover_image
      endif
    endif
    if video_type != 'video_select' and cover_image != blank and image_mobile == blank
      assign image_mobile = cover_image
    endif
  -%}
  {% if cover_image != blank or image_mobile != blank %}
    {% if cover_image != blank %}
      {% if show_hero %}
        <div class="hidden">
          {{ cover_image | image_url: width: 1500 | image_tag: widths: '375, 450, 750, 900, 1100, 1500', preload: true, loading: 'lazy' }}
        </div>
      {% endif %}
      <picture>
        <source 
          srcset="{{ cover_image | image_url: width: 375 }} 375w,
          {{ cover_image | image_url: width: 450 }} 450w,
          {{ cover_image | image_url: width: 750 }} 750w,
          {{ cover_image | image_url: width: 900 }} 900w,
          {{ cover_image | image_url: width: 1100 }} 1100w,
          {{ cover_image | image_url: width: 1500 }} 1500w" 
          media="(max-width: 767px)" 
          width="{{ cover_image.width }}" 
          height="{{ cover_image.height }}"
        />
        <img
          srcset="{{ cover_image | image_url: width: 375 }} 375w,
          {{ cover_image | image_url: width: 450 }} 450w,
          {{ cover_image | image_url: width: 750 }} 750w,
          {{ cover_image | image_url: width: 900 }} 900w,
          {{ cover_image | image_url: width: 1100 }} 1100w,
          {{ cover_image | image_url: width: 1500 }} 1500w,
          {{ cover_image | image_url: width: 1780 }} 1780w,
          {{ cover_image | image_url: width: 2000 }} 2000w,
          {{ cover_image | image_url: width: 3000 }} 3000w,
          {{ cover_image | image_url: width: 3840 }} 3840w"
          sizes="100vw"
          src="{{ cover_image | image_url: width: 3840 }}"
          alt="{{ cover_image.alt | escape }}"
          class="object-cover z-10 absolute top-0 left-0 h-full w-full img-thumbnail image-hover otsb-hidden md:flex"
          {% if show_hero %}
            loading="eager"
            fetchpriority="high"
            decoding="sync"
          {% else %}
            loading="lazy"
          {% endif %} 
          width="{{ cover_image.width }}"
          height="{{ cover_image.height }}"
          style="object-position:{{ cover_image.presentation.focal_point }};"
        >
      </picture>
    {% elsif video_type == 'youtube' %}
      <div
        class="absolute h-full w-full otsb-video video"
        aria-label="image-video">
        <picture>
          <source type="image/webp" srcset="{{ video_id | prepend: 'https://i.ytimg.com/vi_webp/' | append: '/maxresdefault.webp' }}">
          <source type="image/jpeg" srcset="{{ video_id | prepend: 'https://i.ytimg.com/vi/' | append: '/maxresdefault.jpg' }}">
          <img src="{{ video_id | prepend: 'https://i.ytimg.com/vi_webp/' | append: '/maxresdefault.webp' }}" {% if show_hero %}loading="eager" fetchpriority="high" decoding="sync"{% else %}loading="lazy"{% endif %}  class="w-full h-full object-cover" alt="{{ video_alt }}" width="1280" height="890"/>
        </picture>
      </div>
    {% elsif video_type == 'vimeo'%}
      {%- capture options -%}
        {
          'alt': '{{ video_alt }}',
          'width': 1280
        }
      {%- endcapture -%}
      <div class="absolute external-video otsb-external-video w-full h-full bg-black">
        <div class="h-full w-full" x-init="$store.xVideo.renderVimeoFacade($el, '{{ video_id }}', {{ options }})"></div>
      </div>
    {% endif %}
    
    {%- if image_mobile != blank -%}
      {% if show_hero %}
        <div class="otsb-hidden">
          {{ image_mobile | image_url: width: 1500 | image_tag: widths: '375, 450, 750, 900, 1100, 1500', preload: true, loading: 'lazy' }}
        </div>
      {% endif %}
      <img
        srcset="{{ image_mobile | image_url: width: 375 }} 375w,
        {{ image_mobile | image_url: width: 450 }} 450w,
        {{ image_mobile | image_url: width: 750 }} 750w,
        {{ image_mobile | image_url: width: 900 }} 900w,
        {{ image_mobile | image_url: width: 1100 }} 1100w,
        {{ image_mobile | image_url: width: 1500 }} 1500w"
        sizes="100vw"
        src="{{ image_mobile | image_url: width: 750 }}"
        alt="{{ image_mobile.alt | escape }}"
        class="object-cover z-10 absolute top-0 left-0 h-full w-full md:hidden animate_transition_slide__image img-thumbnail"
        {% if show_hero %}
          loading="eager"
          fetchpriority="high"
          decoding="sync"
        {% else %}
          loading="lazy"
        {% endif %}   
        width="{{ image_mobile.width }}"
        height="{{ image_mobile.height }}"
        style="object-position: {{ image_mobile.presentation.focal_point }}"
        x-intersect.once="$el.classList.add('active')"
      />  
    {%- endif %}
    {{ button_play }}
    {% if video_type == 'video_select' %}
      <div x-data="{isHovered: false}" class="min-h-[1px]">
        {{ video
          | video_tag:
            image_size: "1100x",
            loop: false,
            controls: true,
            muted: false,
            class: "w-full h-full absolute top-0 left-0 otsb-video video object-cover",
            alt: video_alt,
            preload: false
            | replace: '<video ', '<video x-on:mouseover="isHovered = true" x-on:mouseleave="isHovered = false" :controls="isHovered" '
        }}
      </div>
    {% endif %}
  {% else %}
    {% if video_type == 'youtube' %}
      {% comment %}theme-check-disable RemoteAsset{% endcomment %}
      <div
        class="absolute h-full w-full otsb-video video"
        aria-label="image-video">
        <picture>
          <source type="image/webp" srcset="{{ video_id | prepend: 'https://i.ytimg.com/vi_webp/' | append: '/maxresdefault.webp' }}">
          <source type="image/jpeg" srcset="{{ video_id | prepend: 'https://i.ytimg.com/vi/' | append: '/maxresdefault.jpg' }}">
          <img src="{{ video_id | prepend: 'https://i.ytimg.com/vi_webp/' | append: '/maxresdefault.webp' }}" {% if show_hero %}loading="eager" fetchpriority="high" decoding="sync"{% else %}loading="lazy"{% endif %}  class="w-full h-full object-cover" alt="{{ video_alt }}" width="1280" height="890"/>
        </picture>
      </div>
      {% comment %}theme-check-enable RemoteAsset{% endcomment %}
      {{ button_play }}
    {% elsif video_type == 'vimeo' %}
      {%- capture options -%}
        {
          'alt': '{{ video_alt }}',
          'width': 1280
        }
      {%- endcapture -%}
      <div class="absolute otsb-external-video external-video w-full h-full bg-black">
        <div class="h-full w-full" x-init="$store.xVideo.renderVimeoFacade($el, '{{ video_id }}', {{ options }})"></div>
        {{ button_play }}
      </div>
    {% elsif video_type == 'video_select' %}
    <div x-data="{isHovered: false}">
      {{ video
        | video_tag:
          image_size: "1100x",
          loop: false,
          controls: true,
          muted: false,
          class: "w-full h-full absolute otsb-video video object-cover",
          alt: video_alt,
          preload: false
          | replace: '<video ', '<video x-on:mouseover="isHovered = true" x-on:mouseleave="isHovered = false" :controls="isHovered" '
      }}
      {{ button_play }}
    </div>
    {% endif %}
  {% endif %}
{%- endif -%}
