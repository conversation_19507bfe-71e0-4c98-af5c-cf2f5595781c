/* Joining Process Timeline Styles */
/* Converted from Tailwind CSS for Shopify theme compatibility */

.joining-process-section {
  position: relative;
  z-index: 10;
  padding-top: 3rem;
  padding-bottom: 3rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(to bottom right, rgba(239, 246, 255, 0.3));
}

@media (min-width: 768px) {
  .joining-process-section {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

/* Container */
.joining-process-container {
  max-width: 100%;
  margin: 0 auto;
  padding-left: 2rem;
  padding-right: 2rem;
}

@media (min-width: 640px) {
  .joining-process-container {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}

@media (min-width: 1024px) {
  .joining-process-container {
    padding-left: 4rem;
    padding-right: 4rem;
  }
}

/* Heading Styles */
.joining-process-heading {
  font-size: 1.875rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1.5rem;
  color: black;
}

@media (min-width: 768px) {
  .joining-process-heading {
    font-size: 2.25rem;
  }
}

.joining-process-heading-highlight {
  color: #dc2626;
  font-weight: 700;
}

/* Desktop Timeline */
.joining-process-desktop {
  display: none;
  position: relative;
  max-width: 100%;
  margin: 0 auto 4rem;
  width: 100%;
}

@media (min-width: 1024px) {
  .joining-process-desktop {
    display: block;
  }
}

/* Progress Line */
.joining-process-progress-line {
  position: absolute;
  top: 2.5rem;
  left: 4rem;
  right: 4rem;
  height: 0.25rem;
  background: linear-gradient(to right, #93c5fd, #5eead4, #fde047);
  z-index: 0;
  border-radius: 9999px;
}

@media (min-width: 1200px) {
  .joining-process-progress-line {
    left: 6rem;
    right: 6rem;
  }
}

/* Steps Grid */
.joining-process-steps-grid {
  position: relative;
  z-index: 10;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  width: 100%;
}

@media (min-width: 1200px) {
  .joining-process-steps-grid {
    gap: 3rem;
  }
}

/* Step Container */
.joining-process-step {
  position: relative;
  cursor: pointer;
  transition: all 0.5s ease;
}

.joining-process-step:hover {
  transform: translateY(-0.5rem);
}

/* Step Circle Container */
.joining-process-circle-container {
  position: relative;
  z-index: 20;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 5rem;
  margin-bottom: 2rem;
}

/* Step Circle */
.joining-process-circle {
  width: 5rem;
  height: 5rem;
  border: 2px solid;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.5s ease;
}

.joining-process-step:hover .joining-process-circle {
  transform: scale(1.1);
}

/* Step Icon */
.joining-process-icon {
  font-size: 1.875rem;
  transition: transform 0.3s ease;
}

.joining-process-step:hover .joining-process-icon {
  transform: scale(1.1);
}

/* Step Number Badge */
.joining-process-badge {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
  font-weight: 700;
}

/* Content Card */
.joining-process-card {
  border: 2px solid;
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  height: 13rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.5s ease;
  box-shadow: none !important;
}

.joining-process-step:hover .joining-process-card {
  transform: translateY(-0.5rem);
  box-shadow: none !important;
}

/* Category Label */
.joining-process-category {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
  opacity: 0.8;
}

@media (max-width: 1023px) {
  .joining-process-category {
    font-size: 0.7rem;
  }
}

/* Title */
.joining-process-title {
  font-weight: 800;
  margin-bottom: 0.5rem;
  color: #111827;
}

.joining-process-title--large {
  font-size: 1.375rem;
  line-height: 1.2;
}

.joining-process-title--small {
  font-size: 1.25rem;
  line-height: 1.2;
}

@media (max-width: 1023px) {
  .joining-process-title--large {
    font-size: 1.25rem;
  }
  
  .joining-process-title--small {
    font-size: 1.125rem;
  }
}

/* Description */
.joining-process-description {
  color: #4b5563;
  line-height: 1.5;
  font-weight: 400;
}

.joining-process-description--large {
  font-size: 0.95rem;
}

.joining-process-description--small {
  font-size: 0.875rem;
}

@media (max-width: 1023px) {
  .joining-process-description--large {
    font-size: 0.95rem;
  }
  
  .joining-process-description--small {
    font-size: 0.875rem;
  }
}

/* Duration and Action Container */
.joining-process-meta {
  padding-top: 0.5rem;
  border-top: 1px solid #e5e7eb;
  margin-left: -0.5rem;
  margin-right: -0.5rem;
  margin-top: auto;
}

.joining-process-meta-content {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-between;
  gap: 0.75rem;
  font-size: 0.75rem;
}

/* Duration */
.joining-process-duration {
  color: #2563eb;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Action */
.joining-process-action {
  color: #16a34a;
  font-weight: 500;
  border: 1px solid #4ade80;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
}

/* Connection Arrow */
.joining-process-arrow {
  position: absolute;
  top: 2.5rem;
  right: -1.5rem;
  z-index: 15;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 0.25rem;
}

@media (min-width: 1200px) {
  .joining-process-arrow {
    right: -2rem;
  }
}

.joining-process-arrow svg {
  width: 2rem;
  height: 1.5rem;
  color: #d1d5db;
  transition: all 0.3s ease;
  stroke-width: 1.5;
}

.joining-process-step:hover .joining-process-arrow svg {
  color: #9ca3af;
  transform: translateX(2px);
}

/* Mobile Timeline */
.joining-process-mobile {
  display: block;
}

@media (min-width: 1024px) {
  .joining-process-mobile {
    display: none;
  }
}

.joining-process-mobile-steps {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Mobile Step */
.joining-process-mobile-step {
  position: relative;
}

/* Mobile Connection Line */
.joining-process-mobile-line {
  position: absolute;
  left: 2.5rem;
  top: 5rem;
  width: 0.125rem;
  height: calc(100% - 2rem);
  background: linear-gradient(to bottom, #d1d5db, #e5e7eb);
  z-index: 0;
  border-radius: 9999px;
}

/* Mobile Arrow */
.joining-process-mobile-arrow {
  position: absolute;
  left: 2.4375rem;
  bottom: 1rem;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.joining-process-mobile-arrow svg {
  width: 1.5rem;
  height: 1.5rem;
  color: #d1d5db;
  stroke-width: 1.5;
}

/* Mobile Step Content */
.joining-process-mobile-content {
  display: flex;
  gap: 1.5rem;
  position: relative;
  z-index: 10;
}

/* Mobile Circle */
.joining-process-mobile-circle {
  flex-shrink: 0;
}

.joining-process-mobile-circle .joining-process-icon {
  font-size: 1.5rem;
}

.joining-process-mobile-circle .joining-process-badge {
  width: 1.75rem;
  height: 1.75rem;
  font-size: 0.75rem;
}

/* Mobile Card */
.joining-process-mobile-card-container {
  flex: 1;
  height: 100%;
}

.joining-process-mobile-card {
  border: 2px solid;
  border-radius: 0.75rem;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 8.75rem;
  box-shadow: none !important;
}

.joining-process-mobile-meta {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #e5e7eb;
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}

.joining-process-mobile-meta-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 0.75rem;
  font-size: 0.75rem;
  flex-direction: column;
}

/* Color Variants */
.joining-process-blue {
  background-color: #eff6ff;
  border-color: #bfdbfe;
}

.joining-process-blue-text {
  color: #1e40af;
}

.joining-process-blue-gradient {
  background: linear-gradient(to right, #3b82f6, #1d4ed8);
}

.joining-process-teal {
  background-color: #f0fdfa;
  border-color: #99f6e4;
}

.joining-process-teal-text {
  color: #115e59;
}

.joining-process-teal-gradient {
  background: linear-gradient(to right, #14b8a6, #0f766e);
}

.joining-process-orange {
  background-color: #fff7ed;
  border-color: #fed7aa;
}

.joining-process-orange-text {
  color: #9a3412;
}

.joining-process-orange-gradient {
  background: linear-gradient(to right, #f97316, #c2410c);
}

.joining-process-yellow {
  background-color: #fefce8;
  border-color: #fde047;
}

.joining-process-yellow-text {
  color: #a16207;
}

.joining-process-yellow-gradient {
  background: linear-gradient(to right, #eab308, #ca8a04);
}

.joining-process-purple {
  background-color: #faf5ff;
  border-color: #d8b4fe;
}

.joining-process-purple-text {
  color: #6b21a8;
}

.joining-process-purple-gradient {
  background: linear-gradient(to right, #a855f7, #7c3aed);
}

.joining-process-green {
  background-color: #f0fdf4;
  border-color: #bbf7d0;
}

.joining-process-green-text {
  color: #166534;
}

.joining-process-green-gradient {
  background: linear-gradient(to right, #22c55e, #16a34a);
}

/* Responsive adjustments */
@media (max-width: 1023px) {
  .joining-process-steps-grid {
    grid-template-columns: 1fr;
  }
}