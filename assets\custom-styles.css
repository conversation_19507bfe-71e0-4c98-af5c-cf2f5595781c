/* Product slider pagination styles */
.product-slider--pagination {
  position: relative !important;
  margin-top: 30px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.product-slider--pagination.swiper-pagination-horizontal {
  bottom: 0 !important;
  width: 100% !important;
}

.product-slider--pagination .swiper-pagination-bullet {
  width: 10px !important;
  height: 10px !important;
  background-color: #d1d1d1 !important;
  opacity: 1 !important;
  margin: 0 6px !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  position: relative !important;
  border: none !important;
  outline: none !important;
}

.product-slider--pagination .swiper-pagination-bullet:hover {
  background-color: #b1b1b1 !important;
  transform: scale(1.2) !important;
}

.product-slider--pagination .swiper-pagination-bullet-active {
  width: 12px !important;
  height: 12px !important;
  background-color: #333 !important;
  transform: scale(1.2) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* Optional: Add a subtle animation for the active bullet */
@keyframes pulse {
  0% {
    transform: scale(1.2) !important;
  }
  50% {
    transform: scale(1.3) !important;
  }
  100% {
    transform: scale(1.2) !important;
  }
}

.product-slider--pagination .swiper-pagination-bullet-active {
  animation: pulse 2s infinite !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .product-slider--pagination {
    margin-top: 20px !important;
  }
  
  .product-slider--pagination .swiper-pagination-bullet {
    width: 8px !important;
    height: 8px !important;
    margin: 0 4px !important;
  }
  
  .product-slider--pagination .swiper-pagination-bullet-active {
    width: 10px !important;
    height: 10px !important;
  }
}
.hero__slider--inner{
border-radius: 0 0 0px 0px;
}
.section-heading__sub_title{
  
    font-weight: 600 !important;
  
}
h2.section-heading {
  font-weight: 600 !important;
}
.rich-text:not(.color-background-1){
  padding-bottom:0;
}
/* Modern Swiper Pagination Styles */
.swiper-pagination {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 25px;
  width: 100%;
}

.swiper-pagination-horizontal {
  bottom: 0;
  height: 20px;
}

/* Default Bullet Style */
.swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background-color: #ccc;
  opacity: 0.8;
  margin: 0 6px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.swiper-pagination-bullet:hover {
  opacity: 1;
  transform: scale(1.2);
}

.swiper-pagination-bullet-active {
  background-color: #4a4a4a;
  opacity: 1;
  transform: scale(1.2);
}

/* Alternative Style 1: Minimal Dots */
.minimal-dots .swiper-pagination-bullet {
  width: 8px;
  height: 8px;
  background-color: #999;
}

.minimal-dots .swiper-pagination-bullet-active {
  background-color: #333;
  transform: scale(1.3);
}

/* Alternative Style 2: Square Bullets */
.square-bullets .swiper-pagination-bullet {
  border-radius: 3px;
  width: 14px;
  height: 14px;
  background-color: #aaa;
}

.square-bullets .swiper-pagination-bullet-active {
  background-color: #555;
  transform: scale(1.1) rotate(45deg);
}

/* Alternative Style 3: Line Indicator */
.line-indicator .swiper-pagination-bullet {
  width: 24px;
  height: 4px;
  border-radius: 2px;
  background-color: #bbb;
}

.line-indicator .swiper-pagination-bullet-active {
  background-color: #666;
  transform: scale(1.2);
}

/* Alternative Style 4: Outline Style */
.outline-style .swiper-pagination-bullet {
  background-color: transparent;
  border: 2px solid #ccc;
}

.outline-style .swiper-pagination-bullet-active {
  background-color: #4a4a4a;
  border-color: #4a4a4a;
}

/* Alternative Style 5: Animated Fill */
.animated-fill .swiper-pagination-bullet {
  position: relative;
  overflow: hidden;
  background-color: #f0f0f0;
}

.animated-fill .swiper-pagination-bullet-active {
  background-color: #4a4a4a;
  animation: fillBullet 0.5s ease-out;
}

@keyframes fillBullet {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1.2);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    margin: 0 4px;
  }
  
  .square-bullets .swiper-pagination-bullet {
    width: 12px;
    height: 12px;
  }
  
  .line-indicator .swiper-pagination-bullet {
    width: 20px;
    height: 3px;
  }
}
:root{
  --primary-button-hover-background:"163, 87, 65, 1" !important;
}
@media (min-width: 280px) and (max-width: 335px) {
  .swiper-pagination {
    margin-top: 0 !important;
  }
}

@media (min-width: 336px) and (max-width: 350px) {
  .swiper-pagination {
    margin-top: 5px !important;
  }
}
@media (min-width: 350px) and (max-width: 380px) {
  .swiper-pagination {
    margin-top: 15px !important;
  }
}
@media (min-width: 380px) and (max-width: 1024px) {
  .hero__slider--wrapper {
   height: 165px !important;
  }
}

}
@media (max-width: 1024px){
  #shopify-section-template--19581138632867__category_showcase_FTeMex{
    display: none !important;
  }
  .slideshow__media{
    border-radius: 0 0 20px 20px !important;
  }
}
.product--thumbnail_slider .col-lg-7{
  max-width: 65vh !important;
}
.product--thumbnail_slider .col-lg-7 ul{
  max-width: 65vh !important;
  max-height: 65vh !important;
  overflow: hidden!important;
}
[data-section-type="product-tab"] img{
  max-width: 80vw !important;
}
.product.row.row-cols-md-2.row-cols-1.product--large.product--thumbnail_slider {
  display: flex;
  justify-content: center;
}
@media (max-width: 1024px){
  .header__logo{
    margin-right:auto !important;
  }
}
@media (min-width: 1024px){
  .mega__menu--full-column .header__mega_menu--inner{
    display:none !important;
  }
}
#product_all .rembg-container{
  display:none;
}
.otsb__root .otsb-h4, .otsb__root h4{
  font-size:23px !important;
}
.about__thumb--play__icon{
  display:none !important;
}
@media (max-width: 1024px){
  .otsb__root .otsb-h2.heading-size--heading_FrFzBW , #slide__slide_MicpPe .ez_heading h2 , .section-heading__title{
    font-size: 29px !important;
    font-weight: 600 !Important;
  }

}
.mobile__navigation--bar{
  display:none !important;
}
@media (max-width: 1024px){
.tcustomizer-enabled .mobile__navigation--bar{
   display:block !important;
  }
  #MainContent{
    overflow-x: hidden !important;
  }
  .swiper-slide .sllideshow__content{
    display:none !important;
  }
}
.mobile__navigate--item-icon{
  display:none !important;
}
.otsb-image-comparison{
  background: none !important;
}
#slide__slide_MicpPe .ez_text{
  background:none !important;
  border:none !important;
}

