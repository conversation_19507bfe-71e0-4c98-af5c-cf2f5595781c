{% liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif

  assign free_shipping_rate = settings.free_shipping_amount | times: 100
  assign free_shipping_only_amount = settings.free_shipping_only_amount | times: 100
  assign free_shiping = 'shipping__threshold--red'
  if cart.total_price < free_shipping_rate
    assign percentage = cart.total_price | times: 100 | divided_by: free_shipping_rate
  else
    assign percentage = 100
  endif
%}

<style>
      {% if percentage < 50 %}
      .shipping__threshold--color{
        --shipping-bar-color: {{ settings.shipping_rate_low }};
      }
        {% elsif percentage >= 50 and percentage < 75 %}
    .shipping__threshold--color{
        --shipping-bar-color: {{ settings.shipping_rate_medium }};
      }

        {% elsif percentage >= 75 and percentage < 100  %}
          .shipping__threshold--color{
        --shipping-bar-color: {{ settings.shipping_rate_high }};
      }
        {% else %}
         .shipping__threshold--color{
        --shipping-bar-color: {{ settings.shipping_rate_sucess }};
      }
        {% endif %}

        {% if theme_rtl %}
          .cart__shipping--threshold {
            padding: 3rem 0 2rem 3rem;
           }
        {% else %}
          .cart__shipping--threshold {
              padding: 3rem 3rem 2rem 0;
          }
        {% endif %}
              .shipping--bar-message {
                  margin-bottom: 1rem;
              }
              .percent_shipping_bar_wrapper {
      border-radius: 5px;
      margin-bottom: 1.2rem;
      position: relative;
      height: 0.8rem;
      background: rgba(var(--color-foreground), 0.15);
  }
            .percent_shipping_bar {
              display: block;
              height: 100%;
              position: relative;
              border-radius: 5px;
              transition: all .4s;
              animation: 5s linear infinite progress;
              background-color: var(--shipping-bar-color);
              -webkit-background-image: linear-gradient(135deg,rgba(255,255,255,.15) 0,rgba(255,255,255,.15) 25%,rgba(255,255,255,0) 25%,rgba(255,255,255,0) 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,rgba(255,255,255,0) 75%,rgba(255,255,255,0) 100%);
              -moz-background-image: linear-gradient(135deg,rgba(255,255,255,.15) 0,rgba(255,255,255,.15) 25%,rgba(255,255,255,0) 25%,rgba(255,255,255,0) 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,rgba(255,255,255,0) 75%,rgba(255,255,255,0) 100%);
              -ms-background-image: linear-gradient(135deg,rgba(255,255,255,.15) 0,rgba(255,255,255,.15) 25%,rgba(255,255,255,0) 25%,rgba(255,255,255,0) 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,rgba(255,255,255,0) 75%,rgba(255,255,255,0) 100%);
              background-image: linear-gradient(135deg,rgba(255,255,255,.15) 0,rgba(255,255,255,.15) 25%,rgba(255,255,255,0) 25%,rgba(255,255,255,0) 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,rgba(255,255,255,0) 75%,rgba(255,255,255,0) 100%);
              background-size: 40px 40px;
          }
        {% if theme_rtl %}
          .free-shipping--icon-wrapper {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            border: 1px solid var(--shipping-bar-color);
            transition: all .4s;
            background: #fff;
            position: absolute;
            left: -15px;
            top: 50%;
            transform: translate(50%,-50%);
            margin-top: -1px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
          {% else %}
          .free-shipping--icon-wrapper {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            border: 1px solid var(--shipping-bar-color);
            transition: all .4s;
            background: #fff;
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translate(50%,-50%);
            margin-top: -1px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
          {% endif %}
    .free-shipping--icon{
      width: 1.5rem;
      color: var(--shipping-bar-color);
    }
        @-moz-keyframes progress {
          from {
            background-position: 0 0
          }

          to {
            background-position: -60px -60px
          }
        }

        @-ms-keyframes progress {
          from {
            background-position: 0 0
          }

          to {
            background-position: -60px -60px
          }
        }

        @-o-keyframes progress {
          from {
            background-position: 0 0
          }

          to {
            background-position: -60px -60px
          }
        }

        @keyframes progress {
          from {
            background-position: 0 0
          }

          to {
            background-position: -60px -60px
          }
        }

.free-shipping--icon-wrapper svg {
    width: 19px;
    height: 19px;
    fill: var(--shipping-bar-color);
}
.shipping--bar-message.sh_free_text {
    animation: flash .8s linear .8s 2 both;
}
.shipping--bar-message {
    font-weight: 600;
}
p#cart-notification-subtotal {
    font-weight: 600;
}
  
</style>

<div class="cart__shipping--threshold shipping__threshold--color">
  <div class="cart__shipping--threshold-inner">
    
    <div class="percent_shipping_bar_wrapper">
      <div
        class="percent_shipping_bar"
        style="width: {{ percentage }}%;"
      >
        <span class="free-shipping--icon-wrapper">
            <svg id="fi_17865314" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg" data-name="Layer 1"><path d="m47.577 23.114-2.35-7.083c-.61-1.841-2.3-3.031-4.304-3.031h-6.292l.23-1.875c.102-.81-.138-1.612-.657-2.201-.518-.587-1.279-.924-2.09-.924h-24.237c-1.6 0-3.052 1.293-3.237 2.873l-.128 1c-.069.548.317 1.049.865 1.119.555.08 1.05-.317 1.119-.865l.129-1.011c.069-.595.654-1.116 1.252-1.116h24.237c.24 0 .449.**************.162.204.384.173.632l-2.342 19.122h-26.252c0-.496-.355-.93-.86-.992-.549-.07-1.048.321-1.115.869l-.373 3.007c-.096.813.149 1.616.672 2.203.523.589 1.261.913 2.076.913h1.936c.015.993.343 1.919.989 2.647.771.872 1.869 1.353 3.089 1.353 2.288 0 4.368-1.765 4.836-4h17.327c.015.993.343 1.918.987 2.646.772.873 1.87 1.354 3.091 1.354 2.287 0 4.367-1.765 4.836-4h2.153c1.618 0 3.04-1.265 3.237-2.878l.768-6.263c.159-1.297.04-2.557-.354-3.745zm-2.145-.114h-6.026l.475-3.878c.007-.051.089-.122.13-.122h4.094zm-11.045-8h6.537c1.145 0 2.065.636 2.405 1.661l.113.339h-3.431c-1.057 0-1.985.825-2.114 1.878l-.49 4c-.067.548.098 1.092.453 1.493.354.399.869.628 1.416.628h6.674c.061.526.065 1.065-.003 1.616l-.415 3.384h-12.983l1.837-15zm-1.1 19h-1.228l.245-2h2.614c-.7.519-1.267 1.206-1.631 2zm-3.242 0h-14.539c-.158-.607-.44-1.17-.861-1.646-.118-.133-.256-.239-.388-.354h16.034l-.245 2zm-23.008 0h-2.355c-.24 0-.441-.084-.582-.242-.146-.163-.209-.391-.181-.635l.139-1.123h4.585c-.694.516-1.251 1.201-1.607 2zm6.596 1.378c-.174 1.422-1.519 2.622-2.937 2.622-.64 0-1.204-.241-1.592-.679-.394-.444-.566-1.048-.487-1.699.175-1.422 1.52-2.622 2.938-2.622.64 0 1.205.241 1.592.679.394.444.566 1.048.486 1.699zm26.241 0c-.175 1.422-1.52 2.622-2.937 2.622-.64 0-1.205-.241-1.593-.679-.394-.444-.566-1.048-.486-1.699.175-1.422 1.52-2.622 2.937-2.622.64 0 1.205.241 1.593.679.394.444.566 1.048.486 1.699zm4.053-1.378h-2.18c-.158-.607-.44-1.17-.861-1.646-.118-.133-.256-.239-.388-.354h4.79l-.108.878c-.073.598-.659 1.122-1.253 1.122z"></path><path d="m9.03 26c0-.553-.447-1-1-1h-7.03c-.553 0-1 .447-1 1s.447 1 1 1h7.03c.553 0 1-.447 1-1z"></path><path d="m4.087 20c-.553 0-1 .448-1 1s.447 1 1 1h4.561c.553 0 1-.448 1-1s-.447-1-1-1z"></path><path d="m2.175 17h8.08c.553 0 1-.448 1-1s-.447-1-1-1h-8.08c-.553 0-1 .448-1 1s.447 1 1 1z"></path></svg>
        </span>
      </div>
    </div>
    
    {% if cart.total_price < free_shipping_rate %}
      {% assign cartTotal = cart.total_price | money_without_currency %}

      {% assign toFreeShip = cartTotal | times: 100 | minus: free_shipping_rate | abs %}

      <div class="shipping--bar-message">
        {% if toFreeShip < free_shipping_only_amount %}
          {{ 'general.free_shipping_message.free_shipping_only_title' | t -}}
        {%- endif %}

        <span>
          {{- cart.currency.symbol -}}
          {{ toFreeShip | money_without_currency -}}
        </span>
        {{ 'general.free_shipping_message.free_shipping_title' | t }}
      </div>
    {% else %}
      <div class="shipping--bar-message sh_free_text">{{ 'general.free_shipping_message.free_shipping_success' | t }}</div>
    {% endif %}
    
  </div>
</div>
