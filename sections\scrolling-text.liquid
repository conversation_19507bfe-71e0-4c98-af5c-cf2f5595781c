{%- liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
-%}
{{ 'scrolling-text.css' | asset_url | stylesheet_tag }}
<script src="{{ 'scrolling-text.js' | asset_url }}" defer></script>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
    margin-top: {{ section.settings.mobile_margin_top }}px;
    margin-bottom: {{ section.settings.mobile_margin_bottom }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }
  {% if section.settings.custom_colors %}
    .custom--color-{{ section.id }}{
      --color-foreground: {{ section.settings.foreground.red }}, {{ section.settings.foreground.green }}, {{ section.settings.foreground.blue }};
      --color-background: {{ section.settings.background.red }}, {{ section.settings.background.green }}, {{ section.settings.background.blue }};
    }
    {% endif %}


.scrolling--item__inner.seprator_image_active {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    gap: 19px;
    margin: 0 10px;
    align-items: center;
}
span.scrool_icon {
    display: flex;
    align-items: center;
}

marquee-scroll.scrolling--text.sec_bdr_on {
    border-bottom: 1px solid #ddd;
    border-top: 1px solid #ddd;
    padding: 25px 0;
}
span.scrool_icon img.scrolling__image {
    width: 60px;
    height: auto;
}
  
{%- endstyle -%}

{% if theme_rtl %}
  {{ 'scrolling-text-rtl.css' | asset_url | stylesheet_tag }}
{% endif %}

<div class="section-{{ section.id }}-padding color-{{ section.settings.color_scheme }} {% if section.settings.custom_colors  %} custom--color-{{ section.id }} {% endif %}">
  <marquee-scroll
    class="scrolling--text scrolling--text--{{ section.settings.direction }} {% if section.settings.sec_border_enable %} sec_bdr_on {% endif %}"
    style="--duration: {{ section.settings.speed }}s"
  >
    <div class="scrolling--item scrolling--animated">
      {%- for block in section.blocks -%}
        <div
          class="scrolling--item__inner {{ section.settings.text_size }} {% if block.settings.text_stroke %} scrolling--item__text--stroke {% endif %}  {% if section.settings.separetor_image %} seprator_image_active {% endif %}"
          {{ block.shopify_attributes }}
        >
          <span class="scrolling--text__content"> {{ block.settings.text }}</span>
         {% if section.settings.separetor_image != blank %}
          <span class="scrool_icon">
              {%- assign img_alt = section.settings.separetor_image.alt | escape -%}
              {%- assign img_height = 60 | divided_by: section.settings.separetor_image.aspect_ratio %}
              {% capture widths %} {{ "60" }}, {{ "60" | times: 1.5 | round }}, {{ "60" | times: 2 }}{% endcapture %}
              {% capture sizes %}(max-width: {{ 60 | times: 2 }}px) 50vw, {{ 60 }}px{% endcapture %}
              {{
                section.settings.separetor_image
                | image_url: width: 100
                | image_tag:
                  class: 'scrolling__image',
                  alt: img_alt,
                  widths: widths,
                  sizes: sizes,
                  height: img_height,
                  width: '60'
              }}
          </span>
          {% endif %} 
        </div>
      {%- endfor -%}
    </div>
  </marquee-scroll>
</div>

{% schema %}
{
  "name": "Scrolling text",
  "class": "section",
  "settings": [
    {
      "type": "range",
      "id": "speed",
      "min": 2,
      "max": 30,
      "step": 1,
      "unit": "s",
      "label": "speed",
      "default": 15
    },
    {
      "type": "select",
      "id": "direction",
      "label": "Direction",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ]
    },
     {
     "type": "select",
     "id": "text_size",
     "options": [
       {
         "value": "body--text",
         "label": "Body"
       },
       {
         "value": "h6",
         "label": "Extra small"
       },
       {
         "value": "h5",
         "label": "Small"
       },
       {
         "value": "h4",
         "label": "Medium"
       },
       {
         "value": "h3",
         "label": "Large"
       },
       {
         "value": "h2",
         "label": "Extra large"
       }
     ],
     "default": "h2",
      "label": "Text size"
    },
    {
      "type": "image_picker",
      "id": "separetor_image",
      "label": "Separetor image"
    }, 
    {
      "type": "checkbox",
      "id": "sec_border_enable",
      "label": "Section border Enable",
      "default": false
    },
    {
      "type": "header",
      "content": "Section spacing"
    },
    {
      "type": "paragraph",
      "content": "Desktop"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "Margin top",
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "min": 0,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "Margin bottom",
      "default": 0
    },
    {
      "type": "paragraph",
      "content": "Mobile"
    },
    {
      "type": "range",
      "id": "mobile_margin_top",
      "min": 0,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "Margin top",
      "default": 0
    },
    {
      "type": "range",
      "id": "mobile_margin_bottom",
      "min": 0,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "Margin bottom",
      "default": 0
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "paragraph",
      "content": "Desktop"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "Padding top",
      "default": 50
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "Padding bottom",
      "default": 50
    },
    {
      "type": "paragraph",
      "content": "Mobile"
    },
    {
      "type": "range",
      "id": "mobile_padding_top",
      "min": 0,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "Padding top",
      "default": 50
    },
    {
      "type": "range",
      "id": "mobile_padding_bottom",
      "min": 0,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "Padding bottom",
      "default": 50
    },
    {
      "type": "header",
      "content": "Colors"
    },

    {
        "type": "color_scheme",
        "id": "color_scheme",
        "label": "t:sections.all.colors.label",
        "default": "background-2"
      },
    {
      "type": "checkbox",
      "id": "custom_colors",
      "label": "Replace with your custom colors",
      "default": false
    },
    {
      "type": "color",
      "id": "foreground",
      "default": "#121212",
      "label": "Foreground color"
    },
    {
      "type": "color",
      "id": "background",
      "default": "#fff",
      "label": "Background color"
    }
  ],
  "blocks": [
     {
      "type": "announcement",
      "name": "Announcement",
      "settings": [
        {
            "type": "text",
            "id": "text",
            "default": "Welcome to store",
            "label": "Text"
        },
        {
          "type": "checkbox",
          "id": "text_stroke",
          "label": "Text stroke",
          "default": false
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Scrolling text",
      "blocks":[
          {
            "type": "announcement"
          },
          {
            "type": "announcement",
             "settings": {
               "text_stroke": true
             }
          },
         {
            "type": "announcement"
         },
         {
            "type": "announcement",
             "settings": {
               "text_stroke": true
             }
         },
         {
            "type": "announcement"
         },
         {
            "type": "announcement",
             "settings": {
               "text_stroke": true
             }
         },
        {
            "type": "announcement"
         },
        {
            "type": "announcement",
             "settings": {
               "text_stroke": true
             }
         }
      ]
    }
  ]
}
{% endschema %}
