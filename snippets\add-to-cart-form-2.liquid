<div class="product-card-list-cart-button">
  {% if product_card_product.has_only_default_variant %}
    <product-form>
      <form action="/cart/add" method="post" enctype="multipart/form-data" data-type="add-to-cart-form">
        <input type="hidden" name="id" value="{{ variant.id }}">
        <button
          type="submit"
          name="add"
          class="product--card-list-button {% if tooltip %} product--tooltip{% endif %}"
          {% if product_card_product.selected_or_first_available_variant.available == false %}
            disabled
          {% endif %}
        >
          {% render 'icon-cart' %}
          {% if tooltip %}
            <div class="product--tooltip-label {{ tooltip_position }} {% if tooltip_desktop != true %} desktop--tooltip-disable{% endif %}">
              {% if product_card_product.selected_or_first_available_variant.available %}
                {%- if product_card_product.selected_or_first_available_variant.inventory_quantity <= 0
                  and product_card_product.selected_or_first_available_variant.inventory_policy == 'continue'
                  and show_preorder_button
                -%}
                  {{ 'products.product.pre_order' | t }}
                {%- else -%}
                  {{ 'products.product.add_to_cart' | t }}
                {%- endif -%}
              {% else %}
                {{ 'products.product.sold_out' | t }}
              {%- endif -%}
            </div>
          {% endif %}
        </button>
      </form>
    </product-form>
  {%- else -%}
    <quick-view-modal>
      <button
        aria-haspopup="dialog"
        type="button"
        class="product--card-list-button {{ className }} {% if tooltip %} product--tooltip{% endif %}"
        data-product-handle="{{ product_card_product.handle }}"
        aria-label="{{ 'products.product.select_options' | t }}"
      >
        {% render 'icon-cart' %}
        {% if tooltip %}
          <div class="product--tooltip-label {{ tooltip_position }} {% if tooltip_desktop != true %} desktop--tooltip-disable{% endif %}">
            {{ 'products.product.select_options' | t }}
          </div>
        {% endif %}
      </button>
    </quick-view-modal>
  {%- endif -%}
</div>
