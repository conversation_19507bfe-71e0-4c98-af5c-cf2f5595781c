.cart-items td,
.cart-items th {
  padding: 0;
  border: none;
}

.cart-items th {
  text-align: left;
  padding-bottom: 1.8rem;
  font-weight: normal;
}

.cart-item__quantity {
  display: flex;
}

.cart-item__totals {
  position: relative;
}

.cart-items *.right {
  text-align: right;
}

.cart-item__image {
  height: auto;
  max-width: 100%;
}

.cart-item__details {
  font-size: 1.6rem;
  line-height: 1.4;
}

.cart-item__details > * {
  margin: 0;
  max-width: 30rem;
}

.cart-item__details > * + * {
  margin-top: 0.8rem;
}

.cart-item__media {
  position: relative;
}

.cart-item__name {
  color: rgb(var(--color-foreground));
  text-decoration: none;
  display: block;
}

.cart-item__name:hover {
  text-decoration: underline;
  text-underline-offset: 0.3rem;
  text-decoration-thickness: 0.2rem;
}

.cart-item__price-wrapper > * {
  display: block;
  margin: 0;
  padding: 0;
}

.cart-item__discounted-prices dd {
  margin: 0;
}

.cart-item__discounted-prices .cart-item__old-price {
  font-size: 1.4rem;
}

.cart-item__old-price {
  opacity: 0.7;
}

.product-option {
  font-size: 1.4rem;
  line-height: 1.5;
}

.cart-item cart-remove-button {
  display: inline-block;
  margin-left: 1rem;
}

@media screen and (min-width: 750px) and (max-width: 989px) {
  .cart-item cart-remove-button {
    width: 4.5rem;
    height: 4.5rem;
  }
}

cart-remove-button .button {
  box-shadow: none;
  color: rgba(var(--color-base-text), 0.75);
  min-width: 4.5rem;
  min-height: 4.5rem;
  padding: 0;
  margin: 0 0.1rem 0.1rem 0;
}

cart-remove-button .button:not([disabled]):hover {
  box-shadow: none;
  color: rgb(var(--color-foreground));
}

cart-remove-button .button:not([disabled]):focus-visible {
  box-shadow: none;
}

@media screen and (min-width: 750px) {
  cart-remove-button .button {
    min-width: 3.5rem;
    min-height: 3.5rem;
  }
}

cart-remove-button .icon-remove {
  height: 1.5rem;
  width: 1.5rem;
}

.cart-item .loading-overlay {
  top: 0;
  left: auto;
  right: auto;
  bottom: 0;
  padding: 0;
}

@media screen and (min-width: 750px) {
  .cart-item .loading-overlay {
    right: 0;
    padding-top: 4.5rem;
    bottom: auto;
  }
}

.loading-overlay:not(.hidden) ~ * {
  visibility: hidden;
}

.cart-item__error {
  font-size: 1.2rem;
  display: flex;
  align-items: flex-start;
}

.cart-item__error-text {
  order: 1;
}

.cart-item__error-text + svg {
  flex-shrink: 0;
  width: 1.2rem;
  margin-right: 0.7rem;
  margin-top: 0.1rem;
}

.cart-item__error-text:empty + svg {
  display: none;
}

.product-option {
  color: rgba(var(--color-foreground), 0.7);
}

.product-option + .product-option {
  margin-top: 0.4rem;
}

.product-option * {
  display: inline;
  margin: 0;
}

.cart-items thead th {
  text-transform: uppercase;
}

@media screen and (max-width: 749px) {
  .cart-items,
  .cart-items thead,
  .cart-items tbody {
    display: block;
    width: 100%;
  }

  .cart-items thead tr {
    display: flex;
    justify-content: space-between;
    border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.2);
    margin-bottom: 4rem;
  }

  .cart-item {
    display: grid;
    grid-template: repeat(2, auto) / repeat(4, 1fr);
    gap: 1.5rem;
    margin-bottom: 3.5rem;
  }

  .cart-item:last-child {
    margin-bottom: 0;
  }

  .cart-item__media {
    grid-row: 1 / 3;
  }

  .cart-item__details {
    grid-column: 2 / 4;
  }

  .cart-item__quantity {
    grid-column: 2 / 5;
    flex-wrap: wrap;
  }

  .cart-item__totals {
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
  }
}

@media screen and (min-width: 750px) {
  .cart-items {
    border-spacing: 0;
    border-collapse: separate;
    box-shadow: none;
    width: 100%;
    display: table;
  }

  .cart-items th {
    border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
  }

  .cart-items thead th:first-child {
    width: 50%;
  }

  .cart-items th + th {
    padding-left: 4rem;
  }

  .cart-items td {
    vertical-align: top;
    padding-top: 4rem;
  }

  .cart-item {
    display: table-row;
  }

  .cart-item > td + td {
    padding-left: 4rem;
  }

  .cart-item__details {
    width: 35rem;
  }

  .cart-item__media {
    width: 10rem;
  }

  .cart-item cart-remove-button {
    margin: 0.5rem 0 0 1.5rem;
  }

  .cart-item__price-wrapper > *:only-child:not(.cart-item__discounted-prices) {
    margin-top: 1rem;
  }

  .cart-item__error {
    align-items: center;
  }

  .cart-item__error-text + svg {
    margin-top: -0.2rem;
  }
}

@media screen and (min-width: 990px) {
  .cart-item .cart-item__quantity,
  .cart-items .cart-items__heading--wide {
    padding-left: 6rem;
  }

  .cart-item__details {
    width: 50rem;
  }

  .cart-items thead th:first-child {
    width: 60%;
  }
}
