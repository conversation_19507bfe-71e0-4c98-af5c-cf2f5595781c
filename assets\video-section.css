.video-section__media {
  position: relative;
  padding-bottom: var(--ratio-percent);
}
.video--banner__media--adapt.video-section__placeholder {
  padding-bottom: 30rem;
}
.video-section__media {
  position: relative;
}

.video-section__media.deferred-media {
  overflow: visible;
}

.video-section__poster.deferred-media__poster:focus {
  outline-offset: 0.3rem;
}

.video-section__media iframe {
  background-color: rgba(var(--color-foreground), 0.03);
  border: 0;
}

.video-section__poster,
.video-section__media iframe,
.video-section__media video {
  position: absolute;
  width: 100%;
  height: 100%;
}
.video-section__media.media-fit-cover video {
  object-fit: cover;
}

.banner__bideo--play {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translatey(-50%) translatex(-50%);
  transform: translatey(-50%) translatex(-50%);
  border: 2px solid rgba(var(--color-button), var(--alpha-button-background));
  padding: 3px;
  border-radius: 50%;
}
.banner__bideo--play__icon {
  width: 6rem;
  height: 6rem;
  background: rgba(var(--color-background));
  color: rgba(var(--color-button), var(--alpha-button-background));
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-animation: animate 2s linear infinite;
  animation: animate 2s linear infinite;
}
@media only screen and (min-width: 750px) {
  .banner__bideo--play__icon {
    width: 8rem;
    height: 8rem;
  }
}
.banner__bideo--play__icon svg {
  width: 28px;
}
.video-section__media.video--banner__media--small {
  height: 30rem;
}
.video-section__media.video--banner__media--medium {
  height: 35rem;
}
.video-section__media.video--banner__media--large {
  height: 43.5rem;
}

@media screen and (min-width: 750px) {
  .video-section__media.video--banner__media--small {
    height: 40rem;
  }
  .video-section__media.video--banner__media--medium {
    height: 50rem;
  }
  .video-section__media.video--banner__media--large {
    height: 65rem;
  }
  .video--banner__media--adapt.video-section__placeholder {
    padding-bottom: 68rem;
  }
}
.video-section__media:is(.rounded--image-1) {
  overflow: hidden;
}
