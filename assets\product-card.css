.placeholder_svg_parent.product--corner-radius-true {
  border-radius: 1rem;
}
.product__card__thumbnail {
  aspect-ratio: 1/1;
  position: relative;
  overflow: hidden;
}
.product__card__badges {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 8;
  pointer-events: none;
}
@media only screen and (max-width: 575px) {
  .product__card__badges {
    top: 10px;
    left: 10px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }
}
.product__card__image {
  display: flex;
}
.product__card__image img {
  width: 100%;
  height: auto;
}
.product__card__content {
  padding-top: 20px;
  position: relative;
  display: grid;
  gap: 0.2rem;
}
.product__card__title {
    margin-bottom: 4px;
    font-weight: 600;
}
.product__card__title a {
  text-decoration: none;
  color: rgba(var(--color-foreground));
}
.product__card-title--link:hover {
  color: rgba(var(--text-link-hover-color));
}
@media only screen and (max-width: 575px) {
  .product__card__title {
    font-size: 1.6rem;
  }
}
@media only screen and (max-width: 479px) {
  .product__card__title {
    font-size: 1.6rem;
  }
}
.product__card__price {
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 2.4rem;
  display: flex;
  align-items: center;
  color: rgba(var(--color-button),var(--alpha-button-background));
}
@media only screen and (max-width: 1024px){
  .product__card__price{
    font-size: 1.5rem !important;
  }
}
.product__card__price .price__compare {
    text-decoration: inherit;
}
/* Product card style 2 */
.product__cart--wrapper.product__card--style2 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  transition: 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateY(100%);
  opacity: 0;
  visibility: hidden;
}
.product__card--cart-btn.button.product__card--style2 {
  border-radius: 0;
}
.product-card-action-buttons-style2 {
  position: absolute;
  right: 2rem;
  display: grid;
}
.product__card--wishlist-btn,
.product__card-style2--action-btn {
  background: rgba(var(--color-background));
  border: none;
  width: 4rem;
  height: 4rem;
  display: inline-flex;
  border-radius: 100%;
  align-items: center;
  justify-content: center;
  color: rgba(var(--color-foreground));
  box-shadow: 0 0 1rem -0.2rem rgba(var(--color-foreground), 0.1);
}

/* Product card style 3 - Duplicates style 2 with additional custom button */
.product__cart--wrapper.product__card--style3 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  transition: 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateY(100%);
  opacity: 0;
  visibility: hidden;
}
.product__card--cart-btn.button.product__card--style3 {
  border-radius: 0;
}
.product-card-action-buttons-style3 {
  position: absolute;
  right: 2rem;
  display: grid;
}
.product__card--wishlist-btn,
.product__card-style3--action-btn {
  background: rgba(var(--color-background));
  border: none;
  width: 4rem;
  height: 4rem;
  display: inline-flex;
  border-radius: 100%;
  align-items: center;
  justify-content: center;
  color: rgba(var(--color-foreground));
  box-shadow: 0 0 1rem -0.2rem rgba(var(--color-foreground), 0.1);
}
.product__card--wishlist-btn > span,
.product__card-style2--action-btn > span,
.product__card-style3--action-btn > span {
  line-height: 0;
}
.product__card--wishlist-btn:hover,
.product__card-style2--action-btn:hover,
.product__card-style3--action-btn:hover {
    background: rgba(var(--color-button),var(--alpha-button-background));
    color: rgba(var(--color-background));
}
.product__card--wishlist-btn.loading::after,
.product__card-style2--action-btn.loading::after,
.product__card-style3--action-btn.loading::after {
  left: 1rem;
  width: 1.5rem;
  height: 1.5rem;
  top: 1.1rem;
}
.product__card--action-btn svg,
.product__card-style2--action-btn svg,
.product__card-style3--action-btn svg {
  width: 2.2rem;
  display: inline-block;
}
.product__card-style2--action-btn.loading:hover:after,
.product__card-style3--action-btn.loading:hover:after {
  border-color: rgba(var(--color-background));
  border-left-color: transparent;
}
.product__card:hover .product__cart--wrapper.product__card--style2,
.product__card:hover .product__card-style2--action-btn,
.product__card:hover .product__cart--wrapper.product__card--style3,
.product__card:hover .product__card-style3--action-btn {
  opacity: 1;
  transform: translateY(0);
  visibility: visible;
}
button.compare__button.product__card-style2--action-btn,
button.compare__button.product__card-style3--action-btn {
  transition: all 0.5s ease 0s;
}
button.product__quick_view.product__card-style2--action-btn,
button.product__quick_view.product__card-style3--action-btn {
  transition: all 0.6s ease 0s;
}

/* Style 3 Custom Button */
.product__card--style3-custom-button-wrapper {
  margin-top: 1rem;
  text-align: center;
}

.product__card--style3-custom-button {
  display: inline-block;
  padding: 0.8rem 1.6rem;
  border-radius: 0.4rem;
  text-decoration: none;
  font-size: 1.4rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  min-width: 120px;
  text-align: center;
  background-color: rgba(var(--color-button));
  color: rgba(var(--color-button-text));
  border-color: rgba(var(--color-button));
  position: relative;
  overflow: hidden;
}

/* Font Size Options */
.product__card--style3-custom-button.style3-font-small {
  font-size: 1.2rem;
}
.product__card--style3-custom-button.style3-font-medium {
  font-size: 1.4rem;
}
.product__card--style3-custom-button.style3-font-large {
  font-size: 1.6rem;
}

/* Font Weight Options */
.product__card--style3-custom-button.style3-weight-normal {
  font-weight: 400;
}
.product__card--style3-custom-button.style3-weight-medium {
  font-weight: 500;
}
.product__card--style3-custom-button.style3-weight-bold {
  font-weight: 700;
}

/* Width Options */
.product__card--style3-custom-button.style3-width-auto {
  width: auto;
  min-width: 120px;
}
.product__card--style3-custom-button.style3-width-full {
  width: 100%;
  max-width: 100%;
}
.product__card--style3-custom-button.style3-width-custom {
  width: auto;
  /* max-width set via inline styles */
}

/* Hover Effects */
.product__card--style3-custom-button.style3-hover-none:hover {
  transform: none;
  box-shadow: none;
}

.product__card--style3-custom-button.style3-hover-lift:hover {
  transform: translateY(-2px);
}

.product__card--style3-custom-button.style3-hover-shadow:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.product__card--style3-custom-button.style3-hover-lift_shadow:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Custom Hover Colors */
.product__card--style3-custom-button:hover {
  background-color: var(--style3-hover-bg, rgba(var(--color-button), 0.8));
  color: var(--style3-hover-text, rgba(var(--color-button-text)));
}

/* Default hover fallback when no custom colors are set */
.product__card--style3-custom-button:not([style*="--style3-hover-bg"]):hover {
  background-color: rgba(var(--color-button), 0.8);
}
.product__card--style3-custom-button:not([style*="--style3-hover-text"]):hover {
  color: rgba(var(--color-button-text));
}
/* Product card style 2 */
@media screen and (min-width: 992px) {
  .product__card__thumbnail:hover
    .media.media--hover-effect
    > img
    ~ img.secondary__img,
  .product__card__thumbnail:hover
    .media.media--hover-effect
    > img.secondary__img
    + img.secondary__img--new {
    opacity: 1;
    transition: transform var(--duration-long) ease;
  }

  .product__card__thumbnail:hover
    .media.media--hover-effect
    > img:first-child:not(:only-child) {
    opacity: 0;
  }

  .product__card__thumbnail.second--image__hide:hover
    .media.media--hover-effect
    > img:first-child:not(:only-child) {
    opacity: 1;
  }
  .product__card__thumbnail:hover .media > img {
    transform: scale(1.03);
  }
  .product__card .media > img {
    transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
      transfrom 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .product__card .media > img:only-child {
    transition: transform var(--duration-long) ease;
  }
  button.product__card-style2--action-btn,
  button.product__card-style3--action-btn {
    border-radius: 50rem;
    position: relative;
    transform: translateY(1rem);
    opacity: 0;
    visibility: hidden;
  }
  .product-card-action-buttons-style2,
  .product-card-action-buttons-style3 {
    top: 2rem;
    gap: 1rem;
  }
  .product__card--action-btn:not(.product__card--cart-btn) {
    min-width: 3.8rem;
  }
}
button.product__card__actions__btn.loading {
  background: #fff;
  color: transparent;
  box-shadow: 0 5px 7px rgb(0 0 0 / 20%);
}
@media only screen and (max-width: 479px) {
  span.remove__wishlist svg {
    height: 18px;
    width: auto;
  }
}
@media only screen and (max-width: 479px) {
  span.sale__text {
    display: none;
  }
}
.product__card--wishlist-btn {
  position: absolute;
  right: 1rem;
  top: 1rem;
}
.product__card--action-btn:not(.product__card--style2):not(.product__card--style3) {
  background: none;
}
.product__card--action-btn {
  display: flex;
  align-items: center;
  border: 0;
  width: 100%;
  justify-content: center;
  border-radius: 0.3rem;
  min-height: 4.4rem;
  height: auto;
  font-size: 1.6rem;
  position: relative;
}
.product__card--action-btn span {
    line-height: 1;
    text-transform: capitalize;
}
.product__card--action-btn:hover {
  background-color: rgba(var(--secondary-button-hover-background));
  color: rgba(var(--secondary-button-hover-text));
  border-color: rgba(var(--secondary-button-hover-background));
}
.product__card--cart-btn {
  gap: 0.5rem;
}
.product__card--action-btn.loading:hover::after {
  border: 2.5px solid rgba(var(--secondary-button-hover-text));
  border-left: 2.5px solid transparent;
}
.product__card--action-btn.loading:after {
  top: 1rem;
}
.product__card--cart-btn.loading:after {
  margin-left: -1rem;
  left: 50%;
}
.product__card--action-btn:not(.product__card--cart-btn).loading:after {
  left: 0.8rem;
}
.product__card__price + .product-card-action-buttons,
.product-card--rating + .product-card-action-buttons {
  margin-top: 1.2rem;
}
.product--inventory-stock + .product-card-action-buttons {
  margin-top: 1.5rem;
}
.product-card-action-buttons {
  margin-top: 0.5rem;
}
@media only screen and (min-width: 1200px) {
  .product-card-action-buttons {
    display: grid;
    grid-template-columns: auto auto auto;
    gap: 1rem;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-card-action-buttons {
    display: grid;
    grid-template-columns: auto 4rem 4rem;
    gap: 1rem;
  }
}
@media only screen and (max-width: 991px) {
.product__card--action-btn:not(.product__card--mobile-btn) .cart__buton--label {
    display: inline-block;
}
  .product__card--action-btn:not(.product__card--mobile-btn) {
    min-width: 4.4rem;
    max-width: 4.4rem;
  }
  .product-card-action-buttons {
    display: flex;
    gap: 1rem;
  }
  .product__card-style2--action-btn,
  .product__card-style3--action-btn {
    width: 3.2rem;
    height: 3.2rem;
  }
  .product__card-style2--action-btn svg,
  .product__card-style3--action-btn svg {
    width: 1.5rem;
  }
  .product-card-action-buttons-style2,
  .product-card-action-buttons-style3 {
    top: 50%;
    gap: 0.5rem;
    transform: translateY(-50%);
  }
  .product__card--cart-btn.button.product__card--style2,
  .product__card--cart-btn.button.product__card--style3 {
    padding: 0 0.3rem;
    font-size: 1.4rem;
    min-height: 4.2rem;
  }

  .product__card-style2--action-btn.loading:after,
  .product__card-style3--action-btn.loading:after {
    left: 0.8rem;
    width: 1rem;
    height: 1rem;
    top: 0.9rem;
  }

  /* Style 3 Custom Button Mobile */
  .product__card--style3-custom-button {
    padding: 0.6rem 1.2rem;
    min-width: 100px;
  }

  /* Mobile Font Size Adjustments */
  .product__card--style3-custom-button.style3-font-small {
    font-size: 1.1rem;
  }
  .product__card--style3-custom-button.style3-font-medium {
    font-size: 1.2rem;
  }
  .product__card--style3-custom-button.style3-font-large {
    font-size: 1.4rem;
  }

  /* Mobile Width Adjustments */
  .product__card--style3-custom-button.style3-width-auto {
    min-width: 100px;
  }
  .product__card--style3-custom-button.style3-width-full {
    width: 100%;
  }

  /* Reduced hover effects on mobile */
  .product__card--style3-custom-button.style3-hover-lift:hover,
  .product__card--style3-custom-button.style3-hover-lift_shadow:hover {
    transform: translateY(-1px);
  }
  .product__card--style3-custom-button.style3-hover-shadow:hover,
  .product__card--style3-custom-button.style3-hover-lift_shadow:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  .product__card--wishlist-btn.loading:after {
    left: 1.2rem;
    width: 1rem;
    height: 1rem;
    top: 1.1rem;
  }
  .product__card--action-btn.loading:hover:after {
    width: 1.5rem;
    height: 1.5rem;
  }
  .product__card--action-btn:not(.product__card--cart-btn).loading:after {
    left: 1.1rem;
    width: 1.5rem;
    height: 1.5rem;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1150px) {
  .product__card--cart-btn > svg {
    display: none;
  }
}
@media only screen and (min-width: 1150px) {
  .product__card--cart-btn {
    gap: 0.5rem;
    min-width: 15rem;
  }
}
@media only screen and (min-width: 750px) and (max-width: 1499px) {
  .product__card--cart-btn svg {
    width: 15rem;
  }
  .product__card--action-btn svg,
  .product__card-style2--action-btn svg,
  .product__card-style3--action-btn svg {
    width: 1.8rem;
  }

  /* Style 3 Custom Button Tablet */
  .product__card--style3-custom-button.style3-font-small {
    font-size: 1.2rem;
  }
  .product__card--style3-custom-button.style3-font-medium {
    font-size: 1.3rem;
  }
  .product__card--style3-custom-button.style3-font-large {
    font-size: 1.5rem;
  }
  .product__card--action-btn {
    font-size: 1.4rem;
  }
  .product__card--cart-btn {
    min-width: 15rem;
  }
}


@media (min-width: 768px) and (max-width: 991px) {
.product__card--action-btn:not(.product__card--mobile-btn) {
    min-width: auto;
    max-width: max-content;
    min-height: auto;
    background: rgba(var(--color-button),var(--alpha-button-background));
    color: rgb(var(--color-button-text));
    padding: 10px 20px;
    border-radius: 31px;
}
.product-card-action-buttons.xxv.d-md-only-visible {
  align-items: center;
  display: flex;
  justify-content: center;
}
.product-card-action-buttons {
    justify-content: center;
}

  

}




@media only screen and (max-width: 749px) {
  .product__card--action-btn:not(.product__card--mobile-btn) {
    min-width: 4rem;
    max-width: 4rem;
    min-height: 4rem;
  }
  .product__card--action-btn svg {
    width: 1.9rem;
  }
  .product__card--cart-btn svg {
    width: 2.1rem;
  }
  .wishlist__button svg {
    width: 1.7rem;
  }
  .product__card--wishlist-btn {
    top: 1rem;
    width: 3.7rem;
    height: 3.7rem;
  }
.product__card--action-btn:not(.product__card--mobile-btn) {
    min-width: auto;
    max-width: max-content;
    min-height: auto;
    background: rgba(163, 87, 65, 1);
    color: rgb(var(--color-button-text));
    padding: 10px 20px;
    border-radius: 31px;
    border: 1px solid rgba(163, 87, 65, 1);
}
  .product__card--action-btn:not(.product__card--mobile-btn):hover {
    background: rgba(var(--color-button),var(--alpha-button-background));
    border-color: rgba(var(--color-button),var(--alpha-button-background));
}
.product--tooltip-label {
    background: rgba(var(--primary-button-hover-background));
}
.product--tooltip-label.tooltip--top:after {
    border-top-color: rgba(var(--primary-button-hover-background));
}
.product--tooltip:hover .product--tooltip-label {
    opacity: 0;
    display: none;
}
  
.product-card-action-buttons.xxv.d-md-only-visible {
  align-items: center;
  display: flex;
  justify-content: center;
}
  .product--card-spacing-true .product__card--cart-btn svg {
    width: 1.8rem;
  }
  .product--card-spacing-true .product__card--action-btn svg {
    width: 1.6rem;
  }
}
@media only screen and (min-width: 1500px) {
  .product__card--cart-btn svg {
    width: 2.5rem;
  }
  .product__card-style2--action-btn svg,
  .product__card-style3--action-btn svg {
    width: 2rem;
  }

  /* Style 3 Custom Button Desktop */
  .product__card--style3-custom-button.style3-font-small {
    font-size: 1.3rem;
  }
  .product__card--style3-custom-button.style3-font-medium {
    font-size: 1.5rem;
  }
  .product__card--style3-custom-button.style3-font-large {
    font-size: 1.7rem;
  }

  /* Enhanced hover effects on desktop */
  .product__card--style3-custom-button.style3-hover-lift:hover,
  .product__card--style3-custom-button.style3-hover-lift_shadow:hover {
    transform: translateY(-3px);
  }
  .product__card--style3-custom-button.style3-hover-shadow:hover,
  .product__card--style3-custom-button.style3-hover-lift_shadow:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }
}
.product__vendor {
  text-transform: capitalize;
}
/* Color swatches css  */
.product--color-swatch {
  width: 2.7rem;
  height: 2.7rem;
  display: inline-flex;
  cursor: pointer;
  border: 0.2rem solid transparent;
  padding: 0.2rem;
  border-radius: 100%;
}
.product--color-swatch.checked-color {
  border-color: rgba(var(--color-foreground), 0.7);
}
.product--color-swatch-wrapper {
  display: flex;
  gap: 0.2rem;
  margin-top: 0.5rem;
  align-items: center;
}
.product--color-swatch .swatch--variant-tooltip {
  position: absolute;
  bottom: 100%;
  background: rgba(var(--color-button), var(--alpha-button-background));
  color: rgb(var(--color-button-text));
  z-index: 9;
  padding: 6px 12px;
  border-radius: 2px;
  left: 50%;
  transform: translate(-50%, -70%);
  transition-property: opacity, transform;
  transition-duration: 0.3s;
  transition-timing-function: ease;
  pointer-events: none;
  line-height: 1;
  opacity: 0;
  font-size: 1.3rem;
}
.product--color-swatch .variant--swatch-color {
  position: relative;
  width: 100%;
  box-shadow: inset 0 0 0 0.1rem rgb(var(--color-foreground), 0.1);
  border-radius: 50%;
  background-color: var(
    --color-swatch-background,
    var(--swatch-background-color)
  );
  background-image: var(--swatch-background-image, var(--background-gradient));
  background-size: cover;
  background-repeat: no-repeat;
}
.product--color-swatch:hover .swatch--variant-tooltip {
  opacity: 1;
  transform: translate(-50%, -50%);
}
.product--color-swatch .swatch--variant-tooltip:after {
  content: "";
  position: absolute;
  bottom: -1.6rem;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 1rem;
  border-color: transparent transparent transparent;
  border-top-color: rgba(var(--color-button), var(--alpha-button-background));
  left: 50%;
  transform: translate(-50%);
}
.swiper-slide
  .product--color-swatch-wrapper
  .product--color-swatch:first-child
  .swatch--variant-tooltip {
  transform: translateY(-70%);
  left: 0;
}
.swiper-slide
  .product--color-swatch-wrapper
  .product--color-swatch:first-child:hover
  .swatch--variant-tooltip {
  transform: translateY(-40%);
}
.swiper-slide
  .product--color-swatch-wrapper
  .product--color-swatch:first-child
  .swatch--variant-tooltip:after {
  left: 10px;
}
.rest__of--color-variants {
  line-height: 1;
  width: 2.7rem;
  height: 2.7rem;
  display: flex;
  align-items: center;
  color: rgba(var(--color-foreground));
}
.product--color-swatch .variant--swatch-custom.variant--swatch-image {
  position: relative;
  line-height: 1;
  box-shadow: inset 0 0 0 0.1rem rgb(var(--color-foreground), 0.1);
  border-radius: 100%;
  width: 100%;
}
.product--color-swatch .variant--swatch-custom.variant--swatch-image > img {
  width: 100%;
  height: auto;
  border-radius: 100%;
}
.product--color-swatch.product--color-swatch-image {
  width: 3.5rem;
  height: 3.5rem;
}
.product__card__content.product--card-spacing-true {
  padding: 2rem 1.5rem;
}
.product__card.product--corner-radius-true {
  border-radius: 1rem;
  overflow: hidden;
}
.product-card--rating {
  margin-top: 0.5rem;
  font-size: 1.4rem;
}
.jdgm-star.jdgm--on:before {
  font-size: 1.2rem;
}
.product--inventory-stock {
  margin-top: 0.5rem;
}
.action__btn--svg svg {
  width: 1.8rem;
}
.action__btn--svg svg {
    width: 2rem;
}
.product__card--action-btn svg,
.product__card-style2--action-btn svg,
.product__card-style3--action-btn svg {
    fill: rgba(var(--color-foreground));
}
.product__card .product__card-style2--action-btn:hover svg,
.product__card .product__card-style3--action-btn:hover svg {
    fill: rgb(var(--color-button-text));
}
.product__cart--wrapper button.product__quick_view svg {
    fill: rgb(var(--color-button-text));
}
.product__card--cart-btn.button.product__card--style2,
.product__card--cart-btn.button.product__card--style3 {
    border-radius: 30px;
    top: -15px;
    width: 50%;
    margin: 0 auto;
    padding: 1rem 1rem !important;
}
.product__card.product__card--style_2 .media,
.product__card.product__card--style_2 .placeholder,
.product__card.product__card--style_3 .media,
.product__card.product__card--style_3 .placeholder {
    border-radius: 1rem;
}
.product-card--rating p.rating-count.caption {
    display: none;
}
.product__card .rating-star {
    --letter-spacing: .1 !important;
}
.product-card--rating {
    margin-top: 0;
}


































