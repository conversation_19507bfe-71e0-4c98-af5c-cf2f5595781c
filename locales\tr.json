{"general": {"password_page": {"login_form_heading": "Parolayı kullanarak mağazaya girin:", "login_password_button": "<PERSON><PERSON><PERSON> k<PERSON> gir", "login_form_password_label": "Pa<PERSON><PERSON>", "login_form_password_placeholder": "Parolanız", "login_form_error": "Yanlış parola!", "login_form_submit": "Gir", "modal": "<PERSON><PERSON><PERSON> modu", "admin_link_html": "Mağaza sahibi misiniz? <a href=\"/admin\" class=\"link underlined-link\">B<PERSON>dan oturum açın</a>", "powered_by_shopify_html": "Bu mağaza {{ shopify }} tarafından desteklenir"}, "social": {"alt_text": {"share_on_facebook": "Facebook'ta paylaş", "share_on_twitter": "Twitter'da tweet'le", "share_on_pinterest": "Pinterest'te pin ekle"}, "links": {"twitter": "Twitter", "facebook": "Facebook", "pinterest": "Pinterest", "instagram": "Instagram", "tumblr": "Tumblr", "snapchat": "Snapchat", "youtube": "YouTube", "vimeo": "Vimeo", "tiktok": "TikTok"}}, "continue_shopping": "Alışverişe devam et", "pagination": {"label": "<PERSON><PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON> {{ number }}", "next": "<PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON> say<PERSON>"}, "search": {"search": "Ara"}, "cart": {"view": "<PERSON><PERSON> göster ({{ count }})", "item_added": "Ürün sepetinize eklendi"}, "share": {"copy_to_clipboard": "Bağlantıyı kopyala", "share": "Paylaş", "share_url": "Bağlantı", "success_message": "Bağlantı panoya kopyalandı", "close": "Paylaşımı kapat"}}, "date_formats": {"month_year": "%B %Y"}, "newsletter": {"label": "E-posta", "success": "Abone olduğunuz için teşekkür ederiz", "button_label": "<PERSON><PERSON> ol"}, "accessibility": {"skip_to_text": "İçeriğe atla", "close": "Ka<PERSON><PERSON>", "unit_price_separator": "/", "vendor": "Satıcı:", "error": "<PERSON><PERSON>", "refresh_page": "<PERSON><PERSON> seçim ya<PERSON>z sayfanın tamamının yenilenmesine neden olur.", "link_messages": {"new_window": "<PERSON>ni bir pencerede açılır.", "external": "Harici web sitesini açar."}, "next_slide": "<PERSON><PERSON><PERSON> ka<PERSON>ı<PERSON>", "previous_slide": "<PERSON>a kaydır", "loading": "Yükleniyor...", "of": "/", "skip_to_product_info": "<PERSON><PERSON><PERSON><PERSON> bilgisine atla", "total_reviews": "toplam değerlendirme", "star_reviews_info": "{{ rating_value }}/{{ rating_max }} yıldız"}, "blogs": {"article": {"blog": "Blog", "read_more_title": "Dev<PERSON>ı<PERSON><PERSON> okuyun: {{ title }}", "read_more": "Devamını okuyun", "comments": {"one": "{{ count }} yorum", "other": "{{ count }} yorum"}, "moderated": "Yorumların yayınlanabilmesi için onaylanması gerektiğini lütfen unutmayın.", "comment_form_title": "<PERSON><PERSON>", "name": "Ad", "email": "E-posta", "message": "<PERSON><PERSON>", "post": "<PERSON><PERSON><PERSON>", "back_to_blog": "<PERSON><PERSON><PERSON>", "share": "<PERSON><PERSON> makal<PERSON>i <PERSON>", "success": "Yorumunuz başarıyla paylaşıldı! Teşekkür ederiz.", "success_moderated": "Yorumunuz başarıyla paylaşıldı. Blogumuz denetlendiğinden yorumunuzu kısa bir süre sonra yayınlayacağız."}}, "onboarding": {"product_title": "Örnek ürün başlığı", "collection_title": "Koleksiyonunuzun adı"}, "products": {"product": {"add_to_cart": "Sepete ekle", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "on_sale": "İndirim", "product_variants": "<PERSON><PERSON><PERSON><PERSON>", "quantity": {"label": "<PERSON><PERSON>", "input_label": "{{ product }} i<PERSON>in adet", "increase": "{{ product }} i<PERSON><PERSON> aded<PERSON>", "decrease": "{{ product }} i<PERSON><PERSON> ad<PERSON>"}, "price": {"from_price_html": "Başlangıç fiyatı: {{ price }}", "regular_price": "Normal fiyat", "sale_price": "İndirimli fi<PERSON>t", "unit_price": "<PERSON><PERSON><PERSON>"}, "share": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "sold_out": "Tükendi", "unavailable": "Kullanım dışı", "vendor": "Satıcı", "video_exit_message": "{{ title }} a<PERSON><PERSON> pencerede tam ekran video açar.", "xr_button": "<PERSON><PERSON>ı<PERSON>ı<PERSON> görüntüleyin", "xr_button_label": "Alanınızda görüntüleyin; <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rılmış gerçeklik penceresinde yüklenir", "pickup_availability": {"view_store_info": "Mağaza bilgilerini görü<PERSON><PERSON><PERSON>in", "check_other_stores": "<PERSON><PERSON><PERSON>alardaki stok durumunu kontrol edin", "pick_up_available": "<PERSON><PERSON><PERSON>ı<PERSON> k<PERSON>bil<PERSON>", "pick_up_available_at_html": "<PERSON><PERSON><PERSON> <span class=\"color-foreground\">{{ location_name }}</span> konumunda kullanılabilir", "pick_up_unavailable_at_html": "<PERSON><PERSON><PERSON> al<PERSON>m <span class=\"color-foreground\">{{ location_name }}</span> konumunda şu anda kullanılamıyor", "unavailable": "<PERSON><PERSON><PERSON> alım stok durumu yüklenemedi", "refresh": "<PERSON><PERSON><PERSON>"}, "media": {"open_featured_media": "Öne çıkan medyayı galeri görünümünde aç", "open_media": "Medya {{ index }} galeri görünümünde aç", "play_model": "3B Görüntüleyici'yi <PERSON>", "play_video": "<PERSON><PERSON> o<PERSON>t"}, "view_full_details": "<PERSON><PERSON>m ayrıntıları görüntüle"}, "modal": {"label": "<PERSON><PERSON><PERSON> galerisi"}}, "templates": {"search": {"no_results": "\"{{ terms }}\" iç<PERSON> sonuç bulunamadı. Ya<PERSON>ım hatası olmadığını doğrulayın veya farklı bir kelime ya da ifade kullanın.", "results_with_count": {"one": "\"{{ terms }}\" için {{ count }} son<PERSON><PERSON> bulundu", "other": "\"{{ terms }}\" için {{ count }} son<PERSON><PERSON> bulundu"}, "title": "<PERSON><PERSON>", "page": "Say<PERSON>", "products": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search_for": "\"{{ terms }}\" i<PERSON><PERSON> arama yap"}, "cart": {"cart": "Sepet"}, "contact": {"form": {"name": "Ad", "email": "E-posta", "phone": "Telefon numarası", "comment": "<PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "post_success": "Bizimle iletişime geçtiğiniz için teşekkür ederiz. Mümkün olan en kısa sürede size dönüş yapacağız.", "error_heading": "Lütfen aşağıdakileri düzenleyin:"}}, "404": {"title": "Sayfa bulunamadı", "subtext": "404"}}, "sections": {"header": {"announcement": "<PERSON><PERSON><PERSON>", "menu": "<PERSON><PERSON>", "cart_count": {"one": "{{ count }} <PERSON><PERSON><PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON>"}}, "cart": {"title": "Sepetiniz", "caption": "Sepet ürünleri", "remove_title": "{{ title }} kanalını kaldır", "subtotal": "Alt toplam", "new_subtotal": "Yeni alt toplam", "note": "Siparişe özel talimatlar", "checkout": "Ödeme", "empty": "Sepetiniz boş", "cart_error": "Sepetiniz güncellenirken bir hata oluştu. Lütfen tekrar deneyin.", "cart_quantity_error_html": "Sepetinize bu üründen yalnızca [quantity] adet ekleyebilirsiniz.", "taxes_and_shipping_policy_at_checkout_html": "Vergiler ve <a href=\"{{ link }}\">kargo</a>, ödeme sayfasında hesaplanır", "taxes_included_but_shipping_at_checkout": "<PERSON><PERSON><PERSON> da<PERSON> ve kargo, ödeme sayfası<PERSON> hesa<PERSON>lanır", "taxes_included_and_shipping_policy_html": "<PERSON><PERSON><PERSON>. <a href=\"{{ link }}\"><PERSON><PERSON></a>, ödeme sayfasında hesaplanır.", "taxes_and_shipping_at_checkout": "Vergiler ve kargo, ödeme sayfasında hesaplanır", "headings": {"product": "<PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "total": "Toplam", "quantity": "<PERSON><PERSON>"}, "update": "<PERSON><PERSON><PERSON><PERSON>", "login": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z var mı?", "paragraph_html": "Daha hızlı ödeme yapmak için <a href=\"{{ link }}\" class=\"link underlined-link\">oturum açın</a>."}}, "footer": {"payment": "<PERSON><PERSON><PERSON>", "social_placeholder": "Bizi sosyal medyadan takip edin!"}, "featured_blog": {"view_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onboarding_title": "Blog gönderisi", "onboarding_content": "Müşterilerinize blog gönderinizin özetini gösterin"}, "featured_collection": {"view_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view_all_label": "{{ collection_name }} koleksiyonundaki tüm ürünleri gö<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "collection_list": {"view_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "collection_template": {"title": "Koleksiyon", "sort_by_label": "Sıralama ölçütü:", "sort_button": "S<PERSON>rala", "product_count": {"one": "{{ count }}/{{ product_count }} ü<PERSON><PERSON><PERSON>", "other": "{{ count }}/{{ product_count }} ü<PERSON><PERSON><PERSON>"}, "empty": "<PERSON><PERSON><PERSON><PERSON> bulunamadı", "apply": "<PERSON><PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON>", "clear_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> temizle", "from": "En düşük", "filter_and_sort": "Filtrele ve sırala", "filter_by_label": "Filtre:", "filter_button": "Filtrele", "max_price": "En yüksek fiyat: {{ price }}", "reset": "Sıfırla", "to": "<PERSON>ü<PERSON>", "use_fewer_filters_html": "<PERSON>ha az filtre kullan veya <a class=\"{{ class }}\" href=\"{{ link }}\">tü<PERSON><PERSON><PERSON><PERSON> temizle</a>", "filters_selected": {"one": "{{ count }} se<PERSON><PERSON><PERSON>", "other": "{{ count }} se<PERSON><PERSON><PERSON>"}, "product_count_simple": {"one": "{{ count }} <PERSON><PERSON><PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON>"}}, "video": {"load_video": "<PERSON><PERSON> yükle: {{ description }}"}}, "localization": {"country_label": "<PERSON><PERSON><PERSON>/bölge", "language_label": "Dil", "update_language": "<PERSON><PERSON>", "update_country": "<PERSON><PERSON><PERSON>/bö<PERSON> bil<PERSON>"}, "customer": {"account": {"title": "<PERSON><PERSON><PERSON>", "details": "<PERSON><PERSON><PERSON>", "view_addresses": "<PERSON><PERSON><PERSON>", "return": "<PERSON><PERSON><PERSON> bilgi<PERSON>ine geri <PERSON>n"}, "account_fallback": "<PERSON><PERSON><PERSON>", "activate_account": {"title": "Hesabı etkinleştirin", "subtext": "Hesabınızı etkinleştirmek için parolanızı oluşturun.", "password": "Pa<PERSON><PERSON>", "password_confirm": "Parolayı doğrula", "submit": "Hesabı etkinleştir", "cancel": "<PERSON><PERSON>"}, "addresses": {"title": "<PERSON><PERSON><PERSON>", "default": "Varsayılan", "add_new": "<PERSON><PERSON> ad<PERSON> ekle", "edit_address": "<PERSON><PERSON><PERSON>", "first_name": "Ad", "last_name": "Soyadı", "company": "Şirket", "address1": "Adres 1", "address2": "Adres 2", "city": "Şehir", "country": "<PERSON><PERSON><PERSON>/bölge", "province": "İl", "zip": "Posta kodu", "phone": "Telefon", "set_default": "Varsayılan adres o<PERSON> a<PERSON>", "add": "<PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "cancel": "İptal Et", "edit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "delete_confirm": "Bu adresi silmek istediğinizden emin misiniz?"}, "log_in": "Oturum aç", "log_out": "<PERSON><PERSON><PERSON><PERSON> kapat", "login_page": {"cancel": "İptal Et", "create_account": "<PERSON><PERSON><PERSON>", "email": "E-posta", "forgot_password": "Parolanızı mı unuttunuz?", "guest_continue": "<PERSON><PERSON>", "guest_title": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> devam edin", "password": "Pa<PERSON><PERSON>", "title": "Oturum aç", "sign_in": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>"}, "orders": {"title": "Sipariş geçmişi", "order_number": "Sipariş", "order_number_link": "Sipariş numarası {{ number }}", "date": "<PERSON><PERSON><PERSON>", "payment_status": "<PERSON><PERSON><PERSON>", "fulfillment_status": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>", "total": "Toplam", "none": "Hen<PERSON>z sipariş vermediniz."}, "recover_password": {"title": "Parolanızı sıfırlayın", "subtext": "Parolanızı sıfırlamanız için size bir e-posta göndereceğiz", "success": "Size parolanızı güncelleme bağlantısının bulunduğu bir e-posta gönderdik."}, "register": {"title": "<PERSON><PERSON><PERSON>", "first_name": "Ad", "last_name": "Soyadı", "email": "E-posta", "password": "Pa<PERSON><PERSON>", "submit": "Oluştur"}, "reset_password": {"title": "<PERSON>sap <PERSON>ını sıfırlayın", "subtext": "{{ email }} i<PERSON><PERSON> yeni bir parola girin", "password": "Pa<PERSON><PERSON>", "password_confirm": "Parolayı doğrula", "submit": "Parolayı sıfırla"}, "order": {"title": "{{ name }} siparişi", "date_html": "<PERSON><PERSON><PERSON><PERSON>: {{ date }}", "cancelled_html": "<PERSON><PERSON><PERSON><PERSON><PERSON>: {{ date }}", "cancelled_reason": "Neden: {{ reason }}", "billing_address": "<PERSON><PERSON>", "payment_status": "<PERSON><PERSON><PERSON>", "shipping_address": "<PERSON><PERSON>", "fulfillment_status": "<PERSON><PERSON><PERSON><PERSON>", "discount": "İndirim", "shipping": "<PERSON><PERSON>", "tax": "<PERSON><PERSON><PERSON>", "product": "<PERSON><PERSON><PERSON><PERSON>", "sku": "SKU", "price": "<PERSON><PERSON><PERSON>", "quantity": "<PERSON><PERSON>", "total": "Toplam", "fulfilled_at_html": "G<PERSON>nderildi: {{ date }}", "track_shipment": "<PERSON><PERSON><PERSON> taki<PERSON> et", "tracking_url": "Takip bağlantısı", "tracking_company": "Kargo <PERSON>", "tracking_number": "Takip numa<PERSON>ı", "subtotal": "Alt toplam"}}, "gift_cards": {"issued": {"title": "İşte {{ shop }} için {{ value }} hediye kartınız!", "subtext": "Hediye kartınız", "gift_card_code": "Hediye kartı kodu", "shop_link": "Alışverişe devam et", "remaining_html": "{{ balance }} kaldı", "add_to_apple_wallet": "Apple Wallet'a ekle", "qr_image_alt": "QR kodu: Hediye kartını kullanmak için tarayın", "copy_code": "Kodu k<PERSON>ala", "expired": "<PERSON><PERSON><PERSON><PERSON> sona erdi", "copy_code_success": "Kod başarıyla kopyalandı", "print_gift_card": "Yazdır"}}}