.banner__list--media.media--adapt.placeholder {
  padding-bottom: 100%;
}
.banner__list--item-content:not(.show__content--image-below) {
    position: absolute;
    bottom: 2rem;
    left: 5rem;
    right: 2rem;
    top: 2rem;
    z-index: 11;
}
.banner__list--item-content-inner {
    display: flex;
    flex-direction: column;
    gap: 1.3rem;
    width: 100%;
}
.banner__list--item-content-inner > * {
  align-self: var(--content-align-self);
}
.banner__list--item-overlay {
  position: relative;
  overflow: hidden;
}
.banner--list-text {
  color: rgba(var(--color-foreground), 0.75);
}
.show__content--image-below .banner__list--item-content-inner {
  margin-top: 2rem;
  width: 100%;
}
.banner__list--media.rounded--image {
  overflow: hidden;
}
.banner__list--media.media::before,
.banner__list--media.placeholder::before {
  position: absolute;
  content: "";
  background: #000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  opacity: var(--banner-list-media-overlay-opacity, 0.3);
}
.banner__list--item-content:not(.show__content--image-below)
  .banner--list-text {
  color: rgba(var(--color-foreground));
}
.banner__list--media.placeholder > svg.placeholder-svg {
  max-width: 38rem;
}
.banner__list--media.media > img {
  transition: all 0.7s ease 0s;
}

.banner__list--item:hover .banner__list--media.media > img {
  transform: scale(1.05);
}
.collection__list--slider-nav-btn {
  width: 4rem;
  height: 4rem;
  border-radius: 100%;
  transition: var(--transition);
}
.swiper__nav--btn.collection__list--slider-nav-btn:after {
  display: none;
}
@media only screen and (min-width: 750px) {
  .banner__list--slider.swiper:not(.swiper-initialized)
    .swiper-wrapper
    .swiper-slide {
    width: 33.33%;
  }
  .banner__list--slider.swiper:not(.swiper-initialized) .swiper-wrapper {
    gap: 3rem;
  } 
}
.container-fluid.p-0
  .banner__list--slider
  .swiper__nav--btn.swiper-button-next {
  right: 1rem;
}
.container-fluid.p-0
  .banner__list--slider
  .swiper__nav--btn.swiper-button-prev {
  left: 1rem;
}
.banner__list--media:not(.media--circle).media.rounded--image {
  border-radius: 1rem;
}
.banner__list--media.placeholder.rounded--image:not(.media--circle) {
  border-radius: 1rem;
}
@media (max-width: 767px) {
.banner__list--item-content:not(.show__content--image-below) {
    left: 2rem;
}
.banner__list--item  .banner__list--media.media--adapt {
    padding-bottom: 65% !important;
}

}
