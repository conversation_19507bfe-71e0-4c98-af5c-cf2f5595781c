{% if section.settings.transparent_header and section.settings.logo_2 != blank and request.page_type == 'index' %}
  <a href="{{ routes.root_url }}" class="{{ className }}">
    {%- if section.settings.logo_2 != blank -%}
      {%- assign image_size = section.settings.logo_width | append: 'x' -%}
      <img
        srcset="{{ section.settings.logo_2 | img_url: image_size }} 1x, {{ section.settings.logo_2 | img_url: image_size, scale: 2 }} 2x"
        src="{{ section.settings.logo_2 | img_url: image_size }}"
        loading="lazy"
        class="header__heading-logo"
        width="{{ section.settings.logo_2.width }}"
        height="{{ section.settings.logo_2.height }}"
        alt="{{ section.settings.logo_2.alt | default: shop.name | escape }}"
      >
    {%- else -%}
      <span class="h2">{{ shop.name }}</span>
    {%- endif -%}
  </a>
{% else %}
  <a href="{{ routes.root_url }}" class="{{ className }}">
    {%- if section.settings.logo != blank -%}
      {%- assign image_size = section.settings.logo_width | append: 'x' -%}
      <img
        srcset="{{ section.settings.logo | img_url: image_size }} 1x, {{ section.settings.logo | img_url: image_size, scale: 2 }} 2x"
        src="{{ section.settings.logo | img_url: image_size }}"
        loading="lazy"
        class="header__heading-logo"
        width="{{ section.settings.logo.width }}"
        height="{{ section.settings.logo.height }}"
        alt="{{ section.settings.logo.alt | default: shop.name | escape }}"
      >
    {%- else -%}
      <span class="h2">{{ shop.name }}</span>
    {%- endif -%}
  </a>
{% endif %}

{% if section.settings.transparent_header and section.settings.logo_3 != blank and request.page_type == 'index' %}
  <a href="{{ routes.root_url }}" class="{{ className }} transparent__header--hover-logo">
    {%- if section.settings.logo_3 != blank -%}
      {%- assign image_size = section.settings.logo_width | append: 'x' -%}
      <img
        srcset="{{ section.settings.logo_3 | img_url: image_size }} 1x, {{ section.settings.logo_3 | img_url: image_size, scale: 2 }} 2x"
        src="{{ section.settings.logo_3 | img_url: image_size }}"
        loading="lazy"
        class="header__heading-logo"
        width="{{ section.settings.logo_3.width }}"
        height="{{ section.settings.logo_3.height }}"
        alt="{{ section.settings.logo_3.alt | default: shop.name | escape }}"
      >
    {%- else -%}
      <span class="h2">{{ shop.name }}</span>
    {%- endif -%}
  </a>
{% endif %}
