{%- liquid
  # Build steps array from section settings
  assign steps_array = blank
  
  # Collect step data in a simple way
  assign step1_title = section.settings.step_1_title
  assign step2_title = section.settings.step_2_title  
  assign step3_title = section.settings.step_3_title
  assign step4_title = section.settings.step_4_title
  assign step5_title = section.settings.step_5_title
  assign step6_title = section.settings.step_6_title
  assign step7_title = section.settings.step_7_title
  
  # Build JSON string carefully
  assign json_parts = ''
  
  if step1_title != blank
    assign json_parts = json_parts | append: '{"number":"01","icon":"' | append: section.settings.step_1_icon | append: '","label":"' | append: step1_title | append: '","color":"' | append: section.settings.step_1_color | append: '"}'
  endif
  
  if step2_title != blank
    if json_parts != blank
      assign json_parts = json_parts | append: ','
    endif
    assign json_parts = json_parts | append: '{"number":"02","icon":"' | append: section.settings.step_2_icon | append: '","label":"' | append: step2_title | append: '","color":"' | append: section.settings.step_2_color | append: '"}'
  endif
  
  if step3_title != blank
    if json_parts != blank
      assign json_parts = json_parts | append: ','
    endif
    assign json_parts = json_parts | append: '{"number":"03","icon":"' | append: section.settings.step_3_icon | append: '","label":"' | append: step3_title | append: '","color":"' | append: section.settings.step_3_color | append: '"}'
  endif
  
  if step4_title != blank
    if json_parts != blank
      assign json_parts = json_parts | append: ','
    endif
    assign json_parts = json_parts | append: '{"number":"04","icon":"' | append: section.settings.step_4_icon | append: '","label":"' | append: step4_title | append: '","color":"' | append: section.settings.step_4_color | append: '"}'
  endif
  
  if step5_title != blank
    if json_parts != blank
      assign json_parts = json_parts | append: ','
    endif
    assign json_parts = json_parts | append: '{"number":"05","icon":"' | append: section.settings.step_5_icon | append: '","label":"' | append: step5_title | append: '","color":"' | append: section.settings.step_5_color | append: '"}'
  endif
  
  if step6_title != blank
    if json_parts != blank
      assign json_parts = json_parts | append: ','
    endif
    assign json_parts = json_parts | append: '{"number":"06","icon":"' | append: section.settings.step_6_icon | append: '","label":"' | append: step6_title | append: '","color":"' | append: section.settings.step_6_color | append: '"}'
  endif
  
  if step7_title != blank
    if json_parts != blank
      assign json_parts = json_parts | append: ','
    endif
    assign json_parts = json_parts | append: '{"number":"07","icon":"' | append: section.settings.step_7_icon | append: '","label":"' | append: step7_title | append: '","color":"' | append: section.settings.step_7_color | append: '"}'
  endif
  
  # Parse the complete JSON
  if json_parts != blank
    assign complete_json = '[' | append: json_parts | append: ']'
    assign steps_array = complete_json | parse_json
  endif
  
  # Use default steps if none configured
  unless steps_array
    assign steps_array = '[{"number":"01","icon":"👥","label":"Step 1","color":"blue"},{"number":"02","icon":"🛒","label":"Step 2","color":"teal"},{"number":"03","icon":"💬","label":"Step 3","color":"orange"}]' | parse_json
  endunless
-%}

<div class="section-step-progress color-{{ section.settings.color_scheme }}">
  <div class="page-width">
    {%- if section.settings.title != blank -%}
      <div class="section-header text-center">
        <h2 class="section-title {{ section.settings.heading_size }}">
          {{ section.settings.title }}
        </h2>
        {%- if section.settings.description != blank -%}
          <div class="section-description rte">
            {{ section.settings.description }}
          </div>
        {%- endif -%}
      </div>
    {%- endif -%}
    
    {%- render 'step-progress',
      steps: steps_array,
      show_numbers: section.settings.show_numbers,
      custom_class: section.settings.custom_class
    -%}
  </div>
</div>

{%- unless section.settings.disable_js -%}
  {{ 'step-progress.js' | asset_url | script_tag }}
{%- endunless -%}

<style>
.section-step-progress {
  padding-top: {{ section.settings.padding_top }}px;
  padding-bottom: {{ section.settings.padding_bottom }}px;
}

.section-step-progress .section-header {
  margin-bottom: 3rem;
}

.section-step-progress .section-title {
  margin-bottom: 1rem;
}

.section-step-progress .section-description {
  font-size: 1.125rem;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .section-step-progress .section-header {
    margin-bottom: 2rem;
  }
  
  .section-step-progress .section-description {
    font-size: 1rem;
  }
}
</style>

{% schema %}
{
  "name": "Step Progress",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "How It Works"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Section Description"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "Title Size",
      "options": [
        { "value": "h3", "label": "Small" },
        { "value": "h2", "label": "Medium" },
        { "value": "h1", "label": "Large" }
      ],
      "default": "h2"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "Color Scheme",
      "options": [
        { "value": "background-1", "label": "Background 1" },
        { "value": "background-2", "label": "Background 2" },
        { "value": "inverse", "label": "Inverse" },
        { "value": "accent-1", "label": "Accent 1" },
        { "value": "accent-2", "label": "Accent 2" }
      ],
      "default": "background-1"
    },
    {
      "type": "header",
      "content": "Component Settings"
    },
    {
      "type": "checkbox",
      "id": "show_numbers",
      "label": "Show Step Numbers",
      "default": true
    },
    {
      "type": "text",
      "id": "custom_class",
      "label": "Custom CSS Class",
      "info": "Add custom CSS classes for additional styling"
    },
    {
      "type": "checkbox",
      "id": "disable_js",
      "label": "Disable JavaScript",
      "info": "Disable animations and interactive features"
    },
    {
      "type": "header",
      "content": "Step 1"
    },
    {
      "type": "text",
      "id": "step_1_title",
      "label": "Step 1 Title",
      "default": "Step 1"
    },
    {
      "type": "text",
      "id": "step_1_icon",
      "label": "Step 1 Icon",
      "default": "👥",
      "info": "Use emoji, image URL, or SVG"
    },
    {
      "type": "select",
      "id": "step_1_color",
      "label": "Step 1 Color",
      "options": [
        { "value": "blue", "label": "Blue" },
        { "value": "teal", "label": "Teal" },
        { "value": "green", "label": "Green" },
        { "value": "orange", "label": "Orange" },
        { "value": "purple", "label": "Purple" }
      ],
      "default": "blue"
    },
    {
      "type": "header",
      "content": "Step 2"
    },
    {
      "type": "text",
      "id": "step_2_title",
      "label": "Step 2 Title",
      "default": "Step 2"
    },
    {
      "type": "text",
      "id": "step_2_icon",
      "label": "Step 2 Icon",
      "default": "🛒"
    },
    {
      "type": "select",
      "id": "step_2_color",
      "label": "Step 2 Color",
      "options": [
        { "value": "blue", "label": "Blue" },
        { "value": "teal", "label": "Teal" },
        { "value": "green", "label": "Green" },
        { "value": "orange", "label": "Orange" },
        { "value": "purple", "label": "Purple" }
      ],
      "default": "teal"
    },
    {
      "type": "header",
      "content": "Step 3"
    },
    {
      "type": "text",
      "id": "step_3_title",
      "label": "Step 3 Title",
      "default": "Step 3"
    },
    {
      "type": "text",
      "id": "step_3_icon",
      "label": "Step 3 Icon",
      "default": "💬"
    },
    {
      "type": "select",
      "id": "step_3_color",
      "label": "Step 3 Color",
      "options": [
        { "value": "blue", "label": "Blue" },
        { "value": "teal", "label": "Teal" },
        { "value": "green", "label": "Green" },
        { "value": "orange", "label": "Orange" },
        { "value": "purple", "label": "Purple" }
      ],
      "default": "orange"
    },
    {
      "type": "header",
      "content": "Step 4 (Optional)"
    },
    {
      "type": "text",
      "id": "step_4_title",
      "label": "Step 4 Title"
    },
    {
      "type": "text",
      "id": "step_4_icon",
      "label": "Step 4 Icon",
      "default": "⭐"
    },
    {
      "type": "select",
      "id": "step_4_color",
      "label": "Step 4 Color",
      "options": [
        { "value": "blue", "label": "Blue" },
        { "value": "teal", "label": "Teal" },
        { "value": "green", "label": "Green" },
        { "value": "orange", "label": "Orange" },
        { "value": "purple", "label": "Purple" }
      ],
      "default": "green"
    },
    {
      "type": "header",
      "content": "Step 5 (Optional)"
    },
    {
      "type": "text",
      "id": "step_5_title",
      "label": "Step 5 Title"
    },
    {
      "type": "text",
      "id": "step_5_icon",
      "label": "Step 5 Icon",
      "default": "🎯"
    },
    {
      "type": "select",
      "id": "step_5_color",
      "label": "Step 5 Color",
      "options": [
        { "value": "blue", "label": "Blue" },
        { "value": "teal", "label": "Teal" },
        { "value": "green", "label": "Green" },
        { "value": "orange", "label": "Orange" },
        { "value": "purple", "label": "Purple" }
      ],
      "default": "purple"
    },
    {
      "type": "header",
      "content": "Step 6 (Optional)"
    },
    {
      "type": "text",
      "id": "step_6_title",
      "label": "Step 6 Title"
    },
    {
      "type": "text",
      "id": "step_6_icon",
      "label": "Step 6 Icon",
      "default": "🚀"
    },
    {
      "type": "select",
      "id": "step_6_color",
      "label": "Step 6 Color",
      "options": [
        { "value": "blue", "label": "Blue" },
        { "value": "teal", "label": "Teal" },
        { "value": "green", "label": "Green" },
        { "value": "orange", "label": "Orange" },
        { "value": "purple", "label": "Purple" }
      ],
      "default": "blue"
    },
    {
      "type": "header",
      "content": "Step 7 (Optional)"
    },
    {
      "type": "text",
      "id": "step_7_title",
      "label": "Step 7 Title"
    },
    {
      "type": "text",
      "id": "step_7_icon",
      "label": "Step 7 Icon",
      "default": "🎉"
    },
    {
      "type": "select",
      "id": "step_7_color",
      "label": "Step 7 Color",
      "options": [
        { "value": "blue", "label": "Blue" },
        { "value": "teal", "label": "Teal" },
        { "value": "green", "label": "Green" },
        { "value": "orange", "label": "Orange" },
        { "value": "purple", "label": "Purple" }
      ],
      "default": "green"
    },
    {
      "type": "header",
      "content": "Section Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top Padding",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom Padding",
      "default": 36
    }
  ],
  "presets": [
    {
      "name": "Step Progress"
    }
  ]
}
{% endschema %}