.map__direction--inner {
  position: relative;
}
.map__direcion--content {
  background: rgba(var(--color-background));
  width: 100%;
  padding: 3rem;
  box-shadow: 0 0 1.5rem -0.2rem rgba(var(--color-foreground), 0.1);
  border-radius: 0.8rem;
}
.map__banner--subtitle.subheading--border {
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.15);
  padding-bottom: 0.6rem;
}
.map__banner--subtitle {
  margin-bottom: 1rem;
}
.map__banner--text {
  margin: 1.5rem 0;
}
.map__direcion--content :last-child:is(.map__banner--button) {
  margin-top: 3rem;
}
@media only screen and (min-width: 750px) {
  .map__direcion--content {
    left: 5rem;
    transform: translateY(-50%);
    max-width: 40rem;
    top: 50%;
    position: absolute;
  }
  .google--map-small iframe {
    width: 100% !important;
    height: 42rem !important;
  }
  .google--map-medium iframe {
    width: 100% !important;
    height: 55rem !important;
  }
  .google--map-large iframe {
    width: 100% !important;
    height: 70rem !important;
  }
}
@media only screen and (max-width: 749px) {
  .map__banner--button .button.button--full-width {
    width: auto;
    display: inline-block;
  }
  .google--map-small iframe {
    width: 100% !important;
    height: 28rem !important;
  }
  .google--map-medium iframe {
    width: 100% !important;
    height: 35rem !important;
  }
  .google--map-large iframe {
    width: 100% !important;
    height: 39rem !important;
  }
}
