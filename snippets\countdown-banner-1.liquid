{%- if section.settings.image != blank -%}
  <div class="countdown--banner__media media">
    <img
      srcset="
        {%- if section.settings.image.width >= 375 -%}{{ section.settings.image | image_url: width: 375 }} 375w,{%- endif -%}
        {%- if section.settings.image.width >= 550 -%}{{ section.settings.image | image_url: width: 550 }} 550w,{%- endif -%}
        {%- if section.settings.image.width >= 750 -%}{{ section.settings.image | image_url: width: 750 }} 750w,{%- endif -%}
        {%- if section.settings.image.width >= 1100 -%}{{ section.settings.image | image_url: width: 1100 }} 1100w,{%- endif -%}
        {%- if section.settings.image.width >= 1500 -%}{{ section.settings.image | image_url: width: 1500 }} 1500w,{%- endif -%}
        {%- if section.settings.image.width >= 1780 -%}{{ section.settings.image | image_url: width: 1780 }} 1780w,{%- endif -%}
        {%- if section.settings.image.width >= 2000 -%}{{ section.settings.image | image_url: width: 2000 }} 2000w,{%- endif -%}
        {%- if section.settings.image.width >= 3000 -%}{{ section.settings.image | image_url: width: 3000 }} 3000w,{%- endif -%}
        {%- if section.settings.image.width >= 3840 -%}{{ section.settings.image | image_url: width: 3840 }} 3840w,{%- endif -%}
        {{ section.settings.image | image_url }} {{ section.settings.image.width }}w
      "
      sizes="100vw"
      src="{{ section.settings.image | image_url: width: 1500 }}"
      loading="lazy"
      alt="{{ section.settings.image.alt | escape }}"
      width="{{ section.settings.image.width }}"
      height="{{ section.settings.image.width | divided_by: section.settings.image.aspect_ratio }}"
    >
  </div>
{%- endif -%}
<div class="{{ container }} countdown--banner__content">
  <div class="timer__video--content">
    <div class="deals__banner--content">
      <div class="deals__banner--content__subtitle text__secondary">{{ section.settings.subheading }}</div>
      <h2 class="deals__banner--content__maintitle">{{ section.settings.heading }}</h2>
      <div class="deals__banner--content__desc">{{ section.settings.text }}</div>

      {%- if section.settings.countdown_timer -%}
        <countdown-timer>
          <div
            class="deals__banner--countdown d-flex"
            data-countdown="{{ block.settings.countdown_year }}-{{ block.settings.countdown_month }}-{{ block.settings.countdown_days }}"
          ></div>
        </countdown-timer>
      {%- endif -%}

      {% liquid
        if section.settings.button_type == 'primary'
          assign button_class = 'button--primary'
        else
          assign button_class = 'button--secondary'
        endif
      %}
      {%- if section.settings.button_label != blank -%}
        <a
          {% if section.settings.button_link != blank %}
            href="{{ section.settings.button_link }}"
          {% endif %}
          class="button button--{{ section.settings.button_size }} {{ button_class }} {% if section.settings.button_icon %} button--with-icon{% endif %}"
          {% if section.settings.button_link == blank %}
            aria-disabled="true"
          {% endif %}
        >
          {{ section.settings.button_label | escape }}

          {% if section.settings.button_icon %}
            <span class="button--icon button--icon-right"> {% render 'icon-arrow-right' %} </span>
          {% endif %}
        </a>
      {%- endif -%}
    </div>

    {%- if section.settings.video_enable -%}
      <div class="deals__banner--play-button">
        <a
          href="{%- if section.settings.video_url.type == 'youtube' -%}https://www.youtube.com/watch?v={{ section.settings.video_url.id }}{%- else -%}https://vimeo.com/{{ section.settings.video_url.id }}{%- endif -%}"
          class="button button--primary video--play-btn glightbox"
          data-gallery="video"
        >
          <span class="video--play-btn-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="40"
              height="40"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="1"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="feather feather-play-circle"
            >
              <circle cx="12" cy="12" r="10"></circle><polygon points="10 8 16 12 10 16 10 8"></polygon>
            </svg>
          </span>
          <span class="visually-hidden">Video Play</span>
        </a>
      </div>
    {%- endif -%}
  </div>
</div>
