{{ 'section-counter-up.css' | asset_url | stylesheet_tag }}

<style>
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

   {%- if section.settings.image != blank -%}
    .counterup__banner--section::before {
        position: absolute;
        content: "";
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
    	opacity: {{ section.settings.image_overlay_opacity | divided_by: 100.0 }};
        background-color: rgba(0,0,0,1);
        z-index: 8;
    }

     {% endif %}

  {% if section.settings.custom_colors %}
    .custom-colors-{{ section.id }}{
      --color-foreground: {{ section.settings.foreground.red }}, {{ section.settings.foreground.green }}, {{ section.settings.foreground.blue }};
      --color-background: {{ section.settings.background.red }}, {{ section.settings.background.green }}, {{ section.settings.background.blue }};
    }
    {% endif %}
</style>

{% liquid
  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif
%}

<div
  class="counterup__banner--section section-{{ section.id }}-padding  color-{{ section.settings.color_scheme }} {% if section.settings.custom_colors  %} custom-colors-{{ section.id }} {% endif %}"
  data-section-id="{{ section.id }}"
  data-section-type="counter_up"
  id="funfactId-{{ section.id }}"
>
  {%- if section.settings.image != blank -%}
    {% liquid
      assign height = section.settings.image.width | divided_by: section.settings.image.aspect_ratio
    %}
    <div class="counterup--banner__media media">
      {{
        section.settings.image
        | image_url: width: 3840
        | image_tag:
          loading: 'lazy',
          height: height,
          sizes: '100vw',
          widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'
      }}
    </div>
  {%- endif -%}
  <div class="{{ container }} counterup--content">
    <div class="row row-cols-1 align-items-center">
      <div class="col">
        <div class="counterup__banner--inner d-flex align-items-center justify-content-between">
          {%- for block in section.blocks -%}
            {%- case block.type -%}
              {%- when 'couterup' -%}
                <div class="counterup__banner--items text-center">
                  <h2 class="counterup__banner--items__text h5">{{ block.settings.heading }}</h2>
                  <span class="counterup__banner--items__number js-counter h1" data-count="{{ block.settings.number }}">
                    {{- block.settings.number -}}
                  </span>
                </div>
            {%- endcase -%}
          {%- endfor -%}
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
 {
   "name": "Counter up",
   "settings": [
     {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
	{
         "type": "image_picker",
         "id": "image",
         "label": "Background image"
       },
       {
         "type": "range",
         "id": "image_overlay_opacity",
         "min": 0,
         "max": 100,
         "step": 10,
         "unit": "%",
         "label": "Image overlay opacity",
         "default": 0
       },
	{
     "type": "header",
     "content": "Section padding"
   },
   {
         "type": "paragraph",
         "content": "Desktop"
       },
       {
         "type": "range",
         "id": "padding_top",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding top",
         "default": 90
       },
       {
         "type": "range",
         "id": "padding_bottom",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding bottom",
         "default": 90
       },
	{
         "type": "paragraph",
         "content": "Mobile"
       },
	{
         "type": "range",
         "id": "mobile_padding_top",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding top",
         "default": 65
       },
       {
         "type": "range",
         "id": "mobile_padding_bottom",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding bottom",
         "default": 65
       },
    {
      "type": "header",
      "content": "Colors"
    },

     {
        "type": "color_scheme",
        "id": "color_scheme",
        "label": "t:sections.all.colors.label",
        "default": "background-2"
      },
     
      {
       "type": "checkbox",
       "id": "custom_colors",
       "label": "Replace with your custom colors",
       "default": false
       },
       {
         "type": "color",
         "id": "foreground",
         "default": "#121212",
         "label": "Foreground color"
       },
       {
         "type": "color",
         "id": "background",
         "default": "#F7F8FC",
         "label": "Background color"
       }
],
"blocks": [
       {
         "type": "couterup",
         "name": "Couterup",
         "settings": [
		{
             "type": "textarea",
             "id": "heading",
             "label": "Heading",
		   "default": "Fun fact"
           },
		{
             "type": "textarea",
             "id": "number",
             "label": "Number",
		   "default": "50",
		   "info": "You must input a integer number"
           }
         ]
       }
   ],
"presets": [
     {
       "name": "Counter up",
	"blocks":[
         {
         "type": "couterup",
         "settings":{
           "heading":"Fun fact 1",
		"number": "100"
           }
         },
         {
         "type": "couterup",
           "settings":{
           "heading":"Fun fact 2",
		 "number": "80"
           }
         },
	  {
         "type": "couterup",
           "settings":{
           "heading":"Fun fact 3",
		 "number": "150"
           }
         },
	  {
         "type": "couterup",
           "settings":{
           "heading":"Fun fact 4",
		 "number": "50"
           }
         }
       ]
     }
 	],
   "disabled_on": {
    "groups": ["header","footer"]
  }
 }
{% endschema %}
