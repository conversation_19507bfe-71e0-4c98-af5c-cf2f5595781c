{% comment %}
  Renders facets (filtering and sorting)

  Accepts:
  - results: {Object} Collection or Search object
  - enable_filtering: {<PERSON><PERSON><PERSON>} Show filtering when true
  - enable_sorting: {<PERSON><PERSON><PERSON>} Show sorting when true
  - filter_type: {String} Type of filter

  Usage:
  {% render 'facets', results: collection, enable_filtering: true, enable_sorting: true, filter_type: 'vertical' %}
{% endcomment %}

{{ 'component-show-more.css' | asset_url | stylesheet_tag }}

{%- liquid
  assign sort_by = results.sort_by | default: results.default_sort_by
  assign total_active_values = 0
  if results.url
    assign results_url = results.url
  else
    assign terms = results.terms | escape
    assign results_url = '?q=' | append: terms | append: '&options%5Bprefix%5D=last&sort_by=' | append: sort_by
  endif
-%}

<div class="facets-container {% if filter_type == 'vertical' %} color-{{ section.settings.facets_color_scheme }} {% if section.settings.filter_box_radius %}filter--radius-true{% endif %} {% if section.settings.filter_box_spacing %}filter--inner-spacing-true{% endif %}  {% endif %} {% if filter_type == 'drawer' %} facets-container-drawer color-background-2{% endif %}">
  {%- if filter_type == 'vertical' or filter_type == 'horizontal' -%}
    <facet-filters-form class="facets small-hide">
      <form
        id="FacetFiltersForm"
        class="{% if filter_type == 'horizontal' %}facets__form{% else %}facets__form-vertical{% endif %}"
      >
        {%- if results.terms -%}
          <input type="hidden" name="q" value="{{ results.terms | escape }}">
          <input name="options[prefix]" type="hidden" value="last">
        {%- endif -%}

        {% if enable_filtering %}
          {% comment %} Heading is the first tabbable element on filter type horizontal {% endcomment %}
          <div
            id="FacetsWrapperDesktop"
            {% if filter_type == 'horizontal' %}
              class="facets__wrapper"
            {% endif %}
          >
            {%- if filter_type == 'horizontal' and results.filters != empty -%}
              <h2 class="facets__heading caption-large text-body" id="verticalTitle" tabindex="-1">
                {{ 'products.facets.filter_by_label' | t }}
              </h2>
            {%- endif -%}
            {% comment %} Pills are right below the title for filter type vertical {% endcomment %}
            {%- if filter_type == 'vertical' -%}
              <div class="active-facets active-facets-desktop">
                <div class="active-facets-vertical-filter">
                  {%- unless results.filters == empty -%}
                    <h2
                      class="facets__heading facets__heading--vertical caption-large text-body"
                      id="verticalTitle"
                      tabindex="-1"
                    >
                      {{ 'products.facets.filter_by_label' | t }}
                    </h2>
                  {%- endunless -%}
                  <facet-remove class="active-facets__button-wrapper">
                    <a href="{{ results_url }}" class="active-facets__button-remove underlined-link">
                      <span>{{ 'products.facets.clear_all' | t }}</span>
                    </a>
                  </facet-remove>
                </div>
                {%- for filter in results.filters -%}
                  {%- for value in filter.active_values -%}
                    <facet-remove>
                      <a href="{{ value.url_to_remove }}" class="active-facets__button active-facets__button--light">
                        <span class="active-facets__button-inner button button--tertiary">
                          {{ filter.label }}: {{ value.label | escape }}
                          {% render 'icon-close-small' %}
                          <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
                        </span>
                      </a>
                    </facet-remove>
                  {%- endfor -%}
                  {% if filter.type == 'price_range' %}
                    {%- if filter.min_value.value != null or filter.max_value.value != null -%}
                      <facet-remove>
                        <a href="{{ filter.url_to_remove }}" class="active-facets__button active-facets__button--light">
                          <span class="active-facets__button-inner button button--tertiary">
                            {%- if filter.min_value.value -%}
                              {{ filter.min_value.value | money }}
                            {%- else -%}
                              {{ 0 | money }}
                            {%- endif -%}
                            -
                            {%- if filter.max_value.value -%}
                              {{ filter.max_value.value | money }}
                            {%- else -%}
                              {{ filter.range_max | money }}
                            {%- endif -%}
                            {% render 'icon-close-small' %}
                            <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
                          </span>
                        </a>
                      </facet-remove>
                    {%- endif -%}
                  {% endif %}
                {%- endfor -%}
              </div>
            {%- endif -%}

            <script src="{{ 'show-more.js' | asset_url }}" defer="defer"></script>
            {% comment %} Filters for both horizontal and vertical filter {% endcomment %}
            {%- for filter in results.filters -%}
              {%- assign total_active_values = total_active_values | plus: filter.active_values.size -%}
              {% case filter.type %}
                {% when 'boolean', 'list' %}
                  <details
                    id="Details-{{ forloop.index }}-{{ section.id }}"
                    class="{% if filter_type == 'horizontal' %}disclosure-has-popup facets__disclosure{% else %} facets__disclosure-vertical  {% if forloop.last == true %} facets__vertical--widget-last {% endif %}{% endif %} js-filter"
                    data-index="{{ forloop.index }}"
                    {% if filter_type == 'vertical' and forloop.index <= 6 %}
                      open
                    {% endif %}
                  >
                    <summary
                      class="facets__summary caption-large focus-offset"
                      aria-label="{{ filter.label }} ({{ 'products.facets.filters_selected.one' | t: count: filter.active_values.size }})"
                    >
                      <div>
                        <span>
                          {{- filter.label | escape }}
                          {%- if filter_type == 'vertical' -%}
                            <span class="facets__selected no-js-hidden{% if filter.active_values.size == 0 %} hidden{% endif %}">
                              ({{ filter.active_values.size }})</span
                            >
                          {%- endif -%}
                        </span>
                        {% render 'icon-caret' %}
                      </div>
                    </summary>
                    <div
                      id="Facet-{{ forloop.index }}-{{ section.id }}"
                      class="parent-display {% if filter_type == 'horizontal' %}facets__display{% else %}facets__display-vertical{% endif %}"
                    >
                      {%- if filter_type != 'vertical' -%}
                        <div class="facets__header">
                          <span class="facets__selected no-js-hidden">
                            {{- 'products.facets.filters_selected' | t: count: filter.active_values.size -}}
                          </span>
                          <facet-remove>
                            <a href="{{ filter.url_to_remove }}" class="facets__reset link underlined-link">
                              {{ 'products.facets.reset' | t }}
                            </a>
                          </facet-remove>
                        </div>
                      {%- endif -%}
                      <fieldset class="facets-wrap parent-wrap {% if filter_type == 'vertical' %} facets-wrap-vertical{% endif %}">
                        <legend class="visually-hidden">{{ filter.label | escape }}</legend>
                        <ul
                          class="{% if filter_type != 'vertical' %} facets__list{% endif %} list-unstyled no-js-hidden"
                          role="list"
                        >
                          {%- for value in filter.values -%}
                            <li class="list-menu__item facets__item{% if forloop.index > 10 and filter_type == 'vertical' %} show-more-item hidden{% endif %}">
                              <label
                                for="Filter-{{ filter.label | escape }}-{{ forloop.index }}"
                                class="facet-checkbox{% if value.count == 0 and value.active == false %} facet-checkbox--disabled{% endif %}"
                              >
                                <input
                                  type="checkbox"
                                  name="{{ value.param_name }}"
                                  value="{{ value.value }}"
                                  id="Filter-{{ filter.label | escape }}-{{ forloop.index }}"
                                  {% if value.active %}
                                    checked
                                  {% endif %}
                                  {% if value.count == 0 and value.active == false %}
                                    disabled
                                  {% endif %}
                                >

                                <span class="checkbox-facet-check"></span>

                                <svg
                                  class="icon icon-checkmark"
                                  width="1.1rem"
                                  height="0.7rem"
                                  viewBox="0 0 11 7"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path d="M1.5 3.5L2.83333 4.75L4.16667 6L9.5 1"
                                    stroke="currentColor"
                                    stroke-width="1.75"
                                    stroke-linecap="round"
                                    stroke-linejoin="round" />
                                </svg>

                                <span aria-hidden="true">{{ value.label | escape }}</span>
                                <span class="facet__checkbox-count"> {{ value.count }}</span>
                                <span class="visually-hidden">
                                  {{- value.label | escape }} (
                                  {%- if value.count == 1 -%}
                                    {{- 'products.facets.product_count_simple.one' | t: count: value.count -}}
                                  {%- else -%}
                                    {{- 'products.facets.product_count_simple.other' | t: count: value.count -}}
                                  {%- endif -%}
                                  )</span
                                >
                              </label>
                            </li>
                          {%- endfor -%}
                        </ul>
                        {% comment %} No show more for no JS {% endcomment %}
                        <ul
                          class="{% if filter_type != 'vertical' %} facets__list{% endif %} list-unstyled no-js"
                          role="list"
                        >
                          {%- for value in filter.values -%}
                            <li class="list-menu__item facets__item">
                              <label
                                for="Filter-{{ filter.label | escape }}-{{ forloop.index }}"
                                class="facet-checkbox{% if value.count == 0 and value.active == false %} facet-checkbox--disabled{% endif %}"
                              >
                                <input
                                  type="checkbox"
                                  name="{{ value.param_name }}"
                                  value="{{ value.value }}"
                                  id="Filter-{{ filter.label | escape }}-{{ forloop.index }}"
                                  {% if value.active %}
                                    checked
                                  {% endif %}
                                  {% if value.count == 0 and value.active == false %}
                                    disabled
                                  {% endif %}
                                >
                                <span class="checkbox-facet-check"></span>
                                <svg
                                  class="icon icon-checkmark"
                                  width="1.1rem"
                                  height="0.7rem"
                                  viewBox="0 0 11 7"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path d="M1.5 3.5L2.83333 4.75L4.16667 6L9.5 1"
                                    stroke="currentColor"
                                    stroke-width="1.75"
                                    stroke-linecap="round"
                                    stroke-linejoin="round" />
                                </svg>

                                <span aria-hidden="true">{{ value.label | escape }}</span>
                                <span class="facet__checkbox--count"> {{ value.count }}</span>
                                <span class="visually-hidden">
                                  {{- value.label | escape }} (
                                  {%- if value.count == 1 -%}
                                    {{- 'products.facets.product_count_simple.one' | t: count: value.count -}}
                                  {%- else -%}
                                    {{- 'products.facets.product_count_simple.other' | t: count: value.count -}}
                                  {%- endif -%}
                                  )</span
                                >
                              </label>
                            </li>
                          {%- endfor -%}
                        </ul>
                      </fieldset>
                      {%- if filter.values.size > 10 and filter_type == 'vertical' -%}
                        <show-more-button>
                          <button
                            class="button-show-more link underlined-link no-js-hidden"
                            id="Show-More-{{ forloop.index }}-{{ section.id }}"
                            type="button"
                          >
                            <span class="label-show-more label-text"
                              ><span aria-hidden="true">+ </span>{{ 'products.facets.show_more' | t -}}
                            </span>
                            <span class="label-show-less label-text hidden"
                              ><span aria-hidden="true">- </span>{{ 'products.facets.show_less' | t -}}
                            </span>
                          </button>
                        </show-more-button>
                      {%- endif %}
                    </div>
                  </details>
                {% when 'price_range' %}
                  {% liquid
                  assign currencies_using_comma_decimals = 'ANG,ARS,BRL,BYN,BYR,CLF,CLP,COP,CRC,CZK,DKK,EUR,HRK,HUF,IDR,ISK,MZN,NOK,PLN,RON,RUB,SEK,TRY,UYU,VES,VND' | split: ','
                  assign uses_comma_decimals = false
                  if currencies_using_comma_decimals contains cart.currency.iso_code
                    assign uses_comma_decimals = true
                  endif
                  
                  assign max_price_amount = filter.range_max | money | strip_html | escape
                %}
                <details id="Details-{{ forloop.index }}-{{ section.id }}" class="{% if filter_type == 'horizontal' %}disclosure-has-popup facets__disclosure price__filter{% else %} facets__disclosure-vertical{% endif %} js-filter" data-index="{{ forloop.index }}"{% if filter_type == 'vertical' and  forloop.index <= 6 %} open{% endif %}>
                  <summary class="facets__summary caption-large focus-offset">
                    <div class="{%- if filter_type != 'vertical' -%} horizontal--filter-header {% endif %}"">
                      <span>{{ filter.label | escape }}</span>
                      {% render 'icon-caret' %}
                     {%- if filter_type != 'vertical' -%}
                      <div class="facets__header--label">
                         <span class="facets__selected">{{ "products.facets.max_price" | t: price: max_price_amount }}</span>
                      </div>
                    {%- endif -%}
                    </div>
                  </summary>
                  <div id="Facet-{{ forloop.index }}-{{ section.id }}" class="{% if filter_type == 'horizontal' %}facets__display color-background-2{% else %}facets__display-vertical{% endif %}">
                    <div class="{% if filter_type == 'horizontal' %}facets__header{% endif %}">
                      {%- if filter_type != 'vertical' -%}
                        <facet-remove>
                          <a href="{{ filter.url_to_remove }}" class="facets__reset link">
                            {{ 'products.facets.reset' | t }}
                          </a>
                        </facet-remove>
                      {%- endif -%}
                      {%- if filter_type == 'vertical' -%}
                        <div class="facets__header--label facets__header--price-max">
                           <span class="facets__selected">{{ "products.facets.max_price" | t: price: max_price_amount }}</span>
                        </div>
                      {%- endif -%}
                    </div>

                  {% comment %} Desktop price slider start {% endcomment %}
                    <price-range class="Desktop--price__with--slider">
                      <div class="price__widget--wrapper {% if section.settings.price_filter_type == "range" %} price__slider--filter {% endif %}">

                       {% comment %} Price fiter box start {% endcomment %}
                        <div class="price__widget  {% if section.settings.price_filter_type == "range" %} hidden {% endif %}">
                          <div class="price__filter_group">
                            <label class="price__field__label" for="{{ attribute }}_Filter-{{ filter.label | escape }}-GTE">{{ 'sections.collection_template.from' | t }}</label>
                            <div class="input__field_form">
                              <span class="field-currency">{{ cart.currency.symbol }}</span>
                              <input class="input__field price__filter_input"
                                     name="{{ filter.min_value.param_name }}"
                                     id="{{ attribute }}_Filter-{{ filter.label | escape }}-GTE"
                                     {%- if filter.min_value.value -%}
                                     {%- if uses_comma_decimals -%}value="{{ filter.min_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' }}"{%- else -%}value="{{ filter.min_value.value | money_without_currency | replace: ',', '' }}"{% endif %}
                                     {%- endif -%}
                                     type="number"
                                     placeholder="0"
                                     min="0"
                                     {%- if uses_comma_decimals -%}max="{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}"{%- else -%}max="{{ filter.range_max | money_without_currency | replace: ',', '' }}"{% endif %}
                                     >
                            </div>
                          </div>
                          <div class="price__divider">
                            <span>-</span>
                          </div>
                          <div class="price__filter_group">
                            <label class="price__field__label" for="{{ attribute }}_Filter-{{ filter.label | escape }}-LTE">{{ 'sections.collection_template.to' | t }}</label>
                            <div class="input__field_form">
                              <span class="field-currency">{{ cart.currency.symbol }}</span>
                              <input class="input__field price__filter_input"
                                     name="{{ filter.max_value.param_name }}"
                                     id="{{ attribute }}_Filter-{{ filter.label | escape }}-LTE"
                                     {%- if filter.max_value.value -%}
                                     {%- if uses_comma_decimals -%}value="{{ filter.max_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' }}"{%- else -%}value="{{ filter.max_value.value | money_without_currency | replace: ',', '' }}"{% endif %}
                                     {%- endif -%}
                                     type="number"
                                     min="0"
                                     {%- if uses_comma_decimals -%}
                                     placeholder="{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
                                     max="{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
                                     {%- else -%}
                                     placeholder="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                                     max="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                                     {% endif %}
                                     >
                            </div>	
                          </div>
                        </div>
                        {% comment %} Price fiter box end {% endcomment %}

                      {% if section.settings.price_filter_type == "range" %}
                       {% comment %} Slider price start {% endcomment %}
                        {% liquid
                          
                          if filter.min_value.value
                            if uses_comma_decimals 
                              assign min_value_of_range = filter.min_value.value | money_without_currency | replace: '.', '' | replace: ',', '.'  
                            else 
                              assign min_value_of_range = filter.min_value.value | money_without_currency | replace: ',', ''
                            endif 
                          else 
                              if uses_comma_decimals 
                                assign min_value_of_range = '0' | money_without_currency | replace: '.', '' | replace: ',', '.'  
                              else 
                                assign min_value_of_range = 0
                              endif 
                          endif

                        
                          if filter.max_value.value
                            if uses_comma_decimals 
                              assign max_value_of_range = filter.max_value.value | money_without_currency | replace: '.', '' | replace: ',', '.'
                            else  
                              assign max_value_of_range =  filter.max_value.value | money_without_currency | replace: ',', '' 
                            endif
                           else
                            if uses_comma_decimals  
                              assign max_value_of_range = filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.'
                            else
                               assign max_value_of_range = filter.range_max | money_without_currency | replace: ',', ''
                            endif
                          endif

                          assign first_range_max_value = filter.range_max | money_without_currency | replace: ',', ''
                          if uses_comma_decimals 
                            assign first_range_max_value = filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' 
                          endif

                          assign second_range_max_value = filter.range_max | money_without_currency | replace: ',', ''
                          if uses_comma_decimals 
                            assign second_range_max_value = filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.'
                          endif

                          assign range_slider_width_math_1 = 100 | times: max_value_of_range | divided_by: second_range_max_value
                          assign range_slider_width_math_2 = 100 | times: min_value_of_range | divided_by: first_range_max_value

                          assign range_slider_final_width = range_slider_width_math_1 | minus: range_slider_width_math_2 

                          assign range_slider_left_position = 100 | times: min_value_of_range | divided_by: first_range_max_value
                        %}
                            <div class="slider-price" style="--width: {{ range_slider_final_width | round: 4 }}%; --left: {{ range_slider_left_position | round: 4 }}%;">
                              <span aria-label="{{ filter.label | escape }}" class="price__range--bar"></span>
                            <input min="0"
                                     max="{%- if uses_comma_decimals -%}{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}{%- else -%}{{ filter.range_max | money_without_currency | replace: ',', '' }}{% endif %}"
                                     step="1" type="range" 
                                     {%- if filter.min_value.value -%}
                                     {%- if uses_comma_decimals -%}value="{{ filter.min_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' }}"{%- else -%}value="{{ filter.min_value.value | money_without_currency | replace: ',', '' }}"{% endif %}
                                     {%- else -%}
                                     value="0"
                                     {%- endif -%}
                                     >
                        {% comment %}
                              <input min="0"
                                     max="{%- if uses_comma_decimals -%}{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}{%- else -%}{{ filter.range_max | money_without_currency | replace: ',', '' }}{% endif %}"
                                     step="1" type="range"
                                     {%- if filter.max_value.value -%}
                                     {%- if uses_comma_decimals -%}value="{{ filter.max_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' }}"{%- else -%}value="{{ filter.max_value.value | money_without_currency | replace: ',', '' }}"{% endif %}
                                     {%- else -%}
                                     value="{%- if uses_comma_decimals -%}{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}{% else %} {{ filter.range_max | money_without_currency | replace: ',', '' }}{% endif %}"
                                     {%- endif -%}
                                       >
                         {% endcomment %}

                              <input min="0"
                                     max="{%- if uses_comma_decimals -%}{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}{%- else -%}{{ filter.range_max | money_without_currency | replace: ',', '' }}{% endif %}"
                                     step="1" type="range"
                                     {%- if filter.max_value.value -%}
                                     {%- if uses_comma_decimals -%}value="{{ filter.max_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' }}"{%- else -%}value="{{ filter.max_value.value | money_without_currency | replace: ',', '' }}"{% endif %}
                                     {%- else -%}
                                     value="{{ max_value_of_range  | round: 0 }}"
                                     {%- endif -%}
                                     >
                        
                            </div>
                            {% comment %} Slider price end {% endcomment %}
    
                            {% comment %} Slider price show {% endcomment %}
                             <div class="field-price filter__price--display d-flex align-items-center">
                                  <span class="h6 mb-0">{{ filter.label | escape }}:</span>
                                  <div class="price__from--to">
                                      <span>{{ cart.currency.symbol }}<span class="price_fiter--min-value">{{ min_value_of_range | round: 0 }}</span></span>
                                      <span>-</span>
                                      <span>{{ cart.currency.symbol }}<span class="price_fiter--max-value">{{ max_value_of_range  | round: 0 }}</span></span>
                                  </div>
                             </div>
                            {% comment %} Slider price show ./ {% endcomment %}
                        
                      {% endif %}
                      </div>
                      </price-range>
                     {% comment %} Desktop price slider end {% endcomment %}
                
                  </div>
                </details>
              {% endcase %}
            {%- endfor -%}
            <noscript>
              <button type="submit" class="facets__button-no-js button button--secondary">
                {{ 'products.facets.filter_button' | t }}
              </button>
            </noscript>
          </div>
          {% comment %} Pills after filtes on filter type horizontal {% endcomment %}
          {%- if filter_type == 'horizontal' -%}
            <div class="active-facets active-facets-desktop">
              {%- for filter in results.filters -%}
                {%- for value in filter.active_values -%}
                  <facet-remove>
                    <a href="{{ value.url_to_remove }}" class="active-facets__button active-facets__button--light">
                      <span class="active-facets__button-inner button button--tertiary">
                        {{ filter.label }}: {{ value.label | escape }}
                        {% render 'icon-close-small' %}
                        <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
                      </span>
                    </a>
                  </facet-remove>
                {%- endfor -%}
                {% if filter.type == 'price_range' %}
                  {%- if filter.min_value.value != null or filter.max_value.value != null -%}
                    <facet-remove>
                      <a href="{{ filter.url_to_remove }}" class="active-facets__button active-facets__button--light">
                        <span class="active-facets__button-inner button button--tertiary">
                          {%- if filter.min_value.value -%}
                            {{ filter.min_value.value | money }}
                          {%- else -%}
                            {{ 0 | money }}
                          {%- endif -%}
                          -
                          {%- if filter.max_value.value -%}
                            {{ filter.max_value.value | money }}
                          {%- else -%}
                            {{ filter.range_max | money }}
                          {%- endif -%}
                          {% render 'icon-close-small' %}
                          <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
                        </span>
                      </a>
                    </facet-remove>
                  {%- endif -%}
                {% endif %}
              {%- endfor -%}
              <facet-remove class="active-facets__button-wrapper">
                <a href="{{ results_url }}" class="active-facets__button-remove underlined-link">
                  <span>{{ 'products.facets.clear_all' | t }}</span>
                </a>
              </facet-remove>
            </div>
          {%- endif -%}
        {% endif %}

        {% if results.current_vendor or results.current_type %}
          <input type="hidden" name="q" value="{{ results.current_vendor }}{{ results.current_type }}">
        {% endif %}

        {%- if filter_type == 'horizontal' -%}
          {% comment %} Sorting and product count are the last elements when filter type is horizontal {% endcomment %}
          {%- if enable_sorting -%}
            <div class="facet-filters sorting caption">
              <div class="facet-filters__field">
                <h2 class="facet-filters__label ">
                  <sort-select>
                    <button class="sortby__button">
                      {% render 'sort-icon' %}
                      <strong>{{ 'products.facets.sort_by_label' | t }}</strong>
                    </button>
                  </sort-select>
                </h2>
                {%- assign sort_by = collection.sort_by | default: collection.default_sort_by -%}
                <ul class="filter__sort--by-conatiner">
                  {%- for option in collection.sort_options -%}
                    <li class="sortlist__Item">
                      <input
                        type="radio"
                        id="{{ option.value }}_{{ forloop.index }}"
                        class="{% if option.value == sort_by %}checked{% endif %}"
                        name="sort_by"
                        value="{{ option.value | escape }}"
                      >
                      <label for="{{ option.value }}_{{ forloop.index }}"> {{ option.name | escape }}</label>
                    </li>
                  {%- endfor -%}
                </ul>
              </div>
              <noscript>
                <button type="submit" class="facets__button-no-js button button--secondary">
                  {{ 'products.facets.sort_button' | t }}
                </button>
              </noscript>
            </div>
          {%- endif -%}
          <div class="product-count light" role="status">
            <h2 class="product-count__text text-body mt-10">
              <span id="ProductCountDesktop">
                {%- if results.results_count -%}
                  {{ 'templates.search.results_with_count' | t: terms: results.terms, count: results.results_count }}
                {%- elsif results.products_count == results.all_products_count -%}
                  {{ 'products.facets.product_count_simple' | t: count: results.products_count }}
                {%- else -%}
                  {{
                    'products.facets.product_count'
                    | t: product_count: results.products_count, count: results.all_products_count
                  }}
                {%- endif -%}
              </span>
            </h2>
            <div class="loading-overlay__spinner">
              <svg
                aria-hidden="true"
                focusable="false"
                role="presentation"
                class="spinner"
                viewBox="0 0 66 66"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
              </svg>
            </div>
          </div>
        {%- endif -%}
      </form>
    </facet-filters-form>
    {% comment %} Sorting for vertical filter are grouped with filter when no JS{% endcomment %}
    {%- if enable_sorting and filter_type == 'vertical' -%}
      <facet-filters-form class="small-hide">
        <form class="no-js">
          <div class="facet-filters sorting caption">
            <div class="facet-filters__field">
              <h2 class="facet-filters__label ">
                <sort-select>
                  <button class="sortby__button">
                    {% render 'sort-icon' %}
                    <strong> {{ 'products.facets.sort_by_label' | t }}</strong>
                  </button>
                </sort-select>
              </h2>
              {%- assign sort_by = collection.sort_by | default: collection.default_sort_by -%}
              <ul class="filter__sort--by-conatiner">
                {%- for option in collection.sort_options -%}
                  <li class="sortlist__Item">
                    <input
                      type="radio"
                      id="{{ option.value }}_{{ forloop.index }}"
                      class="{% if option.value == sort_by %}checked{% endif %}"
                      name="sort_by"
                      value="{{ option.value | escape }}"
                    >
                    <label for="{{ option.value }}_{{ forloop.index }}"> {{ option.name | escape }}</label>
                  </li>
                {%- endfor -%}
              </ul>
            </div>

            <noscript>
              <button type="submit" class="facets__button-no-js button button--secondary">
                {{ 'products.facets.sort_button' | t }}
              </button>
            </noscript>
          </div>

          {% if results.current_vendor or results.current_type %}
            <input type="hidden" name="q" value="{{ results.current_vendor }}{{ results.current_type }}">
          {% endif %}

          {%- if results.terms -%}
            <input type="hidden" name="q" value="{{ results.terms | escape }}">
            <input name="options[prefix]" type="hidden" value="last">
          {%- endif -%}
        </form>
      </facet-filters-form>
    {%- endif -%}
  {%- endif -%}
  {% comment %}  Drawer and mobile filter {% endcomment %}
  <menu-drawer
    class="mobile-facets__wrapper{% if filter_type == 'horizontal' or filter_type == 'vertical' %} medium-hide large-up-hide{% endif %} {% if section.settings.product_view_switcher and filter_type == 'drawer' %} {% else %} flex-grow-1  {% endif %}"
    data-breakpoint="mobile"
  >
    <details class="mobile-facets__disclosure disclosure-has-popup">
      <summary class="mobile-facets__open-wrapper focus-offset">
        <span class="mobile-facets__open{% if filter_type == 'drawer' and enable_filtering == false %} medium-hide large-up-hide{% endif %}">
          {% render 'icon-filter' %}
          <span class="mobile-facets__open-label button-label medium-hide large-up-hide">
            {%- if enable_filtering and enable_sorting -%}
              {{ 'products.facets.filter_and_sort' | t }}
            {%- elsif enable_filtering -%}
              {{ 'products.facets.filter_button' | t }}
            {%- elsif enable_sorting -%}
              {{ 'products.facets.sort_button' | t }}
            {%- endif -%}
          </span>
          <span class="mobile-facets__open-label button-label small-hide">
            {%- if enable_filtering -%}
              {{ 'products.facets.filter_button' | t }}
            {%- endif -%}
          </span>
        </span>
        <span tabindex="0" class="mobile-facets__close mobile-facets__close--no-js">{%- render 'icon-close' -%}</span>
      </summary>
      <facet-filters-form>
        <form id="FacetFiltersFormMobile" class="mobile-facets">
          {%- if results.terms -%}
            <input type="hidden" name="q" value="{{ results.terms | escape }}">
            <input name="options[prefix]" type="hidden" value="last">
          {%- endif -%}
          <div class="mobile-facets__inner gradient">
            <div class="mobile-facets__header">
              <div class="mobile-facets__header-inner">
                <h2 class="mobile-facets__heading medium-hide large-up-hide">
                  {%- if enable_filtering and enable_sorting -%}
                    {{ 'products.facets.filter_and_sort' | t }}
                  {%- elsif enable_filtering -%}
                    {{ 'products.facets.filter_button' | t }}
                  {%- elsif enable_sorting -%}
                    {{ 'products.facets.sort_button' | t }}
                  {%- endif -%}
                </h2>
                <h2 class="mobile-facets__heading small-hide">
                  {%- if enable_filtering -%}
                    {{ 'products.facets.filter_button' | t }}
                  {%- endif -%}
                </h2>
                <p class="mobile-facets__count">
                  {%- if results.results_count -%}
                    {{ 'templates.search.results_with_count' | t: terms: results.terms, count: results.results_count }}
                  {%- elsif results.products_count == results.all_products_count -%}
                    {{ 'products.facets.product_count_simple' | t: count: results.products_count }}
                  {%- else -%}
                    {{
                      'products.facets.product_count'
                      | t: product_count: results.products_count, count: results.all_products_count
                    }}
                  {%- endif -%}
                </p>
              </div>
            </div>
            <div class="mobile-facets__main has-submenu gradient">
              {%- if enable_filtering -%}
                {%- for filter in results.filters -%}
                  {% case filter.type %}
                    {% when 'boolean', 'list' %}
                      <details
                        id="Details-Mobile-{{ forloop.index }}-{{ section.id }}"
                        class="mobile-facets__details js-filter"
                        data-index="mobile-{{ forloop.index }}"
                      >
                        <summary class="mobile-facets__summary focus-inset">
                          <div>
                            <span>{{ filter.label | escape }}</span>
                            <span class="mobile-facets__arrow no-js-hidden">{% render 'icon-arrow' %}</span>
                            <noscript>{% render 'icon-caret' %}</noscript>
                          </div>
                        </summary>
                        <div
                          id="FacetMobile-{{ forloop.index }}-{{ section.id }}"
                          class="mobile-facets__submenu gradient"
                        >
                          <button
                            class="mobile-facets__close-button link link--text focus-inset"
                            aria-expanded="true"
                            type="button"
                          >
                            {% render 'icon-arrow' %}
                            {{ filter.label | escape }}
                          </button>
                          <ul class="mobile-facets__list list-unstyled" role="list">
                            {%- for value in filter.values -%}
                              <li class="mobile-facets__item list-menu__item">
                                <label
                                  for="Filter-{{ filter.label | escape }}-mobile-{{ forloop.index }}"
                                  class="mobile-facets__label{% if value.count == 0 and value.active == false %} mobile-facets__label--disabled{% endif %}"
                                >
                                  <input
                                    class="mobile-facets__checkbox"
                                    type="checkbox"
                                    name="{{ value.param_name }}"
                                    value="{{ value.value }}"
                                    id="Filter-{{ filter.label | escape }}-mobile-{{ forloop.index }}"
                                    {% if value.active %}
                                      checked
                                    {% endif %}
                                    {% if value.count == 0 and value.active == false %}
                                      disabled
                                    {% endif %}
                                  >

                                  <span class="mobile-facets__highlight"></span>

                                  <span class="checkbox-facet-check"></span>

                                  <svg
                                    class="icon icon-checkmark"
                                    width="1.1rem"
                                    height="0.7rem"
                                    viewBox="0 0 11 7"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path d="M1.5 3.5L2.83333 4.75L4.16667 6L9.5 1" stroke="currentColor" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round" />
                                  </svg>

                                  <span aria-hidden="true">{{ value.label | escape }}</span>
                                  <span class="facet__checkbox--count"> {{ value.count }}</span>
                                  <span class="visually-hidden">
                                    {{- value.label | escape }} (
                                    {%- if value.count == '1' -%}
                                      {{- 'products.facets.product_count_simple.one' | t: count: value.count -}}
                                    {%- else -%}
                                      {{- 'products.facets.product_count_simple.other' | t: count: value.count -}}
                                    {%- endif -%}
                                    )</span
                                  >
                                </label>
                              </li>
                            {%- endfor -%}
                          </ul>

                          <div class="no-js-hidden mobile-facets__footer gradient">
                            <facet-remove class="mobile-facets__clear-wrapper">
                              <a href="{{ results_url }}" class="mobile-facets__clear underlined-link">
                                {{- 'products.facets.clear' | t -}}
                              </a>
                            </facet-remove>
                            <button
                              type="button"
                              class="no-js-hidden button button--primary"
                              onclick="this.closest('.mobile-facets__wrapper').querySelector('summary').click()"
                            >
                              {{ 'products.facets.apply' | t }}
                            </button>
                            <noscript
                              ><button class="button button--primary">
                                {{ 'products.facets.apply' | t }}
                              </button></noscript
                            >
                          </div>
                        </div>
                      </details>
                    {% when 'price_range' %}
                      <details
                        id="Details-Mobile-{{ forloop.index }}-{{ section.id }}"
                        class="mobile-facets__details js-filter"
                        data-index="mobile-{{ forloop.index }}"
                      >
                        <summary class="mobile-facets__summary focus-inset">
                          <div>
                            <span>{{ filter.label | escape }}</span>
                            <span class="mobile-facets__arrow no-js-hidden">{% render 'icon-arrow' %}</span>
                            <noscript>{% render 'icon-caret' %}</noscript>
                          </div>
                        </summary>
                        <div
                          id="FacetMobile-{{ forloop.index }}-{{ section.id }}"
                          class="mobile-facets__submenu gradient"
                        >
                          <button
                            class="mobile-facets__close-button link link--text focus-inset"
                            aria-expanded="true"
                            type="button"
                          >
                            {% render 'icon-arrow' %}
                            {{ filter.label | escape }}
                          </button>

                          {%- assign max_price_amount = filter.range_max | money | strip_html | escape -%}
                          <p class="mobile-facets__info mb-0">
                            {{ 'products.facets.max_price' | t: price: max_price_amount }}
                          </p>

                          <div class="single__widget_inner mobile-facets__price--range">
                            {% comment %} Mobile price slider start {% endcomment %}
                            <price-range class="Mobile--price__with--slider">
                          <div class="price__widget--wrapper {% if section.settings.price_filter_type == "range" %} price__slider--filter {% endif %}">
    
                           {% comment %} Price fiter box start {% endcomment %}
                    
                          <div class="price__widget  {% if section.settings.price_filter_type == "range" %} hidden {% endif %}">
                            <div class="price__filter_group">
                                  <label class="price__field__label" for="Mobile-Filter-{{ filter.label | escape }}-GTE">{{ 'products.facets.from' | t }}</label>
                                  <div class="input__field_form">
                                    <span class="field-currency">{{ cart.currency.symbol }}</span>
                                    <input class="input__field price__filter_input"
                                     name="{{ filter.min_value.param_name }}"
                                     id="Mobile-Filter-{{ filter.label | escape }}-GTE"
                                     {%- if filter.min_value.value -%}
                                     {%- if uses_comma_decimals -%}value="{{ filter.min_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' }}"{%- else -%}value="{{ filter.min_value.value | money_without_currency | replace: ',', '' }}"{% endif %}
                                     {%- endif -%}
                                     type="number"
                                     placeholder="0"
                                     min="0"
                                     inputmode="decimal"
                                     {%- if uses_comma_decimals -%}max="{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}"{%- else -%}max="{{ filter.range_max | money_without_currency | replace: ',', '' }}"{% endif %}
                                     >
                                  </div>
                                </div>
                                
                                <div class="price__divider">
                                  <span>-</span>
                                </div>
                                
                                <div class="price__filter_group">
                                  <label class="price__field__label" for="Mobile-Filter-{{ filter.label | escape }}-LTE">{{ 'sections.collection_template.to' | t }}</label>
                                  <div class="input__field_form">
                                    <span class="field-currency">{{ cart.currency.symbol }}</span>
                                   
                                    <input class="input__field price__filter_input"
                                       name="{{ filter.max_value.param_name }}"
                                       id="Mobile-Filter-{{ filter.label | escape }}-LTE"
                                       {%- if filter.max_value.value -%}
                                       {%- if uses_comma_decimals -%}value="{{ filter.max_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' }}"{%- else -%}value="{{ filter.max_value.value | money_without_currency | replace: ',', '' }}"{% endif %}
                                       {%- endif -%}
                                       type="number"
                                       min="0"
                                       inputmode="decimal"
                                       {%- if uses_comma_decimals -%}
                                       placeholder="{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
                                       max="{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
                                       {%- else -%}
                                       placeholder="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                                       max="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
                                       {% endif %}
                                       >
                                  </div>	
                                </div>                    
                          </div>
                          {% comment %} Price fiter box end {% endcomment %}
  
                        {% if section.settings.price_filter_type == "range" %}
                         {% comment %} Slider price start {% endcomment %}
                          {% liquid
                            assign min_value_of_range = 0
                            if filter.min_value.value
                              if uses_comma_decimals 
                                assign min_value_of_range = filter.min_value.value | money_without_currency | replace: '.', '' | replace: ',', '.'  
                              else 
                                assign min_value_of_range = filter.min_value.value | money_without_currency | replace: ',', ''
                              endif 
                            endif
                            
                            if filter.max_value.value
                              if uses_comma_decimals 
                                assign max_value_of_range = filter.max_value.value | money_without_currency | replace: '.', '' | replace: ',', '.'
                              else  
                                assign max_value_of_range =  filter.max_value.value | money_without_currency | replace: ',', '' 
                              endif
                             else
                              if uses_comma_decimals  
                                assign max_value_of_range = filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.'
                              else
                                 assign max_value_of_range = filter.range_max | money_without_currency | replace: ',', ''
                              endif
                            endif
  
                            assign first_range_max_value = filter.range_max | money_without_currency | replace: ',', ''
                            if uses_comma_decimals 
                              assign first_range_max_value = filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' 
                            endif
  
                            assign second_range_max_value = filter.range_max | money_without_currency | replace: ',', ''
                            if uses_comma_decimals 
                              assign second_range_max_value = filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.'
                            endif
  
                            assign range_slider_width_math_1 = 100 | times: max_value_of_range | divided_by: second_range_max_value
                            assign range_slider_width_math_2 = 100 | times: min_value_of_range | divided_by: first_range_max_value
  
                            assign range_slider_final_width = range_slider_width_math_1 | minus: range_slider_width_math_2 
  
                            assign range_slider_left_position = 100 | times: min_value_of_range | divided_by: first_range_max_value
                          %}
                              <div class="slider-price" style="--width: {{ range_slider_final_width | round: 4 }}%; --left: {{ range_slider_left_position | round: 4 }}%;">
                                <span aria-label="{{ filter.label | escape }}" class="price__range--bar"></span>
                                <input min="0"
                                       max="{%- if uses_comma_decimals -%}{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}{%- else -%}{{ filter.range_max | money_without_currency | replace: ',', '' }}{% endif %}"
                                       step="1" type="range" 
                                       {%- if filter.min_value.value -%}
                                       {%- if uses_comma_decimals -%}value="{{ filter.min_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' }}"{%- else -%}value="{{ filter.min_value.value | money_without_currency | replace: ',', '' }}"{% endif %}
                                       {%- else -%}
                                       value="0"
                                       {%- endif -%}
                                       >
                                <input min="0"
                                   max="{%- if uses_comma_decimals -%}{{ filter.range_max | money_without_currency | replace: '.', '' | replace: ',', '.' }}{%- else -%}{{ filter.range_max | money_without_currency | replace: ',', '' }}{% endif %}"
                                   step="1" type="range"
                                   {%- if filter.max_value.value -%}
                                   {%- if uses_comma_decimals -%}value="{{ filter.max_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' }}"{%- else -%}value="{{ filter.max_value.value | money_without_currency | replace: ',', '' }}"{% endif %}
                                   {%- else -%}
                                   value="{{ max_value_of_range  | round: 0 }}"
                                   {%- endif -%}
                                   >
                              </div>
                              {% comment %} Slider price end {% endcomment %}
      
                              {% comment %} Slider price show {% endcomment %}
                               <div class="field-price filter__price--display d-flex align-items-center">
                                    <span class="h6 mb-0">{{ filter.label | escape }}:</span>
                                    <div class="price__from--to">
                                        <span>{{ cart.currency.symbol }}<span class="price_fiter--min-value">{{ min_value_of_range | round: 0 }}</span></span>
                                        <span>-</span>
                                        <span>{{ cart.currency.symbol }}<span class="price_fiter--max-value">{{ max_value_of_range  | round: 0 }}</span></span>
                                    </div>
                               </div>
                              {% comment %} Slider price show ./ {% endcomment %}
                          
                        {% endif %}
                        </div>
                        </price-range>
                            {% comment %} Mobile price slider end {% endcomment %}
                          </div>
                          <div class="no-js-hidden mobile-facets__footer">
                            <facet-remove class="mobile-facets__clear-wrapper">
                              <a href="{{ results_url }}" class="mobile-facets__clear underlined-link">
                                {{- 'products.facets.clear' | t -}}
                              </a>
                            </facet-remove>
                            <button
                              type="button"
                              class="no-js-hidden button button--primary"
                              onclick="this.closest('.mobile-facets__wrapper').querySelector('summary').click()"
                            >
                              {{ 'products.facets.apply' | t }}
                            </button>
                            <noscript
                              ><button class="button button--primary">
                                {{ 'products.facets.apply' | t }}
                              </button></noscript
                            >
                          </div>
                        </div>
                      </details>
                  {% endcase %}
                {%- endfor -%}
              {%- endif -%}

              {%- if enable_sorting -%}
                <div
                  class="mobile-facets__details js-filter{% if filter_type == 'drawer' %} medium-hide large-up-hide{% endif %}"
                  data-index="mobile-{{ forloop.index }}"
                >
                  <div class="mobile-facets__summary">
                    <div class="mobile-facets__sort">
                      <label for="SortBy-mobile">{{ 'products.facets.sort_by_label' | t }}</label>
                      <div class="select">
                        <select
                          name="sort_by"
                          class="select__select"
                          id="SortBy-mobile"
                          aria-describedby="a11y-refresh-page-message"
                        >
                          {%- for option in results.sort_options -%}
                            <option
                              value="{{ option.value | escape }}"
                              {% if option.value == sort_by %}
                                selected="selected"
                              {% endif %}
                            >
                              {{ option.name | escape }}
                            </option>
                          {%- endfor -%}
                        </select>
                        {% render 'icon-caret' %}
                      </div>
                    </div>
                  </div>
                </div>
              {%- endif -%}

              <div class="mobile-facets__footer">
                <facet-remove class="mobile-facets__clear-wrapper">
                  <a href="{{ results_url }}" class="mobile-facets__clear underlined-link">
                    {{- 'products.facets.clear_all' | t -}}
                  </a>
                </facet-remove>
                <button
                  type="button"
                  class="no-js-hidden button button--primary"
                  onclick="this.closest('.mobile-facets__wrapper').querySelector('summary').click()"
                >
                  {{ 'products.facets.apply' | t }}
                </button>
                <noscript
                  ><button class="button button--primary">{{ 'products.facets.apply' | t }}</button></noscript
                >
              </div>
            </div>

            {% if results.current_vendor or results.current_type %}
              <input type="hidden" name="q" value="{{ results.current_vendor }}{{ results.current_type }}">
            {% endif %}
          </div>
        </form>
      </facet-filters-form>
    </details>
  </menu-drawer>

  {%- if section.settings.product_view_switcher and filter_type == 'drawer' -%}
    <div class="product__grid_column_buttons small-hide {% if section.settings.product_view_switcher and filter_type == 'drawer' %} flex-grow-1 {% endif %} d-flex justify-content-center">
      <button
        class="gird__column_icon product_col_two {% if product_column_view == "2" %}active{% endif %}"
        aria-label="Product column button"
      >
        <span>
          <svg fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 5.5 12.5">
            <defs/><defs><style>.cls-1{fill-rule:evenodd}</style></defs><g id="Layer_2" data-name="Layer 2"><g id="Layer_1-2" data-name="Layer 1"><g id="shop_page" data-name="shop page"><g id="Group-10"><path id="Rectangle" d="M.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 01.75 0z" class="cls-1"/><path id="Rectangle-2" d="M4.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 014.75 0z" class="cls-1" data-name="Rectangle"/></g></g></g></g>
          </svg>
        </span>
      </button>

      <button
        class="gird__column_icon product_col_three {% if product_column_view == "3" %}active{% endif %}"
        aria-label="Product column button"
      >
        <span>
          <svg fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 9.5 12.5">
            <defs/><defs><style>.cls-1{fill-rule:evenodd}</style></defs><g id="Layer_2" data-name="Layer 2"><g id="Layer_1-2" data-name="Layer 1"><g id="shop_page" data-name="shop page"><g id="Group-16"><path id="Rectangle" d="M.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 01.75 0z" class="cls-1"/><path id="Rectangle-2" d="M4.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 014.75 0z" class="cls-1" data-name="Rectangle"/><path id="Rectangle-3" d="M8.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 018.75 0z" class="cls-1" data-name="Rectangle"/></g></g></g></g>
          </svg>
        </span>
      </button>

      <button
        class="gird__column_icon product_col_four {% if product_column_view == "4" %}active{% endif %}"
        aria-label="Product column button"
      >
        <span>
          <svg fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13.5 12.5">
            <defs/><defs><style>.cls-1{fill-rule:evenodd}</style></defs><g id="Layer_2" data-name="Layer 2"><g id="Layer_1-2" data-name="Layer 1"><g id="shop_page" data-name="shop page"><g id="_4_col" data-name="4_col"><path id="Rectangle" d="M.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 01.75 0z" class="cls-1"/><path id="Rectangle-2" d="M4.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 014.75 0z" class="cls-1" data-name="Rectangle"/><path id="Rectangle-3" d="M8.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 018.75 0z" class="cls-1" data-name="Rectangle"/><path id="Rectangle-4" d="M12.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11a.76.76 0 01.75-.75z" class="cls-1" data-name="Rectangle"/></g></g></g></g>
          </svg>
        </span>
      </button>
    </div>
  {%- endif -%}

  <div class="active-facets active-facets-mobile medium-hide large-up-hide">
    {%- for filter in results.filters -%}
      {%- for value in filter.active_values -%}
        <facet-remove>
          <a href="{{ value.url_to_remove }}" class="active-facets__button active-facets__button--light">
            <span class="active-facets__button-inner button button--tertiary">
              {{ filter.label }}: {{ value.label | escape }}
              {% render 'icon-close-small' %}
              <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
            </span>
          </a>
        </facet-remove>
      {%- endfor -%}

      {%- if filter.type == 'price_range' -%}
        {%- if filter.min_value.value != null or filter.max_value.value != null -%}
          <facet-remove>
            <a href="{{ filter.url_to_remove }}" class="active-facets__button active-facets__button--light">
              <span class="active-facets__button-inner button button--tertiary">
                {%- if filter.min_value.value -%}
                  {{ filter.min_value.value | money }}
                {%- else -%}
                  {{ 0 | money }}
                {%- endif -%}
                -
                {%- if filter.max_value.value -%}
                  {{ filter.max_value.value | money }}
                {%- else -%}
                  {{ filter.range_max | money }}
                {%- endif -%}
                {% render 'icon-close-small' %}
                <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
              </span>
            </a>
          </facet-remove>
        {%- endif -%}
      {%- endif -%}
    {%- endfor -%}
    <facet-remove class="active-facets__button-wrapper">
      <a href="{{ results_url }}" class="active-facets__button-remove underlined-link">
        <span>{{ 'products.facets.clear_all' | t }}</span>
      </a>
    </facet-remove>
  </div>
  {% comment %} Sort, product count and filter pills at the end when filter is type of Drawer for the correct tabbing order {% endcomment %}
  {%- if enable_sorting and filter_type == 'drawer' -%}
    <facet-filters-form class="facets small-hide">
      <form id="FacetSortDrawerForm" class="facets__form">
        <div class="facet-filters sorting caption small-hide">
          <div class="facet-filters__field">
            <h2 class="facet-filters__label ">
              <sort-select>
                <button class="sortby__button">
                  {% render 'sort-icon' %}
                  <strong> {{ 'products.facets.sort_by_label' | t }}</strong>
                </button>
              </sort-select>
            </h2>
            {%- assign sort_by = collection.sort_by | default: collection.default_sort_by -%}
            <ul class="filter__sort--by-conatiner">
              {%- for option in collection.sort_options -%}
                <li class="sortlist__Item">
                  <input
                    type="radio"
                    id="{{ option.value }}_{{ forloop.index }}"
                    class="{% if option.value == sort_by %}checked{% endif %}"
                    name="sort_by"
                    value="{{ option.value | escape }}"
                  >
                  <label for="{{ option.value }}_{{ forloop.index }}"> {{ option.name | escape }}</label>
                </li>
              {%- endfor -%}
            </ul>
          </div>

          <noscript>
            <button type="submit" class="facets__button-no-js button button--secondary">
              {{ 'products.facets.sort_button' | t }}
            </button>
          </noscript>
        </div>

        {% if results.current_vendor or results.current_type %}
          <input type="hidden" name="q" value="{{ results.current_vendor }}{{ results.current_type }}">
        {% endif %}

        {%- if results.terms -%}
          <input type="hidden" name="q" value="{{ results.terms | escape }}">
          <input name="options[prefix]" type="hidden" value="last">
        {%- endif -%}
      </form>
    </facet-filters-form>
  {%- endif -%}
  <div
    class="product-count light{% if filter_type == 'vertical' or filter_type == 'horizontal' %} medium-hide large-up-hide{% endif %}"
    role="status"
  >
    <h2 class="product-count__text text-body">
      <span id="ProductCount">
        {%- if results.results_count -%}
          {{ 'templates.search.results_with_count' | t: terms: results.terms, count: results.results_count }}
        {%- elsif results.products_count == results.all_products_count -%}
          {{ 'products.facets.product_count_simple' | t: count: results.products_count }}
        {%- else -%}
          {{
            'products.facets.product_count'
            | t: product_count: results.products_count, count: results.all_products_count
          }}
        {%- endif -%}
      </span>
    </h2>
    <div class="loading-overlay__spinner">
      <svg
        aria-hidden="true"
        focusable="false"
        role="presentation"
        class="spinner"
        viewBox="0 0 66 66"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
      </svg>
    </div>
  </div>
  {%- if filter_type == 'drawer' -%}
    <facet-filters-form class="facets facets-pill small-hide">
      <form id="FacetFiltersPillsForm" class="facets__form">
        <div class="active-facets active-facets-desktop">
          {%- for filter in results.filters -%}
            {%- for value in filter.active_values -%}
              <facet-remove>
                <a href="{{ value.url_to_remove }}" class="active-facets__button active-facets__button--light">
                  <span class="active-facets__button-inner button button--tertiary">
                    {{ filter.label }}: {{ value.label | escape }}
                    {% render 'icon-close-small' %}
                    <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
                  </span>
                </a>
              </facet-remove>
            {%- endfor -%}

            {%- if filter.type == 'price_range' -%}
              {%- if filter.min_value.value != null or filter.max_value.value != null -%}
                <facet-remove>
                  <a href="{{ filter.url_to_remove }}" class="active-facets__button active-facets__button--light">
                    <span class="active-facets__button-inner button button--tertiary">
                      {%- if filter.min_value.value -%}
                        {{ filter.min_value.value | money }}
                      {%- else -%}
                        {{ 0 | money }}
                      {%- endif -%}
                      -
                      {%- if filter.max_value.value -%}
                        {{ filter.max_value.value | money }}
                      {%- else -%}
                        {{ filter.range_max | money }}
                      {%- endif -%}
                      {% render 'icon-close-small' %}
                      <span class="visually-hidden">{{ 'products.facets.clear_filter' | t }}</span>
                    </span>
                  </a>
                </facet-remove>
              {%- endif -%}
            {%- endif -%}
          {%- endfor -%}
          <facet-remove class="active-facets__button-wrapper">
            <a href="{{ results_url }}" class="active-facets__button-remove underlined-link">
              <span>{{ 'products.facets.clear_all' | t }}</span>
            </a>
          </facet-remove>
        </div>
      </form>
    </facet-filters-form>
  {%- endif -%}
</div>
