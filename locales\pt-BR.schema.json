{"settings_schema": {"colors": {"name": "Cores", "settings": {"colors_solid_button_labels": {"label": "Etiqueta de botão sólido", "info": "Usada como cor de primeiro plano sobre as cores de destaque."}, "colors_accent_1": {"label": "Destaque 1", "info": "Usada no plano de fundo do botão sólido."}, "colors_accent_2": {"label": "Destaque 2"}, "header__1": {"content": "<PERSON><PERSON> p<PERSON>"}, "header__2": {"content": "<PERSON><PERSON> secu<PERSON>"}, "colors_text": {"label": "Texto", "info": "Usada como cor de primeiro plano sobre as cores de fundo."}, "colors_outline_button_labels": {"label": "Botão com contorno", "info": "Também é usado em links de texto."}, "colors_background_1": {"label": "Plano de fundo 1"}, "colors_background_2": {"label": "Plano de fundo 2"}}}, "typography": {"name": "Tipografia", "settings": {"type_header_font": {"label": "Fonte", "info": "A seleção de uma fonte diferente pode afetar a velocidade da loja. [Saiba mais sobre as fontes do sistema.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)"}, "header__1": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header__2": {"content": "Corpo"}, "type_body_font": {"label": "Fonte", "info": "A seleção de uma fonte diferente pode afetar a velocidade da loja. [Saiba mais sobre as fontes do sistema.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)"}}}, "styles": {"name": "Estilos", "settings": {"sold_out_badge_color_scheme": {"options__1": {"label": "Plano de fundo 1"}, "options__2": {"label": "Inverso"}, "label": "Esquema de cores do selo de esgotado"}, "header__1": {"content": "<PERSON><PERSON>"}, "header__2": {"content": "Elementos decorativos"}, "sale_badge_color_scheme": {"options__1": {"label": "Plano de fundo 2"}, "options__2": {"label": "Destaque 1"}, "options__3": {"label": "Destaque 2"}, "label": "Esquema de cores do selo de promoção"}, "accent_icons": {"options__1": {"label": "Destaque 1"}, "options__2": {"label": "Destaque 2"}, "options__3": {"label": "Botão com contorno"}, "options__4": {"label": "Texto"}, "label": "Ícones de destaque"}}}, "social-media": {"name": "Redes sociais", "settings": {"share_facebook": {"label": "Compartilhar no Facebook"}, "share_twitter": {"label": "Tuitar"}, "share_pinterest": {"label": "<PERSON><PERSON> como Pin no Pinterest"}, "header__1": {"content": "Opções de compartilhamento em redes sociais"}, "header__2": {"content": "Contas de redes sociais"}, "social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "http://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "Contas de redes sociais"}}}, "currency_format": {"name": "Formato de moeda", "settings": {"content": "Códigos de moeda", "currency_code_enabled": {"label": "<PERSON><PERSON><PERSON> de moeda"}, "paragraph": "Os preços do carrinho e do checkout sempre mostram os códigos de moeda. Exemplo: 1,00 USD."}}, "favicon": {"name": "Favicon", "settings": {"favicon": {"label": "<PERSON><PERSON> favicon", "info": "Será reduzida para 32 x 32 pixels"}}}, "layout": {"name": "Layout", "settings": {"page_width": {"label": "<PERSON><PERSON><PERSON> m<PERSON>xi<PERSON>", "options__1": {"label": "1200 px"}, "options__2": {"label": "1600 px"}}}}, "search_input": {"name": "Entrada de pesquisa", "settings": {"header": {"content": "Sugestões de produto"}, "predictive_search_enabled": {"label": "Habilitar sugestões de produto"}, "predictive_search_show_vendor": {"label": "Exibir fabricante", "info": "<PERSON><PERSON><PERSON><PERSON> quando as sugestões de produto estão habilitadas."}, "predictive_search_show_price": {"label": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON> quando as sugestões de produto estão habilitadas."}}}}, "sections": {"announcement-bar": {"name": "Barra de avisos", "blocks": {"announcement": {"name": "Comunicado", "settings": {"text": {"label": "Texto"}, "color_scheme": {"label": "Esquema de cores", "options__1": {"label": "Plano de fundo 1"}, "options__2": {"label": "Plano de fundo 2"}, "options__3": {"label": "Inverso"}, "options__4": {"label": "Destaque 1"}, "options__5": {"label": "Destaque 2"}}, "link": {"label": "Link"}}}}}, "collage": {"name": "Colagem", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "desktop_layout": {"label": "Layout para computador", "options__1": {"label": "Bloco grande esquerdo"}, "options__2": {"label": "Bloco grande direito"}}, "mobile_layout": {"label": "Layout para dispositivo móvel", "options__1": {"label": "Colagem"}, "options__2": {"label": "Coluna"}}}, "blocks": {"image": {"name": "Imagem", "settings": {"image": {"label": "Imagem"}, "image_padding": {"label": "Adicionar preenchimento à imagem", "info": "Selecione o preenchimento se não quiser que a imagem seja cortada."}, "color_scheme": {"options__1": {"label": "Destaque 1"}, "options__2": {"label": "Destaque 2"}, "options__3": {"label": "Plano de fundo 1"}, "options__4": {"label": "Plano de fundo 2"}, "options__5": {"label": "Inverso"}, "label": "Esquema de cores", "info": "Selecione o preenchimento de imagem para tornar a cor visível."}}}, "product": {"name": "Produ<PERSON>", "settings": {"product": {"label": "Produ<PERSON>"}, "secondary_background": {"label": "Exibir plano de fundo secundário"}, "second_image": {"label": "Exibir segunda imagem ao passar o cursor"}, "image_padding": {"label": "Adicionar preenchimento à imagem", "info": "Selecione o preenchimento se não quiser que as imagens sejam cortadas."}}}, "collection": {"name": "Coleção", "settings": {"collection": {"label": "Coleção"}, "image_padding": {"label": "Adicionar preenchimento à imagem", "info": "Selecione o preenchimento se não quiser que a imagem seja cortada."}, "color_scheme": {"options__1": {"label": "Destaque 1"}, "options__2": {"label": "Destaque 2"}, "options__3": {"label": "Plano de fundo 1"}, "options__4": {"label": "Plano de fundo 2"}, "options__5": {"label": "Inverso"}, "label": "Esquema de cores"}}}, "video": {"name": "Vídeo", "settings": {"cover_image": {"label": "<PERSON><PERSON> de capa"}, "video_url": {"label": "URL", "info": "Os vídeos serão reproduzidos em uma janela pop-up se a seção tiver outros blocos.", "placeholder": "Usar um URL do YouTube ou do Vimeo"}, "image_padding": {"label": "Adicionar preenchimento à imagem", "info": "Selecione o preenchimento se não quiser que a imagem de capa seja cortada."}, "description": {"label": "Texto alternativo do vídeo", "info": "Descreva o vídeo para que seja acessível a clientes que usam leitores de tela."}}}}, "presets": {"name": "Colagem"}}, "collection-list": {"name": "Lista de coleções", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}, "info": "<PERSON>e as coleções para adicionar imagens. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/en/manual/products/collections)"}, "color_scheme": {"options__1": {"label": "Destaque 1"}, "options__2": {"label": "Destaque 2"}, "options__3": {"label": "Plano de fundo 1"}, "options__4": {"label": "Plano de fundo 2"}, "options__5": {"label": "Inverso"}, "label": "Esquema de cores"}, "swipe_on_mobile": {"label": "Habilitar deslizamento em dispositivo móvel"}, "image_padding": {"label": "Adicionar preenchimento à imagem"}, "show_view_all": {"label": "Habilitar o botão \"Ver tudo\" se a lista incluir mais coleções que as mostradas"}}, "blocks": {"featured_collection": {"name": "Coleção", "settings": {"collection": {"label": "Coleção"}}}}, "presets": {"name": "Lista de coleções"}}, "contact-form": {"name": "Formulário de contato", "presets": {"name": "Formulário de contato"}}, "custom-liquid": {"name": "Liquid personalizado", "settings": {"custom_liquid": {"label": "Liquid personalizado", "info": "Adicionar snippets de app ou outros códigos do Liquid para criar personalizações avançadas."}}, "presets": {"name": "Liquid personalizado"}}, "featured-blog": {"name": "Posts do blog", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Posts do blog"}, "show_view_all": {"label": "Habilitar o botão \"Ver tudo\" se o blog tiver mais posts que os mostrados"}, "show_image": {"label": "<PERSON><PERSON><PERSON> imagem em destaque", "info": "Use uma imagem com taxa de proporção de 2:3 para ter os melhores resultados. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "soft_background": {"label": "Exibir plano de fundo secundário"}, "show_date": {"label": "Exibir data"}, "show_author": {"label": "<PERSON><PERSON><PERSON> autor"}}, "blocks": {"title": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"show_date": {"label": "Exibir data"}, "show_author": {"label": "<PERSON><PERSON><PERSON> autor"}}}, "summary": {"name": "Resumo"}, "link": {"name": "Link"}}, "presets": {"name": "Posts do blog"}}, "featured-collection": {"name": "Coleção em destaque", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "collection": {"label": "Coleção"}, "products_to_show": {"label": "Máximo de produtos a serem mostrados"}, "show_view_all": {"label": "Habilitar o botão \"Ver tudo\" se a coleção tiver mais produtos que os mostrados"}, "swipe_on_mobile": {"label": "Habilitar deslizamento em dispositivo móvel"}, "header": {"content": "Cartão de produto"}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}}, "show_secondary_image": {"label": "Exibir segunda imagem ao passar o cursor"}, "add_image_padding": {"label": "Adicionar preenchimento de imagem"}, "show_vendor": {"label": "Exibir fabricante"}, "show_image_outline": {"label": "<PERSON><PERSON> borda da <PERSON>m"}}, "presets": {"name": "Coleção em destaque"}}, "footer": {"name": "Rodapé", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Tí<PERSON>lo necessário para exibir o menu."}, "menu": {"label": "<PERSON><PERSON>", "info": "Mostra somente itens de menu de nível superior."}}}, "text": {"name": "Texto", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "subtext": {"label": "Subtexto"}}}}, "settings": {"color_scheme": {"options__1": {"label": "Destaque 1"}, "options__2": {"label": "Destaque 2"}, "options__3": {"label": "Plano de fundo 1"}, "options__4": {"label": "Plano de fundo 2"}, "options__5": {"label": "Inverso"}, "label": "Esquema de cores"}, "newsletter_enable": {"label": "Exibir assinante de e-mail"}, "newsletter_heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "header__1": {"content": "Assinante de e-mail", "info": "Assinantes adicionados automaticamente à lista de clientes que \"aceitam marketing\". [Sai<PERSON> ma<PERSON>](https://help.shopify.com/en/manual/customers/manage-customers)"}, "header__2": {"content": "Ícones de redes sociais", "info": "Para exibir suas contas em redes sociais, crie links nas configurações do tema."}, "show_social": {"label": "<PERSON><PERSON>r <PERSON> de redes sociais"}, "header__3": {"content": "Se<PERSON>or de país/região"}, "header__4": {"info": "Adicione um país/uma região em [configurações de pagamento.](/admin/settings/payments)"}, "enable_country_selector": {"label": "Habilitar seletor de país/região"}, "header__5": {"content": "Se<PERSON>or de idioma"}, "header__6": {"info": "Adicione um idioma em [configurações de idioma.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Habilitar seletor de idioma"}, "header__7": {"content": "Formas de pagamento"}, "payment_enable": {"label": "<PERSON><PERSON><PERSON>mento"}}}, "header": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"logo": {"label": "Imagem do logo"}, "logo_width": {"unit": "px", "label": "Largura personalizada do logo"}, "logo_position": {"label": "Posição do logo em telas grandes", "options__1": {"label": "Centralizado à esquerda"}, "options__2": {"label": "Canto superior esquerdo"}, "options__3": {"label": "Centralizado na parte superior"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "<PERSON><PERSON><PERSON> linha separadora"}, "enable_sticky_header": {"label": "Habilitar cabeçalho fixo", "info": "O cabeçalho fica na tela quando o cliente rola para cima."}}}, "image-banner": {"name": "<PERSON> da imagem", "settings": {"image": {"label": "Primeira imagem"}, "image_2": {"label": "Segunda imagem"}, "desktop_text_box_position": {"options__1": {"label": "Acima"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Abaixo"}, "label": "Posição do texto no computador"}, "color_scheme": {"options__1": {"label": "Destaque 1"}, "options__2": {"label": "Destaque 2"}, "options__3": {"label": "Plano de fundo 1"}, "options__4": {"label": "Plano de fundo 2"}, "options__5": {"label": "Inverso"}, "label": "Esquema de cores", "info": "Vis<PERSON>vel quando a caixa de texto é exibida"}, "stack_images_on_mobile": {"label": "Empilhar imagens em dispositivos móveis"}, "adapt_height_first_image": {"label": "Adaptar a altura da seção para o tamanho da primeira imagem"}, "show_text_box": {"label": "Exibir caixa de texto no desktop"}, "image_overlay_opacity": {"label": "Opacidade de sobreposição de imagem"}, "header": {"content": "Layout para dispositivo móvel"}, "show_text_below": {"label": "Exibir texto abaixo de imagens"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "heading_size": {"options__1": {"label": "Médio"}, "options__2": {"label": "Grande"}, "label": "<PERSON><PERSON><PERSON> da fonte do título"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Descrição"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label_1": {"label": "Primeira etiqueta de botão", "info": "Deixe a etiqueta em branco para ocultar o botão."}, "button_link_1": {"label": "Primeiro link de botão"}, "button_style_secondary_1": {"label": "Usar estilo de botão com contorno"}, "button_label_2": {"label": "Segunda etiqueta de botão", "info": "Deixe a etiqueta em branco para ocultar o botão."}, "button_link_2": {"label": "Segundo link de botão"}, "button_style_secondary_2": {"label": "Usar estilo de botão com contorno"}}}}, "presets": {"name": "<PERSON> da imagem"}}, "image-with-text": {"name": "Imagem com texto", "settings": {"image": {"label": "Imagem"}, "height": {"options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Grande"}, "label": "Proporção da imagem"}, "color_scheme": {"options__1": {"label": "Plano de fundo 1"}, "options__2": {"label": "Plano de fundo 2"}, "options__3": {"label": "Inverso"}, "options__4": {"label": "Destaque 1"}, "options__5": {"label": "Destaque 2"}, "label": "Esquema de cores"}, "layout": {"options__1": {"label": "<PERSON>m primeiro"}, "options__2": {"label": "Texto primeiro"}, "label": "Layout para computador", "info": "O layout padrão para dispositivos móveis coloca a imagem primeiro."}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Descrição"}}}, "button": {"name": "Botão", "settings": {"button_label": {"label": "Etiqueta de botão", "info": "Deixe a etiqueta em branco para ocultar o botão."}, "button_link": {"label": "Link de botão"}}}}, "presets": {"name": "Imagem com texto"}}, "main-article": {"name": "Post do blog", "blocks": {"featured_image": {"name": "Imagem em destaque", "settings": {"image_height": {"label": "Altura da imagem em destaque", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}, "info": "Use uma imagem com taxa de proporção de 16:9 para ter os melhores resultados. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"blog_show_date": {"label": "Exibir data"}, "blog_show_author": {"label": "<PERSON><PERSON><PERSON> autor"}}}, "content": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "social_sharing": {"name": "Botões de compartilhamento nas redes sociais"}, "share": {"name": "Compartilhar", "settings": {"featured_image_info": {"content": "Se você incluir um link em publicações nas redes sociais, a imagem em destaque da página será exibida como na pré-visualização. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Um título e uma descrição da loja estão incluídos na imagem de pré-visualização. [Sai<PERSON> mais](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "Texto"}}}}}, "main-blog": {"name": "Posts do blog", "settings": {"header": {"content": "Cartão de post do blog"}, "show_image": {"label": "<PERSON><PERSON><PERSON> imagem em destaque", "info": "Use uma imagem com taxa de proporção de 2:3 para ter os melhores resultados. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "paragraph": {"content": "<PERSON>e os posts do blog para alterar os resumos. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/en/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "show_date": {"label": "Exibir data"}, "show_author": {"label": "<PERSON><PERSON><PERSON> autor"}}, "blocks": {"title": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"show_date": {"label": "Exibir data"}, "show_author": {"label": "<PERSON><PERSON><PERSON> autor"}}}, "summary": {"name": "Resumo"}, "link": {"name": "Link"}}}, "main-cart-footer": {"name": "Subtotal", "settings": {"show_cart_note": {"label": "Habilitar observação do carrinho"}}, "blocks": {"subtotal": {"name": "Preço subtotal"}, "buttons": {"name": "Botão de checkout"}}}, "main-cart-items": {"name": "<PERSON><PERSON>", "settings": {"show_vendor": {"label": "Exibir fabricante"}}}, "main-collection-banner": {"name": "Banner da coleção", "settings": {"paragraph": {"content": "Edite a coleção para adicionar imagens ou descrições. [<PERSON><PERSON> <PERSON>](https://help.shopify.com/en/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Exibir a descrição da coleção"}, "show_collection_image": {"label": "<PERSON><PERSON><PERSON> da coleção", "info": "Use uma imagem com taxa de proporção de 16:9 para ter os melhores resultados. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-collection-product-grid": {"name": "Grade de produtos", "settings": {"products_per_page": {"label": "Produtos por página"}, "enable_filtering": {"label": "Habilitar filtragem", "info": "Personalizar [filtros](/admin/menus)"}, "enable_sorting": {"label": "Habilitar organização"}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}}, "show_secondary_image": {"label": "Exibir segunda imagem ao passar o cursor"}, "add_image_padding": {"label": "Adicionar preenchimento à imagem"}, "show_vendor": {"label": "Exibir fabricante"}, "header__1": {"content": "Filtragem e organização"}, "header__3": {"content": "Cartão de produto"}, "enable_tags": {"label": "Habilitar a filtragem", "info": "[Personalizar filtros](/admin/menus)"}, "show_image_outline": {"label": "<PERSON><PERSON> borda da <PERSON>m"}, "enable_sort": {"label": "Habilitar organização"}, "collapse_on_larger_devices": {"label": "Recolher em telas maiores"}}}, "main-list-collections": {"name": "Página da lista de coleções", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "sort": {"label": "Organizar coleções por:", "options__1": {"label": "Ordem alfabética, A–Z"}, "options__2": {"label": "Ordem alfabética, Z–A"}, "options__3": {"label": "<PERSON>, mais recente primeiro"}, "options__4": {"label": "<PERSON>, mais antiga primeiro"}, "options__5": {"label": "Contagem de produtos, alta para baixa"}, "options__6": {"label": "Contagem de produtos, baixa para alta"}}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}, "info": "<PERSON>e as coleções para adicionar imagens. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/en/manual/products/collections)"}, "color_scheme": {"options__1": {"label": "Destaque 1"}, "options__2": {"label": "Destaque 2"}, "options__3": {"label": "Plano de fundo 1"}, "options__4": {"label": "Plano de fundo 2"}, "options__5": {"label": "Inverso"}, "label": "Esquema de cores"}, "image_padding": {"label": "Adicionar preenchimento à imagem"}}}, "main-page": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "main-password-footer": {"name": "<PERSON><PERSON><PERSON>", "settings": {"color_scheme": {"options__1": {"label": "Destaque 1"}, "options__2": {"label": "Destaque 2"}, "options__3": {"label": "Plano de fundo 1"}, "options__4": {"label": "Plano de fundo 2"}, "options__5": {"label": "Inverso"}, "label": "Esquema de cores"}}}, "main-password-header": {"name": "Cabeçal<PERSON>", "settings": {"logo": {"label": "Imagem do logo"}, "logo_max_width": {"label": "Largura personalizada do logo", "unit": "px"}, "color_scheme": {"options__1": {"label": "Destaque 1"}, "options__2": {"label": "Destaque 2"}, "options__3": {"label": "Plano de fundo 1"}, "options__4": {"label": "Plano de fundo 2"}, "options__5": {"label": "Inverso"}, "label": "Esquema de cores"}}}, "main-product": {"blocks": {"text": {"name": "Texto", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Letras maiúsculas"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"name": "Preço"}, "quantity_selector": {"name": "Se<PERSON>or de quantidade"}, "variant_picker": {"name": "Se<PERSON>or de variante", "settings": {"picker_type": {"label": "Tipo", "options__1": {"label": "<PERSON>u suspenso"}, "options__2": {"label": "Botão"}}}}, "buy_buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"show_dynamic_checkout": {"label": "<PERSON>ibir bot<PERSON><PERSON> de checkout dinâmico", "info": "Cada cliente vê a forma de pagamento preferencial dentre as disponíveis na loja, como PayPal ou Apple Pay. [<PERSON><PERSON> mais](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "pickup_availability": {"name": "Disponibilidade de retirada"}, "description": {"name": "Descrição"}, "share": {"name": "Compartilhar", "settings": {"featured_image_info": {"content": "Se você incluir um link em publicações nas redes sociais, a imagem em destaque da página será exibida como na pré-visualização. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Um título e uma descrição da loja estão incluídos na imagem de pré-visualização. [Sai<PERSON> mais](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "Texto"}}}, "collapsible_tab": {"name": "Aba recolhível", "settings": {"heading": {"info": "Inclua um título que explique o conteúdo.", "label": "<PERSON><PERSON><PERSON><PERSON>"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> da página"}, "icon": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Caixa"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Marca de se<PERSON>ção"}, "options__5": {"label": "Secador"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "Coração"}, "options__8": {"label": "<PERSON>rro"}, "options__9": {"label": "Fol<PERSON>"}, "options__10": {"label": "<PERSON><PERSON>"}, "options__11": {"label": "Cadeado"}, "options__12": {"label": "Marcador de mapa"}, "options__13": {"label": "Calças"}, "options__14": {"label": "Avião"}, "options__15": {"label": "Etiqueta de preço"}, "options__16": {"label": "Ponto de interrogação"}, "options__17": {"label": "Devolução"}, "options__18": {"label": "Régua"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Sapato"}, "options__21": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__22": {"label": "Estrela"}, "options__23": {"label": "Caminhão"}, "options__24": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Ícone"}}}, "popup": {"name": "Pop-up", "settings": {"link_label": {"label": "Etiqueta de link"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "custom_liquid": {"name": "Liquid personalizado", "settings": {"custom_liquid": {"label": "Liquid personalizado", "info": "Adicionar snippets de app ou outros códigos do Liquid para criar personalizações avançadas."}}}}, "settings": {"header": {"content": "Mí<PERSON>", "info": "Saiba mais sobre [tipos de mídia.](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Habilitar loop de vídeo"}, "enable_sticky_info": {"label": "Habilitar informações persistentes do produto em telas grandes"}, "hide_variants": {"label": "Ocultar a mídia de outras variantes após selecionar uma delas"}}, "name": "Informações do produto"}, "main-search": {"name": "Resultados da pesquisa", "settings": {"image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}}, "show_secondary_image": {"label": "Exibir segunda imagem ao passar o cursor"}, "add_image_padding": {"label": "Adicionar preenchimento à imagem"}, "show_vendor": {"label": "Exibir fabricante"}, "header__1": {"content": "Cartão de produto"}, "header__2": {"content": "Cartão de blog"}, "article_show_date": {"label": "Exibir data"}, "article_show_author": {"label": "<PERSON><PERSON><PERSON> autor"}, "show_image_outline": {"label": "<PERSON><PERSON> borda da <PERSON>m"}}}, "multicolumn": {"name": "Multicoluna", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "image_width": {"label": "<PERSON><PERSON><PERSON> da <PERSON>", "options__1": {"label": "Largura de um terço da coluna"}, "options__2": {"label": "Largura de metade da coluna"}, "options__3": {"label": "Largura total da coluna"}}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "column_alignment": {"label": "Alinham<PERSON><PERSON> da coluna", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}}, "background_style": {"label": "Fundo secundário", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Exibir como plano de fundo da coluna"}, "options__3": {"label": "Exibir como plano de fundo da seção"}}, "button_label": {"label": "Etiqueta de botão"}, "button_link": {"label": "Link de botão"}, "swipe_on_mobile": {"label": "Habilitar deslizamento em dispositivo móvel"}}, "blocks": {"column": {"name": "Coluna", "settings": {"image": {"label": "Imagem"}, "title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "text": {"label": "Descrição"}}}}, "presets": {"name": "Multicoluna"}}, "newsletter": {"name": "Assinante de e-mail", "settings": {"color_scheme": {"label": "Esquema de cores", "options__1": {"label": "Destaque 1"}, "options__2": {"label": "Destaque 2"}, "options__3": {"label": "Plano de fundo 1"}, "options__4": {"label": "Plano de fundo 2"}, "options__5": {"label": "Inverso"}}, "full_width": {"label": "Definir seção com largura total"}, "paragraph": {"content": "Cada assinatura por e-mail cria uma conta de cliente. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/en/manual/customers)"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "paragraph": {"name": "Subtítulo", "settings": {"paragraph": {"label": "Descrição"}}}, "email_form": {"name": "Formulário de e-mail"}}, "presets": {"name": "Assinante de e-mail"}}, "page": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "product-recommendations": {"name": "Recomendações de produtos", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "header__1": {"content": "Recomendações de produtos"}, "header__2": {"content": "Cartão de produto"}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}}, "show_secondary_image": {"label": "Exibir segunda imagem ao passar o cursor"}, "add_image_padding": {"label": "Adicionar preenchimento à imagem"}, "show_vendor": {"label": "Exibir fabricante"}, "paragraph__1": {"content": "As recomendações dinâmicas usam informações sobre pedidos e produtos para mudar e melhorar com o tempo. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/en/themes/development/recommended-products)"}, "show_image_outline": {"label": "<PERSON><PERSON> borda da <PERSON>m"}}}, "rich-text": {"name": "Rich text", "settings": {"color_scheme": {"options__1": {"label": "Destaque 1"}, "options__2": {"label": "Destaque 2"}, "options__3": {"label": "Plano de fundo 1"}, "options__4": {"label": "Plano de fundo 2"}, "options__5": {"label": "Inverso"}, "label": "Esquema de cores"}, "full_width": {"label": "Definir seção com largura total"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "heading_size": {"options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "label": "<PERSON><PERSON><PERSON> da fonte do título", "options__3": {"label": "Grande"}}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Descrição"}}}, "button": {"name": "Botão", "settings": {"button_label": {"label": "Etiqueta de botão"}, "button_link": {"label": "Link de botão"}, "button_style_secondary": {"label": "Usar estilo de botão com contorno"}}}}, "presets": {"name": "Rich text"}}, "apps": {"name": "Apps", "settings": {"include_margins": {"label": "<PERSON><PERSON>ar margens da seção iguais ao tema"}}, "presets": {"name": "Apps"}}, "video": {"name": "Vídeo", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "cover_image": {"label": "<PERSON><PERSON> de capa"}, "video_url": {"label": "URL", "placeholder": "Usar um URL do YouTube ou do Vimeo", "info": "O vídeo é reproduzido na página."}, "description": {"label": "Texto alternativo do vídeo", "info": "Descreva o vídeo para que seja acessível a clientes que usam leitores de tela."}, "image_padding": {"label": "Adicionar preenchimento de imagem", "info": "Selecione o preenchimento se não quiser que as imagens sejam cortadas."}, "full_width": {"label": "Definir seção com largura total"}}, "presets": {"name": "Vídeo"}}, "featured-product": {"name": "Produto em destaque", "blocks": {"text": {"name": "Texto", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Letras maiúsculas"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"name": "Preço"}, "quantity_selector": {"name": "Se<PERSON>or de quantidade"}, "variant_picker": {"name": "Se<PERSON>or de variante", "settings": {"picker_type": {"label": "Tipo", "options__1": {"label": "<PERSON>u suspenso"}, "options__2": {"label": "Botão"}}}}, "buy_buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"show_dynamic_checkout": {"label": "<PERSON>ibir bot<PERSON><PERSON> de checkout dinâmico", "info": "Cada cliente vê a forma de pagamento preferencial dentre as disponíveis na loja, como PayPal ou Apple Pay. [<PERSON><PERSON> mais](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Descrição"}, "share": {"name": "Compartilhar", "settings": {"featured_image_info": {"content": "Se você incluir um link em publicações nas redes sociais, a imagem em destaque da página será exibida como na pré-visualização. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "O título e a descrição da loja estão incluídos na imagem de pré-visualização. [Sai<PERSON> mais](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Texto"}}}, "custom_liquid": {"name": "Liquid personalizado", "settings": {"custom_liquid": {"label": "Liquid personalizado"}}}}, "settings": {"product": {"label": "Produ<PERSON>"}, "secondary_background": {"label": "Exibir plano de fundo secundário"}, "header": {"content": "Mí<PERSON>", "info": "Saiba mais sobre os [tipos de mídia](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Habilitar loop de vídeo"}}, "presets": {"name": "Produto em destaque"}}}}