{{ 'section-multicolumn.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif
-%}

<div class="multicolumn {{ container }} color-{{ section.settings.color_scheme }} {% unless section.settings.background_style == 'none' %} background-{{ section.settings.background_style }}{% endunless %}">
  <div class="section-{{ section.id }}-padding ">
    {%- unless section.settings.title == blank -%}
      <div class="title-wrapper-with-link text-{{ section.settings.heading_align }}">
        <h2 class="title {{ section.settings.heading_size }}">
          {{ section.settings.title | escape }}
        </h2>
      </div>
    {%- endunless -%}
    <div class="row row-cols-lg-{{ section.settings.columns_desktop }} row-cols-md-3 row-cols-sm-2 row-cols-{{ section.settings.columns_mobile }} multicolumn-list contains-content-container">
      {%- liquid
        assign highest_ratio = 0
        for block in section.blocks
          if block.settings.image.aspect_ratio > highest_ratio
            assign highest_ratio = block.settings.image.aspect_ratio
          endif
        endfor
      -%}
      {%- for block in section.blocks -%}
        {% liquid
          case block.settings.button_type
            when 'primary'
              assign button_class = 'button'
            when 'secondary'
              assign button_class = 'button button--secondary'
            when 'icon'
              assign button_class = 'link with--icon'
            else
              assign button_class = 'link'
          endcase
        %}

        <div
          id="Slide-{{ section.id }}-{{ forloop.index }}"
          class="multicolumn-list__item col {% if section.settings.column_alignment == 'center' %} center{% endif %} mb-30"
          {{ block.shopify_attributes }}
        >
          <div class="multicolumn-card content-container">
            {%- if block.settings.image != blank -%}
              {% if section.settings.image_ratio == 'adapt' or section.settings.image_ratio == 'circle' %}
                {% assign spaced_image = true %}
              {% endif %}
              <div class="multicolumn-card__image-wrapper multicolumn-card__image-wrapper--{{ section.settings.image_width }}-width {% if section.settings.image_width != 'full' or spaced_image %} multicolumn-card-spacing{% endif %}">
                <div
                  class="media media--transparent media--{{ section.settings.image_ratio }}"
                  {% if section.settings.image_ratio == 'adapt' %}
                    style="padding-bottom: {{ 1 | divided_by: highest_ratio | times: 100 }}%;"
                  {% endif %}
                >
                  <img
                    class="multicolumn-card__image"
                    srcset="
                      {%- if block.settings.image.width >= 275 -%}{{ block.settings.image | image_url: width: 275 }} 275w,{%- endif -%}
                      {%- if block.settings.image.width >= 550 -%}{{ block.settings.image | image_url: width: 550 }} 550w,{%- endif -%}
                      {%- if block.settings.image.width >= 710 -%}{{ block.settings.image | image_url: width: 710 }} 710w,{%- endif -%}
                      {%- if block.settings.image.width >= 1420 -%}{{ block.settings.image | image_url: width: 1420 }} 1420w,{%- endif -%}
                      {{ block.settings.image | image_url }} {{ block.settings.image.width }}w
                    "
                    src="{{ block.settings.image | image_url: width: 550 }}"
                    sizes="
                      (min-width: 990px) {% if section.blocks.size <= 2 %}710px{% else %}550px{% endif %},
                      (min-width: 750px) {% if section.blocks.size == 1 %}710px{% else %}550px{% endif %},
                      calc(100vw - 30px)
                    "
                    alt="{{ block.settings.image.alt }}"
                    height="{{ block.settings.image.height }}"
                    width="{{ block.settings.image.width }}"
                    loading="lazy"
                  >
                </div>
              </div>
            {%- endif -%}
            <div class="multicolumn-card__info">
              {%- if block.settings.title != blank -%}
                <h3 class="mb-0">{{ block.settings.title | escape }}</h3>
              {%- endif -%}
              {%- if block.settings.text != blank -%}
                <div class="rte">{{ block.settings.text }}</div>
              {%- endif -%}
              {%- if block.settings.link_label != blank -%}
                <a
                  class="{{ button_class }} button--{{ block.settings.button_size }}"
                  {% if block.settings.link == blank %}
                    role="link" aria-disabled="true"
                  {% else %}
                    href="{{ block.settings.link }}"
                  {% endif %}
                >
                  {{- block.settings.link_label | escape }}

                  {%- if block.settings.button_type == 'icon' -%}
                    <span class="multicolumn__arrow--wrap">
                      {%- render 'icon-arrow', icon_class: 'multicolumn__arrow--btn-icon' -%}
                    </span>
                  {%- endif -%}
                </a>
              {%- endif -%}
            </div>
          </div>
        </div>
      {%- endfor -%}
    </div>

    <div class="center">
      {%- if section.settings.button_label != blank -%}
        <a
          class="button button--primary"
          {% if section.settings.button_link == blank %}
            role="link" aria-disabled="true"
          {% else %}
            href="{{ section.settings.button_link }}"
          {% endif %}
        >
          {{ section.settings.button_label | escape }}
        </a>
      {%- endif -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.multicolumn.name",
  "class": "section",
  "tag": "section",
  "settings": [
    {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
    {
      "type": "text",
      "id": "title",
      "default": "Multicolumn",
      "label": "t:sections.multicolumn.settings.title.label"
    },
     {
        "type": "select",
        "id": "heading_size",
        "options": [
          {
            "value": "h2",
            "label": "Small"
          },
          {
            "value": "h1",
            "label": "Medium"
          },
          {
            "value": "h0",
            "label": "Large"
          }
        ],
        "default": "h1",
        "label": "Heading size"
      },
	{
      "type": "select",
      "id": "heading_align",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
		{
          "value": "right",
          "label": "Right"
        },
        {
          "value": "center",
          "label": "Center"
        }
      ],
      "default": "left",
      "label": "Heading alignment"
    },
    {
      "type": "select",
      "id": "image_width",
      "options": [
        {
          "value": "third",
          "label": "t:sections.multicolumn.settings.image_width.options__1.label"
        },
        {
          "value": "half",
          "label": "t:sections.multicolumn.settings.image_width.options__2.label"
        },
        {
          "value": "full",
          "label": "t:sections.multicolumn.settings.image_width.options__3.label"
        }
      ],
      "default": "full",
      "label": "t:sections.multicolumn.settings.image_width.label"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.multicolumn.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.multicolumn.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.multicolumn.settings.image_ratio.options__3.label"
        },
        {
          "value": "circle",
          "label": "t:sections.multicolumn.settings.image_ratio.options__4.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.multicolumn.settings.image_ratio.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 3,
      "label": "t:sections.multicolumn.settings.columns_desktop.label"
    },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.multicolumn.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.multicolumn.settings.column_alignment.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.multicolumn.settings.column_alignment.label"
    },
    {
      "type": "select",
      "id": "background_style",
      "options": [
        {
          "value": "none",
          "label": "t:sections.multicolumn.settings.background_style.options__1.label"
        },
        {
          "value": "primary",
          "label": "t:sections.multicolumn.settings.background_style.options__2.label"
        }
      ],
      "default": "primary",
      "label": "t:sections.multicolumn.settings.background_style.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "t:sections.multicolumn.settings.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.multicolumn.settings.button_link.label"
    },
      {
        "type": "color_scheme",
        "id": "color_scheme",
        "label": "t:sections.all.colors.label",
        "default": "background-1"
      },

    {
      "type": "header",
      "content": "t:sections.multicolumn.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__2.label"
        }
      ],
      "default": "1",
      "label": "t:sections.multicolumn.settings.columns_mobile.label"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
          "type": "paragraph",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        },
		{
          "type": "paragraph",
          "content": "Mobile"
        },
		{
          "type": "range",
          "id": "mobile_padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "mobile_padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        }
  ],
  "blocks": [
    {
      "type": "image_block",
      "name": "t:sections.multicolumn.blocks.column.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.multicolumn.blocks.column.settings.image.label"
        },
        {
          "type": "text",
          "id": "title",
          "default": "Column",
          "label": "t:sections.multicolumn.blocks.column.settings.title.label"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
          "label": "t:sections.multicolumn.blocks.column.settings.text.label"
        },
        {
          "type": "text",
          "id": "link_label",
          "label": "t:sections.multicolumn.blocks.column.settings.link_label.label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.multicolumn.blocks.column.settings.link.label"
        },
		{
          "type": "select",
          "id": "button_type",
          "label": "Button type",
          "default": "icon",
          "options": [
            {
              "value": "primary",
              "label": "Primary button"
            },
            {
              "value": "secondary",
              "label": "Secondary button"
            },
            {
              "value": "icon",
              "label": "Icon button"
            },
            {
              "value": "underline",
              "label": "Underline button"
            }
          ]
        },
		{
              "type": "select",
              "id": "button_size",
              "label": "Button size",
              "default": "small",
			  "info": "Works on the primary/secondary button",
              "options": [
                {
                  "value": "large",
                  "label": "Large"
                },
                {
                  "value": "medium",
                  "label": "Medium"
                },
				        {
                  "value": "small",
                  "label": "Small"
                }
              ]
            }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.multicolumn.presets.name",
      "blocks": [
        {
          "type": "image_block"
        },
        {
          "type": "image_block"
        },
        {
          "type": "image_block"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header","footer"]
  }
}
{% endschema %}
