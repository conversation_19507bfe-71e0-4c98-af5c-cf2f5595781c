@media only screen and (min-width: 768px) {
  .blog__items--meta__icon {
    margin-right: 0;
    margin-left: 10px;
  }
}
@media only screen and (max-width: 575px) {
  .blog__items--meta__icon {
    margin-right: 0;
    margin-left: 5px;
  }
}
p.article-card__excerpt,
h2.article-card__title {
  padding-right: 0;
  padding-left: 6rem;
}
h2.article-card__title > svg {
  right: auto;
  left: 0;
  transform: rotate(-180deg);
}
@media only screen and (min-width: 767px) {
  .blog-articles__article.first--article .article-card__info {
    padding-left: 0;
    padding-right: 50px;
  }
}
