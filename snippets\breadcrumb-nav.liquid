<nav role="navigation" aria-label="breadcrumbs">
  <ol class="breadcrumbs__list">
    <li class="breadcrumbs__item">
      <a class="breadcrumbs__link" href="/">{{ 'general.back_to_home_label' | t }}</a>
    </li>
    {%- case t -%}
    {%- when 'page' -%}
    <li class="breadcrumbs__item">
      <a class="breadcrumbs__link" href="{{ page.url }}" aria-current="page">{{ page.title }}</a>
    </li>
    {%- when 'product' -%}
    {%- if collection.url -%}
    <li class="breadcrumbs__item">
      {{ collection.title | link_to: collection.url }}
    </li>
    {%- endif -%}
    <li class="breadcrumbs__item">
      <a class="breadcrumbs__link" href="{{ product.url }}" aria-current="page">{{ product.title }}</a>
    </li>
    {%- when 'collection' and collection.handle -%}
    {%- if current_tags -%}
    <li class="breadcrumbs__item">
      {{ collection.title | link_to: collection.url }}
    </li>
    <li class="breadcrumbs__item">
      {%- capture tag_url -%}{{ collection.url }}/{{ current_tags | join: "+"}}{%- endcapture -%}
      <a class="breadcrumbs__link" href="{{ tag_url }}" aria-current="page">{{ current_tags | join: " + "}}</a>
    </li>
    {%- else -%}
    <li class="breadcrumbs__item">
      <a class="breadcrumbs__link" href="{{ collection.url }}" aria-current="page">{{ collection.title }}</a>
    </li>
    {%- endif -%}
    {%- when 'blog' -%}
    {%- if current_tags -%}
    <li class="breadcrumbs__item">
      {{ blog.title | link_to: blog.url }}
    </li>
    <li class="breadcrumbs__item">
      {%- capture tag_url -%}{{blog.url}}/tagged/{{ current_tags | join: "+" }}{%- endcapture -%}
      <a class="breadcrumbs__link" href="{{ tag_url }}" aria-current="page">{{ current_tags | join: " + " }}</a>
    </li>
    {%- else -%}
    <li class="breadcrumbs__item">
      <a class="breadcrumbs__link" href="{{ blog.url }}" aria-current="page">{{ blog.title }}</a>
    </li>
    {%- endif -%}
    {%- when 'article' -%}
    <li class="breadcrumbs__item">
      {{ blog.title | link_to: blog.url }}
    </li>
    <li class="breadcrumbs__item">
      <a class="breadcrumbs__link" href="{{ article.url }}" aria-current="page">{{ article.title }}</a>
    </li>
    {%- when 'customers/addresses' -%}
    <li class="breadcrumbs__item">
      <a href="{{ routes.account_url }}">
        {{ 'customer.account.title' | t }}
      </a>
    </li>
    <li class="breadcrumbs__item">
      {{ 'Adresss' }}
    </li>
    {%- when 'customers/account' -%} 
    <li class="breadcrumbs__item">
       {{ 'customer.account.title' | t }}
    </li>
    {%- else -%}
    <li class="breadcrumbs__item">
      <a class="breadcrumbs__link" href="{{ request.path }}" aria-current="page">{{ page_title }}</a>
    </li>
    {%- endcase -%}
  </ol>
</nav>