@media only screen and (max-width: 559px) {
  .category__product--list > div + div {
    margin-top: 30px;
  }
}

@media only screen and (min-width: 600px) and (max-width: 991px) {
  .category__product--list > div:last-child,
  .category__product--list > div:nth-child(3) {
    margin-top: 30px;
  }
}

.product__grid--heading {
  margin-bottom: 25px;
}
.product__grid--heading__title {
  font-size: 2.2rem;
  line-height: 2.5rem;
  font-weight: 700;
  position: relative;
  padding-bottom: 20px;
}

.product__grid--heading__title:before {
  position: absolute;
  content: "";
  width: 6rem;
  height: 0.3rem;
  background: var(--proudct-title-border-color);
  left: 0;
  bottom: 0;
}
@media only screen and (max-width: 991px) {
  .product__grid--heading__title {
    padding-bottom: 18px;
  }
}

.product__grid--items+.product__grid--items {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(var(--color-foreground),.1);
}

.product__grid--items:hover .product__items--img {
  -webkit-transform: scale(1.05);
  transform: scale(1.05);
}

.product__grid--items--thumbnail {
  position: relative;
  width: 81px;
}

.product__grid--items__img {
  display: block;
}

.product__grid--items--content {
  width: calc(100% - 81px);
  padding-left: 18px;
}

.product__grid--items--content__title {
    margin-bottom: 0.5rem;
}
@media only screen and (max-width: 991px) {
  .product__banner2 {
    text-align: center;
  }
}

.product__banner2--thumbnail {
  overflow: hidden;
}

@media only screen and (max-width: 575px) {
  .product__banner2--thumbnail {
    width: 100%;
  }
}

.product__banner2--thumbnail:hover .product__banner2--thumbnail__img {
  -webkit-transform: scale(1.03);
  transform: scale(1.03);
}

@media only screen and (max-width: 575px) {
  .product__banner2--thumbnail__img {
    width: 100%;
  }
}

.product__banner2--content {
  text-align: center;
  z-index: 9;
}
.product__banner2--content__title {
  font-weight: 700;
  color: rgba(var(--color-foreground));
  margin-bottom: 15px;
}

.product__banner2--content__subtitle {
  font-size: 1.6rem;
  line-height: 2.4rem;
  font-weight: 400;
  margin-bottom: 20px;
  color: rgba(var(--color-foreground));
}

@media only screen and (min-width: 576px) {
  .product__banner2--content__subtitle {
    font-size: 1.6rem;
    line-height: 2.2rem;
    margin-bottom: 19px;
  }
}
.product__banner2--thumbnail {
  overflow: hidden;
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.product__banner2--thumbnail::before {
  content: "";
  display: block;
}
.text--with__banner--media {
  position: absolute;
  height: 100%;
  top: 0;
  width: 100%;
  bottom: 0;
}

.product__grid--items .placeholder {
  width: 80px;
  height: 80px;
}
.product__grid--items .placeholder__content {
  padding-left: 15px;
}
.banner__items--media--small {
  min-height: 28rem;
}
.banner__items--media--medium {
  min-height: 35rem;
}
.banner__items--media--large {
  min-height: 40rem;
}

@media screen and (min-width: 767px) {
  .banner__items--media--small {
    min-height: 30rem;
  }
  .banner__items--media--medium {
    min-height: 40rem;
  }
  .banner__items--media--large {
    min-height: 50rem;
  }
}
a.product__items--link {
  display: block;
}
.product__items--link .media {
  height: 80px;
}

@media screen and (min-width: 990px) {
  .product__grid--items .media > img + img {
    opacity: 0;
  }
  .product__grid--items:hover .media > img ~ img.secondary__img {
    opacity: 1;
    transition: transform var(--duration-long) ease;
  }

  .product__grid--items:hover .media > img:first-child:not(:only-child) {
    opacity: 0;
  }
}
.product__items--price + .product__items--rating {
  margin-top: 5px;
}
a.product__banner2--thumbnail.banner__items--thumbnail.placeholder {
  height: 40rem;
}
.product__grid--items--content .price.product__card__price {
  justify-content: flex-start;
}
.banner__items--thumbnail::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: #000;
}

.product__banner2--thumbnail:hover .media > img {
  transform: scale(1.07);
}

.product__banner2--thumbnail .media > img {
  transition: all 0.7s ease 0s;
}
.product__grid--items--title-link:hover {
    color: rgba(var(--text-link-hover-color));
}