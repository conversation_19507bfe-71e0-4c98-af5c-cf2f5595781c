/* Recipient form */
.recipient-form {
  /* (2.88[line-height] - 1.6rem) / 2 */
  --recipient-checkbox-margin-top: 0.64rem;

  display: block;
  position: relative;
  max-width: 44rem;
  margin-bottom: 2.5rem;
}
.recipient-fields__field .text-area {
  max-height: 10rem;
  resize: auto;
  border-radius: 0.5rem;
  border: 0.1rem solid rgba(var(--color-foreground), 0.55);
}
.recipient-form-field-label {
  margin: 0.6rem 0;
}

.recipient-form-field-label--space-between {
  display: flex;
  justify-content: space-between;
}

.recipient-checkbox {
  flex-grow: 1;
  font-size: 1.6rem;
  display: flex;
  word-break: break-word;
  align-items: center;
  max-width: inherit;
  position: relative;
}

.no-js .recipient-checkbox {
  display: none;
}

.recipient-form > input[type="checkbox"] {
  position: absolute;
  opacity: 1;
  width: 1.6rem;
  height: 1.6rem;
  left: -0.4rem;
  z-index: -1;
  appearance: none;
  -webkit-appearance: none;
  margin-top: 7px;
}
.recipient-fields__field {
  margin: 0 0 2rem 0;
}
.facet-checked-box {
  background: rgba(var(--color-foreground));
  width: 1rem;
  height: 1rem;
  border-radius: 0.2rem;
  position: absolute;
  left: 0.4rem;
  transition: var(--transition);
  opacity: 0;
  top: 1rem;
}
.recipient-fields .field__label {
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: calc(100% - 3.5rem);
  overflow: hidden;
}

.recipient-checkbox > svg {
  margin-top: var(--recipient-checkbox-margin-top);
  margin-right: 1.2rem;
  flex-shrink: 0;
}

.recipient-form .icon-checkmark {
  visibility: hidden;
  position: absolute;
  left: 0.28rem;
  z-index: 5;
  top: 0.4rem;
}
.recipient-form > input[type="checkbox"]:checked + label .facet-checked-box {
  opacity: 0.9;
}
.js .recipient-fields {
  display: none;
}

.recipient-fields hr {
  margin: 1.6rem auto;
}

.recipient-form > input[type="checkbox"]:checked ~ .recipient-fields {
  display: block;
  animation: animateMenuOpen var(--duration-default) ease;
}
.recipient-form
  > input[type="checkbox"]:not(:checked, :disabled)
  ~ .recipient-fields,
.recipient-email-label {
  display: none;
}

.js .recipient-email-label.required,
.no-js .recipient-email-label.optional {
  display: inline;
}

.recipient-form ul {
  line-height: calc(1 + 0.6 / var(--font-body-scale));
  padding-left: 4.4rem;
  text-align: left;
}

.recipient-form ul a {
  display: inline;
}

.recipient-form .error-message::first-letter {
  text-transform: capitalize;
}
.recipient-form > input[type="checkbox"]:checked + label .icon-checkmark {
  visibility: visible;
  color: rgb(var(--color-background));
}
.recipient-form > input[type="checkbox"]:checked + label .checkbox-facet-check {
  background-color: rgba(var(--color-button), var(--alpha-button-background));
  box-shadow: 0 0 0 1px
    rgba(var(--color-button), var(--alpha-button-background));
}
@media screen and (forced-colors: active) {
  .recipient-fields > hr {
    border-top: 0.1rem solid rgb(var(--color-background));
  }

  .recipient-checkbox > svg {
    background-color: inherit;
    border: 0.1rem solid rgb(var(--color-background));
  }

  .recipient-form > input[type="checkbox"]:checked + label .icon-checkmark {
    border: none;
  }
}
