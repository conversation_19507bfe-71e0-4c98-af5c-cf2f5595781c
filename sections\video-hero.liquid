{% liquid
  assign dummy_video_URL = 'https://cdn.shopify.com/videos/c/o/v/fbb140fd46e3429eba459e1549dbbe38.mp4'
  assign video_url = section.settings.video.sources[1].url

  if video_url == blank
    assign video_url = dummy_video_URL
  endif

  assign data_type = 'mp4'
  if section.settings.video == null and section.settings.video_url != null
    assign video_url = section.settings.video_url
    if section.settings.video_url.type == 'youtube'
      assign data_type = 'youtube'
    else
      assign data_type = 'vimeo'
    endif
  endif
%}

{{ 'video-hero.css' | asset_url | stylesheet_tag }}

<style>
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
   .section-{{ section.id }}-padding .video__hero--media.media::before {
    opacity: {{ section.settings.image_overlay_opacity | divided_by: 100.0 }};
  }
  .video__hero--section.section-{{ section.id }}-padding{
    --color-foreground: {{ section.settings.foreground.red }}, {{ section.settings.foreground.green }}, {{ section.settings.foreground.blue }};
  }
  .section-{{ section.id }}-padding .button--secondary{
    --color-button: {{ section.settings.foreground.red }}, {{ section.settings.foreground.green }}, {{ section.settings.foreground.blue }};
    --color-button-text: {{ section.settings.foreground.red }}, {{ section.settings.foreground.green }}, {{ section.settings.foreground.blue }};
  }
</style>

<script src="{{ 'video-hero.js' | asset_url }}" defer></script>

{% liquid
  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif
%}

<div class="video__hero--section section-{{ section.id }}-padding">
  <div class="{{ container }} {% unless section.settings.show_offset %}p-0{% endunless %}">
    <div class="video__hero--media-wrapper">
      <div
        class="video__hero--media media video--hero__media--{{ section.settings.height }} {% if section.settings.video == blank %} video-section__placeholder {% endif %}"
        {% if section.settings.height == 'adapt' and section.settings.video != blank %}
          style="padding-bottom: {{ 1 | divided_by: section.settings.video.aspect_ratio | times: 100 }}%;"
        {% endif %}
      >
        <video-hero
          class="video--hero-inner"
          data-init="false"
          data-type="{{ data_type }}"
          data-video-id="{{ section.settings.video_url.id }}"
        >
          {% if data_type == 'mp4' and video_url != blank %}
            <video class="video-playsinline" src="{{ video_url }}" playsinline autoplay muted loop></video>
          {% endif %}
        </video-hero>
      </div>
      <div class="video__hero--banner-wrapper">
        <div class="video__hero--banner-grid">
          <div class="video__hero--banner-content">
            {%- for block in section.blocks -%}
              {% case block.type %}
                {%- when 'heading' -%}
                  <h2
                    class="video__hero--banner__heading {{ block.settings.heading_size }} mb-0"
                    {{ block.shopify_attributes }}
                  >
                    {{ block.settings.heading | escape }}
                  </h2>
                {%- when 'subheading' -%}
                  <div class="video__hero--banner-subheading h6 mb-0" {{ block.shopify_attributes }}>
                    {{ block.settings.subheading | escape }}
                  </div>
                {%- when 'text' -%}
                  <div
                    class="video__hero--banner__text rte {{ block.settings.text_style }}"
                    {{ block.shopify_attributes }}
                  >
                    {{ block.settings.text }}
                  </div>
                {%- when 'button' -%}
                  {% liquid
                    if block.settings.button_style == 'primary'
                      assign button_class = 'button--primary'
                    else
                      assign button_class = 'button--secondary'
                    endif
                  %}

                  {%- if block.settings.button_label != blank -%}
                    <div class="button__wrapper">
                      <a
                        {% if block.settings.button_link == blank %}
                          role="link" aria-disabled="true"
                        {% else %}
                          href="{{ block.settings.button_link }}"
                        {% endif %}
                        class="button button--{{ block.settings.button_size }} {{ button_class }} {% if block.settings.button_icon %} button--with-icon{% endif %}"
                        {{ block.shopify_attributes }}
                      >
                        {{ block.settings.button_label | escape }}

                        {% if block.settings.button_icon %}
                          <span class="button--icon button--icon-right"> {% render 'icon-arrow-right' %} </span>
                        {% endif %}
                      </a>
                    </div>
                  {%- endif -%}
              {%- endcase -%}
            {%- endfor -%}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Video hero",
  "tag": "section",
  "class": "spaced-section spaced-section--full-width",
  "settings": [
    {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
     {
       "type": "checkbox",
       "id": "show_offset",
       "label": "Enable Offset (left & right)",
       "default": false
     },
     {
      "type": "header",
      "content": "t:sections.video.settings.header__1.content"
    },
    {
      "type": "video",
      "id": "video",
      "label": "t:sections.video.settings.video.label"
    },
    {
      "type": "video_url",
      "id": "video_url",
      "accept": ["youtube", "vimeo"],
      "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc",
      "label": "External video URL"
    },
    {
      "type": "select",
      "id": "height",
      "options": [
        {
          "value": "adapt",
          "label": "Auto"
        },
         {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "small",
          "label": "t:sections.image-with-text.settings.height.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.image-with-text.settings.height.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.image-with-text.settings.height.label"
    },
    {
       "type": "range",
       "id": "image_overlay_opacity",
       "min": 0,
       "max": 100,
       "step": 10,
       "unit": "%",
       "label": "Video overlay opacity",
       "default": 50
     },
    {
      "type": "color",
      "id": "foreground",
      "default": "#fff",
      "label": "Text color"
    },
	{
      "type": "header",
      "content": "Section padding"
    },
    {
          "type": "paragraph",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        },
		{
          "type": "paragraph",
          "content": "Mobile"
        },
		{
          "type": "range",
          "id": "mobile_padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "mobile_padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "t:sections.image-with-text.blocks.heading.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Video hero title",
          "label": "t:sections.image-with-text.blocks.heading.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h3",
              "label": "Small"
            },
            {
              "value": "h2",
              "label": "Medium"
            },
            {
              "value": "h1",
              "label": "Large"
            },
            {
              "value": "h0",
              "label": "Extra large"
            }
          ],
          "default": "h1",
          "label": "t:sections.all.heading_size.label"
        }
      ]
    },
    {
      "type": "subheading",
      "name": "Subheading",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "subheading",
          "default": "Add a tagline",
          "label": "Subheading"
        },
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "h6",
              "label": "small"
            },
            {
              "value": "h5",
              "label": "Medium"
            },
            {
              "value": "h4",
              "label": "Large"
            }
          ],
          "default": "h5",
          "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.label"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.image-with-text.blocks.text.name",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
          "label": "t:sections.image-with-text.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.image-with-text.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.image-with-text.blocks.text.settings.text_style.options__2.label"
            }
          ],
          "default": "body",
          "label": "t:sections.image-with-text.blocks.text.settings.text_style.label"
        }
      ]
    },
    {
      "type": "button",
      "name": "t:sections.image-with-text.blocks.button.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "t:sections.image-with-text.blocks.button.settings.button_label.label",
          "info": "t:sections.image-with-text.blocks.button.settings.button_label.info"
        },
        {
             "type": "checkbox",
             "id": "button_icon",
             "label": "Use arrow icon",
             "default": true
           },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.image-with-text.blocks.button.settings.button_link.label"
        },
		{
              "type": "select",
              "id": "button_style",
              "label": "Button style",
              "default": "primary",
              "options": [
                {
                  "value": "secondary",
                  "label": "Secondary"
                },
                {
                  "value": "primary",
                  "label": "Primary"
                }
              ]
            },
			{
              "type": "select",
              "id": "button_size",
              "label": "Button size",
              "default": "small",
              "options": [
                {
                  "value": "large",
                  "label": "Large"
                },
                {
                  "value": "medium",
                  "label": "Medium"
                },
				        {
                  "value": "small",
                  "label": "Small"
                }
              ]
            }
      ]
    }
  ],
  "presets": [
    {
      "name": "Video hero",
       "blocks": [
        {
          "type": "subheading"
        },
        {
          "type": "heading"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header","footer"]
  }
}
{% endschema %}
