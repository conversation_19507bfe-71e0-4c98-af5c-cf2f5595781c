/**
 * Category Showcase - Advanced Interactive Features
 * Revolutionary category section with modern UX patterns
 */

class CategoryShowcase {
  constructor() {
    this.section = document.querySelector('.category-showcase');
    if (!this.section) return;

    this.cards = this.section.querySelectorAll('.category-card');
    this.isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    this.init();
  }

  init() {
    this.setupIntersectionObserver();
    this.setupCardInteractions();
    this.setupParallaxEffect();
    this.setupMagneticButtons();
    this.setupQuickActions();
    this.setupKeyboardNavigation();
    this.setupTouchInteractions();
    this.setupCustomRowLayout();
  }

  /**
   * Intersection Observer for animations
   */
  setupIntersectionObserver() {
    if (this.isReducedMotion) return;

    const observerOptions = {
      threshold: 0.1,
      rootMargin: '50px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const delay = entry.target.dataset.delay || 0;
          setTimeout(() => {
            entry.target.style.animationPlayState = 'running';
          }, delay);
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe animated elements
    const animatedElements = this.section.querySelectorAll('[class*="animate-"]');
    animatedElements.forEach(el => {
      el.style.animationPlayState = 'paused';
      observer.observe(el);
    });
  }

  /**
   * Enhanced card interactions
   */
  setupCardInteractions() {
    this.cards.forEach((card, index) => {
      // Mouse tracking for 3D effect
      if (!this.isReducedMotion) {
        card.addEventListener('mousemove', (e) => this.handleCardMouseMove(e, card));
        card.addEventListener('mouseleave', () => this.handleCardMouseLeave(card));
      }

      // Click analytics
      card.addEventListener('click', () => this.trackCardClick(card, index));

      // Performance optimized hover states
      card.addEventListener('mouseenter', () => this.preloadCardAssets(card));
    });
  }

  /**
   * 3D card tilt effect
   */
  handleCardMouseMove(e, card) {
    if (this.isReducedMotion) return;

    const rect = card.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const mouseX = e.clientX - centerX;
    const mouseY = e.clientY - centerY;
    
    const rotateX = (mouseY / rect.height) * -10;
    const rotateY = (mouseX / rect.width) * 10;
    
    const image = card.querySelector('.category-card__image');
    const content = card.querySelector('.category-card__content');
    
    requestAnimationFrame(() => {
      card.style.transform = `
        perspective(1000px) 
        rotateX(${rotateX}deg) 
        rotateY(${rotateY}deg) 
        translateZ(20px)
      `;
      
      if (image) {
        image.style.transform = `scale(1.1) translateZ(30px)`;
      }
      
      if (content) {
        content.style.transform = `translateZ(40px)`;
      }
    });
  }

  handleCardMouseLeave(card) {
    if (this.isReducedMotion) return;

    const image = card.querySelector('.category-card__image');
    const content = card.querySelector('.category-card__content');
    
    requestAnimationFrame(() => {
      card.style.transform = '';
      if (image) image.style.transform = '';
      if (content) content.style.transform = '';
    });
  }

  /**
   * Parallax scrolling effect
   */
  setupParallaxEffect() {
    if (this.isReducedMotion) return;

    let ticking = false;

    const updateParallax = () => {
      const scrolled = window.pageYOffset;
      const sectionTop = this.section.offsetTop;
      const sectionHeight = this.section.offsetHeight;
      const windowHeight = window.innerHeight;

      // Only apply parallax when section is in view
      if (scrolled + windowHeight > sectionTop && scrolled < sectionTop + sectionHeight) {
        const parallaxSpeed = (scrolled - sectionTop) * 0.1;
        
        this.cards.forEach((card, index) => {
          const speed = (index % 2 === 0) ? parallaxSpeed : -parallaxSpeed * 0.5;
          card.style.transform = `translateY(${speed}px)`;
        });
      }
      
      ticking = false;
    };

    const requestParallaxUpdate = () => {
      if (!ticking) {
        requestAnimationFrame(updateParallax);
        ticking = true;
      }
    };

    window.addEventListener('scroll', requestParallaxUpdate, { passive: true });
  }

  /**
   * Magnetic buttons effect
   */
  setupMagneticButtons() {
    if (this.isReducedMotion) return;

    const buttons = this.section.querySelectorAll('.category-card__button, .view-all-button');
    
    buttons.forEach(button => {
      button.addEventListener('mousemove', (e) => {
        const rect = button.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;
        
        const moveX = x * 0.3;
        const moveY = y * 0.3;
        
        button.style.transform = `translate(${moveX}px, ${moveY}px) scale(1.05)`;
      });
      
      button.addEventListener('mouseleave', () => {
        button.style.transform = '';
      });
    });
  }

  /**
   * Quick actions functionality
   */
  setupQuickActions() {
    const favoriteButtons = this.section.querySelectorAll('.quick-action--favorite');
    const shareButtons = this.section.querySelectorAll('.quick-action--share');

    favoriteButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.stopPropagation();
        this.toggleFavorite(button);
      });
    });

    shareButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.stopPropagation();
        this.shareCategory(button);
      });
    });
  }

  toggleFavorite(button) {
    const card = button.closest('.category-card');
    const isActive = button.classList.toggle('active');
    
    // Visual feedback
    if (!this.isReducedMotion) {
      button.style.transform = 'scale(1.2)';
      setTimeout(() => {
        button.style.transform = '';
      }, 200);
    }

    // Store in localStorage
    const categoryId = card.dataset.categoryId;
    if (categoryId) {
      const favorites = JSON.parse(localStorage.getItem('categoryFavorites') || '[]');
      if (isActive) {
        favorites.push(categoryId);
      } else {
        const index = favorites.indexOf(categoryId);
        if (index > -1) favorites.splice(index, 1);
      }
      localStorage.setItem('categoryFavorites', JSON.stringify(favorites));
    }

    // Trigger analytics
    this.trackFavoriteAction(categoryId, isActive);
  }

  async shareCategory(button) {
    const card = button.closest('.category-card');
    const link = card.querySelector('.category-card__link');
    const title = card.querySelector('.category-card__title');
    
    if (!link || !title) return;

    const shareData = {
      title: title.textContent.trim(),
      url: new URL(link.href, window.location.origin).href
    };

    try {
      if (navigator.share && navigator.canShare(shareData)) {
        await navigator.share(shareData);
        this.trackShareAction(shareData.title, 'native');
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(shareData.url);
        this.showShareFeedback(button);
        this.trackShareAction(shareData.title, 'clipboard');
      }
    } catch (error) {
      console.warn('Share failed:', error);
    }
  }

  showShareFeedback(button) {
    const originalHTML = button.innerHTML;
    button.innerHTML = '✓';
    button.style.color = '#22c55e';
    
    setTimeout(() => {
      button.innerHTML = originalHTML;
      button.style.color = '';
    }, 1500);
  }

  /**
   * Keyboard navigation support
   */
  setupKeyboardNavigation() {
    this.cards.forEach((card, index) => {
      card.setAttribute('tabindex', '0');
      card.setAttribute('role', 'button');
      card.setAttribute('aria-label', `Navigate to category ${index + 1}`);

      card.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          const link = card.querySelector('.category-card__link');
          if (link) link.click();
        }
      });

      // Focus management
      card.addEventListener('focus', () => {
        if (!this.isReducedMotion) {
          card.style.outline = '3px solid rgba(66, 153, 225, 0.6)';
          card.style.outlineOffset = '4px';
        }
      });

      card.addEventListener('blur', () => {
        card.style.outline = '';
        card.style.outlineOffset = '';
      });
    });
  }

  /**
   * Touch interactions for mobile
   */
  setupTouchInteractions() {
    if (!('ontouchstart' in window)) return;

    this.cards.forEach(card => {
      let touchStartTime = 0;
      let touchStartPos = { x: 0, y: 0 };

      card.addEventListener('touchstart', (e) => {
        touchStartTime = Date.now();
        touchStartPos = {
          x: e.touches[0].clientX,
          y: e.touches[0].clientY
        };
      }, { passive: true });

      card.addEventListener('touchend', (e) => {
        const touchEndTime = Date.now();
        const touchDuration = touchEndTime - touchStartTime;
        
        const touchEndPos = {
          x: e.changedTouches[0].clientX,
          y: e.changedTouches[0].clientY
        };
        
        const distance = Math.sqrt(
          Math.pow(touchEndPos.x - touchStartPos.x, 2) + 
          Math.pow(touchEndPos.y - touchStartPos.y, 2)
        );

        // Long press for quick actions
        if (touchDuration > 500 && distance < 10) {
          this.showQuickActionsMenu(card, touchEndPos);
        }
      }, { passive: true });
    });
  }

  showQuickActionsMenu(card, position) {
    // Create floating menu for mobile quick actions
    const menu = document.createElement('div');
    menu.className = 'quick-actions-menu';
    menu.style.cssText = `
      position: fixed;
      top: ${position.y - 60}px;
      left: ${position.x - 50}px;
      z-index: 1000;
      background: rgba(0, 0, 0, 0.9);
      border-radius: 25px;
      padding: 10px;
      display: flex;
      gap: 10px;
    `;

    const favoriteBtn = this.createMenuButton('♥', () => {
      const favoriteButton = card.querySelector('.quick-action--favorite');
      if (favoriteButton) this.toggleFavorite(favoriteButton);
    });

    const shareBtn = this.createMenuButton('↗', () => {
      const shareButton = card.querySelector('.quick-action--share');
      if (shareButton) this.shareCategory(shareButton);
    });

    menu.appendChild(favoriteBtn);
    menu.appendChild(shareBtn);
    document.body.appendChild(menu);

    // Remove menu after delay or on next touch
    setTimeout(() => {
      if (menu.parentNode) menu.remove();
    }, 3000);

    document.addEventListener('touchstart', () => {
      if (menu.parentNode) menu.remove();
    }, { once: true });
  }

  createMenuButton(icon, onClick) {
    const button = document.createElement('button');
    button.textContent = icon;
    button.style.cssText = `
      width: 40px;
      height: 40px;
      border: none;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border-radius: 50%;
      font-size: 18px;
      cursor: pointer;
    `;
    button.addEventListener('click', onClick);
    return button;
  }

  /**
   * Performance optimization - preload assets
   */
  preloadCardAssets(card) {
    const image = card.querySelector('.category-card__image');
    if (image && !image.dataset.preloaded) {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = image.src;
      document.head.appendChild(link);
      image.dataset.preloaded = 'true';
    }
  }

  /**
   * Analytics tracking
   */
  trackCardClick(card, index) {
    const categoryTitle = card.querySelector('.category-card__title')?.textContent?.trim();
    
    if (typeof gtag !== 'undefined') {
      gtag('event', 'category_card_click', {
        category_name: categoryTitle,
        card_position: index + 1,
        section_name: 'enhanced_category_showcase'
      });
    }

    // Custom analytics
    this.dispatchCustomEvent('categoryCardClick', {
      categoryTitle,
      cardPosition: index + 1,
      timestamp: Date.now()
    });
  }

  trackFavoriteAction(categoryId, isActive) {
    if (typeof gtag !== 'undefined') {
      gtag('event', isActive ? 'add_to_favorites' : 'remove_from_favorites', {
        category_id: categoryId,
        section_name: 'enhanced_category_showcase'
      });
    }

    this.dispatchCustomEvent('categoryFavorite', {
      categoryId,
      action: isActive ? 'add' : 'remove',
      timestamp: Date.now()
    });
  }

  trackShareAction(categoryTitle, method) {
    if (typeof gtag !== 'undefined') {
      gtag('event', 'share', {
        method: method,
        content_type: 'category',
        item_id: categoryTitle
      });
    }

    this.dispatchCustomEvent('categoryShare', {
      categoryTitle,
      method,
      timestamp: Date.now()
    });
  }

  dispatchCustomEvent(eventName, detail) {
    const event = new CustomEvent(`CategoryShowcase:${eventName}`, {
      detail,
      bubbles: true
    });
    this.section.dispatchEvent(event);
  }

  /**
   * Accessibility improvements
   */
  announceToScreenReader(message) {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    setTimeout(() => announcement.remove(), 1000);
  }

  /**
   * Responsive behavior
   */
  handleResize() {
    // Reset transforms on resize to prevent layout issues
    this.cards.forEach(card => {
      card.style.transform = '';
      const image = card.querySelector('.category-card__image');
      const content = card.querySelector('.category-card__content');
      if (image) image.style.transform = '';
      if (content) content.style.transform = '';
    });
  }
  
  /**
   * Custom Row Layout Handler - Works with ALL layout styles
   */
  setupCustomRowLayout() {
    // Look for any grid that has a custom pattern, regardless of layout style
    const grid = this.section.querySelector('.category-grid[data-custom-pattern]');
    if (!grid) {
      console.log('Custom row layout: No grid with custom pattern found');
      return;
    }

    const pattern = grid.dataset.customPattern;
    if (!pattern || pattern.trim() === '') {
      console.log('Custom row layout: No pattern specified');
      return;
    }

    console.log('Custom row layout: Initializing with pattern:', pattern);
    this.initializeCustomRows(grid, pattern);
    
    // Handle window resize to maintain layout
    let resizeTimeout;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        this.initializeCustomRows(grid, pattern);
      }, 250);
    });
  }

  initializeCustomRows(grid, pattern) {
    const cards = grid.querySelectorAll('.category-card');
    const rowPattern = pattern.split(',').map(num => parseInt(num.trim())).filter(num => !isNaN(num) && num > 0);
    const isMobile = window.innerWidth < 750;
    
    console.log('Custom row layout:', { 
      pattern, 
      rowPattern, 
      cardCount: cards.length, 
      isMobile,
      gridClasses: grid.className
    });
    
    // Validate pattern
    if (rowPattern.length === 0) {
      console.warn('Custom row layout: Invalid pattern, no valid numbers found');
      return;
    }
    
    if (isMobile) {
      // On mobile, use existing mobile layout logic
      cards.forEach((card, index) => {
        card.style.removeProperty('width');
        card.style.removeProperty('max-width');
        card.style.removeProperty('flex');
        card.style.removeProperty('margin-bottom');
      });
      return;
    }

    // Force grid to use flex layout for custom rows
    grid.style.setProperty('display', 'flex', 'important');
    grid.style.setProperty('flex-wrap', 'wrap', 'important');
    grid.style.setProperty('align-items', 'flex-start', 'important');
    grid.style.setProperty('justify-content', 'flex-start', 'important');
    grid.style.setProperty('max-width', '1099px');
    grid.style.setProperty('margin', '0 auto');

    

    // Apply custom row pattern on desktop
    let currentRow = 0;
    let positionInRow = 0;
    let totalCardsProcessed = 0;

    cards.forEach((card, index) => {
      const cardsInCurrentRow = rowPattern[currentRow % rowPattern.length];
      const widthPercent = 100 / cardsInCurrentRow;
      
      // Get actual gap value from CSS
      const computedStyle = getComputedStyle(grid);
      const gapHorizontal = computedStyle.getPropertyValue('--custom-gap-horizontal') || 
                          computedStyle.getPropertyValue('gap') || '2rem';
      
      // Convert rem/px to a workable value
      let gapValue = gapHorizontal;
      if (gapHorizontal.includes('rem')) {
        const remValue = parseFloat(gapHorizontal);
        gapValue = `${remValue * 16}px`; // Convert rem to px (assuming 16px = 1rem)
      }
      
      // Calculate width with gap adjustment
      const gapMultiplier = cardsInCurrentRow - 1;
      let finalWidth;
      
      if (gapMultiplier > 0) {
        finalWidth = `calc(${widthPercent}% - (${gapValue} * ${gapMultiplier} / ${cardsInCurrentRow}))`;
      } else {
        finalWidth = `${widthPercent}%`;
      }
      
      console.log(`Card ${index + 1}: Row ${currentRow + 1}, Position ${positionInRow + 1}/${cardsInCurrentRow}, Width: ${finalWidth}`);
      
      // Clear any existing positioning styles
      card.style.removeProperty('position');
      card.style.removeProperty('left');
      card.style.removeProperty('top');
      card.style.removeProperty('transform');
      
      // Apply custom row styles with maximum specificity
      card.style.setProperty('width', finalWidth, 'important');
      card.style.setProperty('max-width', finalWidth, 'important');
      card.style.setProperty('flex', `0 0 ${finalWidth}`, 'important');
      card.style.setProperty('box-sizing', 'border-box', 'important');
      card.style.setProperty('margin-bottom', 'var(--custom-gap-vertical, 2rem)', 'important');
      
      // Add margin-right for spacing except for last card in row
      if (positionInRow < cardsInCurrentRow - 1) {
        card.style.setProperty('margin-right', '0', 'important'); // Let gap handle spacing
      } else {
        card.style.setProperty('margin-right', '0', 'important');
      }
      
      positionInRow++;
      totalCardsProcessed++;
      
      // Move to next row when current row is filled
      if (positionInRow >= cardsInCurrentRow) {
        currentRow++;
        positionInRow = 0;
      }
    });
    
    console.log(`Custom row layout applied to ${totalCardsProcessed} cards across ${currentRow + 1} rows`);
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  const showcase = new CategoryShowcase();
  
  // Handle resize events
  let resizeTimeout;
  window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      if (showcase) showcase.handleResize();
    }, 100);
  });
});

// Load favorites from localStorage on page load
document.addEventListener('DOMContentLoaded', () => {
  const favorites = JSON.parse(localStorage.getItem('categoryFavorites') || '[]');
  
  favorites.forEach(categoryId => {
    const card = document.querySelector(`[data-category-id="${categoryId}"]`);
    if (card) {
      const favoriteBtn = card.querySelector('.quick-action--favorite');
      if (favoriteBtn) favoriteBtn.classList.add('active');
    }
  });
});
