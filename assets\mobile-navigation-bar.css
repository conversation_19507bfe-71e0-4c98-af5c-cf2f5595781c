@media only screen and (min-width: 750px) {
  .mobile__navigation--bar {
    display: none;
  }
}
.mobile__navigate--item {
  display: inline-flex;
  flex: 1;
  padding: 1.2rem 0;
  min-height: 4.5rem;
  align-items: center;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  flex-flow: column-reverse;
  background: none;
  border: none;
  position: relative;
}
.mobile__navigate--item-label {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 100%;
    line-height: 1;
    margin-top: .5rem;
    flex: 0 0 auto;
    color: #fff;
}
.mobile__navigate--item-icon > svg {
  width: 2rem;
}
.mobile__navigate--item.mobile__navigate--cart {
  padding: 0;
  min-height: auto;
}
.mobile__navigation--bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(var(--primary-button-hover-background));
    z-index: 98;
    padding-right: 8px;
    box-shadow: 0 0 10px #00000026;
}
.mobile__navigate--item-icon {
  line-height: 1;
  color: rgba(var(--secondary-button-hover-text));
  flex: 0 0 auto;
  position: relative;
}
.mobile__navigate--item-icon>svg {
    width: 2rem;
    height: 2rem;
    fill: rgba(var(--secondary-button-hover-text));
}
.mobile__navigation--bar .header__actions_btn_cart_num {
    top: -9px !important;
    right: -9px !important;
}


