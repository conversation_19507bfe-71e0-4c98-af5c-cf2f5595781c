theme.headerMainMenuModule=function(){function mainMenu(){let menuLiSelector;document.querySelectorAll(".header__menu_li").forEach(item=>{if(item.classList.contains("menu__item_has_children")){let menuItemUrl="",menuItemLocation="";item.addEventListener("mouseover",event=>{let listDetails;item.querySelector("details").setAttribute("open","")}),item.addEventListener("mouseleave",event=>{let listDetails;item.querySelector("details").removeAttribute("open")}),item.querySelector("summary").addEventListener("click",event=>{let itemAttr;event.stopPropagation(),menuItemUrl=item.querySelector("summary").dataset.href,menuItemLocation=`${window.location.origin}${menuItemUrl}`,item.querySelector("details").hasAttribute("open")&&(location.href=`${menuItemLocation}`)})}})}return mainMenu}();