<div class="product__grid--items d-flex">
  <div class="product__grid--items--thumbnail">
    <a class="product__items--link" href="{{ product_card_product.url | default: '#' }}">
      <div class="media">
        {%- liquid
          assign second_img_position = product_card_product.featured_media.position
          assign second_img_position = second_img_position | plus: 1
        -%}
        <img
          srcset="
            {%- if product_card_product.featured_media.width >= 165 -%}{{ product_card_product.featured_media | img_url: '165x' }} 165w,{%- endif -%}
            {%- if product_card_product.featured_media.width >= 360 -%}{{ product_card_product.featured_media| img_url: '360x' }} 360w,{%- endif -%}
            {%- if product_card_product.featured_media.width >= 533 -%}{{ product_card_product.featured_media | img_url: '533x' }} 533w,{%- endif -%}
          "
          src="{{ product_card_product.featured_media | img_url: '533x' }}"
          sizes="100px"
          alt="{{ product_card_product.featured_media.alt | escape }}"
          loading="lazy"
          class="motion-reduce"
          width="{{ product_card_product.featured_media.width }}"
          height="{{ product_card_product.featured_media.height }}"
        >
        {%- if product_card_product.media[second_img_position] != null and show_secondary_image -%}
          <img
            srcset="
              {%- if product_card_product.media[second_img_position].width >= 165 -%}{{ product_card_product.media[second_img_position] | img_url: '165x' }} 165w,{%- endif -%}
              {%- if product_card_product.media[second_img_position].width >= 360 -%}{{ product_card_product.media[second_img_position] | img_url: '360x' }} 360w,{%- endif -%}
              {%- if product_card_product.media[second_img_position].width >= 533 -%}{{ product_card_product.media[second_img_position] | img_url: '533x' }} 533w,{%- endif -%}
            "
            src="{{ product_card_product.media[second_img_position] | img_url: '533x' }}"
            sizes="100px"
            alt="{{ product_card_product.media[second_img_position].alt | escape }}"
            loading="lazy"
            class="motion-reduce secondary__img"
            width="{{ product_card_product.media[second_img_position].width }}"
            height="{{ product_card_product.media[second_img_position].height }}"
          >
        {%- endif -%}
      </div>
    </a>
  </div>
  <div class="product__grid--items--content smgrd_desc">
    {%- if show_rating and product_card_product.metafields.reviews.rating.value != blank -%}
        <div class="trustshop-collection-rating--item" rating-value="{{ product_card_product.metafields.reviews.rating.value }}" rating-count="{{ product_card_product.metafields.reviews.rating_count }}"></div>
    {%- endif -%}
    <h3 class="product__grid--items--content__title h6">
      <a class="product__grid--items--title-link" href="{{ product_card_product.url | default: '#' }}">
        {{- product_card_product.title -}}
      </a>
    </h3>
    <div class="product__items--price">
      {% render 'price', product: product_card_product, price_class: 'product__card__price' %}
    </div>
  </div>
</div>
