.facets-container {
  display: grid;
  grid-template-columns: repeat(2, auto);
  grid-template-rows: repeat(2, auto);
}

.active-facets-mobile {
  margin-bottom: 0.5rem;
}

.mobile-facets__list {
  overflow-y: auto;
}

@media screen and (min-width: 750px) {
  .facets-container > * + * {
    margin-top: 0;
  }

  .facets__form .product-count {
    grid-column-start: 3;
    align-self: flex-start;
  }
}

@media screen and (max-width: 989px) {
  .facets-container {
    grid-template-columns: auto minmax(0, max-content);
    column-gap: 2rem;
  }
}
.facet-filters {
  align-items: flex-start;
  display: flex;
  grid-column: 2;
  grid-row: 1;
  padding-left: 2.5rem;
}
@media screen and (min-width: 990px) {
  .facet-filters {
    padding-left: 3rem;
  }
}
.facet-filters__label {
  display: block;
  color: var(--color-foreground-85);
  font-size: 1.4rem;
  margin: 0 2rem 0 0;
}

.facet-filters__summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 1.4rem;
  cursor: pointer;
  height: 4.5rem;
  padding: 0 1.5rem;
  min-width: 25rem;
  margin-top: 2.4rem;
  border: 0.1rem solid rgba(var(--color-foreground), 0.55);
}

.facet-filters__summary::after {
  position: static;
}

.facet-filters__field {
  align-items: center;
  display: flex;
  flex-grow: 1;
  justify-content: flex-end;
}

.facet-filters__field .select {
  width: auto;
}

.facet-filters__field .select:after,
.facet-filters__field .select:before,
.mobile-facets__sort .select:after,
.mobile-facets__sort .select:before {
  content: none;
}

.facet-filters__field .select__select,
.mobile-facets__sort .select__select {
  border-radius: 0;
  min-width: auto;
  min-height: auto;
  transition: none;
}

.facet-filters button {
  margin-left: 2.5rem;
}

.facet-filters__sort {
  background-color: transparent;
  border: 0;
  border-radius: 0;
  font-size: 1.4rem;
  height: auto;
  margin: 0;
  padding: 8px 40px 7px 12px !important;
  border: 1px solid rgba(var(--color-foreground), 0.2);
}

.facet-filters__sort + .icon-caret {
  right: 1rem;
}

@media screen and (forced-colors: active) {
  .facet-filters__sort {
    border: none;
  }
}

.facet-filters__sort,
.facet-filters__sort:hover {
  box-shadow: none;
  filter: none;
  transition: none;
}

.mobile-facets__sort .select__select:focus-visible {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 0.3rem;
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}

.mobile-facets__sort .select__select.focused,
.no-js .mobile-facets__sort .select__select:focus {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 0.3rem;
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}

.facet-filters__sort:focus-visible {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
}

.facet-filters__sort.focused,
.no-js .facet-filters__sort:focus {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 1rem;
  box-shadow: 0 0 0 1rem rgb(var(--color-background)),
    0 0 0.2rem 1.2rem rgba(var(--color-foreground), 0.3);
}

.no-js .facet-filters__sort:focus:not(:focus-visible),
.no-js .mobile-facets__sort .select__select:focus:not(:focus-visible) {
  outline: 0;
  box-shadow: none;
}

.facets {
  display: block;
  grid-column-start: span 2;
}

.facets__form {
  display: grid;
  gap: 0 3.5rem;
  grid-template-columns: 1fr max-content max-content;
  margin-bottom: 0.5rem;
}

.facets__wrapper {
  align-items: center;
  align-self: flex-start;
  grid-column: 1;
  grid-row: 1;
  display: flex;
  flex-wrap: wrap;
}

.facets__heading {
  display: block;
  color: rgba(var(--color-foreground), 0.85);
  font-size: 1.4rem;
  margin: -1.5rem 2rem 0 0;
}

.facets__reset {
  margin-left: auto;
}

.facets__disclosure {
  margin-right: 3.5rem;
}

.facets__summary {
  color: rgba(var(--color-foreground), 0.75);
  font-size: 1.6rem;
  padding: 0 1.75rem 0 0;
  margin-bottom: 1.5rem;
}

.facets__disclosure fieldset {
  padding: 0;
  margin: 0;
  border: 0;
}

.facets__disclosure[open] .facets__summary,
.facets__summary:hover {
  color: rgb(var(--color-foreground));
}

.facets__disclosure[open] .facets__display {
  animation: animateMenuOpen var(--duration-default) ease;
}

.facets__summary span {
  transition: text-decoration var(--duration-short) ease;
  font-weight: 600;
}

.facets__summary:hover span {
    text-decoration: none;
    text-underline-offset: .3rem;
}
.facet-checkbox {
    text-transform: capitalize;
}
.disclosure-has-popup[open] > .facets__summary::before {
  z-index: 2;
}

.facets__summary .icon-caret {
  right: 0;
}

.facets__display {
  background-color: rgb(var(--color-background));
  position: absolute;
  top: calc(100% + 0.5rem);
  left: -1.2rem;
  width: 35rem;
  max-height: 55rem;
  overflow-y: auto;
}

.facets__header {
  border-bottom: 1px solid rgba(var(--color-foreground), 0.2);
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  font-size: 1.4rem;
  position: sticky;
  top: 0;
  background-color: rgb(var(--color-background));
  z-index: 1;
}

.facets__list {
  padding: 0.5rem 2rem;
}

.facets__item {
  display: flex;
  align-items: center;
}

.facets__item label,
.facets__item input[type="checkbox"] {
  cursor: pointer;
}

.facet-checkbox {
  padding: 1rem 0 1rem 0;
  flex-grow: 1;
  position: relative;
  font-size: 1.4rem;
  display: flex;
  word-break: break-word;
  align-items: center;
}
span.facet__checkbox-count {
  margin-left: auto;
  padding-left: 1rem;
}
.facet-checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 1;
  width: 1.6rem;
  height: 1.6rem;
  left: -0.4rem;
  z-index: -1;
  appearance: none;
  -webkit-appearance: none;
}

.no-js .facet-checkbox input[type="checkbox"] {
  z-index: 0;
}
.facet-checkbox .icon-checkmark {
  visibility: hidden;
  position: absolute;
  left: 0.3rem;
  z-index: 5;
}
.facet-checkbox > input[type="checkbox"]:checked ~ .icon-checkmark {
  visibility: visible;
}

@media screen and (forced-colors: active) {
  .facet-checkbox > svg {
    background-color: inherit;
    border: 0.1rem solid rgb(var(--color-background));
  }

  .facet-checkbox > input[type="checkbox"]:checked ~ .icon-checkmark {
    border: none;
  }
}

/* checkbox css start  */
.checkbox-facet-check {
  flex: none;
  position: relative;
  z-index: 1;
  width: 1.8rem;
  height: 1.8rem;
  margin-right: 1rem;
  background-color: rgba(var(--color-background));
  border-radius: 3px;
  transition: background-color 0.18s ease;
  box-shadow: 0 0 0 1px rgba(var(--color-foreground), 0.2);
}
.facet-checkbox > input[type="checkbox"]:checked ~ .checkbox-facet-check {
  background-color: rgba(var(--color-button), var(--alpha-button-background));
  box-shadow: 0 0 0 1px
    rgba(var(--color-button), var(--alpha-button-background));
}
.facet-checkbox > svg {
  color: rgb(var(--color-background));
  margin-right: 1.2rem;
  flex-shrink: 0;
}
.facet-checkbox:hover .checkbox-facet-check {
  box-shadow: 0 0 0 1px
    rgba(var(--color-button), var(--alpha-button-background));
}
/* checkbox css end  */

.facet-checkbox--disabled {
  color: rgba(var(--color-foreground), 0.4);
}

.facets__price {
  display: flex;
  padding: 2rem;
}

.facets__price .field + .field-currency {
  margin-left: 2rem;
}

.facets__price .field {
  align-items: center;
}

.facets__price .field-currency {
  align-self: center;
  margin-right: 0.6rem;
}

.facets__price .field__label {
  left: 1.5rem;
}

button.facets__button {
  min-height: 0;
  margin: 0 0 0 0.5rem;
  box-shadow: none;
  padding-top: 1.4rem;
  padding-bottom: 1.4rem;
}

.facets__button-no-js {
  transform: translateY(-0.6rem);
}

.active-facets {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  grid-column: 1 / -1;
  grid-row: 2;
  margin-top: -0.5rem;
}

.active-facets__button {
  display: block;
  margin-right: 1.5rem;
  margin-top: 1.5rem;
  padding-left: 0.2rem;
  padding-right: 0.2rem;
  text-decoration: none;
}

span.active-facets__button-inner {
    color: rgb(var(--color-foreground));
    box-shadow: 0 0 0 .1rem rgb(var(--color-foreground));
    border-radius: 2.6rem;
    font-size: 1rem;
    min-height: 0;
    min-width: 0;
    padding: .5rem 2rem;
    display: flex;
    align-items: stretch;
}

span.active-facets__button-inner:before,
span.active-facets__button-inner:after {
  display: none;
}

.active-facets__button-wrapper {
  align-items: center;
  display: flex;
  justify-content: center;
  padding-top: 1.5rem;
}

.active-facets__button-wrapper * {
  font-size: 1rem;
}

@media screen and (min-width: 990px) {
  .active-facets__button {
    margin-right: 1.5rem;
  }

  .active-facets__button-wrapper *,
  span.active-facets__button-inner {
    font-size: 1.4rem;
  }
}

@media screen and (max-width: 989px) {
  .active-facets {
    margin: 0 -1.2rem -1.2rem;
  }

  .active-facets__button,
  .active-facets__button-remove {
    margin: 0;
    padding: 1.2rem;
  }

  span.active-facets__button-inner {
    padding-bottom: 0.3rem;
    padding-top: 0.3rem;
  }

  .active-facets__button-wrapper {
    padding-top: 0;
    margin-left: 1.2rem;
  }
}

.active-facets__button:hover .active-facets__button-inner {
  box-shadow: 0 0 0 0.2rem rgb(var(--color-foreground));
}

.active-facets__button--light .active-facets__button-inner {
  box-shadow: 0 0 0 0.1rem rgba(var(--color-foreground), 0.2);
}

.active-facets__button--light:hover .active-facets__button-inner {
  box-shadow: unset;
}

a.active-facets__button:focus-visible {
  outline: none;
  box-shadow: none;
}

a.active-facets__button.focused,
.no-js a.active-facets__button:focus {
  outline: none;
  box-shadow: none;
}

a.active-facets__button:focus-visible .active-facets__button-inner {
  box-shadow: 0 0 0 0.1rem rgba(var(--color-foreground), 0.2),
    0 0 0 0.2rem rgb(var(--color-background)),
    0 0 0 0.4rem rgb(var(--color-foreground));
  outline: none;
}

a.active-facets__button.focused .active-facets__button-inner,
.no-js a.active-facets__button:focus .active-facets__button-inner {
  box-shadow: 0 0 0 0.1rem rgba(var(--color-foreground), 0.2),
    0 0 0 0.2rem rgb(var(--color-background)),
    0 0 0 0.4rem rgb(var(--color-foreground));
  outline: none;
}

.active-facets__button svg {
  align-self: center;
  flex-shrink: 0;
  margin-left: 0.6rem;
  margin-right: -0.2rem;
  pointer-events: none;
  width: 1.2rem;
}

@media all and (min-width: 990px) {
  .active-facets__button svg {
    margin-right: -0.4rem;
    margin-top: 0.1rem;
    width: 1.4rem;
  }
}

.active-facets facet-remove:only-child {
  display: none;
}

.facets-vertical
  .active-facets
  .active-facets-vertical-filter:only-child
  > facet-remove {
  display: none;
}

.facets-vertical .active-facets-vertical-filter {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.facets-vertical .active-facets-vertical-filter .active-facets__button-wrapper {
  padding-top: 0;
  display: flex;
  align-items: flex-start;
}

.facets-vertical .active-facets__button {
  margin-top: 0;
}

.active-facets__button.disabled,
.mobile-facets__clear.disabled {
  pointer-events: none;
}

.mobile-facets__clear-wrapper {
  align-items: center;
  display: flex;
  justify-content: center;
}

.mobile-facets {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  background-color: rgba(var(--color-foreground), 0.5);
  pointer-events: none;
}

.mobile-facets__disclosure {
  display: flex;
}

.mobile-facets__wrapper {
  margin-left: 0;
  display: flex;
}

.mobile-facets__wrapper .disclosure-has-popup[open] > summary::before {
  height: 100vh;
  z-index: 3;
}

.mobile-facets__inner {
  background-color: rgb(var(--color-background));
  width: calc(100% - 5rem);
  margin-left: auto;
  height: 100%;
  overflow-y: auto;
  pointer-events: all;
  transition: transform var(--duration-short) ease;
  max-width: 37.5rem;
  display: flex;
  flex-direction: column;
}
.menu-opening .mobile-facets__inner {
  transform: translateX(0);
}

.js .disclosure-has-popup:not(.menu-opening) .mobile-facets__inner {
  transform: translateX(105vw);
}

.mobile-facets__header {
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
  padding: 1rem 2.5rem;
  text-align: center;
  display: flex;
  position: sticky;
  top: 0;
  z-index: 2;
}

.mobile-facets__header-inner {
  flex-grow: 1;
  position: relative;
}

.mobile-facets__info {
  padding: 0 2.6rem;
}

.mobile-facets__heading {
  font-size: calc(var(--font-heading-scale) * 1.4rem);
  margin: 0;
}

.mobile-facets__count {
  color: rgba(var(--color-foreground), 0.7);
  font-size: 1.3rem;
  margin: 0;
  flex-grow: 1;
}

.mobile-facets__open-wrapper {
  display: inline-block;
}

.mobile-facets__open {
  text-align: left;
  width: 100%;
  display: flex;
  align-items: center;
  color: rgba(var(--color-link), var(--alpha-link));
  background: rgba(var(--color-foreground), 0.06);
  padding: 1rem;
  border-radius: 3px;
}
.mobile-facets__open:hover {
  color: rgb(var(--color-link));
}

.mobile-facets__open:hover line,
.mobile-facets__open:hover circle {
  stroke: rgb(var(--color-link));
}

.mobile-facets__open-label {
  transition: text-decoration var(--duration-short) ease;
}

.mobile-facets__open:hover .mobile-facets__open-label {
  text-decoration: underline;
  text-underline-offset: 0.3rem;
}

.mobile-facets__open > * + * {
  margin-left: 1rem;
}

.mobile-facets__open svg {
  width: 2rem;
}

.mobile-facets__open line,
.mobile-facets__open circle {
  stroke: rgba(var(--color-link), var(--alpha-link));
}

.mobile-facets__close {
  display: none;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0.7rem;
  right: 1rem;
  width: 4.4rem;
  height: 4.4rem;
  z-index: 101;
  opacity: 0;
  transition: opacity var(--duration-short) ease;
}

.mobile-facets__close svg {
  width: 2.2rem;
}

details.menu-opening .mobile-facets__close {
  display: flex;
  opacity: 1;
}

details.menu-opening .mobile-facets__close svg {
  margin: 0;
}

.mobile-facets__close-button {
  align-items: center;
  background-color: transparent;
  display: flex;
  font-size: 1.4rem;
  font: inherit;
  letter-spacing: inherit;
  margin-top: 1.5rem;
  padding: 1.2rem 2.6rem;
  text-decoration: none;
  width: calc(100% - 5.2rem);
}

.no-js .mobile-facets__close-button {
  display: none;
}

.mobile-facets__close-button .icon-arrow {
  transform: rotate(180deg);
  margin-right: 1rem;
}

.mobile-facets__main {
  padding: 2.7rem 0 0;
  position: relative;
  z-index: 1;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.mobile-facets__details[open] .icon-caret {
  transform: rotate(180deg);
}

.no-js .mobile-facets__details {
  border-bottom: 1px solid rgba(var(--color-foreground), 0.04);
}

.mobile-facets__highlight {
  opacity: 0;
  visibility: hidden;
}

.mobile-facets__checkbox:checked + .mobile-facets__highlight {
  visibility: visible;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  display: block;
  background-color: rgba(var(--color-foreground), 0.04);
}

.mobile-facets__summary {
  padding: 1.3rem 2.5rem;
}

.mobile-facets__summary svg {
  margin-left: auto;
}

.mobile-facets__summary > div,
.facets__summary > div {
  display: flex;
  align-items: center;
}

.js .mobile-facets__submenu {
  position: absolute;
  top: 0;
  width: 100%;
  bottom: 0;
  left: 0;
  z-index: 3;
  transform: translateX(100%);
  visibility: hidden;
  display: flex;
  flex-direction: column;
}

.js details[open] > .mobile-facets__submenu {
  transition: transform 0.4s cubic-bezier(0.29, 0.63, 0.44, 1),
    visibility 0.4s cubic-bezier(0.29, 0.63, 0.44, 1);
}

.js details[open].menu-opening > .mobile-facets__submenu {
  transform: translateX(0);
  visibility: visible;
}

.js .menu-drawer__submenu .mobile-facets__submenu {
  overflow-y: auto;
}

.js .mobile-facets .submenu-open {
  visibility: hidden; /* hide menus from screen readers when hidden by submenu */
}

.mobile-facets__item {
  position: relative;
}

input.mobile-facets__checkbox {
  border: 0;
  position: absolute;
  width: 1.6rem;
  height: 1.6rem;
  position: absolute;
  left: 2.1rem;
  top: 1.2rem;
  z-index: 0;
  appearance: none;
  -webkit-appearance: none;
}
.mobile-facets__label {
  padding: 1rem 2rem 1rem 2.5rem;
  width: 100%;
  transition: background-color 0.2s ease;
  word-break: break-word;
  display: flex;
  align-items: center;
}

.mobile-facets__label > svg {
  background-color: rgb(var(--color-background));
  position: relative;
  z-index: 2;
  margin-right: 1.2rem;
  flex-shrink: 0;
}

.mobile-facets__label .icon-checkmark {
  position: absolute;
  top: 2.1rem;
  left: 2.8rem;
  visibility: hidden;
}
input.mobile-facets__checkbox:focus {
  box-shadow: unset;
}
.mobile-facets__label > input[type="checkbox"]:checked ~ .icon-checkmark {
  visibility: visible;
}

.mobile-facets__arrow,
.mobile-facets__summary .icon-caret {
  margin-left: auto;
  display: block;
}

.mobile-facets__label--disabled {
  opacity: 0.5;
}

.mobile-facets__footer {
  border-top: 0.1rem solid rgba(var(--color-foreground), 0.08);
  padding: 2rem;
  bottom: 0;
  position: sticky;
  display: flex;
  z-index: 2;
  margin-top: auto;
  background-color: rgb(var(--color-background));
}

.mobile-facets__footer > * + * {
  margin-left: 1rem;
}

.mobile-facets__footer > * {
  width: 50%;
}

.mobile-facets__footer noscript .button {
  width: 100%;
}

.mobile-facets__sort {
  display: flex;
  justify-content: space-between;
}

.mobile-facets__sort label {
  flex-shrink: 0;
}

.mobile-facets__sort .select {
  width: auto;
}

.no-js .mobile-facets__sort .select {
  position: relative;
  right: -1rem;
}

.mobile-facets__sort .select .icon-caret {
  right: 0;
}

.mobile-facets__sort .select__select {
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
  filter: none;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  padding-left: 0.5rem;
  padding-right: 1.5rem;
}

.product-count {
  align-self: center;
  position: relative;
  text-align: right;
}

.product-count__text {
  font-size: 1.4rem;
  margin: 0;
}

.product-count__text.loading {
  visibility: hidden;
}

.product-count .loading-overlay__spinner,
.product-count-vertical .loading-overlay__spinner {
  display: none;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1.8rem;
}

.product-count__text.loading + .loading-overlay__spinner {
  display: block;
}

@media screen and (min-width: 750px) {
  .facets-vertical {
    display: flex;
  }

  .facets-wrap-vertical {
    border: none;
    padding-left: 0;
    padding-right: 0;
  }

  .facets__form-vertical {
    display: flex;
    flex-direction: column;
    width: 26rem;
  }

  .facets__disclosure-vertical {
    border-top: 1px solid rgba(var(--color-foreground), 0.2);
    margin-right: 0;
  }

  .facets-vertical .facets__summary {
    padding-top: 1.5rem;
    margin-bottom: 0;
    padding-bottom: 1.5rem;
  }

  .facets__heading--vertical {
    margin: 0 0 1.5rem 0;
    font-size: 1.5rem;
  }

  .facets__header-vertical {
    padding: 1.5rem 2rem 0 0;
    font-size: 1.4rem;
  }

  .facets__display-vertical {
    padding-bottom: 1.5rem;
  }

  .facets-vertical .facets-wrapper {
    padding-right: 3rem;
  }

  .facets-vertical .facets-wrapper--no-filters {
    display: none;
  }

  .no-js .facets-vertical .facets-wrapper--no-filters {
    display: block;
  }

  .facets-vertical .product-grid-container {
    width: 100%;
  }

  .facets-vertical-form {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .product-count-vertical {
    margin-left: 2rem;
  }

  .facets-vertical .active-facets__button-wrapper {
    margin-bottom: 2rem;
  }

  .facets-vertical .no-js .facets__button-no-js {
    transform: none;
    margin-left: 0;
  }

  .facets-vertical .no-js .facet-filters__field {
    justify-content: flex-start;
    padding-bottom: 1rem;
    padding-top: 2rem;
  }

  .facets-vertical .facets__price {
    padding: 0.5rem 0.5rem 0.5rem 0;
  }

  .facets-vertical .facets__price .field:last-of-type {
    margin-left: 1rem;
  }

  .facets-vertical .active-facets__button {
    margin-bottom: 1.5rem;
  }

  .no-js .facets-vertical .facet-filters.sorting {
    padding-left: 0;
    flex-direction: column;
  }

  .facets-vertical .facet-checkbox input[type="checkbox"] {
    z-index: 0;
  }

  .no-js .facets-vertical .facets-container {
    display: flex;
    flex-direction: column;
  }

  .facets-vertical .active-facets facet-remove:last-of-type {
    margin-bottom: 1rem;
  }

  .facets-vertical .active-facets {
    margin: 0;
    align-items: flex-start;
  }

  .facets__disclosure-vertical[open] .facets__summary .icon-caret {
    transform: rotate(180deg);
  }

  .facets-container-drawer {
    display: flex;
    flex-flow: row wrap;
    align-items: center;
    column-gap: 0;
  }

  .facets-container-drawer .mobile-facets__wrapper {
    margin-right: 2rem;
  }

  .facets-container-drawer .product-count {
    margin: 0 0 0.5rem 3.5rem;
  }

  .facets-container-drawer .facets-pill {
    width: 100%;
  }

  .facets-container-drawer .facets__form {
    display: block;
  }
}

@media screen and (min-width: 750px) and (max-width: 989px) {
  .facets-vertical .active-facets__button {
    padding: 1rem;
    margin-bottom: 0;
    margin-left: -0.5rem;
  }

  .facets-vertical .active-facets__button-remove {
    padding: 0 1rem 1rem;
  }
}
.icon-arrow {
  width: 1.5rem;
}
.button-show-more.link {
  font-size: 1.5rem;
}
.underlined-link {
  color: rgba(var(--color-link), var(--alpha-link));
  text-underline-offset: 0.3rem;
  text-decoration-thickness: 0.1rem;
  transition: text-decoration-thickness ease 0.1s;
  text-decoration: underline;
}
.single__widget_inner.mobile-facets__price--range {
  padding: 0 2.6rem;
  margin-top: 0;
}
.Mobile--price__with--slider .slider-price {
  margin-top: 1rem;
}
.single__widget_inner.horizontal-facets__price--range {
  padding: 1.5rem 2.5rem 2.5rem;
}
/*  New css */
.Mobile--price__with--slider {
  padding: 0 2.5rem;
}
.facets__disclosure.price__filter .price__slider--filter {
  padding: 1.5rem 2rem;
}

.facets__disclosure.price__filter .price__widget:not(.price__slider--filter) {
  padding: 1rem 2rem 2rem;
}
.facets__header--price-max {
  font-size: 1.6rem;
  color: rgba(var(--color-foreground));
  margin-bottom: 1rem;
}

.facets__disclosure-vertical.facets__vertical--widget-last
  .facets__display-vertical {
  padding-bottom: 0;
}
.facets__disclosure-vertical.facets__vertical--widget-last
  fieldset.facets-wrap {
  padding-bottom: 0;
}
.facets-container.filter--inner-spacing-true {
  padding: 1.5rem;
}
.facets-container.filter--radius-true {
  border-radius: 1rem;
}
.facets-wrap-vertical {
    max-height: 240px;
    overflow: auto;
    padding-right: 20px;
    padding-left: 1px;
}

































