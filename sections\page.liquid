<link rel="stylesheet" href="{{ 'component-rte.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'section-main-page.css' | asset_url }}" media="print" onload="this.media='all'">

<noscript>{{ 'component-rte.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'section-main-page.css' | asset_url | stylesheet_tag }}</noscript>

<div class="page-width page-width--narrow">
  <h2 class="page-title">
    {%- if section.settings.page.title != blank -%}
      {{ section.settings.page.title | escape }}
    {%- else -%}
      Page title
    {%- endif -%}
  </h2>
  <div class="rte">
    {%- if section.settings.page.content != blank -%}
      {{ section.settings.page.content }}
    {%- else -%}
      <div class="page-placeholder-wrapper placeholder">
        {{ 'page' | placeholder_svg_tag: 'page-placeholder' }}
      </div>
    {%- endif -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.page.name",
  "tag": "section",
  "class": "spaced-section",
  "settings": [
    {
      "type": "page",
      "id": "page",
      "label": "t:sections.page.settings.page.label"
    }
  ],
  "presets": [
    {
      "name": "t:sections.page.presets.name"
    }
  ],
  "disabled_on": {
    "groups": ["header","footer"]
  }
}
{% endschema %}
