.breadcrumbs.color-background-3 {
  background: rgba(var(--color-background), 0.1);
}
.breadcrumbs__list {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.breadcrumbs__item {
  display: inline-block;
}

.breadcrumbs__link {
  text-decoration: underline;
}

.breadcrumbs__link[aria-current="page"] {
  color: inherit;
  font-weight: normal;
  text-decoration: none;
}

.breadcrumbs__link[aria-current="page"]:hover,
.breadcrumbs__link[aria-current="page"]:focus {
  text-decoration: underline;
}
.breadcrumbs__list {
  margin: 0;
  padding: 0;
  list-style: none;
}
.breadcrumbs__item + .breadcrumbs__item {
  margin-left: 15px;
  padding-left: 15px;
  position: relative;
}
.breadcrumbs__item + .breadcrumbs__item::before {
  position: absolute;
  content: "/";
  left: -4px;
}
.account__page--title-padding {
  padding-top: var(--account-padding-top);
  padding-bottom: var(--account-padding-bottom);
}
@media only screen and (max-width: 750px) {
  .account__page--title-padding {
    padding-top: var(--account-padding-top-sm);
    padding-bottom: var(--account-padding-bottom-sm);
  }
}
.breadcrumb--banner-media-wrapper {
  position: relative;
}
.breadcrumb--banner__media {
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
}
.breadcrumb--content {
  position: relative;
  z-index: 10;
}
