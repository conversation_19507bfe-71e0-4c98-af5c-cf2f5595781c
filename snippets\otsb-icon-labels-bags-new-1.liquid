{%- if icon != 'none' -%}
  <svg style="fill: none" width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid meet">
    {% case icon %}
      {% when "flower" %}
        <path d="M7.07684 7.38462V2L12.4615 5.07692L17.8461 2V7.38462C17.8461 10.3585 15.4353 12.7692 12.4615 12.7692C9.48761 12.7692 7.07684 10.3585 7.07684 7.38462Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M12.4614 12.7692V22" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M12.4614 20.4615V21.9999H16.3076C18.8567 21.9999 20.923 19.9336 20.923 17.3845V16.6153C20.923 16.1905 20.5785 15.8461 20.1537 15.8461H17.0768C14.5278 15.8461 12.4614 17.9124 12.4614 20.4615Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M4 16.6153V17.3845C4 19.9336 6.06638 21.9999 8.61538 21.9999H12.4615V20.4615C12.4615 17.9124 10.3952 15.8461 7.84615 15.8461H4.76923C4.3444 15.8461 4 16.1905 4 16.6153Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "leaf" %}
        <g clip-path="url(#clip0_598_8021)">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M23.7079 0.841167L23.2105 0.789469L23.1588 0.292149L23.7716 0.228455L23.7079 0.841167ZM22.6425 1.35754C22.2592 1.40348 21.7141 1.47212 21.052 1.56444C19.7006 1.75289 17.8646 2.03963 15.9233 2.43294C13.98 2.82667 11.9423 3.32501 10.1834 3.93447C8.40572 4.55044 6.98443 5.25655 6.20925 6.03172C2.96207 9.27891 2.96207 14.5436 6.20925 17.7908C9.45644 21.0379 14.7211 21.0379 17.9683 17.7908C18.7435 17.0156 19.4496 15.5943 20.0655 13.8167C20.675 12.0578 21.1733 10.02 21.5671 8.0767C21.9604 6.13544 22.2471 4.29943 22.4356 2.94802C22.5279 2.28591 22.5965 1.74082 22.6425 1.35754ZM23.2105 0.789469C23.1588 0.292149 23.159 0.292132 23.1588 0.292149L23.1479 0.293301L23.1171 0.296588C23.0901 0.299494 23.0502 0.303832 22.9982 0.309619C22.8942 0.321193 22.7417 0.338564 22.5467 0.361869C22.1567 0.408475 21.5966 0.478831 20.9139 0.574025C19.5491 0.76433 17.6919 1.0543 15.7248 1.45286C13.7597 1.851 11.6738 2.35969 9.85598 2.98959C8.05687 3.61298 6.4482 4.37857 5.50215 5.32462C1.86444 8.96233 1.86444 14.8602 5.50215 18.4979C9.13986 22.1356 15.0377 22.1356 18.6754 18.4979C19.6214 17.5518 20.387 15.9432 21.0104 14.1441C21.6403 12.3262 22.149 10.2404 22.5472 8.27528C22.9457 6.30813 23.2357 4.45088 23.426 3.08613C23.5212 2.40345 23.5915 1.84328 23.6381 1.45333C23.6615 1.25834 23.6788 1.10586 23.6904 1.00185C23.6962 0.94985 23.7005 0.909962 23.7034 0.882934L23.7067 0.852128L23.7079 0.841167C23.7079 0.841008 23.7079 0.841167 23.2105 0.789469Z" fill="currentColor"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.143982 23.1489L17.4988 5.7941L18.2059 6.5012L0.85109 23.856L0.143982 23.1489Z" fill="currentColor"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M12.3521 5.88919H13.3521V10.6265H17.9732V11.6265H12.3521V5.88919Z" fill="currentColor"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.49829 9.74307H9.49829V14.4803H14.1194V15.4803H8.49829V9.74307Z" fill="currentColor"/>
        </g>
        <defs>
        <clipPath id="clip0_598_8021">
        <rect width="24" height="24" fill="white"/>
        </clipPath>
        </defs>
      {% when "shopping-bag" %}
        <path d="M5.59998 7.64H18.4" stroke="currentColor" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M8.79999 7.64V6.2C8.79999 4.43268 10.2327 3 12 3C13.7673 3 15.2 4.43268 15.2 6.2V7.64" stroke="currentColor" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M15.2 10.84C15.2 12.6073 13.7673 14.04 12 14.04C10.2327 14.04 8.79999 12.6073 8.79999 10.84" stroke="currentColor" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M18.4 7.64L19.9874 20.078C19.9957 20.1441 20 20.2116 20 20.28C20 21.1636 19.2836 21.88 18.4 21.88H5.6C4.71636 21.88 4 21.1636 4 20.28C4 20.2116 4.00428 20.1441 4.01264 20.078L5.6 7.64" stroke="currentColor" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>        
      {% when "shopping-bag-1" %}
        <path d="M20.1417 19.463C20.1636 19.6573 20.1439 19.8539 20.0842 20.0401C20.0246 20.2262 19.9263 20.3976 19.7956 20.543C19.6646 20.6881 19.5042 20.804 19.3253 20.8827C19.1463 20.9615 18.9526 21.0015 18.7571 21H5.24324C5.04769 21.0015 4.85405 20.9615 4.67506 20.8827C4.49607 20.804 4.33578 20.6881 4.20477 20.543C4.07411 20.3976 3.97571 20.2262 3.91605 20.0401C3.85639 19.8539 3.83681 19.6573 3.85862 19.463L5.07708 8.5384H18.9232L20.1417 19.463Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M8.53864 8.53847V6.46154C8.53864 5.54349 8.90333 4.66302 9.55249 4.01386C10.2017 3.3647 11.0821 3 12.0002 3C12.9182 3 13.7987 3.3647 14.4479 4.01386C15.097 4.66302 15.4617 5.54349 15.4617 6.46154V8.53847" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>          
      {% when "shopping-cart" %}
        <path d="M2.99036 7.2L4.19036 15.6H16.8L21.6 7.2H2.99036Z" stroke="currentColor" stroke-width="2" stroke-miterlimit="10" stroke-linejoin="round"/>
        <path d="M5.39045 21.7488C6.38457 21.7488 7.19045 20.9429 7.19045 19.9488C7.19045 18.9547 6.38457 18.1488 5.39045 18.1488C4.39634 18.1488 3.59045 18.9547 3.59045 19.9488C3.59045 20.9429 4.39634 21.7488 5.39045 21.7488Z" fill="currentColor"/>
        <path d="M13.2 21.7488C14.1941 21.7488 15 20.9429 15 19.9488C15 18.9547 14.1941 18.1488 13.2 18.1488C12.2059 18.1488 11.4 18.9547 11.4 19.9488C11.4 20.9429 12.2059 21.7488 13.2 21.7488Z" fill="currentColor"/>
        <path d="M2.40002 2.39999H6.00002" stroke="currentColor" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>        
      {% when "star" %}
        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.9999 2L14.4076 9.63907H21.9999L15.5912 14.3023L18.5498 22L11.9999 17.2329L5.45004 22L8.40869 14.3023L1.99994 9.63907H9.59227L11.9999 2ZM11.9999 5.34225L10.3288 10.6444H5.08987L9.62316 13.943L7.6078 19.1865L11.9999 15.9898L16.3921 19.1865L14.3767 13.943L18.91 10.6444H13.6711L11.9999 5.34225Z" fill="currentColor"/>       
      {% when "sun" %}
        <path fill-rule="evenodd" clip-rule="evenodd" d="M4.3254 4.3254C4.52066 4.13014 4.83725 4.13014 5.03251 4.3254L6.87688 6.16977C7.07214 6.36504 7.07214 6.68162 6.87688 6.87688C6.68162 7.07214 6.36504 7.07214 6.16977 6.87688L4.3254 5.03251C4.13014 4.83725 4.13014 4.52066 4.3254 4.3254Z" fill="currentColor"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M16.6231 16.6232C16.8184 16.4279 17.135 16.4279 17.3302 16.6232L19.1747 18.4676C19.3699 18.6628 19.3699 18.9794 19.1747 19.1747C18.9794 19.3699 18.6628 19.3699 18.4675 19.1747L16.6231 17.3303C16.4279 17.135 16.4279 16.8184 16.6231 16.6232Z" fill="currentColor"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M1.25 11.75C1.25 11.4739 1.47386 11.25 1.75 11.25H4.35491C4.63106 11.25 4.85491 11.4739 4.85491 11.75C4.85491 12.0262 4.63106 12.25 4.35491 12.25H1.75C1.47386 12.25 1.25 12.0262 1.25 11.75Z" fill="currentColor"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M18.6451 11.75C18.6451 11.4739 18.8689 11.25 19.1451 11.25H21.75C22.0261 11.25 22.25 11.4739 22.25 11.75C22.25 12.0262 22.0261 12.25 21.75 12.25H19.1451C18.8689 12.25 18.6451 12.0262 18.6451 11.75Z" fill="currentColor"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M6.87692 16.6232C7.07218 16.8184 7.07218 17.135 6.87692 17.3303L5.03251 19.1747C4.83725 19.3699 4.52066 19.3699 4.3254 19.1747C4.13014 18.9794 4.13014 18.6628 4.3254 18.4676L6.16982 16.6232C6.36508 16.4279 6.68166 16.4279 6.87692 16.6232Z" fill="currentColor"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M19.1747 4.3254C19.3699 4.52066 19.3699 4.83725 19.1747 5.03251L17.3302 6.87692C17.135 7.07218 16.8184 7.07218 16.6231 6.87692C16.4279 6.68166 16.4279 6.36508 16.6231 6.16982L18.4675 4.3254C18.6628 4.13014 18.9794 4.13014 19.1747 4.3254Z" fill="currentColor"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.75 18.6451C12.0261 18.6451 12.25 18.8689 12.25 19.1451V21.75C12.25 22.0261 12.0261 22.25 11.75 22.25C11.4739 22.25 11.25 22.0261 11.25 21.75V19.1451C11.25 18.8689 11.4739 18.6451 11.75 18.6451Z" fill="currentColor"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.75 1.25003C12.0261 1.25003 12.25 1.47389 12.25 1.75003V4.35495C12.25 4.63109 12.0261 4.85495 11.75 4.85495C11.4739 4.85495 11.25 4.63109 11.25 4.35495V1.75003C11.25 1.47389 11.4739 1.25003 11.75 1.25003Z" fill="currentColor"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.75 7.34787C9.31876 7.34787 7.34784 9.31879 7.34784 11.75C7.34784 14.1813 9.31876 16.1522 11.75 16.1522C14.1813 16.1522 16.1522 14.1813 16.1522 11.75C16.1522 9.31879 14.1813 7.34787 11.75 7.34787ZM6.34784 11.75C6.34784 8.7665 8.76647 6.34787 11.75 6.34787C14.7335 6.34787 17.1522 8.7665 17.1522 11.75C17.1522 14.7336 14.7335 17.1522 11.75 17.1522C8.76647 17.1522 6.34784 14.7336 6.34784 11.75Z" fill="currentColor"/>                 
      {% when "arrow-left" %}
        <path d="M9.30024 7L4 12.3789L9.30024 17.7577M20 12.3789H5.03024" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
      {% when "arrow-right" %}
        <path d="M14.6998 17.7577L20 12.3788L14.6998 7M4 12.3789H18.9697" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>         
      {% when "chevron-left" %}
        <path fill-rule="evenodd" clip-rule="evenodd" d="M15.3783 21.9712L6.3173 12.9103C5.89423 12.4873 5.89423 11.8013 6.3173 11.3783L15.3783 2.3173C15.8013 1.89423 16.4873 1.89423 16.9103 2.3173C17.3334 2.74036 17.3334 3.42631 16.9103 3.84937L9.69873 11.0609C9.69873 11.0609 8.16661 12.0678 9.69869 13.2276L16.9103 20.4392C17.3334 20.8623 17.3334 21.5482 16.9103 21.9712C16.4873 22.3943 15.8013 22.3943 15.3783 21.9712Z" fill="currentColor"/>  
      {% when "chevron-right" %}
        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.84937 2.3173L17.9103 11.3783C18.3334 11.8013 18.3334 12.4873 17.9103 12.9103L8.84937 21.9712C8.4263 22.3944 7.74038 22.3944 7.31731 21.9712C6.89424 21.5482 6.89424 20.8623 7.31731 20.4392L14.5289 13.2276C14.5289 13.2276 16.061 12.2208 14.529 11.061L7.3173 3.84937C6.89423 3.4263 6.89423 2.74036 7.3173 2.3173C7.74037 1.89423 8.4263 1.89423 8.84937 2.3173Z" fill="currentColor"/>          
    {% endcase %}
  </svg>
{%- endif -%}