.collection-list-title {
  margin: 0;
}
.collection-list__item:only-child .media {
  height: 35rem;
}
@media screen and (max-width: 600px) {
  .collection-list .collection-list__item {
    width: 100%;
  }
}
.collection__card {
  display: block;
}
.collection__media--wrap {
  position: relative;
}
.collection-overlay-card {
  background-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  transition: 0.3s;
  opacity: 0;
  visibility: hidden;
}
span.link__hover_arrow_inner {
  background: rgba(var(--color-background));
  color: rgba(var(--color-foreground));
  display: inline-flex;
  padding: 10px 20px;
  border-radius: 30px;
}
.collection__card:hover .collection-overlay-card {
  opacity: 1;
  visibility: visible;
}
.collection__card_text {
  padding: 1rem 1.5rem;
  box-shadow: 0 0px 10px rgb(var(--color-foreground), 0.05);
}
.collection__card_text.full--center {
  top: 50%;
  transform: translate(-50%);
  left: 50%;
}
.collection__card_text.bottom--left {
  bottom: 1.5rem;
  left: 1.5rem;
}
.collection__card_text.bottom--right {
  right: 1.5rem;
  bottom: 1.5rem;
}
.collection__card_text.top--left {
  top: 1.5rem;
  left: 1.5rem;
}
.collection__card_text.top--right {
  top: 1.5rem;
  right: 1.5rem;
}
@media only screen and (min-width: 768px) {
  .collection__card_text.bottom--left {
    bottom: 3rem;
    left: 1.5rem;
  }
  .collection__card_text.bottom--right {
    right: 1.5rem;
    bottom: 3rem;
  }
  .collection__card_text.top--left {
    top: 3rem;
    left: 1.5rem;
  }
  .collection__card_text.top--right {
    top: 3rem;
    right: 1.5rem;
  }
}

h5.collection__title {
  padding-right: 10px;
  text-align: center;
}
.collection__product_count {
  flex-shrink: 0;
}

@media only screen and (min-width: 992px) {
  .collection__card_text {
    padding: 1.5rem 3rem;
  }
}

@media only screen and (max-width: 991px) {
  .collection__title {
    font-size: 1.6rem;
  }
}
.collection__card_text.column__five--design {
  padding: 1rem 1.5rem;
}

.column__five--design h5.collection__title {
  font-size: 1.6rem;
}
.collection__card_text.placeholder__position {
  bottom: 3rem;
  left: 1.5rem;
}
.collection__media--wrap .placeholder_svg_parent {
  max-height: 35rem;
}
.collection__media--wrap .media > img {
  transition: all 0.7s ease 0s;
}

.collection__media--wrap:hover img {
  transform: scale(1.09);
}
