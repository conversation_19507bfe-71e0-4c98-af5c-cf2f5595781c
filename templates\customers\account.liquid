{% liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
%}
{{ 'customer.css' | asset_url | stylesheet_tag }} 
{{ 'page-title.css' | asset_url | stylesheet_tag }}

{% if theme_rtl %}
  {{ 'breadcrumbs-rtl.css' | asset_url | stylesheet_tag }}
  {{ 'customer-rtl.css' | asset_url | stylesheet_tag }}
{% endif %}
{%- assign t = template | split: '.' | first -%}

{%- if settings.account_page_title_bar -%}
<div class="breadcrumbs account__page--title-padding text-center color-{{ settings.customer_color_scheme }}"
 	style="
     --account-padding-top: {{ settings.account_page_title_padding_top }}px;
     --account-padding-bottom: {{ settings.account_page_title_padding_bottom }}px;
     --account-padding-top-sm: {{ settings.account_page_title_padding_top_sm }}px;
     --account-padding-bottom-sm: {{ settings.account_page_title_padding_bottom_sm }}px;
 ">
  <div class="container">
    {%- if settings.account_page_title -%}
    <div class="page_header__title">
      <h1 class="page_header__title_label">{{ 'customer.account.title' | t }}</h1>
    </div>
    {%- endif -%}
    {%- if settings.account_page_breadcrumb -%}
    {%- render 'breadcrumb-nav', t: t -%}
    {%- endif -%}
  </div>
</div>
{%- endif -%}

<div class="customer account">
  <div class="container">
    <h4 class="mb-20">{{ 'customer.account.welcome' | t: customer_name: customer.name }}</h4>

    <div class="row account__pages_inner">
      <div class="col">
        <div class="customer__menu">
          <ul>
            <li>
              <a
                 href="{{ routes.account_url }}"
                 class="{% if request.path == routes.account_url %}active{% endif %}"
                 >
                {{ 'customer.dashboard' | t }}
              </a>
            </li>
            <li>
              <a
                 href="{{ routes.account_addresses_url }}"
                 class="{% if request.path == routes.account_addresses_url %} active {% endif %}"
                 >
                {{ 'customer.addresses.title' | t }}
              </a>
            </li>
            <li><a href="{{ routes.account_logout_url }}">
              <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" fill="none" viewBox="0 0 18 19">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M6 4.5a3 3 0 116 0 3 3 0 01-6 0zm3-4a4 4 0 100 8 4 4 0 000-8zm5.58 12.15c1.12.82 1.83 2.24 1.91 4.85H1.51c.08-2.6.79-4.03 1.9-4.85C4.66 11.75 6.5 11.5 9 11.5s4.35.26 5.58 1.15zM9 10.5c-2.5 0-4.65.24-6.17 1.35C1.27 12.98.5 14.93.5 18v.5h17V18c0-3.07-.77-5.02-2.33-6.15-1.52-1.1-3.67-1.35-6.17-1.35z" fill="currentColor">
                  </svg>
                {{ 'customer.log_out' | t }}

                </a></li>
          </ul>
        </div>
        <div class="account__details">

          <h4>{{ 'customer.account.details' | t }}</h4>

          {{ customer.default_address | format_address }}

          <a href="{{ routes.account_addresses_url }}">
            {{ 'customer.account.view_addresses' | t }} ({{ customer.addresses_count }})
          </a>
        </div>
      </div>
      
      <div class="col flex-grow-1">
        <h3 class="mb-20">{{ 'customer.orders.title' | t }}</h3>

        {% paginate customer.orders by 20 %}
        {%- if customer.orders.size > 0 -%}
        <table role="table" class="order-history">
          <caption class="visually-hidden">{{ 'customer.orders.title' | t }}</caption>
          <thead role="rowgroup">
            <tr role="row">
              <th id="ColumnOrder" scope="col" role="columnheader">{{ 'customer.orders.order_number' | t }}</th>
              <th id="ColumnDate" scope="col" role="columnheader">{{ 'customer.orders.date' | t }}</th>
              <th id="ColumnPayment" scope="col" role="columnheader">{{ 'customer.orders.payment_status' | t }}</th>
              <th id="ColumnFulfillment" scope="col" role="columnheader">{{ 'customer.orders.fulfillment_status' | t }}</th>
              <th id="ColumnTotal" scope="col" role="columnheader">{{ 'customer.orders.total' | t }}</th>
            </tr>
          </thead>
          <tbody role="rowgroup">
            {%- for order in customer.orders -%}
            <tr role="row">
              <td
                  id="RowOrder"
                  role="cell"
                  headers="ColumnOrder"
                  data-label="{{ 'customer.orders.order_number' | t }}"
                  >
                <a href="{{ order.customer_url }}" aria-label="{{ 'customer.orders.order_number_link' | t: number: order.name }}">
                  {{ order.name }}
                </a>
              </td>
              <td headers="RowOrder ColumnDate" role="cell" data-label="{{ 'customer.orders.date' | t }}">
                {{ order.created_at | time_tag: format: 'date' }}
              </td>
              <td headers="RowOrder ColumnPayment" role="cell" data-label="{{ 'customer.orders.payment_status' | t }}">
                {{ order.financial_status_label }}
              </td>
              <td headers="RowOrder ColumnFulfillment" role="cell" data-label="{{ 'customer.orders.fulfillment_status' | t }}">
                {{ order.fulfillment_status_label }}
              </td>
              <td headers="RowOrder ColumnTotal" role="cell" data-label="{{ 'customer.orders.total' | t }}">
                {{ order.total_price | money_with_currency }}</td>
            </tr>
            {%- endfor -%}
          </tbody>
        </table>
        {%- else -%}
        <p>{{ 'customer.orders.none' | t }}</p>
        {%- endif -%}

        {%- if paginate.pages > 1 -%}
        {%- if paginate.parts.size > 0 -%}
        <nav class="pagination" role="navigation" aria-label="{{ 'general.pagination.label' | t }}">
          <ul role="list">
            {%- if paginate.previous -%}
            <li>
              <a href="{{ paginate.previous.url }}" aria-label="{{ 'general.pagination.previous' | t }}">
                <svg aria-hidden="true" focusable="false" viewBox="0 0 10 6">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M9.354.646a.5.5 0 00-.708 0L5 4.293 1.354.646a.5.5 0 00-.708.708l4 4a.5.5 0 00.708 0l4-4a.5.5 0 000-.708z" fill="currentColor">
                    </svg>
                  </a>
                </li>
              {%- endif -%}

              {%- for part in paginate.parts -%}
            <li>
              {%- if part.is_link -%}
              <a href="{{ part.url }}" aria-label="{{ 'general.pagination.page' | t: number: part.title }}">{{ part.title }}</a>
              {%- else -%}
              {%- if part.title == paginate.current_page -%}
              <span aria-current="page" aria-label="{{ 'general.pagination.page' | t: number: part.title }}">{{ part.title }}</span>
              {%- else -%}
              <span>{{ part.title }}</span>
              {%- endif -%}
              {%- endif -%}
            </li>
            {%- endfor -%}

            {%- if paginate.next -%}
            <li>
              <a href="{{ paginate.next.url }}" aria-label="{{ 'general.pagination.next' | t }}" >
                <svg aria-hidden="true" focusable="false" viewBox="0 0 10 6">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M9.354.646a.5.5 0 00-.708 0L5 4.293 1.354.646a.5.5 0 00-.708.708l4 4a.5.5 0 00.708 0l4-4a.5.5 0 000-.708z" fill="currentColor">
                    </svg>
                  </a>
                </li>
              {%- endif -%}
          </ul>
        </nav>
        {%- endif -%}
        {%- endif -%}
        {% endpaginate %}
      </div>
    </div>
  </div>
</div>
