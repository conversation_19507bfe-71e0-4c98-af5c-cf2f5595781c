theme.headerSticky=function(){function ScrollSticky(){const headerStickyWrapper=document.querySelector("header"),headerStickyTarget=document.querySelector(".header__sticky");if(headerStickyTarget){let headerHeight=headerStickyWrapper.clientHeight;window.addEventListener("scroll",(function(){let StickyTargetElement,TargetElementTopOffset=TopOffset(headerStickyWrapper).top;window.scrollY>TargetElementTopOffset?headerStickyTarget.classList.add("sticky"):headerStickyTarget.classList.remove("sticky")}))}}return ScrollSticky}();