{%- if cart != empty -%}
{%- for item in cart.items -%}
<div class="cart-notification-product" data-item-id="{{ item.id }}">
  <div class="loading-overlay hidden">
    <div class="loading-overlay__spinner">
      <svg aria-hidden="true" focusable="false" role="presentation" class="spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
        <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
      </svg>
    </div>
  </div>

  {% if item.image %}
  <a href="{{ item.url }}" class="cart-notification__product--image">
    <img class="cart-notification-product__image"
         src="{{ item.image | img_url: '150x' }}"
         alt="{{ item.image.alt | escape }}"
         width="120"
         height="{{ 120 | divided_by: item.image.aspect_ratio | ceil }}"
         loading="lazy"
         >
  </a>
  {% endif %}

  <div class="cart-notification-product__info">
    {%- if settings.show_vendor_cart_drawer -%}
    <p class="caption-with-letter-spacing mb-0">{{ item.product.vendor }}</p>
    {%- endif -%}
    <h6 class="cart-notification-product__name"><a href="{{ item.url }}">{{ item.product.title | escape }}</a></h6>
    {%- unless item.product.has_only_default_variant -%}
    {%- for option in item.options_with_values -%}
    <span class="cart-notification-product__option">
      <span><b>{{ option.name }}:</b> </span>
      <span>{{ option.value }}</span>
    </span>
    {%- endfor -%}
    {%- endunless -%}
    
    <div class="cart__item_price">
      
      {%- if item.original_line_price != item.final_line_price -%}
      <div class="cart-item__discounted-prices">
        <span class="visually-hidden">
          {{ 'products.product.price.regular_price' | t }}
        </span>
        <s class="cart-item__old-price price">
          {{ item.original_line_price | money }}
        </s>
        <span class="visually-hidden">
          {{ 'products.product.price.sale_price' | t }}
        </span>
        <span class="price">
          {{ item.final_line_price | money }}
        </span>
      </div>
      {%- else -%}
      <span class="price ">
        {{ item.original_line_price | money }}
      </span>
      {%- endif -%}
      
      {%- if item.variant.available and item.unit_price_measurement -%}
      <div class="unit-price caption">
        <span class="visually-hidden">{{ 'products.product.price.unit_price' | t }}</span>
        {{ item.variant.unit_price | money }}
        <span aria-hidden="true">/</span>
        <span class="visually-hidden">&nbsp;{{ 'accessibility.unit_price_separator' | t }}&nbsp;</span>
        {%- if item.variant.unit_price_measurement.reference_value != 1 -%}
        {{- item.variant.unit_price_measurement.reference_value -}}
        {%- endif -%}
        {{ item.variant.unit_price_measurement.reference_unit }}
      </div>
      {%- endif -%}
      
      <ul class="discounts list-unstyled" role="list" aria-label="{{ 'customer.order.discount' | t }}">
        {%- for discount in item.discounts -%}
        <li class="discounts__discount">
          {%- render 'icon-discount' -%}
          {{ discount.title }}
        </li>
        {%- endfor -%}
      </ul>
      
    </div>
    
    <div class="cart__item--footer d-flex align-items-center">
      <quantity-input class="quantity mt-10">
        <button class="quantity__button no-js-hidden" name="minus" type="button">
          <span class="visually-hidden">{{ 'products.product.quantity.decrease' | t: product: item.product.title | escape }}</span>
          {% render 'icon-minus' %}
        </button>
        <input class="quantity__input"
               type="number"
               name="updates[]"
               value="{{ item.quantity }}"
               min="0"
               aria-label="{{ 'products.product.quantity.input_label' | t: product: item.product.title | escape }}"
               id="Quantity-{{ item.index | plus: 1 }}"
               data-index="{{ item.index | plus: 1 }}"
               >
        <button class="quantity__button no-js-hidden" name="plus" type="button">
          <span class="visually-hidden">{{ 'products.product.quantity.increase' | t: product: item.product.title | escape }}</span>
          {% render 'icon-plus' %}
        </button>
      </quantity-input>


      <cart-remove-button-2 class="cart-close-icon" id="Remove-{{ item.index | plus: 1 }}" data-index="{{ item.index | plus: 1 }}">
        <button class="link rmov_icon"> 
            <svg height="18" viewBox="-40 0 427 427.00131" width="18" xmlns="http://www.w3.org/2000/svg" id="fi_1214428"><path d="m232.398438 154.703125c-5.523438 0-10 4.476563-10 10v189c0 5.519531 4.476562 10 10 10 5.523437 0 10-4.480469 10-10v-189c0-5.523437-4.476563-10-10-10zm0 0"></path><path d="m114.398438 154.703125c-5.523438 0-10 4.476563-10 10v189c0 5.519531 4.476562 10 10 10 5.523437 0 10-4.480469 10-10v-189c0-5.523437-4.476563-10-10-10zm0 0"></path><path d="m28.398438 127.121094v246.378906c0 14.5625 5.339843 28.238281 14.667968 38.050781 9.285156 9.839844 22.207032 15.425781 35.730469 15.449219h189.203125c13.527344-.023438 26.449219-5.609375 35.730469-15.449219 9.328125-9.8125 14.667969-23.488281 14.667969-38.050781v-246.378906c18.542968-4.921875 30.558593-22.835938 28.078124-41.863282-2.484374-19.023437-18.691406-33.253906-37.878906-33.257812h-51.199218v-12.5c.058593-10.511719-4.097657-20.605469-11.539063-28.03125-7.441406-7.421875-17.550781-11.5546875-28.0625-11.46875h-88.796875c-10.511719-.0859375-20.621094 4.046875-28.0625 11.46875-7.441406 7.425781-11.597656 17.519531-11.539062 28.03125v12.5h-51.199219c-19.1875.003906-35.394531 14.234375-37.878907 33.257812-2.480468 19.027344 9.535157 36.941407 28.078126 41.863282zm239.601562 279.878906h-189.203125c-17.097656 0-30.398437-14.6875-30.398437-33.5v-245.5h250v245.5c0 18.8125-13.300782 33.5-30.398438 33.5zm-158.601562-367.5c-.066407-5.207031 1.980468-10.21875 5.675781-13.894531 3.691406-3.675781 8.714843-5.695313 13.925781-5.605469h88.796875c5.210937-.089844 10.234375 1.929688 13.925781 5.605469 3.695313 3.671875 5.742188 8.6875 5.675782 13.894531v12.5h-128zm-71.199219 32.5h270.398437c9.941406 0 18 8.058594 18 18s-8.058594 18-18 18h-270.398437c-9.941407 0-18-8.058594-18-18s8.058593-18 18-18zm0 0"></path><path d="m173.398438 154.703125c-5.523438 0-10 4.476563-10 10v189c0 5.519531 4.476562 10 10 10 5.523437 0 10-4.480469 10-10v-189c0-5.523437-4.476563-10-10-10zm0 0"></path></svg>
        </button>
      </cart-remove-button-2>

    </div>
    
    <p class="drawer_cart-item__error no-js-inline" id="Line-item-error-{{ item.index | plus: 1 }}">
      <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-error" viewBox="0 0 13 13">
        <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
        <circle cx="6.5" cy="6.5" r="5.5" fill="#EB001B" stroke="#EB001B" stroke-width="0.7"/>
        <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
        <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="#EB001B" stroke-width="0.7">
          </svg>
        <span class="cart-item__error-text"></span>
        </p>
    
    {%- for property in item.properties -%}
    {%- assign property_first_char = property.first | slice: 0 -%}
    {%- if property.last != blank and property_first_char != '_' -%}
    <div class="product-option">
      <span>{{ property.first }}: </span>
      <span>
        {%- if property.last contains '/uploads/' -%}
        <a href="{{ property.last }}" class="link" target="_blank">
          {{ property.last | split: '/' | last }}
        </a>
        {%- else -%}
        {{ property.last }}
        {%- endif -%}
      </span>
    </div>
    {%- endif -%}
    {%- endfor -%}
    
    {%- if item.selling_plan_allocation != nil -%}
    <p class="product-option">{{ item.selling_plan_allocation.selling_plan.name }}</p>
    {%- endif -%}

    
  </div>
</div>
{%- endfor -%}
{%- endif -%}
