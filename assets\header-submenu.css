/* Header Sub Menu */

.header__mega_menu_item:hover {
  color: rgba(var(--text-link-hover-color));
}
.header__mega_sub_menu_item:hover,
.header__sub_menu_item:hover {
  color: rgba(var(--text-link-hover-color));
}
.header__mega_sub_menu_item:hover, .header__mega_menu_item:hover, .header__sub_menu_item:hover {
    color: rgba(var(--color-button),var(--alpha-button-background));
}
.header__sub_menu_li.header__sub--has-children {
  position: relative;
}
ul.header__sub--children__menu {
  position: absolute;
  width: 22rem;
  z-index: 9;
  left: 100%;
  top: 10%;
  box-shadow: 0 10px 20px rgba(var(--color-foreground), 0.15);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
  padding: 1rem 0;
  margin: 0;
}
a.header__sub_menu_item {
  word-break: break-word;
}
.header__sub--has-children > a.header__sub_menu_item {
  display: flex;
  justify-content: space-between;
}
span.header__sub--has-children--icon > svg {
  width: 1.5rem;
}
.header__sub--has-children:hover ul.header__sub--children__menu,
.header__sub--has-children:focus-within ul.header__sub--children__menu {
  opacity: 1;
  visibility: visible;
  top: 0;
}
.header__sub--has-children:hover > .header__sub_menu_item {
  color: rgba(var(--text-link-hover-color));
}
.mega__menu--wrapper--column:not(.mega__menu--collection-list-two-rows):not(
    .mega__menu--collection-inline
  ):not(.mega__menu--products-two-rows):not(.mega__menu--products-inline) {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}
.mega__menu--promo {
  flex: 0 0 20rem;
}

@media only screen and (min-width: 1200px) {
  .mega__menu--promo {
    flex: 0 0 28rem;
  }
}
.header__mega_menu--inner:not(:only-child) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  padding: 0;
  gap: 2rem;
}
.header__mega_menu--inner:only-child {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  gap: 2rem;
}
a.mega__menu--promo-link {
  display: block;
}
.header__mega_menu:not(.mega__menu--wrapper),
.header__mega_menu--inner {
  flex: 1 1 100%;
}
@media only screen and (min-width: 1200px) and (max-width: 1400px) {
  .header__mega_menu--inner:not(:only-child) {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  }
}

@media only screen and (max-width: 1199px) {
  .header__mega_menu--inner:not(:only-child) {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}
