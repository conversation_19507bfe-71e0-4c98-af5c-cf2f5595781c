{%- comment -%}
  {% render 'lookbook-product-card',
            product_card_product: product,
            media_size: settings.image_ratio,
            show_secondary_image: settings.show_secondary_image,
            show_vendor: settings.show_vendor,
            show_badge: settings.show_badges,
            show_cart_button: settings.show_cart_button,
            show_quick_view: settings.show_quick_view_button,
            show_quick_compare: settings.show_compare_view_button,
            show_wishlist: settings.show_wishlist_button,
            show_countdown: settings.show_countdown,
            show_title: settings.show_title,
            show_price: settings.show_price,
            show_product_description: settings.show_product_description,
            show_rating: settings.show_product_rating
  %}
{%- endcomment -%}

{%- liquid
  assign variant = product_card_product.selected_or_first_available_variant

  assign productCountdown = product_card_product.metafields.metaname.countdown_timer.value
  assign todayDate = 'now' | date: '%s'
  assign countDownDate = productCountdown | date: '%s'

  assign first_variant_featured_media_check = false
  if variant.featured_media == null
    assign first_variant_featured_media_check = true
  endif

  assign second_img_position = 1

  assign product_card_option = settings.pcard_option_design
  assign product_card_option_display = settings.pcard_option_display

  unless show_secondary_image
    assign second_image_class = 'second--image__hide'
  endunless

  assign pcard_option_values_limit = settings.product_option_values_limit

  assign total_item = product_card_product.metafields.metaname.total_stock.value | default: 500
  assign current_total_stock = 0

  for variant in product_card_product.variants
    assign current_total_stock = current_total_stock | plus: variant.inventory_quantity
  endfor

  assign itemsold = total_item | minus: current_total_stock
  assign sales_progress = total_item | minus: current_total_stock | times: 100 | divided_by: total_item
  assign sales_progress_percent = 100 | minus: sales_progress

  case settings.product_content_alignment
    when 'left'
      assign price_class = 'product__card__price justify-content-start'
    when 'right'
      assign price_class = 'product__card__price justify-content-end'
    else
      assign price_class = 'product__card__price justify-content-center'
  endcase
-%}

<div class="product__card {{ className }} product__card--{{ card_style }} {% if box_shadow %} product__shadow {% endif %}">
  <div class="product__card__thumbnail {{ second_image_class }}">
    {%- if show_badge and product_card_product != blank -%}
      {%- render 'product-badge', product: product_card_product -%}
    {%- endif -%}

    {%- if product_card_product.featured_media -%}
      {%- liquid
        assign featured_media_aspect_ratio = product_card_product.featured_media.aspect_ratio

        if product_card_product.featured_media.aspect_ratio == null
          assign featured_media_aspect_ratio = 1
        endif
      -%}
      <a
        href="{{ product_card_product.url | default: '#' }}"
        class="d-block product__media_thumbnail product__card--link"
      >
        {%- render 'product-card-media',
          product_card_product: product_card_product,
          variant: variant,
          media_size: media_size,
          featured_media_aspect_ratio: featured_media_aspect_ratio,
          second_img_position: second_img_position,
          show_secondary_image: show_secondary_image,
          first_variant_featured_media_check: first_variant_featured_media_check
        -%}
      </a>

    {%- else -%}
      <div class="card__content">
        <a href="{{ product_card_product.url | default: '#' }}" class="d-block">
          <div class="placeholder placeholder_svg_parent" style="padding-bottom: 100%">
            {{ 'product-1' | placeholder_svg_tag: 'placeholder-svg-2' }}
          </div>
        </a>
      </div>
    {%- endif -%}

    {% if product_card_product != blank %}
      {%- if show_countdown -%}
        {%- if todayDate < countDownDate -%}
          <countdown-timer class="product__grid_timer">
            <div class="deals__product--countdown d-flex" data-countdown="{{ productCountdown }}"></div>
          </countdown-timer>
        {%- endif -%}
      {%- endif -%}

      {% if card_style == 'style_1' and show_wishlist %}
        {%- render 'button-wishlist',
          product: product_card_product,
          className: 'product__card--wishlist-btn',
          tooltip: true,
          tooltip_position: 'tooltip--left'
        -%}
      {%- endif -%}

      {% if card_style == 'style_2' and show_wishlist %}
        <div class="d-md-only-visible">
          {%- render 'button-wishlist',
            product: product_card_product,
            className: 'product__card--wishlist-btn',
            tooltip: true,
            tooltip_position: 'tooltip--left'
          -%}
        </div>
      {%- endif -%}

      {% comment %} Add To cart button style 2 desktop {% endcomment %}
      {%- if show_cart_button -%}
        {% if card_style == 'style_2' %}
          {% render 'add-to-cart-form',
            product_card_product: product_card_product,
            variant: variant,
            custom_class: 'product__card--style2 d-md-none',
            button_class: 'button product__card--style2'
          %}
        {%- endif -%}
      {%- endif -%}
      {% comment %} Add To cart button style 2 desktop ./ {% endcomment %}

      <!-- Product action button style 2 -->
      {% if card_style == 'style_2' %}
        <div class="product-card-action-buttons-style2 d-md-none">
          {%- if show_wishlist -%}
            {%- render 'button-wishlist',
              product: product_card_product,
              tooltip: true,
              tooltip_position: 'tooltip--left',
              className: 'product__card-style2--action-btn'
            -%}
          {%- endif -%}

          {%- if show_quick_compare -%}
            {%- render 'button-compare',
              product: product_card_product,
              tooltip: true,
              tooltip_position: 'tooltip--left',
              className: 'product__card-style2--action-btn'
            -%}
          {%- endif -%}

          {%- if show_quick_view -%}
            {%- render 'quick-view',
              product_card_product: product_card_product,
              variant: variant,
              type: 'quick_view',
              tooltip: true,
              tooltip_position: 'tooltip--left',
              className: 'product__card-style2--action-btn'
            -%}
          {%- endif -%}
        </div>
      {%- endif -%}
      <!-- Product action button style 2 ./ -->
    {%- endif -%}
  </div>

  <div class="product__card__content text-{{ settings.product_content_alignment }}">
    {%- if show_vendor and product_card_product != blank -%}
      <span class="visually-hidden">{{ 'accessibility.vendor' | t }}</span>
      <div class="product__vendor">{{ product_card_product.vendor }}</div>
    {%- endif -%}

    {%- if show_title and product_card_product != blank -%}
      <h3 class="product__card__title {{ settings.product_title_size }}">
        <a class="product__card-title--link" href="{{ product_card_product.url | default: '#' }}">
          {{- product_card_product.title -}}
        </a>
      </h3>
    {% else %}
      <h3 class="product__card__title h5 p_blank_title">Example product title {{ index }}</h3>
    {%- endif -%}
    {%- if show_rating and product_card_product.metafields.reviews.rating.value != blank -%}
    <div class="trustshop-collection-rating--item" rating-value="{{ product_card_product.metafields.reviews.rating.value }}"
      rating-count="{{ product_card_product.metafields.reviews.rating_count }}">
    </div>
    {% else %}
    <div class="blank_p_rating"> 
      <div class="trustshop-collection-rating-star--wrap"><span class="trustshop trustshop-rating-star--container"><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none"><path d="M7.16604 12.1708C7.48383 11.979 7.88171 11.979 8.19951 12.1708L10.858 13.7754C11.6153 14.2325 12.5495 13.5535 12.3486 12.692L11.6431 9.6678C11.5587 9.30631 11.6816 8.92784 11.9621 8.68479L14.3124 6.64873C14.981 6.06952 14.6236 4.97126 13.7422 4.89648L10.65 4.63414C10.2804 4.60278 9.95851 4.36944 9.81377 4.02794L8.60349 1.17237C8.2589 0.359342 7.10664 0.359342 6.76205 1.17237L5.55177 4.02794C5.40703 4.36944 5.08517 4.60278 4.71559 4.63414L1.62335 4.89648C0.741922 4.97126 0.384515 6.06952 1.05311 6.64873L3.40341 8.68479C3.68397 8.92784 3.80682 9.30631 3.72249 9.6678L3.01697 12.692C2.81601 13.5535 3.75024 14.2325 4.50756 13.7754L7.16604 12.1708Z" fill="url(#rating-8u203vy4q)"></path><defs><linearGradient id="rating-8u203vy4q" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0" id="stop-0gx4e6i2g" stop-color="#F88E0F"></stop>
              <stop offset="0" id="stop-uthvrgyn4" stop-color="#F88E0F"></stop>
              </linearGradient></defs></svg><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none"><path d="M7.16604 12.1708C7.48383 11.979 7.88171 11.979 8.19951 12.1708L10.858 13.7754C11.6153 14.2325 12.5495 13.5535 12.3486 12.692L11.6431 9.6678C11.5587 9.30631 11.6816 8.92784 11.9621 8.68479L14.3124 6.64873C14.981 6.06952 14.6236 4.97126 13.7422 4.89648L10.65 4.63414C10.2804 4.60278 9.95851 4.36944 9.81377 4.02794L8.60349 1.17237C8.2589 0.359342 7.10664 0.359342 6.76205 1.17237L5.55177 4.02794C5.40703 4.36944 5.08517 4.60278 4.71559 4.63414L1.62335 4.89648C0.741922 4.97126 0.384515 6.06952 1.05311 6.64873L3.40341 8.68479C3.68397 8.92784 3.80682 9.30631 3.72249 9.6678L3.01697 12.692C2.81601 13.5535 3.75024 14.2325 4.50756 13.7754L7.16604 12.1708Z" fill="url(#rating-pa1yc3w95)"></path><defs><linearGradient id="rating-pa1yc3w95" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0" id="stop-uiy93vgri" stop-color="#F88E0F"></stop>
              <stop offset="0" id="stop-5cf94q29b" stop-color="#F88E0F"></stop>
              </linearGradient></defs></svg><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none"><path d="M7.16604 12.1708C7.48383 11.979 7.88171 11.979 8.19951 12.1708L10.858 13.7754C11.6153 14.2325 12.5495 13.5535 12.3486 12.692L11.6431 9.6678C11.5587 9.30631 11.6816 8.92784 11.9621 8.68479L14.3124 6.64873C14.981 6.06952 14.6236 4.97126 13.7422 4.89648L10.65 4.63414C10.2804 4.60278 9.95851 4.36944 9.81377 4.02794L8.60349 1.17237C8.2589 0.359342 7.10664 0.359342 6.76205 1.17237L5.55177 4.02794C5.40703 4.36944 5.08517 4.60278 4.71559 4.63414L1.62335 4.89648C0.741922 4.97126 0.384515 6.06952 1.05311 6.64873L3.40341 8.68479C3.68397 8.92784 3.80682 9.30631 3.72249 9.6678L3.01697 12.692C2.81601 13.5535 3.75024 14.2325 4.50756 13.7754L7.16604 12.1708Z" fill="url(#rating-vubamemww)"></path><defs><linearGradient id="rating-vubamemww" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0" id="stop-uwlabx0yz" stop-color="#F88E0F"></stop>
              <stop offset="0" id="stop-gotf3cx0m" stop-color="#F88E0F"></stop>
              </linearGradient></defs></svg><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none"><path d="M7.16604 12.1708C7.48383 11.979 7.88171 11.979 8.19951 12.1708L10.858 13.7754C11.6153 14.2325 12.5495 13.5535 12.3486 12.692L11.6431 9.6678C11.5587 9.30631 11.6816 8.92784 11.9621 8.68479L14.3124 6.64873C14.981 6.06952 14.6236 4.97126 13.7422 4.89648L10.65 4.63414C10.2804 4.60278 9.95851 4.36944 9.81377 4.02794L8.60349 1.17237C8.2589 0.359342 7.10664 0.359342 6.76205 1.17237L5.55177 4.02794C5.40703 4.36944 5.08517 4.60278 4.71559 4.63414L1.62335 4.89648C0.741922 4.97126 0.384515 6.06952 1.05311 6.64873L3.40341 8.68479C3.68397 8.92784 3.80682 9.30631 3.72249 9.6678L3.01697 12.692C2.81601 13.5535 3.75024 14.2325 4.50756 13.7754L7.16604 12.1708Z" fill="url(#rating-z3ih6grlv)"></path><defs><linearGradient id="rating-z3ih6grlv" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0" id="stop-s3vpjv7hr" stop-color="#F88E0F"></stop>
              <stop offset="0" id="stop-2rq7m01u7" stop-color="#F88E0F"></stop>
              </linearGradient></defs></svg><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none"><path d="M7.16604 12.1708C7.48383 11.979 7.88171 11.979 8.19951 12.1708L10.858 13.7754C11.6153 14.2325 12.5495 13.5535 12.3486 12.692L11.6431 9.6678C11.5587 9.30631 11.6816 8.92784 11.9621 8.68479L14.3124 6.64873C14.981 6.06952 14.6236 4.97126 13.7422 4.89648L10.65 4.63414C10.2804 4.60278 9.95851 4.36944 9.81377 4.02794L8.60349 1.17237C8.2589 0.359342 7.10664 0.359342 6.76205 1.17237L5.55177 4.02794C5.40703 4.36944 5.08517 4.60278 4.71559 4.63414L1.62335 4.89648C0.741922 4.97126 0.384515 6.06952 1.05311 6.64873L3.40341 8.68479C3.68397 8.92784 3.80682 9.30631 3.72249 9.6678L3.01697 12.692C2.81601 13.5535 3.75024 14.2325 4.50756 13.7754L7.16604 12.1708Z" fill="url(#rating-to7o1jppg)"></path><defs><linearGradient id="rating-to7o1jppg" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0" id="stop-n09bd7ezw" stop-color="#F88E0F"></stop>
              <stop offset="0" id="stop-di9tete50" stop-color="#F88E0F"></stop>
              </linearGradient></defs></svg></span>
      </div>
    </div>
    {%- endif -%}
    {%- if show_price and product_card_product != blank  -%}
      {% render 'price', product: product_card_product, price_class: price_class %}
        {% else %}
      <span class="h6 p_blank_price">$99.99</span>
    {%- endif -%}



    {% if product_card_product != blank %}
      <!-- Product action button -->
      <div class="product-card-action-buttons  {% if card_style == 'style_2' %}d-md-only-visible{% endif %}">
        {% comment %} Add To cart button style 1 {% endcomment %}
        {%- if show_cart_button -%}
          {% render 'add-to-cart-form',
            product_card_product: product_card_product,
            variant: variant,
            tooltip: true,
            tooltip_position: 'tooltip--top',
            tooltip_desktop: false
          %}
        {%- endif -%}
        {% comment %} Add To cart button style 1 ./ {% endcomment %}
 {% comment %}
        {%- if show_quick_compare -%}
          {%- render 'button-compare',
            product: product_card_product,
            className: 'product__card--action-btn product__card--action-btn-icon',
            tooltip: true,
            tooltip_position: 'tooltip--top'
          -%}
        {%- endif -%}

        {%- if show_quick_view -%}
          {%- render 'quick-view',
            product_card_product: product_card_product,
            variant: variant,
            type: 'quick_view',
            className: 'product__card--action-btn product__card--action-btn-icon',
            tooltip: true,
            tooltip_position: 'tooltip--top'
          -%}
        {%- endif -%}
{% endcomment %}

        
      </div>
      <!-- Product action button ./ -->
      {% if settings.color_swatches %}
        {% if color_swatches %}
          {% render 'product-card-color-swatch', product: product_card_product, current_variant: variant %}
        {% endif %}
      {% endif %}
    {%- endif -%}
  </div>
</div>
