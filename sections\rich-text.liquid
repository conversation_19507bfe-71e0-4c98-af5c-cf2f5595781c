<link rel="stylesheet" href="{{ 'component-rte.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'section-rich-text.css' | asset_url }}" media="print" onload="this.media='all'">

<noscript>{{ 'component-rte.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'section-rich-text.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
    --padding-top: {{ section.settings.mobile_padding_top }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
      --padding-top: {{ section.settings.padding_top }}px;
    }
  }
{%- endstyle -%}

<div class="rich-text container section--top-space-{{ section.id }} color-{{ section.settings.color_scheme }} section-{{ section.id }}-padding">
  <div class="rich-text__blocks text-{{ section.settings.alignment }} mobile-text-{{ section.settings.mobile_alignment }}">
    {%- for block in section.blocks -%}
      {%- case block.type -%}
        {%- when 'heading' -%}
          <h2 class="{{ block.settings.heading_size }}" {{ block.shopify_attributes }}>
            {{ block.settings.heading | replace: 'p>', 'span>' }}
          </h2>
        {%- when 'text' -%}
          <div
            class="rich-text__text rte rich-text__{{ block.settings.text_size }} richt_text--{{ block.settings.text_color }}"
            {{ block.shopify_attributes }}
          >
            {{ block.settings.text }}
          </div>
        {%- when 'button' -%}
          {% liquid
            if block.settings.button_style == 'primary'
              assign button_class = ''
            else
              assign button_class = 'button--secondary'
            endif
          %}

          <a
            {% if block.settings.button_link == blank %}
              aria-disabled="true"
            {% else %}
              href="{{ block.settings.button_link }}"
            {% endif %}
            class="button button--{{ block.settings.button_size }} {{ button_class }}"
            {{ block.shopify_attributes }}
          >
            {{ block.settings.button_label | escape }}
          </a>
      {%- endcase -%}
    {%- endfor -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.rich-text.name",
  "tag": "section",
  "settings": [
    {
      "type": "select",
      "id": "alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center",
      "label": "Desktop content alignment"
    },
    {
      "type": "select",
      "id": "mobile_alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center",
      "label": "Mobile content alignment"
    },

    {
        "type": "color_scheme",
        "id": "color_scheme",
        "label": "t:sections.all.colors.label",
        "default": "background-1"
      },
	{
          "type": "header",
          "content": "Section padding"
        },
		{
          "type": "paragraph",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 50
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 50
        },
		{
          "type": "paragraph",
          "content": "Mobile"
        },
		{
          "type": "range",
          "id": "mobile_padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 50
        },
        {
          "type": "range",
          "id": "mobile_padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 50
        }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "t:sections.rich-text.blocks.heading.name",
      "limit": 1,
      "settings": [
         {
          "type": "richtext",
          "id": "heading",
          "default": "<p>Talk about your brand</p>",
          "label": "t:sections.rich-text.blocks.heading.settings.heading.label"
        },
        {
             "type": "select",
             "id": "heading_size",
             "options": [
               {
                 "value": "h3",
                 "label": "Small"
               },
               {
                 "value": "h2",
                 "label": "Medium"
               },
               {
                 "value": "h1",
                 "label": "Large"
               },
               {
                 "value": "h0",
                 "label": "Extra large"
               }
             ],
             "default": "h3",
             "label": "Heading size"
           }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.rich-text.blocks.text.name",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.</p>",
          "label": "t:sections.rich-text.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "large",
              "label": "Large"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "small",
              "label": "Small"
            }
          ],
          "default": "small",
          "label": "Text size"
        },
        {
          "type": "select",
          "id": "text_color",
          "options": [
            {
              "value": "body",
              "label": "Body"
            },
            {
              "value": "heading",
              "label": "Heading"
            }
          ],
          "default": "body",
          "label": "Text color"
        }
      ]
    },
    {
      "type": "button",
      "name": "t:sections.rich-text.blocks.button.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "t:sections.rich-text.blocks.button.settings.button_label.label"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.rich-text.blocks.button.settings.button_link.label"
        },
		{
          "type": "select",
          "id": "button_style",
          "label": "Button style",
          "default": "primary",
          "options": [
            {
              "value": "secondary",
              "label": "Secondary"
            },
            {
              "value": "primary",
              "label": "Primary"
            }
          ]
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "Button size",
          "default": "large",
          "options": [
          {
            "value": "large",
            "label": "Large"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
          {
            "value": "small",
            "label": "Small"
            }
          ]
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.rich-text.presets.name",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "button"
        }
      ]
    }
  ]
}
{% endschema %}
