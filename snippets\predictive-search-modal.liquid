{% liquid
  assign predictive_result_type = 'product'

  if settings.show_collection
    assign predictive_result_type = predictive_result_type | append: ',collection'
  endif

  if settings.show_article
    assign predictive_result_type = predictive_result_type | append: ',article'
  endif

  if settings.show_page
    assign predictive_result_type = predictive_result_type | append: ',page'
  endif

  assign my_array = settings.search_filter_tags

  assign new_my_array = my_array | split: ','
  assign result = new_my_array | uniq | sort_natural | join: ', '
  assign product_tags = result | split: ', '

  assign popular_search = routes.search_url | append: '?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q='
  assign popular_search_tag = settings.popular_search_queries | split: ','
%}

<svg style="display: none">
  <symbol id="icon-caret" viewBox="0 0 10 6">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.354.646a.5.5 0 00-.708 0L5 4.293 1.354.646a.5.5 0 00-.708.708l4 4a.5.5 0 00.708 0l4-4a.5.5 0 000-.708z" fill="currentColor">
      </symbol>
</svg>
<div id="predictive__search_overlay" class="predictive--search-drawer" tabindex="-1">
  <div class="search__content_inner predictive--search-drawer-inner">
    {%- if settings.predictive_search_enabled -%}
      <predictive-search
        class="search-modal__form"
        data-loading-text="{{ 'accessibility.loading' | t }}"
        data-limit="{{ settings.number_of_search_result }}"
        data-unavailable="{{ settings.show_unavailable_product }}"
        data-types="{{ predictive_result_type }}"
      >
    {% else %}
      <search-form class="search-modal__form">
    {%- endif -%}
    <form action="{{ routes.search_url }}" method="get" role="search" class="search search-modal__form">
      {% unless settings.search_type == 'none' %}
        <div class="category__search--box">
          <div class="select-custom header-global-search-select">
            <div class="select__field_form">
              <main-select-search>
                <label class="visually-hidden" for="category__serch--form">
                  {{ 'customer.addresses.country' | t }}
                </label>
              </main-select-search>
            </div>
          </div>
          <button
            type="button"
            class="modal__close-button link link--text focus-inset"
            id="search__close_btn"
            aria-label="{{ 'accessibility.close' | t }}"
          >
            <svg class="icon icon-close" aria-hidden="true" focusable="false">
              <use href="#icon-close">
            </svg>
          </button>
        </div>
      {% endunless %}

      <div class="predictive__search--drawer--form-inner">
        <div class="search__input_field input__field_form">
          <label class="visually-hidden" for="Search-In-Modal">{{ 'general.search.search' | t }}</label>
          <input
            class="search__input input__field"
            id="Search-In-Modal"
            type="search"
            name="q"
            value=""
            placeholder="{{ 'general.search.search' | t }}"
            {%- if settings.predictive_search_enabled -%}
              role="combobox"
              aria-expanded="false"
              aria-owns="predictive-search-results-list"
              aria-controls="predictive-search-results-list"
              aria-haspopup="listbox"
              aria-autocomplete="list"
              autocorrect="off"
              autocomplete="off"
              autocapitalize="off"
              spellcheck="false"
            {%- endif -%}
          >
          <input type="hidden" name="options[prefix]" value="last">
          <button
            type="reset"
            class="reset__button field__button{% if search.terms == blank %} hidden{% endif %}"
            aria-label="{{ 'general.search.reset' | t }}"
          >
            <svg class="icon icon-close" aria-hidden="true" focusable="false">
              <use xlink:href="#icon-reset">
            </svg>
          </button>
          <button
            class="search__button input__field_form_button popup__search--button"
            aria-label="{{ 'general.search.search' | t }}"
          >


            <svg class="icon icon-search" aria-hidden="true" focusable="false" role="presentation" clip-rule="evenodd" fill-rule="evenodd" stroke-linejoin="round" stroke-miterlimit="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" id="fi_15953176"><g id="Icon"><path d="m10 .75c5.105 0 9.25 4.145 9.25 9.25s-4.145 9.25-9.25 9.25-9.25-4.145-9.25-9.25 4.145-9.25 9.25-9.25zm0 1.5c-4.277 0-7.75 3.473-7.75 7.75s3.473 7.75 7.75 7.75 7.75-3.473 7.75-7.75-3.473-7.75-7.75-7.75z"></path><path d="m10 4.75c-.414 0-.75-.336-.75-.75s.336-.75.75-.75c2.346 0 4.415 1.2 5.625 3.019.229.344.135.81-.21 1.04-.344.229-.81.135-1.04-.21-.941-1.415-2.55-2.349-4.375-2.349z"></path><path d="m15.47 16.53c-.293-.292-.293-.768 0-1.06.292-.293.768-.293 1.06 0l6.5 6.5c.293.292.293.768 0 1.06-.292.293-.768.293-1.06 0z"></path></g></svg>    
          </button>
        </div>
        {% if settings.search_type == 'none' %}
          <button
            type="button"
            class="modal__close-button link link--text focus-inset"
            id="search__close_btn"
            aria-label="{{ 'accessibility.close' | t }}"
          >
            <svg class="icon icon-close" aria-hidden="true" focusable="false">
              <use href="#icon-close">
            </svg>
          </button>
        {% endif %}
      </div>

      <div class="popular__search--history">
        <div class="popular__search--tags">
          {% if settings.popular_search_heading != blank %}
            <h4 class="popular__search--title">{{ settings.popular_search_heading }}</h4>
          {% endif %}
          <ul class="d-flex flex-wrap popular__search--tag-list">
            {% for tag in popular_search_tag %}
              <li class="popular__search--tag--item">
                {% assign popuplar_tag = tag | strip | url_encode %}
                <a class="popular__search--tag--link" href="{{ popular_search | append: popuplar_tag }}"> {{ tag }} </a>
                <br>
              </li>
            {% endfor %}
          </ul>
        </div>

        {%- liquid
          assign popular_product_limit = settings.most_searced_products_limit
          assign collection = collections[settings.most_searced_products_collection]
        -%}

        <div class="popular__search--tags">
          {% if settings.most_searched_product_heading != blank %}
            <h4 class="popular__search--title">{{ settings.most_searched_product_heading }}</h4>
          {% endif %}
          <ul
            id="predictive-search-results-products-list"
            class="predictive-search__results-list list-unstyled"
            role="group"
            aria-labelledby="predictive-search-products"
          >
            {%- for product in collection.products limit: popular_product_limit -%}
              <li
                id="predictive-search-option-product-{{ forloop.index }}"
                class="predictive-search__list-item"
                role="option"
                aria-selected="false"
              >
                <a
                  href="{{ product.url }}"
                  class="predictive-search__item predictive-search__item--link-with-thumbnail link link--text"
                  tabindex="-1"
                >
                  {%- if product.featured_media != blank -%}
                    <img
                      class="predictive-search__image"
                      src="{{ product.featured_media | image_url: width: 150 }}"
                      alt="{{ product.featured_media.alt }}"
                      width="70"
                      height="{{ 70 | divided_by: product.featured_media.preview_image.aspect_ratio }}"
                    >
                  {%- endif -%}
                  <div class="predictive-search__item-content{% unless settings.predictive_search_show_vendor or settings.predictive_search_show_price %} predictive-search__item-content--centered{% endunless %}">
                    {%- if settings.predictive_search_show_vendor -%}
                      <span class="visually-hidden">{{ 'accessibility.vendor' | t }}</span>
                      <div class="predictive-search__item-vendor caption-with-letter-spacing">{{ product.vendor }}</div>
                    {%- endif -%}
                    <p class="predictive-search__item-heading h6">{{ product.title }}</p>
                    {%- if settings.predictive_search_show_price -%}
                      {% render 'price', product: product, use_variant: true, show_badges: false %}
                    {%- endif -%}
                  </div>
                </a>
              </li>
            {%- endfor -%}
          </ul>
        </div>
      </div>

      {%- if settings.predictive_search_enabled -%}
        <div
          class="predictive-search predictive-search--header predictive-search-drawer-result"
          tabindex="-1"
          data-predictive-search
        >
          <div class="predictive-search__loading-state">
            <svg
              aria-hidden="true"
              focusable="false"
              role="presentation"
              class="spinner"
              viewBox="0 0 66 66"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
            </svg>
          </div>
        </div>

        <span class="predictive-search-status visually-hidden" role="status" aria-hidden="true"></span>
      {%- endif -%}
    </form>
    {%- if settings.predictive_search_enabled -%}
      </predictive-search>
    {%- else -%}
      </search-form>
    {%- endif -%}
  </div>
</div>
<div class="offcanvas-overlay"></div>
