<style>
  .input__field_form_button {
    width: 35px;
  }
  .offset__section {
    margin-left: auto;
    margin-right: auto;
    max-width: 50rem;
  }
  main {
    flex-grow: 1;
  }
  .password_page_wrapper {
    padding: 100px 0;
  }
</style>


{%- liquid

 	assign container = ''
  	if section.settings.container == "container"
    assign container = 'container'
    elsif section.settings.container == "container-fluid"
    assign container = 'container-fluid'
    else
    assign container = 'container-fluid px-0'
    endif
  
-%}


<div class="password_page_wrapper text-center color-{{ section.settings.color_scheme }}">
  <div class="{{ container }}">
    <div class="row">
      <div class="col-12">
        <div class="password_page_header mb-50">
          {%- if section.settings.password_heading != blank -%}
          <h1>{{ section.settings.password_heading }}</h1>
          {%- endif -%}
         {%- if shop.password_message != blank -%}
          <p> {{ shop.password_message }}</p>
          {%- else -%}
          {%- if section.settings.password_description != blank -%}
          <p> {{ section.settings.password_description }}</p>
          {%- endif -%}
          {%- endif -%}
        </div>

		{%- if section.settings.show_nesletter -%}
        <div class="newsletter__subscription offset__section">
          {%- if section.settings.newsletter_heading -%}
          <h4 class="newsletter_title">{{ section.settings.newsletter_heading }}</h4>
          {%- endif -%}
          <div class="newsletter_subscription_inner">
            {%- form 'customer', id: 'ContactFooter', class: 'footer__newsletter newsletter-form' -%}
            <input type="hidden" name="contact[tags]" value="newsletter">
            <div class="newsletter-form__field-wrapper">
              <div class="input__field_form">
                <input
                       id="NewsletterForm--{{ section.id }}"
                       type="email"
                       name="contact[email]"
                       class="input__field"
                       value="{{ form.email }}"
                       aria-required="true"
                       autocorrect="off"
                       autocapitalize="off"
                       autocomplete="email"
                       {% if form.errors %}
                       autofocus
                       aria-invalid="true"
                       aria-describedby="ContactFooter-error"
                       {% elsif form.posted_successfully? %}
                       aria-describedby="ContactFooter-success"
                       {% endif %}
                       placeholder="{{ 'newsletter.label' | t }}"
                       required
                       >
                <button type="submit" class="input__field_form_button" name="commit" id="Subscribe" aria-label="{{ 'newsletter.button_label' | t }}">
                  {% render 'icon-arrow' %}
                </button>
              </div>
              {%- if form.errors -%}
              <small class="newsletter-form__message form__message" id="ContactFooter-error">{% render 'icon-error' %}{{ form.errors.translated_fields['email'] | capitalize }} {{ form.errors.messages['email'] }}</small>
              {%- endif -%}
            </div>
            {%- if form.posted_successfully? -%}
            <h3 class="newsletter-form__message newsletter-form__message--success form__message" id="ContactFooter-success" tabindex="-1">{% render 'icon-success' %}{{ 'newsletter.success' | t }}</h3>
            {%- endif -%}
            {%- endform -%}
          </div>
        </div>
		{%- endif -%}

        {%- if section.settings.show_password -%}
        <div class="password-modal__content offset__section mt-50" >
          
          {%- if section.settings.password_text -%}
          <h4 class="password-modal__content-heading">
            {{ section.settings.password_text }}
          </h4>
          {%- endif -%}
          
          {%- form 'storefront_password', class: 'password-form' -%}
          <div class="password-field field{% if form.errors %} password-field--error{% endif %}">

            <div class="input__field_form">
              <input
                     type="password"
                     name="password"
                     id="Password"
                     class="input__field"
                     autocomplete="current-password"
                     {% if form.errors %}
                     aria-invalid="true"
                     aria-describedby="PasswordLoginForm-password-error"
                     {%- endif -%}
                     placeholder="{{ 'general.password_page.login_form_password_placeholder' | t }}"
                     >
              <button type="submit" class="input__field_form_button" name="commit" aria-label="{{ 'newsletter.button_label' | t }}">
                {% render 'icon-arrow' %}
              </button>
            </div>


            {%- if form.errors -%}
            <div id="PasswordLoginForm-password-error" role="status">
              <span class="visually-hidden">{{ 'accessibility.error' | t }}</span>
              <span class="form__message">{% render 'icon-error' %} {{ 'general.password_page.login_form_error' | t }}</span>
            </div>
            {%- endif -%}
          </div>

          {%- endform -%}
          <small class="password__footer-text">{{ 'general.password_page.admin_link_html' | t }}</small>
        </div>
        
        {%- endif -%}

      </div>    
    </div>
  </div>
</div>

{% schema %}
  {
    "name": "Password main",
    "settings": [
		{
          "type": "select",
          "id": "container",
          "label": "Container type",
          "default": "container",
          "options": [
            {
              "value": "container",
              "label": "Fixed container"
            },
            {
              "value": "container-fluid",
              "label": "Fluid container"
            },
            {
              "value": "no-offset",
              "label": "Full width without offset"
            }
          ]
        },
        {
          "type": "text",
          "id": "password_heading",
          "default": "Website is coming soon ",
          "label": "Heading"
        },
		{
          "type": "richtext",
          "id": "password_description",
		  "default": "<p>Successful companies need time to embed themselves in people's minds. In a magazine, there's a blurb. An article in the newspaper. A remark made by a buddy.</p>",
          "label": "Description"
        },
         {
        "type": "color_scheme",
        "id": "color_scheme",
        "label": "t:sections.all.colors.label",
        "default": "background-1"
      },     
      
		{
          "type": "header",
          "content": "Newsletter"
        },
		{
          "type": "text",
          "id": "newsletter_heading",
          "default": "Subscribe to our newsletter",
          "label": "Newsletter heading"
        },
		{
          "type": "checkbox",
          "id": "show_nesletter",
          "default": true,
          "label": "Show newsletter"
        },
		{
          "type": "header",
          "content": "password"
        },
		{
          "type": "text",
          "id": "password_text",
          "default": "Enter store using password:",
          "label": "Password heading"
        },
		{
          "type": "checkbox",
          "id": "show_password",
          "default": true,
          "label": "Show password"
        }
	],
  "presets": [
    {
      "name": "Password"
    }
  ],
  "templates": ["password"]
  }
{% endschema %}