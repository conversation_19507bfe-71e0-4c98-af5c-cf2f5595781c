{%- liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
-%}
{{ 'featured-collection.css' | asset_url | stylesheet_tag }}
{{ 'component-rating.css' | asset_url | stylesheet_tag }}
{{ 'countdown-timer.css' | asset_url | stylesheet_tag }}
{{ 'stock-countdwon.css' | asset_url | stylesheet_tag }}
{{ 'product-tooltip.css' | asset_url | stylesheet_tag }}
{{ 'section-title.css' | asset_url | stylesheet_tag }}
{{ 'product-card.css' | asset_url | stylesheet_tag }}

<link rel="stylesheet" href="{{ 'component-price.css' | asset_url }}" media="print" onload="this.media='all'">

<noscript>{{ 'component-price.css' | asset_url | stylesheet_tag }}</noscript>
{% if theme_rtl %}
  {{ 'product-card-rtl.css' | asset_url | stylesheet_tag }}
{% endif %}

{%- liquid

  assign container = ''
   if section.settings.container == "container"
   assign container = 'container'
   elsif section.settings.container == "container-fluid"
   assign container = 'container-fluid'
   else
   assign container = 'container-fluid px-0'
   endif

   assign productShowXl = section.settings.products_show_on_xl
   assign productShowLg = section.settings.products_show_on_lg
   assign productShowMd = section.settings.products_show_on_md
   assign productShowSm = section.settings.products_show_on_sm
   assign sliderRows = section.settings.slider_rows
   assign slideLoop = section.settings.slider_loop

   assign loadMoreButton = true
   assign slider_enable = true
   assign productItem = "col mb-30"
   
   if slider_enable
   assign productItem = "swiper-slide"
   assign showPagination = section.settings.show_pagination
   assign showNavigation = section.settings.show_navigation
   endif
 
-%}
<style>
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
</style>

{%- capture productWrapper -%}
{%- if slider_enable -%}
productSlider swiper
{%- else -%}
row row-cols-xl-{{ productShowXl }} row-cols-lg-{{ productShowLg }} row-cols-md-{{ productShowMd }} row-cols-{{ productShowSm }}
{%- endif -%}
{%- endcapture -%}

<div
class="recently_viewed_proudct no-js-inline section-{{ section.id }}-padding"
data-product-handle="{{ product.handle }}"
data-section-id="{{ section.id }}"
data-section-type="product-slider"
data-slider-enable="true"
data-card-style="{{ section.settings.card_style | default: 'style_1' }}"
data-image-ratio="{{ section.settings.image_ratio | default: 'adapt' }}"
data-show-secondary-image="{{ section.settings.show_secondary_image | default: false }}"
data-color-swatches="{{ section.settings.color_swatches | default: true }}"
data-show-badges="{{ section.settings.show_badges | default: true }}"
data-show-cart-button="{{ section.settings.show_cart_button | default: true }}"
data-show-preorder-button="{{ section.settings.show_preorder_button | default: true }}"
data-show-quick-view="{{ section.settings.show_quick_view_button | default: true }}"
data-show-compare="{{ section.settings.show_compare_view_button | default: true }}"
data-show-wishlist="{{ section.settings.show_wishlist_button | default: true }}"
data-show-title="{{ section.settings.show_title | default: true }}"
data-show-price="{{ section.settings.show_price | default: true }}"
data-show-vendor="{{ section.settings.show_vendor | default: false }}"
data-show-countdown="{{ section.settings.show_countdown | default: false }}"
data-show-rating="{{ section.settings.show_product_rating | default: false }}"
data-inventory-status="{{ section.settings.inventory_status | default: false }}"
data-card-radius="{{ section.settings.product_card_radius | default: false }}"
data-card-spacing="{{ section.settings.product_card_spacing | default: false }}"
data-color-scheme="{{ section.settings.product_card_color_scheme | default: 'background-1' }}"
{% if slider_enable %}
  data-show-mobile="{{ productShowSm }}"
  data-show-tablet="{{ productShowMd }}"
  data-show-large="{{ productShowLg }}"
  data-show-extra-large="{{ productShowXl }}"
  data-slider-rows="{{sliderRows}}"
  data-loop="{{slideLoop}}"
{% endif %}
 >
  <div class="{{ container }}">
    <div class="row">
      <div class="col-12">
        <div class="section-heading text-center mb-30">
          <h2 class="section-heading__title">{{- section.settings.heading | escape -}}</h2>
        </div>
      </div>
    </div>
    <div class="relative product_slider_wrapper">
      <div class="{{ productWrapper }}" {% unless slider_enable %} grid-recentViewProduct {% endunless %}>
        {%- if slider_enable -%}<div class="swiper-wrapper" grid-recentViewProduct> {%- endif -%}

        {%- if slider_enable -%}</div>{%- endif -%}
      </div>
     {%- if slider_enable -%}
    {%- if showNavigation -%}
    <div class="swiper-button-next product-slider--nav-button">{% render 'icon-arrow-right' %}</div>
    <div class="swiper-button-prev product-slider--nav-button">{% render 'icon-arrow-left' %}</div>
    {%- endif -%}
    {%- if showPagination -%}
    <div class="swiper-pagination product-slider--pagination"></div>
    {%- endif -%}
    {%- endif -%}
    </div>
  </div>
</div>

<script src="{{ 'recently_viewed_product.js' | asset_url }}" defer></script>
{% schema %}
  {
    "name": "Recently Viewed Product",
    "settings": [
      {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
      {
        "type": "text",
        "id": "heading",
        "default": "Recently viewed product",
        "label": "Heading"
      },
      {
        "type": "header",
        "content": "Grid settings"
      },
      {
        "type": "range",
        "id": "products_show_on_xl",
        "min": 2,
        "max": 8,
        "step": 1,
        "default": 4,
        "label": "Products per row on the extra-large desktop"
      },
      {
        "type": "range",
        "id": "products_show_on_lg",
        "min": 2,
        "max": 5,
        "step": 1,
        "default": 3,
        "label": "Products per row on the large desktop"
      },
      {
        "type": "range",
        "id": "products_show_on_md",
        "min": 2,
        "max": 4,
        "step": 1,
        "default": 3,
        "label": "Products per row on the tablet"
      },
      {
        "type": "range",
        "id": "products_show_on_sm",
        "min": 1,
        "max": 3,
        "step": 1,
        "default": 2,
        "label": "Products per row on the mobile"
      },
      {
        "type": "header",
        "content": "Slider settings"
      },
      {
          "type": "checkbox",
          "id": "show_pagination",
          "label": "Show pagination",
          "default": false
      },
      {
          "type": "checkbox",
          "id": "show_navigation",
          "label": "Show navigation",
          "default": true
      },
      {
          "type": "checkbox",
          "id": "slider_loop",
          "label": "Slider loop",
          "default": false
      },
      {
        "type": "select",
        "id": "slider_rows",
        "label": "Slider rows",
        "default": "1",
        "info": "rows > 1 is currently not compatible with loop mode (loop: true)",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          }
        ]
      },
      {
        "type": "header",
        "content": "Recently viewed product",
        "info": "Warning! Please press the save button if the \"recently viewed\" product does not display"
      },
      {
        "type": "select",
        "id": "card_style",
        "label": "Card style",
        "options": [
          {
            "value": "style_1",
            "label": "Style 1"
          },
          {
            "value": "style_2",
            "label": "Style 2"
          },
          {
            "value": "style_3",
            "label": "Style 3"
          }
        ],
        "default": "style_1"
      },
      {
        "type": "select",
        "id": "image_ratio",
        "options": [
          {
            "value": "adapt",
            "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__1.label"
          },
          {
            "value": "portrait",
            "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__2.label"
          },
          {
            "value": "square",
            "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__3.label"
          },
          {
            "value": "landscape",
            "label": "Landscape"
          }
        ],
        "default": "adapt",
        "label": "t:sections.main-collection-product-grid.settings.image_ratio.label"
      },
      {
        "type": "checkbox",
        "id": "show_secondary_image",
        "default": false,
        "label": "t:sections.main-collection-product-grid.settings.show_secondary_image.label"
      },
      {
        "type": "checkbox",
        "id": "color_swatches",
        "default": true,
        "label": "Enable color swatches",
        "info": "To display color swatches, you need to enable it. [Learn more](https://team90degree.com/suruchi-theme/theme-settings/color-swatches)."
      },
      {
        "type": "checkbox",
        "id": "show_badges",
        "default": true,
        "label": "Show badges"
      },
      {
        "type": "checkbox",
        "id": "show_cart_button",
        "default": true,
        "label": "Show cart button"
      },
      {
        "type": "checkbox",
        "id": "show_preorder_button",
        "default": true,
        "label": "Show pre-order button",
        "info": "You may want to allow customers to purchase out-of-stock items. [Learn more](https://team90degree.com/suruchi-theme/general-topics-faq/how-to-enable-the-pre-order-product-to-your-store)"
      },
      {
        "type": "checkbox",
        "id": "show_quick_view_button",
        "default": true,
        "label": "Show quick view"
      },
      {
        "type": "checkbox",
        "id": "show_compare_view_button",
        "default": true,
        "label": "Show compare button"
      },
      {
        "type": "checkbox",
        "id": "show_wishlist_button",
        "default": true,
        "label": "Show wishlist button"
      },
      {
        "type": "checkbox",
        "id": "show_title",
        "default": true,
        "label": "Show title"
      },
      {
        "type": "checkbox",
        "id": "show_price",
        "default": true,
        "label": "Show price"
      },
      {
        "type": "checkbox",
        "id": "show_vendor",
        "default": false,
        "label": "t:sections.main-collection-product-grid.settings.show_vendor.label"
      },
      {
        "type": "checkbox",
        "id": "show_countdown",
        "default": false,
        "label": "Show countdown",
        "info": "To display countdown timer, you need to enable it. [Learn more](https://team90degree.com/suruchi-theme/collections-and-products/products/add-a-countdown-timer-to-the-product)."
      },
      {
        "type": "checkbox",
        "id": "show_product_rating",
        "default": false,
        "label": "Show product rating"
      },
      {
        "type": "checkbox",
        "id": "inventory_status",
        "label": "Show inventory status",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "product_card_radius",
        "label": "Round corner",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "product_card_spacing",
        "label": "Card spacing",
        "default": false
      },
      {
        "type": "color_scheme",
        "id": "product_card_color_scheme",
        "label": "Product card color scheme",
        "default": "background-1"
      },
        {
      "type": "header",
      "content": "Section padding"
    },
    {
          "type": "paragraph",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        },
		{
          "type": "paragraph",
          "content": "Mobile"
        },
		{
          "type": "range",
          "id": "mobile_padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "mobile_padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        }
    ]
  }
{% endschema %}