<style>
    div#quickViewWrapper {
      position: fixed;
      z-index: 999;
      visibility: hidden;
      opacity: 0;
    }
    div#quickViewWrapper:not(.quick__shop--drawer) {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0,0,0,0.5);
    }
    .quick__view.quick__shop--drawer {
        position: fixed;
        width: 30rem;
        height: 100%;
        right: 0;
        z-index: 99;
        top: 0;
        right: 0;
        opacity: 0;
        visibility: hidden;
        background-color: rgb(var(--color-background));
        border-color: rgba(var(--color-foreground), 0.2);
        transform: translateX(100%);
        transition: all 0.3s ease 0s;
        background: rgba(var(--color-background));
    }
    #quickViewWrapper.quick__view.quick__shop--drawer.show__modal {
        transform: translate(0);
        opacity: 1;
        visibility: visible;
    }
    @media only screen and (min-width: 500px) and (max-width: 991px){
      .quick__view.quick__shop--drawer {
          width: 45rem;
      }
    }
    @media only screen and (min-width: 992px){
      .quick__view.quick__shop--drawer {
          width: 48rem;
      }
    }

    .quick__view__content {
      width: 50%;
      padding: 30px;
      flex-grow: 1;
    }
    .quick__View_img_wrapper {
      width: 50%;
    }
    div#quickViewWrapper:not(.quick__shop--drawer) > div {
      max-width: 1150px;
      margin: 150px auto;
      position: relative;
      transition: transform .3s ease-out,-webkit-transform .3s ease-out;
      background: rgba(var(--color-background));
      max-height: calc(100% - 5rem);
      overflow: auto;
  }
    @media only screen and (min-width: 1199px){
      div#quickViewWrapper:not(.quick__shop--drawer) > div {
        max-height: calc(100% - 30rem);
      }
    }
    @media only screen and (min-width: 992px){
      div#quickViewWrapper:not(.quick__shop--drawer) > div {
        max-height: calc(100% - 10rem);
      }
    }
    div#quickViewWrapper:not(.quick__shop--drawer).show__modal {
      visibility: visible;
      overflow-y: auto;
      opacity: 1;
    }
    .quick__view--drawer__content {
        width: 100%;
    }
    .quick__view.quick__shop--drawer > div {
        height: 100%;
    }
    .quick__view--drawer__content--wrapper {
        flex-grow: 1;
        overflow-y: scroll;
    }
    .quick__view--drawer {
        display: flex;
        height: 100%;
        padding-top: 6rem;
    }
    .quick__view--drawer-header {
        text-align: center;
        padding: 0 2rem;
    }
    .quick__View_img_wrapper.quick__view--drawer__image--gallery {
        width: 70%;
        margin: 0 auto;
    }
    .quick-view-overlay {
        position: fixed;
        z-index: 99;
        top: 0;
        left: 0;
        visibility: hidden;
        width: 100%;
        height: 100%;
        transition: var(--transition);
        opacity: 0;
        background-color: rgba(var(--color-foreground));
        cursor: crosshair;
        opacity: 0;
    }
    .quick__shop--drawer.show__modal + .quick-view-overlay {
        visibility: visible;
        opacity: 0.5;
    }
    .quick__view__content .price {
        font-size: 2.2em;
    }
    @media only screen and (min-width: 750px){
      .quick__view__content .price {
          font-size: 2.8rem;
      }
    }
</style>

<div
  role="dialog"
  class="quick__view {% if settings.quick_shop_type == "drawer" %} quick__shop--drawer {% endif %}"
  id="quickViewWrapper"
  aria-modal="true"
  tabindex="-1"
></div>
{% if settings.quick_shop_type == 'drawer' %}
  <div class="quick-view-overlay"></div>
{% endif %}
