/* Custom css */
.modal__close-button > svg {
  pointer-events: none;
}
#mini-cart-drawer.active {
  transform: translateX(0);
  opacity: 1;
  visibility: visible;
}
.cart-notification-product {
  align-items: flex-start;
  display: flex;
}
.cart-close-icon a {
  display: block;
  width: 20px;
  height: 20px;
}

@media screen and (min-width: 750px) {
  .header-wrapper:not(.header-wrapper--border-bottom)
    + cart-notification
    .cart-notification {
    border-top-width: 0.1rem;
  }
}
.cart-close-icon a:hover {
  color: rgba(var(--color-base-solid-button-labels)) !important;
  background: rgba(var(--color-base-accent-2));
}

.cart-notification-product dl {
  margin-bottom: 0;
  margin-top: 0;
}

.cart-notification-product__image {
  border: 0.1rem solid rgba(var(--color-foreground), 0.03);
  margin-right: 1.5rem;
}
.cart-notification-product__name {
  margin-bottom: 0;
  margin-top: 0;
  padding-right: 15px;
}

.cart-notification-product__option {
  color: rgba(var(--color-foreground), 0.7);
  margin-top: 1rem;
}

.cart-notification-product__option + .cart-notification-product__option {
  margin-top: 0.5rem;
}

.cart-notification-product__option > * {
  display: inline-block;
  margin: 0;
}

.cart-notification-product {
  position: relative;
}
.cart-close-icon a {
  display: block;
  width: 30px;
  height: 30px;
  color: rgb(var(--color-button-text));
  background: rgba(var(--color-button), var(--alpha-button-background));
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
}
.cart-close-icon .icon.icon-remove {
  width: 20px;
  height: 20px;
}
.cart-notification-product + .cart-notification-product {
  margin-top: 25px;
}
.cart-notification-product .loading-overlay {
  z-index: 9;
  left: 50%;
  transform: translate(-50%, -50%);
  top: 50%;
}

.cart-notification-product.loading::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: 8;
}

.cart-notification-product svg.spinner {
  height: 30px;
}
.cart_notification_topbar {
  padding: 20px 20px 0 20px;
}
.cart-notification .cart__items {
  padding: 0 20px 20px;
}
.cart_notification_links_inner {
  padding: 20px;
}
.cart__notification--attribute {
  padding: 1.2rem 20px 1.2rem;
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.15);
}
.mini-cart-popup-summary {
  align-items: center;
  display: flex;
  font-weight: 600;
  font-size: 1.4rem;
  gap: 0.2rem;
  flex-direction: column;
}
.mini-cart-popup-summary>svg {
    width: 1.8rem;
    height: 1.9rem;
}
.cart_notification_action_button + .cart_notification_action_button {
  border-left: 1px solid rgba(var(--color-foreground), 0.3);
  padding-left: 35px;
}
.action_drawer_footer button + button {
  margin-top: 1rem;
  font-weight: 600;
}
.notification_action_drawer {
  box-shadow: 20px 0 20px rgba(var(--color-foreground), 0.1);
  padding: 1.5rem 2rem 5rem;
  position: absolute;
  transform: translate3d(0, 100%, 0);
  bottom: 0;
  width: 100%;
  transition: 0.25s ease-in-out;
  background: #fff;
  z-index: 98;
}
.notification_action_drawer.active {
  transform: translateZ(0);
}
.cart_action_drawer_overlay.active {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.7);
  z-index: 8;
}
.cart-notification-product__option + .cart-notification-product__option {
  margin-left: 10px;
}
.product-option span a {
  word-break: break-all;
}
.product-option {
  display: flex;
}
.product-option span + span {
  margin-left: 10px;
}
.product-option {
  margin-top: 10px;
}
.cart-notification-product__info .quantity__button {
  width: 28px;
}
.cart-notification-product__info .quantity {
    height: 40px;
    width: 120px;
}
cart-remove-button-2 {
  margin-left: 10px;
}
button.cart_notification_action_button > svg {
  width: 20px;
  display: block;
  margin: 0 auto;
}
.cart--checkout__button {
  width: 100%;
}
.cart_notification--footer {
  gap: 1.5rem;
}
.cart-notification__header.empty__cart {
  justify-content: flex-end;
}
.empty__cart__item:not(.no-js-inline) {
  text-align: center;
  margin-top: 5rem;
}
.action_drawer_footer button.loading:after {
  left: 50%;
  top: 10px;
  transform: translateX(-50%);
}
li.discounts__discount svg.icon.icon-discount {
  width: 1.2rem;
}
.cart-item__discounted-prices {
  display: flex;
  align-items: center;
  gap: 1.2rem;
}
/* Cart details popup */
div#cart-notification {
  position: relative;
}
.cart--drawer-details-popup[open] > summary::before {
  position: fixed;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  max-width: 100%;
  max-height: 100%;
  background: rgba(var(--color-foreground), 0.7);
}
.cart--drawer-details-popup[open] > summary + div {
  position: fixed;
  bottom: 0;
  background: #fff;
  z-index: 101;
  left: 0;
  width: 100%;
  padding: 3rem 2rem;
}
/* Gift wrapping css  */
.add__gift--wrap-in--mini-cart {
  font-size: 1.5rem;
  padding: 1.2rem 2rem;
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.15);
}
.facet-checkbox {
  padding: 1rem 0;
  flex-grow: 1;
  font-size: 1.4rem;
  display: flex;
  word-break: break-word;
  align-items: center;
  z-index: 8;
  position: relative;
  cursor: pointer;
}
.facet-checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 1;
  width: 1.6rem;
  height: 1.6rem;
  left: -0.4rem;
  z-index: 0;
  appearance: none;
  -webkit-appearance: none;
}
.facet-checkbox > input[type="checkbox"]:checked ~ .checkbox-facet-check {
  background-color: rgba(var(--color-button), var(--alpha-button-background));
  box-shadow: 0 0 0 1px
    rgba(var(--color-button), var(--alpha-button-background));
}
.checkbox-facet-check {
  flex: none;
  position: relative;
  z-index: 1;
  width: 1.8rem;
  height: 1.8rem;
  margin-right: 1rem;
  background-color: rgba(var(--color-background));
  border-radius: 3px;
  transition: background-color 0.18s ease;
  box-shadow: 0 0 0 1px rgba(var(--color-foreground), 0.2);
}
.facet-checkbox > input[type="checkbox"]:checked ~ .icon-checkmark {
  visibility: visible;
}
.facet-checkbox .icon-checkmark {
  visibility: hidden;
  position: absolute;
  left: 0.3rem;
  z-index: 5;
}
.facet-checkbox > svg {
  color: rgb(var(--color-background));
  margin-right: 1.2rem;
  flex-shrink: 0;
}
