.hover--border-team {
  border: 4px solid transparent;
  padding: 0.3rem;
}

.team__items:hover .hover--border-team {
  border-color: rgba(var(--color-foreground));
}

@media only screen and (max-width: 991px) {
  .team__items {
    margin-bottom: 2.5rem;
  }
}

.team__thumb {
  position: relative;
  line-height: 1;
  display: block;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}

.team__social--list {
    margin-right: 1.5rem;
    list-style: none;
    background: #000;
    text-align: center;
    border-radius: 50px;
    padding: 5px 5px;
}
li.team__social--list:hover {
    background: rgba(var(--social-icon-hover-color));
}


ul.team__social {
  margin: 1.5rem 0 0;
  padding: 0;
}

.team__social--list:last-child {
  margin-right: 0;
}

@media only screen and (max-width: 575px) {
  .team__social--list {
    margin-right: 0.8rem;
  }
}
.team__social--icon {
  width: 3.2rem;
  height: 3.2rem;
  text-align: center;
  line-height: 3.5rem;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  color: #fff;
}
@media only screen and (max-width: 575px) {
  .team__social--icon {
    width: 2.8rem;
    height: 2.8rem;
  }
}

.team__content {
  padding: 2rem 0 0;
}

@media only screen and (max-width: 991px) {
  .team__content {
    padding: 1.5rem 0 0;
  }
}

.team__content--subtitle {
  color: var(--sky-color);
  font-size: 1.5rem;
  line-height: 2rem;
  margin-bottom: 1.5rem;
}

@media only screen and (min-width: 7px) {
  .team__content--subtitle {
    font-size: 1.6rem;
    line-height: 2.2rem;
  }
}

.team__content--title {
  font-weight: 700;
  margin-bottom: 1rem;
  font-size: 1.7rem;
  line-height: 2rem;
  margin-bottom: 0;
}

@media only screen and (min-width: 992px) {
  .team__content--title {
    font-size: 1.8rem;
  }
}

@media only screen and (min-width: 1200px) {
  .team__content--title {
    font-size: 2rem;
    line-height: 2.2rem;
  }
}

.border-radius-100 {
  border-radius: 100%;
}
.team__social--icon svg {
    width: 2rem;
    color: #fff;
}
.team__social--icon svg.icon-tumblr {
  width: 1rem;
}
ul.team__social {
  flex-wrap: wrap;
  margin: 0;
}
.team__social--media {
    position: absolute;
    left: 0;
    right: 0;
    top: auto;
    bottom: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    transition: var(--transition);
    opacity: 0;
}
.team__thumb .media:before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 1);
  z-index: 1;
  left: 0;
  opacity: 0;
  transition: var(--transition);
}
.team__items:hover .team__thumb .media:before {
  opacity: 0.5;
}
.team__items:hover .team__social--media {
  opacity: 1;
}
.team__social--icon:hover {
  color: rgba(var(--social-icon-hover-color));
}
