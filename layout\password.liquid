<!doctype html>
<html class="no-js full-height" lang="{{ request.locale.iso_code }}">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="">
    <link rel="canonical" href="{{ canonical_url }}">
    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>

    {%- if settings.favicon != blank -%}
      <link rel="icon" type="image/png" href="{{ settings.favicon | img_url: '32x32' }}">
    {%- endif -%}

    {%- unless settings.type_header_font.system? -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endunless -%}

    <title>{{ shop.name }}</title>

    <meta name="description" content="{{ page_description | escape }}">

    {% render 'meta-tags' %}

    {{ content_for_header }}

    {%- liquid
      assign body_font_bold = settings.type_body_font | font_modify: 'weight', 'bold'
      assign body_font_italic = settings.type_body_font | font_modify: 'style', 'italic'
      assign body_font_bold_italic = body_font_bold | font_modify: 'style', 'italic'
      assign body_fonts = settings.custom_body_font | split: '&'
      assign heading_fonts = settings.custom_heading_font | split: '&'
    %}

    {%- capture body_font_face -%}
        {%- for font in body_fonts -%}
         {%- assign font_URL = font | split: '@' | first  -%}
       {%- assign font_Weight = font | split: '@' | last  -%}
         @font-face {
          font-family: Body-Font;
          font-weight: {{- font_Weight -}};
          src: url({{- font_URL -}});
          font-display: swap;
        }
      {%- endfor -%}
    {%- endcapture -%}

    {% capture heading_font_face %}
      {%- for font in heading_fonts -%}
       {%- assign heading_font_URL = font | split: '@' | first -%}
       {%- assign heading_font_Weight = font | split: '@' | last  -%}
         @font-face {
          font-family: Heading-Font;
          font-weight: {{- heading_font_Weight -}};
          src: url({{- heading_font_URL -}});
          font-display: swap;
        }
      {%- endfor -%}
    {%- endcapture -%}

    {% style %}
         {%- if settings.use_custom_body_font and settings.custom_body_font != blank -%}
        {{ body_font_face }}
        {%- else  -%}
        {{ settings.type_body_font | font_face: font_display: 'swap' }}
        {{ body_font_bold | font_face: font_display: 'swap' }}
        {{ body_font_italic | font_face: font_display: 'swap' }}
        {{ body_font_bold_italic | font_face: font_display: 'swap' }}
        {%- endif -%}

        {%- if settings.use_custom_heading_font and settings.custom_heading_font != blank -%}
         {{ heading_font_face }}
        {%- else  -%}
         {{ settings.type_header_font | font_face: font_display: 'swap' }}
        {%- endif -%}


        {% for scheme in settings.color_schemes -%}
          {% assign scheme_classes = scheme_classes | append: ', .color-' | append: scheme.id %}
          {% if forloop.index == 1 -%}
            :root,
          {%- endif %}
          .color-{{ scheme.id }} {
            --color-background: {{ scheme.settings.background.red }},{{ scheme.settings.background.green }},{{ scheme.settings.background.blue }};
          {% if scheme.settings.background_gradient != empty %}
            --gradient-background: {{ scheme.settings.background_gradient }};
          {% else %}
            --gradient-background: {{ scheme.settings.background }};
          {% endif %}
            --color-foreground: {{ scheme.settings.text.red }},{{ scheme.settings.text.green }},{{ scheme.settings.text.blue }};
            --color-shadow: {{ scheme.settings.shadow.red }},{{ scheme.settings.shadow.green }},{{ scheme.settings.shadow.blue }};
            --color-button: {{ scheme.settings.button.red }},{{ scheme.settings.button.green }},{{ scheme.settings.button.blue }};
            --color-button-text: {{ scheme.settings.button_label.red }},{{ scheme.settings.button_label.green }},{{ scheme.settings.button_label.blue }};
            --color-secondary-button: {{ scheme.settings.background.red }},{{ scheme.settings.background.green }},{{ scheme.settings.background.blue }};
            --color-secondary-button-text: {{ scheme.settings.secondary_button_label.red }},{{ scheme.settings.secondary_button_label.green }},{{ scheme.settings.secondary_button_label.blue }};
            --color-link: {{ scheme.settings.secondary_button_label.red }},{{ scheme.settings.secondary_button_label.green }},{{ scheme.settings.secondary_button_label.blue }};
            --color-badge-foreground: {{ scheme.settings.text.red }},{{ scheme.settings.text.green }},{{ scheme.settings.text.blue }};
            --color-badge-background: {{ scheme.settings.background.red }},{{ scheme.settings.background.green }},{{ scheme.settings.background.blue }};
            --color-badge-border: {{ scheme.settings.text.red }},{{ scheme.settings.text.green }},{{ scheme.settings.text.blue }};
            --payment-terms-background-color: rgb({{ scheme.settings.background.rgb }});
            --primary-button-hover-background: {{ scheme.settings.primary_button_hover_bg.red }},{{ scheme.settings.primary_button_hover_bg.green }},{{ scheme.settings.primary_button_hover_bg.blue }};
            --primary-button-hover-text: {{ scheme.settings.primary_button_hover_text.red }},{{ scheme.settings.primary_button_hover_text.green }},{{ scheme.settings.primary_button_hover_text.blue }};
            --secondary-button-hover-background:  {{ scheme.settings.secondary_button_hover_bg.red }},{{ scheme.settings.secondary_button_hover_bg.green }},{{ scheme.settings.secondary_button_hover_bg.blue }};
            --secondary-button-hover-text:  {{ scheme.settings.secondary_button_hover_text.red }},{{ scheme.settings.secondary_button_hover_text.green }},{{ scheme.settings.secondary_button_hover_text.blue }};
            --text-link-hover-color:  {{ scheme.settings.tex_link_hover_color.red }},{{ scheme.settings.tex_link_hover_color.green }},{{ scheme.settings.tex_link_hover_color.blue }};
          }
          {% endfor %}

          {{ scheme_classes | prepend: 'body' }} {
            color: rgba(var(--color-foreground), 0.75);
            background-color: rgb(var(--color-background));
          }


        :root {
          {%- if settings.use_custom_body_font and settings.custom_body_font != blank -%}
           --font-body-family: Body-Font;
           --font-body-weight: {{ settings.body_font_weight | default: 400 }};
          {%- else -%}
          --font-body-family: {{ settings.type_body_font.family }}, {{ settings.type_body_font.fallback_families }};
          --font-body-style: {{ settings.type_body_font.style }};
          --font-body-weight: {{ settings.type_body_font.weight }};
         {%- endif -%}

         {%- if settings.use_custom_heading_font and settings.custom_heading_font != blank -%}
          --font-heading-family: Heading-Font;
          --font-heading-weight: {{ settings.heading_font_weight | default: 600 }};
          {%- else -%}
          --font-heading-family: {{ settings.type_header_font.family }}, {{ settings.type_header_font.fallback_families }};
          --font-heading-style: {{ settings.type_header_font.style }};
          --font-heading-weight: {{ settings.type_header_font.weight }};
          {%- endif -%}

          --placeholder-background: {{ settings.placeholder_background.red }}, {{ settings.placeholder_background.green }}, {{ settings.placeholder_background.blue }};
          --placeholder-foreground: {{ settings.placeholder_foreground.red }}, {{ settings.placeholder_foreground.green }}, {{ settings.placeholder_foreground.blue }};

          --gradient-base-background-1: {% if settings.gradient_background_1 != blank %}{{ settings.gradient_background_1 }}{% else %}{{ settings.colors_background_1 }}{% endif %};
          --gradient-base-background-2: {% if settings.gradient_background_2 != blank %}{{ settings.gradient_background_2 }}{% else %}{{ settings.colors_background_2 }}{% endif %};
          --gradient-base-accent-1: {% if settings.gradient_accent_1 != blank %}{{ settings.gradient_accent_1 }}{% else %}{{ settings.colors_accent_1 }}{% endif %};
          --gradient-base-accent-2: {% if settings.gradient_accent_2 != blank %}{{ settings.gradient_accent_2 }}{% else %}{{ settings.colors_accent_2 }}{% endif %};

      	  --font-body-size: {{ settings.body_font_size | divided_by: 100.0 }};
      	  --font-heading-size: {{ settings.heading_font_size | times: 1.0 | divided_by: settings.body_font_size }};
      	  --heading-letter-spacing: {{ settings.heading_letter_spacing }}px;
      	  --header-text-case: {{ settings.header_text_case }};

      	  --button-border-width: {{ settings.button_border_width }}px;
          --button-border-radius: {{ settings.button_border_radius }}px;
          --button-letter-spacing: {{ settings.button_letter_spacing }}px;
      	  --button-font-size: {{ settings.button_font_size | times: 1.0 | divided_by: settings.body_font_size }};
      	  --button-text-case: {{ settings.button_text_case }};

          --container-lg-width: {{ settings.container_lg_width | divided_by: 10 }}rem;
          --container-fluid-offset: {{ settings.container_fluid_offset | divided_by: 10 }}rem;
          --transition: all 0.3s ease 0s;
          --duration-long: 500ms;
        }

       :root {
        --white-color: #FFFFFF;
      }

        *,
        *::before,
        *::after {
          box-sizing: inherit;
        }

        html {
          box-sizing: border-box;
          height: 100%;
          margin: 0;
          padding: 0;
      	font-size: calc(var(--font-body-size) * 62.5%);
        }

        body {
      	margin: 0;
          min-height: 100%;
          font-size: 1.5rem;
      	letter-spacing: {{ settings.body_letter_spacing }}px;
          line-height: calc(1 + 0.8 / var(--font-body-size));
          font-family: var(--font-body-family);
          font-style: var(--font-body-style);
          font-weight: var(--font-body-weight);
          position: relative;
          visibility: visible;
          overflow-x: hidden;
        }
      @media only screen and (min-width: 992px){
          body {
            font-size: 1.6rem;
          }
      }
    {% endstyle %}

    {%- unless settings.type_body_font.system? -%}
      <link rel="preload" as="font" href="{{ settings.type_body_font | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}
    {%- unless settings.type_header_font.system? -%}
      <link rel="preload" as="font" href="{{ settings.type_header_font | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}

    {{ 'section-password.css' | asset_url | stylesheet_tag }}
    {{ 'base.css' | asset_url | stylesheet_tag }}
    {{ 'component-list-social.css' | asset_url | stylesheet_tag }}

    <script>
      document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
    </script>
    <script src="{{ 'global.js' | asset_url }}" defer="defer"></script>
  </head>

  <body class="password gradient">
    <main id="MainContent" class="password-main">
      {{ content_for_layout }}
    </main>
    {%- section 'password-footer' -%}
  </body>
</html>
