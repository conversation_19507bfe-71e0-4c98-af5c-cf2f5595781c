.social-links a {
    display: inline-flex;
    margin: 0 10px 10px 0;
    padding: 5px;
    font-size: 1.4rem;
    align-items: center;
    border-radius: 3px;
}
.social-links svg {
  width: 1.5rem;
}
.share-button {
    position: relative;
    padding: 5px;
    border-radius: 3px;
    align-items: center;
    margin-right: 0;
}
.social__share--text {
  margin-left: 5px;
}
.social__share_box>div+.share-button {
    margin-bottom: 1rem;
}
.social-links a .social__share--text {
  text-decoration: underline;
  text-underline-offset: 3px;
}
/* Share Button CSS  */
.share-button details {
  width: fit-content;
}

.share-button__button {
  font-size: 1.4rem;
  display: flex;
  align-items: center;
  color: rgba(var(--color-link), var(--alpha-link));
    margin-left: 0;
    padding-left: 0;
    }

.share-button__button:hover,
details[open] > .share-button__button {
  color: rgb(var(--color-link));
    }

details[open] > .share-button__fallback {
  animation: animateMenuOpen var(--duration-default) ease;
    }

.share-button__button {
  text-decoration: underline;
  text-underline-offset: 0.3rem;
}

.share-button__button,
.share-button__fallback button {
  cursor: pointer;
  background-color: transparent;
  border: none;
}

.share-button__button .icon-share {
  margin-right: 1rem;
}

.share-button__fallback {
  background: rgb(var(--color-background));
    display: flex;
    align-items: center;
    position: absolute;
    top: 3rem;
    left: 0.1rem;
    z-index: 3;
    width: 100%;
    min-width: 31rem;
    box-shadow: 0 0 0 0.1rem rgba(var(--color-foreground), 0.55);
      }

.share-button__fallback button {
  width: 4.4rem;
  height: 4.4rem;
  padding: 0;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.share-button__fallback button:hover {
  color: rgba(var(--color-foreground), 0.75);
    }

.share-button__fallback button:hover svg {
  transform: scale(1.07);
}

.share-button__close:not(.hidden) + .share-button__copy {
  display: none;
}

.share-button__close,
.share-button__copy {
  background-color: transparent;
  color: rgb(var(--color-foreground));
    }

.share-button__fallback .field__input {
  box-shadow: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.share-button__fallback .icon {
  width: 1.5rem;
  height: 1.5rem;
}

.share-button__message:not(:empty) {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  margin-top: 0;
  padding: 0.8rem 0 0.8rem 1.5rem;
}

.share-button__message:not(:empty):not(.hidden) ~ * {
  display: none;
}
.product__info-container .social__share_box {
  margin: 2rem 0 0;
}
.social__share_box.d-flex {
    flex-wrap: wrap;
}
span.social-links {
    display: flex;
    flex-wrap: wrap;
}