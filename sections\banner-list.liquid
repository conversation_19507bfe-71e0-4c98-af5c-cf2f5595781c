{{ 'component-grid.css' | asset_url | stylesheet_tag }}
{{ 'component-collage.css' | asset_url | stylesheet_tag }}
{{ 'banner-list.css' | asset_url | stylesheet_tag }}
{{ 'component-mobile-scrolling.css' | asset_url | stylesheet_tag }}
{{ 'section-title.css' | asset_url | stylesheet_tag }}

{% liquid
  assign productShowXl = section.settings.products_show_on_xl
  assign productShowSm = section.settings.products_show_on_sm

  assign content_position = 'align-items-start'
  if section.settings.content_position == 'middle'
    assign content_position = 'align-items-center'
  elsif section.settings.content_position == 'bottom'
    assign content_position = 'align-items-end'
  endif

  assign content_alignment = ''
  assign align_self = 'flex-start'
  if section.settings.content_alignment == 'center'
    assign content_alignment = 'justify-content-center text-center'
    assign align_self = 'center'
  elsif section.settings.content_alignment == 'right'
    assign content_alignment = 'text-right'
    assign align_self = 'flex-end'
  endif

  assign grid = 'grid'
  if section.settings.layout == 'mosaic'
    assign grid = 'collage'
  endif

  assign collage_column = 'collage_column_1'
  if section.blocks.size == 6
    assign collage_column = 'collage_column_6'
  elsif section.blocks.size == 5
    assign collage_column = 'collage_column_5'
  elsif section.blocks.size == 4
    assign collage_column = 'collage_column_4'
  elsif section.blocks.size == 3
    assign collage_column = 'collage_column_3'
  elsif section.blocks.size == 2
    assign collage_column = 'collage_column_2'
  elsif section.blocks.size > 6
    assign collage_column = 'collage_column_many'
  endif
  assign slider_enable = false
  if section.settings.layout == 'slider'
    assign slider_enable = true
  endif

  assign grid_item = ''
  if section.settings.layout == 'grid'
    assign grid_item = 'grid__item'
  elsif section.settings.layout == 'mosaic'
    assign grid_item = 'collage__item'
  elsif section.settings.layout == 'slider'
    assign grid_item = 'swiper-slide'
  endif

  assign container = ''
  assign page_offset = false
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
    unless section.settings.show_offset
      assign page_offset = true
    endunless
  else
    assign container = 'container-fluid px-0'
  endif
%}

{%- style -%}
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
     --padding-top: {{ section.settings.mobile_padding_top }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
      --padding-top: {{ section.settings.padding_top }}px;
    }
  }

  {%- if section.settings.image_ratio == 'adapt' and section.blocks.first.settings.image != blank -%}
   .banner__list--{{ section.id }} .banner__list--media {
     padding-bottom: {{ 1 | divided_by: section.blocks.first.settings.image.aspect_ratio | times: 100 }}%;
   }
  {% endif %}
  #section__{{ section.id }}.banner__list--slider.slider--controls--button{
     --slider-button-background: {{ section.settings.button_background }};
     --slider-button-color: {{ section.settings.button_text_color }};
  }
  :root{
    --grid-desktop-vertical-spacing: 20px;
    --grid-desktop-horizontal-spacing: 20px;
    --grid-mobile-vertical-spacing: 20px;
    --grid-mobile-horizontal-spacing: 20px;
  }
.banner__list--item-content-inner h3.h4 {
    font-weight: 600;
}
  
{%- endstyle -%}

{%- capture container_class -%}
{%- if slider_enable -%}
banner__list--slider hero__slider--activation swiper
{%- else -%}
{{ grid }} {{ collage_column }} {% if section.settings.layout == "grid" %} grid--{{ productShowXl }}-col-desktop grid--{{ productShowSm }}-col-tablet-down {% endif %}
{%- endif -%}
{%- endcapture -%}

<div
  class="banner__list section--top-space-{{ section.id }} banner__list--{{ section.id }} section-{{ section.id }}-padding slideShow"
  data-section-id="{{ section.id }}"
  data-section-type="slideShow"
  id="slideshow__{{ section.id }}"
  data-slide-autoplay="{{ section.settings.auto_rotate }}"
  data-autoplay-duration="{{ section.settings.change_slides_speed }}000"
  data-slide-loop="{{ section.settings.enable_loop }}"
  data-slideshow="30"
  data-slidesperview="{{ productShowXl }}"
  data-samlldesktopview="{{ productShowXl }}"
  data-show-tablet="{{ productShowSm }}"
  data-show-mobile="{{ productShowSm }}"
>
  <div class="{{ container }} {% if page_offset %}p-0{% endif %}">
    {% if section.settings.heading != blank or section.settings.subtitle != blank %}
      <div class="section-heading text-{{ section.settings.text_center }}  {% if section.settings.border_line %} mb-70 {% else %} mb-50{% endif %}">
        {% if section.settings.heading_position == 'bottom' and section.settings.subtitle != blank %}
          <span class="section-heading__sub_title">{{- section.settings.subtitle -}}</span>
        {% endif %}
        <h2 class="section-heading__title {% if section.settings.heading_position == "top" and section.settings.subtitle != blank and section.settings.border_line %}heading__space--bottom{% endif %} ">
          {% if section.settings.border_line %}<span>{% endif %}
          {{- section.settings.heading -}}
          {% if section.settings.border_line %}</span>{% endif %}
        </h2>
        {% if section.settings.heading_position == 'top' and section.settings.subtitle != blank %}
          <span class="section-heading__sub_title">{{- section.settings.subtitle -}}</span>
        {% endif %}
      </div>
    {% endif %}

    {% if section.settings.mobile_stack == false and section.settings.layout != 'slider' %}
      <div class="mobile--scoller">
      <div class="mobile--scoller-inner">
    {% endif %}

    <div class="{{ container_class }}">
      {%- if slider_enable -%}<div class="swiper-wrapper">{% endif %}
      {%- for block in section.blocks -%}
        {% case block.type %}
          {% when 'banner' %}
            {% comment %} Banner item {% endcomment %}
            {% liquid
              case block.settings.button_type
                when 'primary'
                  assign button_class = 'button button--primary'
                when 'secondary'
                  assign button_class = 'button button--secondary'
                when 'icon'
                  assign button_class = 'link with--icon'
                else
                  assign button_class = 'link'
              endcase

              assign highest_ratio = 0
              for block in section.blocks
                if block.settings.image.aspect_ratio > highest_ratio
                  assign highest_ratio = block.settings.image.aspect_ratio
                endif
              endfor

              assign content_position_single = ''
              if block.settings.content_position == 'middle'
                assign content_position_single = 'align-items-center'
              elsif block.settings.content_position == 'bottom'
                assign content_position_single = 'align-items-end'
              endif

              assign content_alignment_single = ''
              assign align_self_single = 'flex-start'
              if block.settings.content_alignment == 'center'
                assign content_alignment_single = 'text-center'
                assign align_self_single = 'center'
              elsif block.settings.content_alignment == 'right'
                assign content_alignment_single = 'text-right'
                assign align_self_single = 'flex-end'
              endif
            %}
            <div
              class="banner__list--item {{ grid_item }}"
              {{ block.shopify_attributes }}
            >
              {% if block.settings.enble_entire_link and block.settings.link %}
                <a
                  {% if block.settings.link %}
                    href="{{ block.settings.link }}"
                  {% else %}
                    role="link"
                  {% endif %}
                  class="d-block"
                >
              {%- endif %}
              <div
                class="banner__list--item-overlay {% if section.settings.round_corner %} rounded--image {% endif %}"
                style="--banner-list-media-overlay-opacity: {{ block.settings.image_overlay_opacity | divided_by: 100.0 }};"
              >
                <div class="banner__list--media media--{{ section.settings.image_ratio }} {% if block.settings.image != blank %}media--transparent media{% else %} placeholder {% endif %} {% if section.settings.round_corner %} rounded--image {% endif %}">
                  {%- if block.settings.image != blank -%}
                    {% if section.settings.layout == 'mosaic' %}
                      {%- capture sizes -%}(min-width: 992px) {{ settings.container_lg_width }}px, (min-width: 750px) 800px, calc(100vw - 30px){%- endcapture -%}
                    {% else %}
                      {%- capture sizes -%}(min-width: 992px) {% if section.blocks.size <= 2 %} {{ settings.container_lg_width }}px{% else %}{{ settings.container_lg_width | minus: 60 | divided_by: 3 }}px{% endif %}, (min-width: 750px) {% if section.blocks.size == 1 %}calc(100vw - 60px){% else %}550px{% endif %}, calc(100vw - 30px){%- endcapture -%}
                    {% endif %}
                    {{
                      block.settings.image
                      | image_url: width: 1420
                      | image_tag: loading: 'lazy', sizes: sizes, widths: '275, 550, 710, 1420'
                    }}
                  {%- else -%}
                    {{ 'collection-1' | placeholder_svg_tag: 'placeholder-svg-2' }}
                  {%- endif -%}
                </div>
                <div
                  class="banner__list--item-content d-flex {% if block.settings.override_general %}{{ content_alignment_single }} {{ content_position_single }}{% else %}{{ content_alignment }} {{ content_position }}{% endif %} {% if section.settings.show_content_below_image %} show__content--image-below {% endif %}"
                  style="
                     --content-align-self: {%- if block.settings.override_general -%} {{ align_self_single }}{%- else -%} {{ align_self }}{%- endif -%};
                    --color-foreground: {{ block.settings.foreground.red }}, {{ block.settings.foreground.blue }}, {{ block.settings.foreground.blue }};
                    --color-button: {{ block.settings.button_background.red }}, {{ block.settings.button_background.green }}, {{ block.settings.button_background.blue }};
                    --color-button-text: {{ block.settings.button_foreground.red }}, {{ block.settings.button_foreground.green }}, {{ block.settings.button_foreground.blue }};
                    --color-base-outline-button-labels: {{ block.settings.foreground.red }}, {{ block.settings.foreground.blue }}, {{ block.settings.foreground.blue }};
                    --color-link: {{ block.settings.foreground.red }}, {{ block.settings.foreground.blue }}, {{ block.settings.foreground.blue }};
                  "
                >
                  <div class="banner__list--item-content-inner {% if block.settings.override_general %}{{ content_alignment_single }} {% else %}{{ content_alignment }}{% endif %}">
                    {%- if block.settings.title != blank -%}
                      <h3 class="mb-0 {{ section.settings.card_title_size }}">{{ block.settings.title }}</h3>
                    {%- endif -%}
                    {%- if block.settings.text != blank -%}
                      <div class="rte banner--list-text">{{ block.settings.text }}</div>
                    {%- endif -%}
                    {%- if block.settings.link_label != blank -%}
                      {% unless block.settings.enble_entire_link %}
                        <a
                          class="{{ button_class }} {% unless block.settings.button_type == "icon" %} button--{{ block.settings.button_size }} {% endunless %}"
                          {% if block.settings.link == blank %}
                            role="link" aria-disabled="true"
                          {% else %}
                            href="{{ block.settings.link }}"
                          {% endif %}
                        >
                      {% endunless %}
                      {% if block.settings.enble_entire_link %}
                        <button
                          class="{{ button_class }} {% unless block.settings.button_type == "icon" %} button--{{ block.settings.button_size }} {% endunless %}"
                        >
                      {%- endif %}
                      {{- block.settings.link_label | escape }}
                      {% if block.settings.enble_entire_link %} </button> {% endif %}
                      {% unless block.settings.enble_entire_link %}
                        </a>
                      {% endunless %}
                    {%- endif -%}
                  </div>
                </div>
              </div>
              {% if block.settings.enble_entire_link and block.settings.link %} </a>{% endif %}
            </div>
            {% comment %} Banner item {% endcomment %}
        {% endcase %}
      {% endfor %}
      {%- if slider_enable -%}</div>{% endif %}

      {%- if slider_enable and section.settings.show_navigation -%}
        <div class="swiper__nav--btn swiper-button-prev  collection__list--slider-nav-btn">
          {% render 'icon-arrow-left' %}
        </div>
        <div class="swiper__nav--btn swiper-button-next collection__list--slider-nav-btn">
          {% render 'icon-arrow-right' %}
        </div>
      {%- endif -%}
    </div>

    {% if section.settings.mobile_stack == false and section.settings.layout != 'slider' %}
      </div>
      </div>
    {% endif %}

    {% liquid
      if section.settings.button_style == 'primary'
        assign button_class = ''
      else
        assign button_class = 'button--secondary'
      endif
    %}

    {%- if section.settings.show_view_all and section.settings.button_label != blank -%}
      <div class="row text-center mt-50">
        <div class="col">
          <a
            href="{{ section.settings.button_link }}"
            class="button button--{{ section.settings.button_size }} {{ button_class }}"
          >
            {{- section.settings.button_label -}}
          </a>
        </div>
      </div>
    {%- endif -%}
  </div>
</div>

{% schema %}
{
  "name": "Banner list",
  "tag": "section",
  "disabled_on": {
    "groups": ["header"]
  },
  "settings": [
    {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
    {
       "type": "checkbox",
       "id": "show_offset",
       "label": "Enable Offset (left & right)",
        "info": "It will work if you select the \"Full width\" of the page",
       "default": true
     },
    {
      "type": "header",
      "content": "Section header"
    },
     {
      "type": "inline_richtext",
      "id": "subtitle",
      "label": "Subheading"
    },
    {
      "type": "text",
      "id": "heading",
      "default": "Banner list",
      "label": "t:sections.featured-collection.settings.title.label"
    },
    {
      "type": "select",
      "id": "heading_position",
      "options": [
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        }
      ],
      "default": "bottom",
      "label": "Heading position"
    },
    {
      "type": "select",
      "id": "text_center",
      "options": [
        {
          "value": "right",
          "label": "Right"
        },
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        }
      ],
      "default": "center",
      "label": "Alignment"
    },
    {
       "type": "checkbox",
       "id": "border_line",
       "label": "Enable border line",
       "default": true
     },
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "layout",
        "options": [
          {
            "value": "grid",
            "label": "Grid"
          },
          {
            "value": "mosaic",
            "label": "Mosaic"
          },
          {
            "value": "slider",
            "label": "Slider"
          }
        ],
        "default": "grid",
        "label": "Desktop layout"
      },
     {
        "type": "select",
        "id": "products_show_on_xl",
        "label": "Number of columns on desktop",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          },
          {
            "value": "5",
            "label": "5"
          }
        ],
        "default": "3",
        "info": "This is only applicable to the Grid and Mosaic layouts."
      },
     {
        "type": "select",
        "id": "products_show_on_sm",
        "label": "Number of columns on mobile",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          }
        ],
        "default": "2",
       "info": "This is only applicable to the Grid and Slider layouts."
      },
      {
           "type": "checkbox",
           "id": "mobile_stack",
           "label": "Stack on mobile",
           "default": false,
           "info": "This is only applicable to the Grid and Mosaic layouts."
       },
      {
        "type": "header",
        "content": "Banner card"
      },
      {
        "type": "select",
        "id": "image_ratio",
        "options": [
          {
            "value": "adapt",
            "label": "Adapt to first image"
          },
          {
            "value": "square",
            "label": "t:sections.multicolumn.settings.image_ratio.options__3.label"
          },
          {
            "value": "portrait",
            "label": "t:sections.multicolumn.settings.image_ratio.options__2.label"
          },
          {
            "value": "landscape",
            "label": "Landscape"
          },
          {
            "value": "circle",
            "label": "t:sections.multicolumn.settings.image_ratio.options__4.label"
          }
        ],
        "default": "square",
        "label": "t:sections.multicolumn.settings.image_ratio.label"
      },
      {
             "type": "select",
             "id": "card_title_size",
             "options": [
               {
                 "value": "h6",
                 "label": "Extra small"
               },
               {
                 "value": "h5",
                 "label": "Small"
               },
               {
                 "value": "h4",
                 "label": "Medium"
               },
               {
                 "value": "h3",
                 "label": "Large"
               },
               {
                 "value": "h2",
                 "label": "Extra large"
               }
             ],
             "default": "h3",
             "label": "Heading size"
           },
      {
        "type": "select",
        "id": "content_position",
        "options": [
          {
            "value": "top",
            "label": "t:sections.image-with-text.settings.desktop_content_position.options__1.label"
          },
          {
            "value": "middle",
            "label": "t:sections.image-with-text.settings.desktop_content_position.options__2.label"
          },
          {
            "value": "bottom",
            "label": "t:sections.image-with-text.settings.desktop_content_position.options__3.label"
          }
        ],
        "default": "bottom",
        "label": "Content position",
        "info": "It works unless you enable the \"Show content below the image\""
      },
      {
        "type": "select",
        "id": "content_alignment",
        "options": [
          {
            "value": "left",
            "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__1.label"
          },
          {
            "value": "center",
            "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__2.label"
          },
          {
            "value": "right",
            "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__3.label"
          }
        ],
        "default": "left",
        "label": "Content alignment"
      },
      {
        "type": "checkbox",
        "id": "round_corner",
        "label": "Round corner",
        "default": true
      },
      {
           "type": "checkbox",
           "id": "show_content_below_image",
           "label": "Show content below the images",
           "default": false
       },
      {
      "type": "header",
        "content": "Button settings"
      },
      {
        "type": "checkbox",
        "id": "show_view_all",
        "default": false,
        "label": "Show button"
      },
      {
        "type": "text",
        "id": "button_label",
        "label": "Button label",
        "default": "Shop now"
      },
      {
        "type": "url",
        "id": "button_link",
        "label": "Button link"
      },
  	{
        "type": "select",
        "id": "button_style",
        "label": "Button type",
        "default": "primary",
        "options": [
          {
            "value": "secondary",
            "label": "Secondary"
          },
          {
            "value": "primary",
            "label": "Primary"
          }
        ]
      },
      {
        "type": "select",
        "id": "button_size",
        "label": "Button size",
        "default": "large",
        "options": [
          {
            "value": "large",
            "label": "Large"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
                  {
            "value": "small",
            "label": "Small"
          }
        ]
      },
      {
      "type": "header",
        "content": "Slider settings"
      },
  	{
        "type": "checkbox",
        "id": "auto_rotate",
        "label": "Auto-rotate slides",
        "default": false
      },
      {
        "type": "range",
        "id": "change_slides_speed",
        "min": 2,
        "max": 9,
        "step": 1,
        "unit": "s",
        "label": "Change slides every",
        "default": 3
      },
      {
        "type": "checkbox",
        "id": "enable_loop",
        "label": "Slider loop",
        "default": true
      },
      {
          "type": "checkbox",
          "id": "show_navigation",
          "label": "Show navigation",
          "default": true
      },
      {
        "type": "header",
        "content": "Section padding"
      },
      {
          "type": "paragraph",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 50
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 50
        },
		{
          "type": "paragraph",
          "content": "Mobile"
        },
		{
          "type": "range",
          "id": "mobile_padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 50
        },
        {
          "type": "range",
          "id": "mobile_padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 50
        }
  ],
   "blocks": [
    {
      "type": "banner",
      "name": "Banner",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.multicolumn.blocks.column.settings.image.label"
        },
        {
          "type": "text",
          "id": "title",
          "default": "Banner title",
          "label": "t:sections.multicolumn.blocks.column.settings.title.label"
        },
        {
          "type": "textarea",
          "id": "text",
          "default": "Pair text with an icon to focus on your store's features",
          "label": "t:sections.multicolumn.blocks.column.settings.text.label"
        },
        {
          "type": "text",
          "id": "link_label",
          "label": "t:sections.multicolumn.blocks.column.settings.link_label.label",
          "default": "Button label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.multicolumn.blocks.column.settings.link.label"
        },
        {
        "type": "checkbox",
          "id": "enble_entire_link",
          "label": "Enable the link on the entire image",
          "default": false
        },
		{
          "type": "select",
          "id": "button_type",
          "label": "Button type",
          "default": "primary",
          "options": [
            {
              "value": "primary",
              "label": "Primary button"
            },
            {
              "value": "secondary",
              "label": "Secondary button"
            },
            {
              "value": "icon",
              "label": "Link button"
            }
          ]
        },
		{
              "type": "select",
              "id": "button_size",
              "label": "Button size",
              "default": "small",
			  "info": "Works on the primary/secondary button",
              "options": [
                {
                  "value": "large",
                  "label": "Large"
                },
                {
                  "value": "medium",
                  "label": "Medium"
                },
				        {
                  "value": "small",
                  "label": "Small"
                }
              ]
            },
        {
             "type": "range",
             "id": "image_overlay_opacity",
             "min": 0,
             "max": 100,
             "step": 10,
             "unit": "%",
             "label": "Image overlay opacity",
             "default": 20
           },
        {
           "type": "checkbox",
           "id": "override_general",
           "label": "Override general settings",
           "default": false,
           "info": "If you enable it, the section general settings of \"BANNER CARD\" will be overridden"
        },
         {
        "type": "select",
        "id": "content_position",
        "options": [
          {
            "value": "top",
            "label": "t:sections.image-with-text.settings.desktop_content_position.options__1.label"
          },
          {
            "value": "middle",
            "label": "t:sections.image-with-text.settings.desktop_content_position.options__2.label"
          },
          {
            "value": "bottom",
            "label": "t:sections.image-with-text.settings.desktop_content_position.options__3.label"
          }
        ],
        "default": "bottom",
        "label": "Content position",
        "info": "It works unless you enable the \"Show content below the image\" from section general settings.\"General Settings Override\" needs to be enabled for this to work"
      },
      {
        "type": "select",
        "id": "content_alignment",
        "options": [
          {
            "value": "left",
            "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__1.label"
          },
          {
            "value": "center",
            "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__2.label"
          },
          {
            "value": "right",
            "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__3.label"
          }
        ],
        "default": "left",
        "label": "Content alignment",
        "info": "\"General Settings Override\" needs to be enabled for this to work"
      },
        {
          "type": "header",
          "content": "Colors"
      },
      {
        "type": "color",
        "id": "foreground",
        "default": "#fff",
        "label": "Text color"
      },
      {
        "type": "color",
        "id": "button_background",
        "default": "#fff",
        "label": "Primary button background color"
      },
      {
        "type": "color",
        "id": "button_foreground",
        "default": "#121212",
        "label": "Primary button foreground color"
      }
      ]
    }
  ],
  "presets": [
    {
      "name": "Banner list",
      "blocks": [
        {
          "type": "banner"
        },
        {
          "type": "banner"
        },
        {
          "type": "banner"
        }
      ]
    }
  ]
}
{% endschema %}
