{{ 'component-cart.css' | asset_url | stylesheet_tag }}
{{ 'component-totals.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-discounts.css' | asset_url | stylesheet_tag }}

{%- liquid
  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif
-%}
<div class="cart_subtotal {{ container }} mb-50" id="main-cart-footer" data-id="{{ section.id }}">
  <div class="row">
    <div class="col-12">
      <div class="{% if cart == empty %} is-empty{% endif %}">
        <div class="cart__footer--wrapper nrb_cart_f">
          <div class="cart__footer">
       {% if section.settings.shipping_calc %}
            <div class="shipping_calculator nrb" id="shipping_drawer">
              <div class="action_drawer_heading text-center">
                <h6>{{ section.settings.shipping_heading }}</h6>
              </div>
              <div class="action_drawer_body">
                <div class="select__field_form ">
                  <select
                    id="AddressCountry_Shipping"
                    name="address[country]"
                    autocomplete="country"
                  >
                    {{ all_country_option_tags }}
                  </select>
                  <svg aria-hidden="true" focusable="false" viewBox="0 0 10 6">
                    <use href="#icon-caret" />
                  </svg>
                </div>

                <div id="AddressProvinceContainerNewShiping" class="select__field_form " style="display: none">
                  <select
                    id="AddressProvince_shipping"
                    name="address[province]"
                    autocomplete="address-level1"
                  ></select>
                  <svg aria-hidden="true" focusable="false" viewBox="0 0 10 6">
                    <use href="#icon-caret" />
                  </svg>
                </div>

                <div class="input__field_form ">
                  <input
                    type="text"
                    class="input__field"
                    id="ShippingAddressZip"
                    autocapitalize="characters"
                    autocomplete="postal-code"
                    placeholder="{{ 'customer.addresses.zip' | t }}"
                  >
                </div>

                <div class="action_drawer_footer d-flex flex-direction-column">
                  <button class="button button--primary shipping_calc_save" data-action="shipping">
                    Shipping Caluculator
                  </button>
                </div>
              </div>

              <div class="shipping__calculator--wrapper text-center">
                <div class="shipping_rate_message mt-15 no-js-inline">
                  <p>
                    {{ 'general.shipping_calculator.address_first_label' | t }}
                    <span class="shipping_address_count"></span>
                    {{ 'general.shipping_calculator.address_second_label' | t }}
                  </p>
                </div>
                <div class="shipping_rate_package"></div>
              </div>
            </div>
          {% endif %}
            
            <div class="cart__information nrb_cart_f">
              {% if section.settings.discount_input %}
                <div class="coupon__box  mb-20">
                  <label for="coupon_code_cart" class="coupon__title"> <h6> {{ section.settings.discount_title }} </h6> </label>
                  <input
                    id="coupon_code_cart"
                    placeholder="Enter discount code here"
                    type="text"
                    name="discount"
                    form="cart"
                    value=""
                  >
                </div>
              {% endif %}

              {%- if section.settings.show_cart_note -%}
                <cart-note class="cart__note field nrb_cart_f">
                  <label for="Cart-note"><h6>{{ 'sections.cart.note' | t }}</h6> </label>
                  <textarea
                    class="text-area text-area--resize-vertical field__input"
                    form="cart"
                    name="note"
                    id="Cart-note"
                    placeholder="{{ 'sections.cart.note' | t }}"
                  >{{ cart.note }}</textarea>
                </cart-note>
              {%- endif -%}
            </div>

            <div class="cart__blocks js-contents">
              {% for block in section.blocks %}
                {%- case block.type -%}
                  {%- when '@app' -%}
                    {% render block %}
                  {%- when 'subtotal' -%}
                    <div {{ block.shopify_attributes }}>
                      <div class="totals">
                        <h3 class="totals__subtotal">{{ 'sections.cart.subtotal' | t }}</h3>
                        <p class="totals__subtotal-value">{{ cart.total_price | money_with_currency }}</p>
                      </div>

                      <div>
                        {%- if cart.cart_level_discount_applications.size > 0 -%}
                          <ul
                            class="discounts list-unstyled"
                            role="list"
                            aria-label="{{ 'customer.order.discount' | t }}"
                          >
                            {%- for discount in cart.cart_level_discount_applications -%}
                              <li class="discounts__discount discounts__discount--end">
                                {%- render 'icon-discount' -%}
                                {{ discount.title }}
                                (-{{ discount.total_allocated_amount | money }})
                              </li>
                            {%- endfor -%}
                          </ul>
                        {%- endif -%}
                      </div>

                      <small class="tax-note caption-large rte">
                        {%- if cart.taxes_included and shop.shipping_policy.body != blank -%}
                          {{
                            'sections.cart.taxes_included_and_shipping_policy_html'
                            | t: link: shop.shipping_policy.url
                          }}
                        {%- elsif cart.taxes_included -%}
                          {{ 'sections.cart.taxes_included_but_shipping_at_checkout' | t }}
                        {%- elsif shop.shipping_policy.body != blank -%}
                          {{
                            'sections.cart.taxes_and_shipping_policy_at_checkout_html'
                            | t: link: shop.shipping_policy.url
                          }}
                        {%- else -%}
                          {{ 'sections.cart.taxes_and_shipping_at_checkout' | t }}
                        {%- endif -%}
                      </small>
                    </div>
                  {%- else -%}
                    <div class="cart__ctas" {{ block.shopify_attributes }}>
                      <noscript>
                        <button type="submit" class="cart__update-button button button--secondary" form="cart">
                          {{ 'sections.cart.update' | t }}
                        </button>
                      </noscript>

                      <button
                        type="submit"
                        class="cart__checkout-button button"
                        name="checkout"
                        {% if cart == empty %}
                          disabled
                        {% endif %}
                        form="cart"
                      >
                        {{ 'sections.cart.checkout' | t }}
                      </button>
                    </div>

                    {%- if additional_checkout_buttons -%}
                      <div class="cart__dynamic-checkout-buttons additional-checkout-buttons">
                        {{ content_for_additional_checkout_buttons }}
                      </div>
                    {%- endif -%}
                {%- endcase -%}
              {% endfor %}

              <div id="cart-errors"></div>
            </div>
          </div>


        </div>
      </div>
    </div>
  </div>
</div>

{% javascript %}
  
  

  class CartNote extends HTMLElement {
    constructor() {
      super();

      this.addEventListener('change', debounce((event) => {
        const body = JSON.stringify({ note: event.target.value });
        fetch(`${routes.cart_update_url}`, {...fetchConfig(), ...{ body }});
      }, 300))
    }
  }
  customElements.define('cart-note', CartNote);

  {% if section.settings.discount_input %}
  let couponCodeField = document.getElementById('coupon_code_cart'),
	  couponCodeGet = localStorage.getItem("coupon");
  couponCodeField.value = couponCodeGet;
  {% endif %}}



{% endjavascript %}

<script>
  document.addEventListener('DOMContentLoaded', function() {
    function isIE() {
      const ua = window.navigator.userAgent;
      const msie = ua.indexOf('MSIE ');
      const trident = ua.indexOf('Trident/');

      return (msie > 0 || trident > 0);
    }

    if (!isIE()) return;
    const cartSubmitInput = document.createElement('input');
    cartSubmitInput.setAttribute('name', 'checkout');
    cartSubmitInput.setAttribute('type', 'hidden');
    document.querySelector('#cart').appendChild(cartSubmitInput);
    document.querySelector('#checkout').addEventListener('click', function(event) {
      document.querySelector('#cart').submit();
    });
  });
</script>
<script src="{{ 'shipping_calculator.js' | asset_url }}" defer></script>
{% schema %}
{
  "name": "t:sections.main-cart-footer.name",
  "class": "cart__footer-wrapper",
  "settings": [
	{
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
    {
      "type": "checkbox",
      "id": "show_cart_note",
      "default": false,
      "label": "t:sections.main-cart-footer.settings.show_cart_note.label"
    },
     {
      "type": "header",
      "content": "Discount input"
    },
    {
       "type": "checkbox",
       "id": "discount_input",
       "default": true,
       "label": "Enable"
     },
    {
       "type": "text",
       "id": "discount_title",
       "default": "Apply a discount code",
       "label": "Heading"
     },
    {
      "type": "header",
      "content": "Shpping calculator"
    },
    {
      "type": "checkbox",
      "id": "shipping_calc",
      "default": true,
      "label": "Enable"
    },
    {
       "type": "text",
       "id": "shipping_heading",
       "default": "Shipping calculator",
       "label": "Heading"
     },
    {
       "type": "text",
       "id": "button_text",
       "default": "Shipping calculator",
       "label": "Button label"
     }

  ],
  "blocks": [
    {
      "type": "subtotal",
      "name": "t:sections.main-cart-footer.blocks.subtotal.name",
      "limit": 1
    },
    {
      "type": "buttons",
      "name": "t:sections.main-cart-footer.blocks.buttons.name",
      "limit": 1
    },
    {
      "type": "@app"
    }
  ]
}
{% endschema %}
