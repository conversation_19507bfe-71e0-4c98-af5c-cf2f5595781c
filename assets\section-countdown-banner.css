.deals__banner--countdown {
  margin-bottom: 3rem;
  gap: 3rem;
}
.deals__banner--content__subtitle {
  margin-bottom: 12px;
  color: rgba(var(--color-button));
}

@media only screen and (min-width: 992px) {
  .deals__banner--content__subtitle {
    font-size: 2rem;
    line-height: 2.2rem;
    margin-bottom: 15px;
  }
  .deals__banner--countdown {
    gap: 4rem;
  }
}

.deals__banner--content__maintitle {
  font-size: 2rem;
  line-height: 2.7rem;
  margin-bottom: 15px;
}

@media only screen and (min-width: 750px) {
  .deals__banner--content__maintitle {
    font-size: 2.6rem;
    line-height: 3.4rem;
    margin-bottom: 14px;
  }
}

@media only screen and (min-width: 992px) {
  .deals__banner--content__maintitle {
    font-size: 2.8rem;
    line-height: 3.8rem;
    margin-bottom: 15px;
  }
}

@media only screen and (min-width: 1200px) {
  .deals__banner--content__maintitle {
    font-size: 3rem;
    line-height: 4rem;
  }
}

@media only screen and (min-width: 1366px) {
  .deals__banner--content__maintitle {
    font-size: 3.2rem;
    line-height: 4.2rem;
  }
}

@media only screen and (min-width: 1600px) {
  .deals__banner--content__maintitle {
    font-size: 3.6rem;
    line-height: 4.8rem;
  }
}

@media only screen and (min-width: 1200px) {
  .deals__banner--content__desc {
    font-size: 1.7rem;
  }
}
.deals__banner--countdown > .countdown__item {
  box-shadow: 0 0 5rem rgba(var(--color-foreground), 0.19);
  padding: 0.5rem 0.8rem;
  min-width: 6.5rem;
  text-align: center;
  border-radius: 0.5rem;
  position: relative;
}
.deals__banner--countdown > .countdown__item + .countdown__item:before {
  position: absolute;
  content: ":";
  left: -2rem;
  top: 50%;
  font-size: 3rem;
  font-weight: 700;
  -webkit-transform: translatey(-50%);
  transform: translatey(-50%);
  color: rgba(var(--color-foreground));
}
.deals__banner--countdown .countdown__number {
  font-weight: 700;
  font-size: 2rem;
  line-height: 3.5rem;
  color: rgba(var(--color-foreground));
}
.deals__banner--countdown .countdown__text {
  text-align: center;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 1.3rem;
  line-height: 2rem;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .deals__banner--countdown > .countdown__item {
    padding: 0.8rem 1rem;
    min-width: 8.5rem;
  }
  .deals__banner--countdown .countdown__number {
    font-size: 2.4rem;
  }
}
@media only screen and (min-width: 992px) {
  .deals__banner--countdown .countdown__text {
    font-size: 1.5rem;
    line-height: 2.2rem;
  }

  .deals__banner--countdown > .countdown__item + .countdown__item:before {
    left: -2.6rem;
  }
}

@media only screen and (min-width: 1200px) {
  .deals__banner--countdown .countdown__text {
    font-size: 1.6rem;
  }
  .deals__banner--countdown .countdown__number {
    font-size: 3.2rem;
    line-height: 4.8rem;
  }
  .deals__banner--countdown > .countdown__item {
    padding: 1rem 1.5rem;
    min-width: 10rem;
  }
}
.deals__banner--section {
  position: relative;
}
.countdown--banner__media {
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
}
.countdown--banner__content {
  position: relative;
  z-index: 8;
}

.countdown__item > span {
  display: block;
}
.countdown--banner__content--box {
  position: relative;
}
.timer__video--content {
  position: relative;
  z-index: 8;
  display: grid;
}
.timer__video--content.timer__video--padding {
  padding-left: 5rem;
  padding-right: 5rem;
}
.video--play-btn-icon {
  line-height: 1;
}
.video--play-btn {
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-animation: animate 2s linear infinite;
  animation: animate 2s linear infinite;
}
.video--play-btn-icon > svg {
  width: 3rem;
  display: inherit;
}
@media only screen and (min-width: 750px) {
  .timer__video--content {
    grid-template-columns: 70% 30%;
    align-items: center;
    gap: 1.5rem;
  }
  .video--play-btn-icon > svg {
    width: 4rem;
  }
  .video--play-btn {
    width: 7rem;
    height: 7rem;
  }
}
.deals__banner--content__desc > p {
  margin-bottom: 0;
}
.deals__banner--content__desc {
  margin-bottom: 2.5rem;
}
@media only screen and (min-width: 750px) and (max-width: 1199px) {
  .video--play-btn {
    margin: auto;
  }
}
@media only screen and (max-width: 749px) {
  .deals__banner--content {
    text-align: center;
    margin-top: 23px;
    order: 2;
  }
  .video--play-btn {
    margin: 0 auto 2rem;
  }
  .timer__video--content.timer__video--padding {
    padding-left: 3rem;
    padding-right: 3rem;
  }
  .deals__banner--countdown {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    flex-wrap: wrap;
  }
}
