<div class="collage-card">
  <a
    {% if collection.all_products_count > 0 %}
      href="{{ collection.url }}"
    {% endif %}
    class="{% if collection.featured_image != blank %} card--media{% else %}{% if section.settings.image_ratio != 'adapt' %} card--stretch{% endif %}{% endif %} collection__card"
  >
    {%- if collection.featured_image != blank or custom_image != blank -%}
      <div
        class="media ratio media--hover-effect overflow-hidden"
        style="--ratio-percent: {% if custom_image != blank %} {{ 1 | divided_by: custom_image.aspect_ratio | times: 100 }}%; {% else %}{{ 1 | divided_by: collection.featured_image.aspect_ratio | times: 100 }}%;{% endif %}"
      >
        {% if custom_image != blank %}
          {%- capture sizes -%} (min-width: 768px) {% if columns > 1 %}calc((100vw - 10rem) / 2){% else %}calc(100vw - 10rem){% endif %}, calc(100vw - 3rem) {%- endcapture -%}
          {% liquid
            assign height = custom_image.width | divided_by: custom_image.aspect_ratio | round
          %}
          {{
            custom_image
            | image_url: width: 1500
            | image_tag:
              loading: 'lazy',
              height: height,
              sizes: sizes,
              widths: '165, 360, 750, 1000, 1500, 1780, 2000, 3000, 3840'
          }}
        {% else %}
          <img
            srcset="
              {%- if collection.featured_image.width >= 165 -%}{{ collection.featured_image | img_url: '165x' }} 165w,{%- endif -%}
                 {%- if collection.featured_image.width >= 360 -%}{{ collection.featured_image | img_url: '360x' }} 360w,{%- endif -%}
                 {%- if collection.featured_image.width >= 535 -%}{{ collection.featured_image | img_url: '535x' }} 535w,{%- endif -%}
                 {%- if collection.featured_image.width >= 750 -%}{{ collection.featured_image | img_url: '750x' }} 750w,{%- endif -%}
                 {%- if collection.featured_image.width >= 1000 -%}{{ collection.featured_image | img_url: '1000x' }} 1000w,{%- endif -%}
                 {%- if collection.featured_image.width >= 1500 -%}{{ collection.featured_image | img_url: '1500x' }} 1500w,{%- endif -%}
              {%- if collection.featured_image.width >= 1780 -%}{{ collection.featured_image | img_url: '1780x' }} 1780w,{%- endif -%}
                 {%- if collection.featured_image.width >= 2000 -%}{{ collection.featured_image | img_url: '2000x' }} 2000w,{%- endif -%}
                 {%- if collection.featured_image.width >= 3000 -%}{{ collection.featured_image | img_url: '3000x' }} 3000w,{%- endif -%}
                 {%- if collection.featured_image.width >= 3840 -%}{{ collection.featured_image | img_url: '3840x' }} 3840w,{%- endif -%}
                 {{ collection.featured_image | img_url: 'master' }} {{ collection.featured_image.width }}w
            "
            src="{{ collection.featured_image | img_url: '1500x' }}"
            sizes="(min-width: 768px) {% if columns > 1 %}calc((100vw - 10rem) / 2){% else %}calc(100vw - 10rem){% endif %}, calc(100vw - 3rem)"
            alt="{{ collection.title | escape }}"
            height="{{ collection.featured_image.height | divided_by: collection.featured_image.aspect_ratio }}"
            width="{{ collection.featured_image.width  }}"
            loading="lazy"
          >
        {% endif %}
      </div>
    {%- else -%}
      <div
        class="ratio media"
        {% if media_aspect_ratio == 'adapt' %}
          style="--ratio-percent: 100%"
        {% endif %}
      >
        {{ placeholder_image | placeholder_svg_tag }}
      </div>
    {%- endif -%}

    {%- if collection != blank -%}
      <div class="collage--collection-card-text">
        <div class="collage__collection--card-inner collage__collection--padding-{{ block.settings.card_padding }}">
          {% if block.settings.heading != blank %}
            <div class="collage__collection--heading {{ block.settings.main_heading_size }}">
              {{ block.settings.heading }}
            </div>
          {% endif %}

          <div class="collection--title-wrapper">
            {% if title_design == 'button' %}
              {%- liquid
                case button_type
                  when 'primary'
                    assign button_class = 'button'
                  when 'secondary'
                    assign button_class = 'button button--secondary'
                endcase
              -%}
              <button
                class="{{ button_class }} button--{{ button_size }} text--with-btn-icon"
                {% if use_custom_color %}
                  style="--color-button-text: {{ button_foreground_color.red }}, {{ button_foreground_color.green }}, {{ button_foreground_color.blue }}; --color-button: {{ button_background_color.red }}, {{ button_background_color.green }}, {{ button_background_color.blue }};"
                {% endif %}
              >
                <span class="collection--title-text">{{- collection.title -}}</span>
                {%- render 'icon-arrow' -%}
              </button>
            {% else %}
              <h3 class="collection__title mb-0 text--with-btn-icon {{ title_size }}">
                <span class="collection--title-text">{{- collection.title -}}</span>
                {%- render 'icon-arrow' -%}
              </h3>
            {% endif %}
            {%- if product_count -%}
              <span class="collection__product_count"
                >({{ collection.products_count }}
                {{ 'sections.collection_list.items' | t }})</span
              >
            {%- endif -%}
          </div>
        </div>
      </div>
    {%- else -%}
      <div class="collage--collection-card-text ">
        <div class="collage__collection--card-inner collage__collection--padding-{{ block.settings.card_padding }}">
          {% if block.settings.heading != blank %}
            <div class="collage__collection--heading {{ block.settings.main_heading_size }}">
              {{ block.settings.heading }}
            </div>
          {% endif %}

          <div class="collection--title-wrapper">
            {% if title_design == 'button' %}
              {%- liquid
                case button_type
                  when 'primary'
                    assign button_class = 'button'
                  when 'secondary'
                    assign button_class = 'button button--secondary'
                endcase
              -%}
              <button
                class="{{ button_class }} button--{{ button_size }}"
                {% if use_custom_color %}
                  style="--color-button-text: {{ button_foreground_color.red }}, {{ button_foreground_color.green }}, {{ button_foreground_color.blue }}; --color-button: {{ button_background_color.red }}, {{ button_background_color.green }}, {{ button_background_color.blue }};"
                {% endif %}
              >
                {{ 'sections.collection_list.default_title' | t }}
              </button>
            {% else %}
              <h3 class="collection__title mb-0">{{ 'sections.collection_list.default_title' | t }}</h3>
            {% endif %}
          </div>
        </div>
      </div>
    {%- endif -%}
  </a>
</div>
