.video--hero__media--small {
  height: 28rem;
}
.video--hero__media--medium {
  height: 35rem;
}
.video--hero__media--large {
  height: 43.5rem;
}

@media screen and (min-width: 767px) {
  .video--hero__media--small {
    height: 35rem;
  }
  .video--hero__media--medium {
    height: 50rem;
  }
  .video--hero__media--large {
    height: 69.5rem;
  }
}
.video__hero--media-wrapper {
  position: relative;
}
.video__hero--banner-content {
    position: relative;
    z-index: 10;
    max-width: 50rem;
    text-align: center;
    display: grid;
    gap: 1rem;
    padding: 0 1.5rem;
}
.video__hero--banner-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.video__hero--banner-grid {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.video__hero--banner-content .button__wrapper {
  margin-top: 1.5rem;
}
.video__hero--media.media::before {
  background: #000;
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  z-index: 1;
}
.video__hero--banner__text {
  color: rgba(var(--color-foreground));
}
.video--hero__media--adapt.video-section__placeholder {
  padding-bottom: 56.25%;
}
.video__hero--media.media > video {
  object-fit: cover;
  object-position: center center;
}
h2.video__hero--banner__heading {
    font-weight: 700;
}
.video__hero--section .container .video__hero--media-wrapper .video__hero--media.media {
    border-radius: 1rem;
}















