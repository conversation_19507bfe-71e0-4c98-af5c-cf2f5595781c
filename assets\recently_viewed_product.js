// Global variables
let recentViewdProduct;
let recentViewedProductHandle;
let sectionSettings;

// Function to get section settings from data attributes
function getSectionSettings() {
  if (!recentViewdProduct) return null;

  return {
    card_style: recentViewdProduct.dataset.cardStyle || 'style_1',
    image_ratio: recentViewdProduct.dataset.imageRatio || 'adapt',
    show_secondary_image: recentViewdProduct.dataset.showSecondaryImage || 'false',
    color_swatches: recentViewdProduct.dataset.colorSwatches || 'true',
    show_badges: recentViewdProduct.dataset.showBadges || 'true',
    show_cart_button: recentViewdProduct.dataset.showCartButton || 'true',
    show_preorder_button: recentViewdProduct.dataset.showPreorderButton || 'true',
    show_quick_view: recentViewdProduct.dataset.showQuickView || 'true',
    show_compare: recentViewdProduct.dataset.showCompare || 'true',
    show_wishlist: recentViewdProduct.dataset.showWishlist || 'true',
    show_title: recentViewdProduct.dataset.showTitle || 'true',
    show_price: recentViewdProduct.dataset.showPrice || 'true',
    show_vendor: recentViewdProduct.dataset.showVendor || 'false',
    show_countdown: recentViewdProduct.dataset.showCountdown || 'false',
    show_rating: recentViewdProduct.dataset.showRating || 'false',
    inventory_status: recentViewdProduct.dataset.inventoryStatus || 'false',
    card_radius: recentViewdProduct.dataset.cardRadius || 'false',
    card_spacing: recentViewdProduct.dataset.cardSpacing || 'false',
    color_scheme: recentViewdProduct.dataset.colorScheme || 'background-1'
  };
}

// Build URL parameters string
function buildUrlParams(settings) {
  if (!settings) return '';

  const params = new URLSearchParams();
  Object.keys(settings).forEach(key => {
    params.append(key, settings[key]);
  });
  return params.toString();
}
var LOCAL_STORAGE_RECENTVIEWPRODUCT_KEY = "shopify-recent-view";
var LOCAL_STORAGE_DELIMITER = ",";
var BUTTON_ACTIVE_CLASS = "active";

var selectors = {
  grid: "[grid-recentViewProduct]",
};

document.addEventListener("DOMContentLoaded", function () {
  // Initialize DOM elements and settings
  recentViewdProduct = document.querySelector(".recently_viewed_proudct");
  if (!recentViewdProduct) {
    console.error("[recentViewPorduct] Recently viewed product container not found.");
    return;
  }

  recentViewedProductHandle = recentViewdProduct.dataset.productHandle;
  sectionSettings = getSectionSettings();

  console.log("Recently Viewed Settings:", sectionSettings); // Debug log
  console.log("Card Style from data attribute:", recentViewdProduct.dataset.cardStyle); // Debug log

  if (!recentViewedProductHandle) {
    console.error("[recentViewPorduct] Missing `data-product-handle` attribute. Failed to update the recentViewPorduct.");
    return;
  }

  updaterecentViewPorduct(recentViewedProductHandle);

  var Recentgrid = document.querySelector(selectors.grid);
  if (Recentgrid) {
    recentViewsetupGrid(Recentgrid);
  }
});

var recentViewsetupGrid = function (Recentgrid) {
  var recentViewPorduct = getrecentViewPorduct();
  if (recentViewPorduct.length > 1) {
    recentViewdProduct.classList.remove("no-js-inline");
  }

  // Get fresh section settings
  var currentSettings = getSectionSettings();
  console.log("Building URLs with settings:", currentSettings); // Debug log

  var requestsRecentViewed = recentViewPorduct
    .slice(0)
    .reverse()
    .map(function (handle) {
      if (recentViewedProductHandle !== handle) {
        var urlParams = buildUrlParams(currentSettings);
        var productTileTemplateUrl = "/products/" + handle + "?view=card&" + urlParams;
        console.log("Fetching URL:", productTileTemplateUrl); // Debug log
        return fetch(productTileTemplateUrl).then(function (res) {
          if(res.status == 200){
          	return res.text();
          }
        });
      }
    });

  Promise.all(requestsRecentViewed).then(function (responses) {
    var recentViewPorductProductCards = responses.join("");
    Recentgrid.innerHTML = recentViewPorductProductCards;
    console.log("Recently viewed products loaded successfully"); // Debug log
  }).catch(function(error) {
    console.error("Error loading recently viewed products:", error);
  });
};

var getrecentViewPorduct = function () {
  var recentViewPorduct =
    localStorage.getItem(LOCAL_STORAGE_RECENTVIEWPRODUCT_KEY) || false;
  if (recentViewPorduct)
    return recentViewPorduct.split(LOCAL_STORAGE_DELIMITER);
  return [];
};

var setrecentViewPorduct = function (array) {
  var recentViewPorduct = array.join(LOCAL_STORAGE_DELIMITER);
  if (array.length)
    localStorage.setItem(
      LOCAL_STORAGE_RECENTVIEWPRODUCT_KEY,
      recentViewPorduct
    );
  return recentViewPorduct;
};

var updaterecentViewPorduct = function (handle) {
  var recentViewPorduct = getrecentViewPorduct();
  var indexInrecentViewPorduct = recentViewPorduct.indexOf(handle);
  if (indexInrecentViewPorduct === -1) {
    recentViewPorduct.push(handle);
  } else {
    let currentViewdProduct = recentViewPorduct.splice(
      indexInrecentViewPorduct,
      1
    );
    recentViewPorduct.push(currentViewdProduct);
  }
  return setrecentViewPorduct(recentViewPorduct);
};

var recentViewPorductContains = function (handle) {
  var recentViewPorduct = getrecentViewPorduct();
  return recentViewPorduct.indexOf(handle) !== -1;
};

var resetrecentViewPorduct = function () {
  return setrecentViewPorduct([]);
};
