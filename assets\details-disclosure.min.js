class DetailsDisclosure extends HTMLElement{constructor(){super(),this.mainDetailsToggle=this.querySelector("details"),this.mainDetailsToggle.addEventListener("focusout",this.onFocusOut.bind(this))}onFocusOut(){setTimeout(()=>{this.contains(document.activeElement)||this.close()})}close(){this.mainDetailsToggle.removeAttribute("open"),this.mainDetailsToggle.querySelector("summary").setAttribute("aria-expanded",!1)}}customElements.define("details-disclosure",DetailsDisclosure);