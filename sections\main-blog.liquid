{% liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
%}
{{ 'section-featured-blog.css' | asset_url | stylesheet_tag }}

{% comment %}
  {{ 'component-article-card.css' | asset_url | stylesheet_tag }}
  {{ 'component-card.css' | asset_url | stylesheet_tag }}
  {{ 'section-main-blog.css' | asset_url | stylesheet_tag }}
{% endcomment %}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

{% if theme_rtl %}
  {{ 'section-featured-blog-rtl.css' | asset_url | stylesheet_tag }}
{% endif %}

{%- liquid
  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif

  assign productShowLg = section.settings.products_show_on_desktop
  assign productShowMd = section.settings.products_show_on_tablet
  assign productShowSm = section.settings.products_show_on_mobile
-%}

{%- paginate blog.articles by section.settings.article_to_show -%}
  <div class="main-blog {{ container }} section-{{ section.id }}-padding">
    <h1 class="title--primary">{{ blog.title | escape }}</h1>

    <div class="row row-cols-lg-{{ productShowLg }} row-cols-md-{{ productShowMd }} row-cols-{{ productShowSm }}">
      {%- for article in blog.articles -%}
        <div class="blog-articles__article col mb-30">
          {% render 'article-card',
            show_content: section.settings.show_content,
            blog: section.settings.blog,
            article: article,
            show_image: section.settings.show_image,
            show_date: section.settings.show_date,
            show_author: section.settings.show_author,
            show_comment: section.settings.show_comment,
            show_content: section.settings.show_content
          %}
        </div>
      {%- endfor -%}
    </div>

    {%- if paginate.pages > 1 -%}
      {%- render 'pagination', paginate: paginate -%}
    {%- endif -%}
  </div>
{%- endpaginate -%}

{% schema %}
{
  "name": "t:sections.main-blog.name",
  "tag": "section",
  "class": "spaced-section",
  "settings": [
      {
      "type": "header",
      "content": "Width"
    },
    	{
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
    {
      "type": "header",
      "content": "t:sections.main-blog.settings.header.content"
    },
    {
      "type": "range",
      "id": "article_to_show",
      "min": 2,
      "max": 50,
      "step": 1,
      "default": 6,
      "label": "Posts per page"
    },
    {
      "type": "checkbox",
      "id": "show_image",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_image.label",
      "info": "t:sections.featured-blog.settings.show_image.info"
    },
	{
      "type": "checkbox",
      "id": "show_content",
      "default": false,
      "label": "Show content"
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "default": false,
      "label": "t:sections.featured-blog.settings.show_date.label"
    },
    {
      "type": "checkbox",
      "id": "show_author",
      "default": false,
      "label": "t:sections.featured-blog.settings.show_author.label"
    },
	{
      "type": "checkbox",
      "id": "show_comment",
      "default": false,
      "label": "Show comment"
    },
     {
        "type": "header",
        "content": "Grid settings"
      },
      {
        "type": "range",
        "id": "products_show_on_desktop",
        "min": 2,
        "max": 8,
        "step": 1,
        "default": 3,
        "label": "Products per row on the desktop"
      },
      {
        "type": "range",
        "id": "products_show_on_tablet",
        "min": 2,
        "max": 4,
        "step": 1,
        "default": 3,
        "label": "Products per row on the tablet"
      },
      {
        "type": "range",
        "id": "products_show_on_mobile",
        "min": 1,
        "max": 3,
        "step": 1,
        "default": 2,
        "label": "Products per row on the mobile"
      },
	  {
      "type": "header",
      "content": "Section padding"
    },
    {
          "type": "paragraph",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        },
		{
          "type": "paragraph",
          "content": "Mobile"
        },
		{
          "type": "range",
          "id": "mobile_padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "mobile_padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        }
  ]
}
{% endschema %}
