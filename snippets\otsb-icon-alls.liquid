{%- case icon -%}
  {%- when 'icon-transition-arrow' -%}
    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 9 18" fill="none" style="fill:none;">
      <path d="M1.00122 1L8.00122 9L1.00122 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  {%- when 'icon-voice' -%}
    <svg width="100%" height="100%" viewBox="0 0 14 19" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path d="M12.3 9C12.3 12 9.76 14.1 7 14.1C4.24 14.1 1.7 12 1.7 9H0C0 12.41 2.72 15.23 6 15.72V19H8V15.72C11.28 15.23 14 12.41 14 9M5.8 2.9C5.8 2.24 6.34 1.7 7 1.7C7.66 1.7 8.2 2.24 8.2 2.9L8.19 9.1C8.19 9.76 7.66 10.3 7 10.3C6.34 10.3 5.8 9.76 5.8 9.1M7 12C7.79565 12 8.55871 11.6839 9.12132 11.1213C9.68393 10.5587 10 9.79565 10 9V3C10 2.20435 9.68393 1.44129 9.12132 0.87868C8.55871 0.316071 7.79565 0 7 0C6.20435 0 5.44129 0.316071 4.87868 0.87868C4.31607 1.44129 4 2.20435 4 3V9C4 9.79565 4.31607 10.5587 4.87868 11.1213C5.44129 11.6839 6.20435 12 7 12Z" fill="currentColor"></path>
    </svg>
  {%- when 'icon-tick-table' -%}
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="100%"
      height="100%"
      viewBox="0 0 24 25"
      fill="none"
      style="fill: none;"
    >
      <path d="M20 6.5L9 17.5L4 12.5" stroke="currentcolor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  {%- when 'icon-account' -%}
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="100%"
      height="100%"
      viewBox="0 0 16 18"
      fill="none"
      style="fill: none;"
    >
      <path d="M10.6768 1.76956C10.0249 1.282 9.21778 1.00012 8.33305 1.00012C6.19106 1.00012 4.45264 2.70658 4.45264 4.80919C4.45264 6.91179 6.19106 8.61825 8.33305 8.61825C10.475 8.61825 12.2135 6.91179 12.2135 4.80919" stroke="currentcolor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M15 16.9999C15 14.0516 12.0122 11.6672 8.33357 11.6672C4.6549 11.6672 1.66699 14.0516 1.66699 16.9999" stroke="currentcolor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  {%- when 'icon-double-arrow' -%}
    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 8 9" fill="none" style="fill:none;">
      <g>
      <path d="M4.5 0.642578L7.5 4.49972L4.5 8.35686" stroke="currentcolor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M0.5 0.642578L3.5 4.49972L0.5 8.35686" stroke="currentcolor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
      </g>
    </svg>
  {%- when 'icon-arrow' -%}
    <svg
      class="{% if enable_email_footer or enable_blog_detail %} h-[16px] w-[16px)]{% else %} w-full h-full{% endif %}"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      style="{% if w_min_fill %}min-inline-size: -webkit-fill-available;{% endif %}"
    >
      <path d="M597.34016 170.65984q18.00192 0 30.33088 12.32896l298.65984 298.65984q12.32896 12.32896 12.32896 30.33088t-12.32896 30.33088l-298.65984 298.65984q-12.32896 12.32896-30.33088 12.32896-18.3296 0-30.49472-12.16512t-12.16512-30.49472q0-18.00192 12.32896-30.33088l225.9968-225.66912-665.00608 0q-17.67424 0-30.16704-12.4928t-12.4928-30.16704 12.4928-30.16704 30.16704-12.4928l665.00608 0-225.9968-225.66912q-12.32896-12.32896-12.32896-30.33088 0-18.3296 12.16512-30.49472t30.49472-12.16512z"  />
    </svg>
  {%- when 'icon-caret' -%}
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 9 7"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style="fill: none;"
    >
      <path d="M0.75 1.625L4.5 5.375L8.25 1.625" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  {%- when 'icon-check' -%}
    <svg width="100%" height="100%" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_1602_2141)">
      <path fill="none" d="M20.3232 6.71851L9.32324 17.7185L4.32324 12.7185" stroke="currentcolor" stroke-width="5" stroke-linecap="square" stroke-linejoin="round"/>
      </g>
    </svg>
  {%- when 'icon-close' -%}
    <svg width="100%" height="100%" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M0.757812 8.75781L4.75782 4.75783M4.75782 4.75783L8.7578 0.757812M4.75782 4.75783L0.757812 0.757812M4.75782 4.75783L8.7578 8.75781" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  {%- when 'icon-comment' -%}
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60" fill="none">
      <path fill="currentColor" d="M7.062,27.716a1.173,1.173,0,0,1-1.13-1.485c2.612-9.438,12.71-15.99,23.006-14.912a1.171,1.171,0,1,1-.244,2.33c-9.191-.96-18.188,4.842-20.5,13.208A1.172,1.172,0,0,1,7.062,27.716Z"/>
      <path fill="currentColor" d="M11.834,55.553a9.34,9.34,0,0,1-1.12-.058l-2.209-.322,1.52-1.635c4.121-4.432,4.379-7.295,4.3-8.316-.07-.029-.14-.06-.208-.092C5.335,41.082.091,34.1.091,26.462.091,14.322,13.509,4.447,30,4.447s29.908,9.875,29.908,22.015S46.492,48.476,30,48.476a40.327,40.327,0,0,1-6.538-.537A12.339,12.339,0,0,1,11.834,55.553ZM30,6.79c-15.2,0-27.566,8.825-27.566,19.672,0,6.7,4.734,12.886,12.664,16.54.14.064.284.124.429.184l.759.324.17.484c.145.353,1.266,3.617-3,9.13,2.478-.349,6.239-1.763,8.185-6.909l.348-.919.969.173A37.969,37.969,0,0,0,30,46.133c15.2,0,27.564-8.824,27.564-19.671S45.2,6.79,30,6.79Z"/>
    </svg>
  {%- when 'icon-copy' -%}
    <svg height="100%" width="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill="none" d="M20 9H11C9.89543 9 9 9.89543 9 11V20C9 21.1046 9.89543 22 11 22H20C21.1046 22 22 21.1046 22 20V11C22 9.89543 21.1046 9 20 9Z" stroke="currentcolor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path fill="none" d="M5 15H4C3.46957 15 2.96086 14.7893 2.58579 14.4142C2.21071 14.0391 2 13.5304 2 13V4C2 3.46957 2.21071 2.96086 2.58579 2.58579C2.96086 2.21071 3.46957 2 4 2H13C13.5304 2 14.0391 2.21071 14.4142 2.58579C14.7893 2.96086 15 3.46957 15 4V5" stroke="currentcolor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  {%- when 'icon-dark' -%}
    <svg
      class="svg-icon"
      style="vertical-align: middle;fill: currentColor;overflow: hidden;"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M557.250088 959.028243c-247.490697 0-448.826192-199.306278-448.826192-446.783673 0-244.284679 198.73118-446.636317 443.002556-446.636317 0.119727 0 0.226151 0 0.346901 0 10.354835 0 19.840884 3.807722 24.477484 13.094226 4.688789 9.379625 3.593851 19.61371-2.805906 27.92398-53.604872 69.477374-81.928964 152.101163-81.928964 239.895719 0 217.308281 176.792519 393.82553 394.1008 393.82553l5.051039-0.239454c9.927093-0.134053 20.175505 5.638417 24.864294 15.043624 4.704139 9.379625 3.621481 20.603247-2.804882 28.91454C826.947554 895.269104 697.373453 959.028243 557.250088 959.028243zM497.340627 120.789017c-188.636265 29.113061-334.191338 193.506179-334.191338 389.410987 0 217.294978 176.792519 394.1008 394.1008 394.1008 104.550171 0 202.412013-40.456411 275.696086-112.540146-222.772736-26.1598-396.157645-216.07929-396.157645-445.747063C436.787506 265.754666 457.578018 188.916651 497.340627 120.789017z"  /><path d="M577.719282 865.501126c-179.75806 0-332.640006-128.359436-363.490641-305.211307-1.296529-7.441483 3.687996-14.536065 11.129478-15.833617 7.54893-1.2689 14.537088 3.687996 15.832594 11.130502 28.55229 163.705456 170.09191 282.536888 336.52857 282.536888 7.563256 0 13.682627 6.119371 13.682627 13.681604C591.40191 859.381755 585.281515 865.501126 577.719282 865.501126z"  /><path d="M222.886835 530.788925c-7.255241 0-13.307074-6.734378-13.654998-14.055111-0.306992-6.333242-0.467651-13.267165-0.467651-19.694551 0-7.562233 6.118348-13.935384 13.681604-13.935384s13.681604 5.998621 13.681604 13.560854c0 5.998621 0.160659 12.920264 0.441045 18.839067 0.361227 7.54893-5.464455 15.285125-13.013385 15.285125C223.326856 530.788925 223.112985 530.788925 222.886835 530.788925z"  /><path d="M642.680806 310.140573c4.300956 12.746302-2.566452 26.587542-15.311731 30.890544-12.747325 4.288676-26.588565-2.565429-30.891568-15.325034-4.275373-12.746302 2.565429-26.575262 15.311731-30.877241C624.56317 290.525839 638.377804 297.394271 642.680806 310.140573z"  /><path d="M801.146934 193.09788c4.275373 12.746302-2.565429 26.588565-15.337313 30.890544-12.747325 4.275373-26.561959-2.565429-30.864962-15.325034-4.300956-12.746302 2.566452-26.574239 15.311731-30.877241C783.003715 173.48417 796.844955 180.351578 801.146934 193.09788z"  /><path d="M748.131487 411.503145c4.301979 12.759605-2.565429 26.588565-15.311731 30.890544-12.746302 4.288676-26.587542-2.565429-30.891568-15.311731-4.300956-12.746302 2.566452-26.587542 15.311731-30.890544C729.987245 391.889435 743.829508 398.756843 748.131487 411.503145z"  />
    </svg>
  {%- when 'icon-discount' -%}
    <svg
      fill="currentColor"
      viewBox="0 0 14 14"
      role="img"
      focusable="false"
      aria-hidden="true"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="m 3.83096,4.235405 q 0,-0.33492 -0.23696,-0.57188 -0.23697,-0.23697 -0.57188,-0.23697 -0.33492,0 -0.57188,0.23697 -0.23697,0.23696 -0.23697,0.57188 0,0.33491 0.23697,0.57188 0.23696,0.23696 0.57188,0.23696 0.33491,0 0.57188,-0.23696 0.23696,-0.23697 0.23696,-0.57188 z m 6.7425,3.63981 q 0,0.33491 -0.23381,0.56872 l -3.10268,3.109 q -0.24645,0.23381 -0.57504,0.23381 -0.33492,0 -0.56872,-0.23381 L 1.575039,7.028455 Q 1.334913,6.794645 1.167457,6.390225 1,5.985795 1,5.650885 v -2.62875 q 0,-0.3286 0.240126,-0.56872 0.240127,-0.24013 0.568721,-0.24013 H 4.4376 q 0.33491,0 0.73934,0.16746 0.40442,0.16745 0.64454,0.40758 l 4.51817,4.51185 q 0.23381,0.24644 0.23381,0.57504 z m 2.42654,0 q 0,0.33491 -0.23381,0.56872 l -3.10268,3.109 q -0.24645,0.23381 -0.57504,0.23381 -0.22749,0 -0.37283,-0.0885 -0.14534,-0.0885 -0.33491,-0.28436 l 2.96998,-2.96998 q 0.23381,-0.23381 0.23381,-0.56872 0,-0.3286 -0.23381,-0.57504 L 6.83254,2.788295 Q 6.59242,2.548165 6.18799,2.380715 5.78357,2.213255 5.44866,2.213255 h 1.41548 q 0.33491,0 0.73934,0.16746 0.40442,0.16745 0.64455,0.40758 l 4.51816,4.51185 Q 13,7.546615 13,7.875215 z"/>
    </svg>
  {%- when 'icon-error' -%}
    <svg
      class="text-[rgba(var(--color-error,225,43,40))]{% if enable_blog_detail or enable_customer_page %} w-3.5 h-3.5 inline{% endif %}"
      version="1.1"
      id="Capa_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      viewBox="0 0 800 800"
      enable-background="new 0 0 800 800"
      xml:space="preserve"
    >
      <g>
      <path fill="currentColor" d="M400,0C179.1,0,0,179.1,0,400c0,220.9,179.1,400,400,400c220.9,0,400-179.1,400-400
        C800,179.1,620.9,0,400,0z M400,655.333c-31.267,0-52.667-24.1-52.667-55.333c0-32.134,22.3-55.333,52.667-55.333
        c32.1,0,52.667,23.199,52.667,55.333C452.667,631.267,432.1,655.333,400,655.333z M420.733,444.033
        c-7.967,27.167-33.066,27.634-41.434,0c-9.633-31.866-43.866-152.833-43.866-231.4c0-103.667,129.699-104.167,129.699,0
        C465.1,291.667,429.033,415.767,420.733,444.033z"/>
      </g>
    </svg>
  {%- when 'icon-filter' -%}
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="100%"
      height="100%"
      viewBox="0 0 20 16"
      fill="none"
      style="fill: none;"
    >
      <path d="M0 3H4V5H0V3Z" fill="currentColor"/>
      <path d="M7 7C8.65685 7 10 5.65685 10 4C10 2.34315 8.65685 1 7 1C5.34315 1 4 2.34315 4 4C4 5.65685 5.34315 7 7 7Z" stroke="currentColor" stroke-width="1.5"/>
      <path d="M12 3H20V5H12V3Z" fill="currentColor"/>
      <path d="M0 11H8V13H0V11Z" fill="currentColor"/>
      <path d="M13 15C14.6569 15 16 13.6569 16 12C16 10.3431 14.6569 9 13 9C11.3431 9 10 10.3431 10 12C10 13.6569 11.3431 15 13 15Z" stroke="currentColor" stroke-width="1.5"/>
      <path d="M16 11H20V13H16V11Z" fill="currentColor"/>
    </svg>
  {%- when 'icon-hamburger' -%}
    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 400 280" enable-background="new 0 0 400 280">
      <g>
        <g>
          <path fill-rule="evenodd" clip-rule="evenodd" fill="currentColor" d="M-0.002-0.039v40h400.001v-40H-0.002z M-0.002,159.969h400.001
            v-40.006H-0.002V159.969z M-0.002,279.965h280.001v-40.012H-0.002V279.965z"/>
        </g>
      </g>
    </svg>
  {%- when 'icon-light' -%}
    <svg
      class="svg-icon"
      style="fill: currentColor;overflow: hidden;"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M512 704a192 192 0 1 0 0-384 192 192 0 0 0 0 384z m0 64a256 256 0 1 1 0-512 256 256 0 0 1 0 512zM32 480h128a32 32 0 1 1 0 64H32a32 32 0 1 1 0-64z m832 0h128a32 32 0 1 1 0 64H864a32 32 0 1 1 0-64zM480 992V864a32 32 0 1 1 64 0v128a32 32 0 1 1-64 0z m0-832V32a32 32 0 1 1 64 0v128a32 32 0 1 1-64 0z m348.8 714.027l-90.539-90.496a32 32 0 1 1 45.27-45.227l90.496 90.453a32 32 0 0 1-45.227 45.27zM240.47 285.739L149.972 195.2a32 32 0 1 1 45.227-45.227l90.539 90.496a32 32 0 1 1-45.27 45.227z m633.6-90.539l-90.54 90.539a32 32 0 1 1-45.226-45.27l90.453-90.496a32 32 0 0 1 45.227 45.227zM285.652 783.53L195.2 874.028a32 32 0 0 1-45.27-45.227l90.497-90.539a32 32 0 1 1 45.226 45.27z"  />
    </svg>
  {%- when 'icon-loading' -%}
    <svg
      version="1.0"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 512.000000 512.000000"
      preserveAspectRatio="xMidYMid meet"
    >
      <g transform="translate(0.000000,512.000000) scale(0.100000,-0.100000)"
        fill="currentcolor" stroke="none">
        <path d="M2474 4676 c-43 -19 -101 -81 -115 -123 -18 -56 -7 -145 24 -190 52 -75 79 -85 262 -99 90 -7 207 -22 260 -33 436 -92 824 -352 1070 -721 521  -781 321 -1824 -453 -2358 -501 -345 -1172 -393 -1715 -121 -700 350 -1075  1118 -917 1876 37 180 86 305 209 536 51 96 54 147 15 226 -37 73 -86 104 -174 109 -105 6 -151 -24 -224 -145 -137 -230 -241 -543 -276 -833 -13 -110  -13 -355 1 -478 52 -475 268 -922 614 -1267 351 -352 787 -560 1284 -615 171  -19 442 -8 615 24 423 80 802 282 1111 591 407 407 625 931 625 1505 0 574  -218 1098 -625 1505 -296 296 -654 493 -1055 579 -210 45 -470 61 -536 32z"/>
      </g>
    </svg>
  {%- when 'icon-minus' -%}
    <svg
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      focusable="false"
      role="presentation"
      class="{%- if class %} {{ class }}{% endif -%}"
      fill="none"
      viewBox="0 0 10 2"
    >
      <path fill-rule="evenodd" clip-rule="evenodd" d="M.5 1C.******* 1 .5h8a.5.5 0 110 1H1A.5.5 0 01.5 1z" fill="currentColor">
    </svg>
  {%- when 'icon-padlock' -%}
    <svg
      aria-hidden="true"
      focusable="false"
      class="icon icon-padlock"
      role="presentation"
      viewBox="0 0 16 21"
      fill="none"
    >
      <path fill-rule="evenodd" d="M5.03 1.79A3.73 3.73 0 018 .5c1.28 0 2.28.48 2.97 ********* 1 1.87 1.03 3V7.5h3c.28 0 .5.22.5.5v12a.5.5 0 01-.5.5H1a.5.5 0 01-.5-.5V8c0-.28.22-.5.5-.5h3V4.8c0-1.1.36-2.15 1.03-3.01zM11 6.4v1.09H5V4.82c.03-.99.31-1.82.8-2.4A2.75 2.75 0 018 1.49c1.01 0 1.73.37 *********.58.77 1.41.8 2.4V6.4zM1.5 8.49v11h13v-11h-13zm6.51 2.5a1.5 1.5 0 00-.7 2.82v2.5a.68.68 0 001.36 0v-2.47A1.5 1.5 0 008 11l.01-.01z" clip-rule="evenodd" fill="currentColor" />
    </svg>
  {%- when 'icon-plus' -%}
    <svg
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      focusable="false"
      role="presentation"
      class="{%- if class %} {{ class }}{% endif -%}"
      fill="none"
      viewBox="0 0 10 10"
    >
      <path fill-rule="evenodd" clip-rule="evenodd" d="M1 4.51a.5.5 0 000 1h3.5l.01 3.5a.5.5 0 001-.01V5.5l3.5-.01a.5.5 0 00-.01-1H5.5L5.49.99a.5.5 0 00-1 .01v3.5l-3.5.01H1z" fill="currentColor">
    </svg>
  {%- when 'icon-remove' -%}
    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" style="fill: none;">
      <path d="M6 7V18C6 19.1046 6.89543 20 8 20H16C17.1046 20 18 19.1046 18 18V7M6 7H5M6 7H8M18 7H19M18 7H16M10 11V16M14 11V16M8 7V5C8 3.89543 8.89543 3 10 3H14C15.1046 3 16 3.89543 16 5V7M8 7H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  {%- when 'icon-search' -%}
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="100%"
      height="100%"
      viewBox="0 0 19 18"
      fill="none"
      style="fill: none;"
    >
      <path d="M9.2665 16.2001C13.4639 16.2001 16.8665 12.7975 16.8665 8.60009C16.8665 4.40272 13.4639 1.00009 9.2665 1.00009C5.06914 1.00009 1.6665 4.40272 1.6665 8.60009C1.6665 12.7975 5.06914 16.2001 9.2665 16.2001Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M17.6669 16.9999L16.0669 15.3999" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  {%- when 'icon-search-quickview' -%}
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="100%"
      height="100%"
      viewBox="0 0 16 16"
      fill="none"
      style="fill:none;"
    >
      <path d="M7.67451 13.8502C11.0849 13.8502 13.8496 11.0855 13.8496 7.67508C13.8496 4.26468 11.0849 1.5 7.67451 1.5C4.2641 1.5 1.49942 4.26468 1.49942 7.67508C1.49942 11.0855 4.2641 13.8502 7.67451 13.8502Z" stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M14.5003 14.5L13.2003 13.2" stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  {%- when 'icon-sizechart' -%}
    <svg class="w-full h-full text-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 122.88 89.03">
      <path d="M118.29,48.22c-29.73,2.45-58.61,3.68-80.22.88C22.76,47.12,11,43.09,4.93,36V47.86c0,1.07,0,2.82-.06,4.54-.18,9.75-.33,18,8.57,24.74V72.49a2.3,2.3,0,1,1,4.59,0V80a37.46,37.46,0,0,0,7,2.56c.59.15,1.18.3,1.79.43V64a2.3,2.3,0,1,1,4.59,0v19.8a79.79,79.79,0,0,0,8.23.66V72.49a2.3,2.3,0,1,1,4.59,0v12h8.23V64a2.3,2.3,0,1,1,4.59,0V84.44h8.24V71.26a2.3,2.3,0,1,1,4.59,0V84.44h8.23V64a2.3,2.3,0,1,1,4.59,0V84.44H91V71.86a2.3,2.3,0,0,1,4.59,0V84.44h8.24s0-.09,0-.13V64a2.3,2.3,0,1,1,4.59,0V84.44h9.9V48.22ZM14.78,83.43a2.49,2.49,0,0,1-.28-.15,31.36,31.36,0,0,1-3.73-2.41C-.07,72.67.1,63.38.3,52.33c0-1.22,0-2.47,0-4.47V27A13.91,13.91,0,0,1,0,24C0,17.12,5.24,11,13.71,6.66,21.75,2.55,32.79,0,44.93,0S68.68,2.58,77.1,6.75,91,16.75,91.38,23.32c0,0,0-.08,0-.13.39,3.69,0,14.74,0,22.27,9.4-.48,19.12-1.18,28.91-2h.28a2.3,2.3,0,0,1,2.29,2.29v41a2.3,2.3,0,0,1-2.29,2.3H41.22a73.16,73.16,0,0,1-17.28-2.08,41.16,41.16,0,0,1-9.16-3.51ZM45.56,12.67c12.23,0,23.19,5.23,23.19,12s-11,11.73-23.19,11.73-22.15-5-22.15-11.73,9.92-12,22.15-12Zm29.09-1.9C66.94,7,56.31,4.6,44.93,4.6s-21.4,2.31-28.71,6.05C9.36,14.17,5.11,18.89,5.11,24s4.25,9.84,11.11,13.35c7.31,3.74,17.45,6,28.71,6s22-2.36,29.72-6.17C81.77,33.71,86.29,29,86.29,24s-4.52-9.71-11.64-13.23Z"/>
    </svg>
  {%- when 'icon-success' -%}
    <svg
      class="{% if enable_blog_detail %} w-3.5 h-3.5 inline{% endif %}"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 64 64"
      enable-background="new 0 0 64 64"
    >
      <path d="M32,2C15.431,2,2,15.432,2,32c0,16.568,13.432,30,30,30c16.568,0,30-13.432,30-30C62,15.432,48.568,2,32,2z M25.025,50 l-0.02-0.02L24.988,50L11,35.6l7.029-7.164l6.977,7.184l21-21.619L53,21.199L25.025,50z" fill="currentColor"/>
    </svg>
  {%- when 'icon-tick' -%}
    <svg
      version="1.0"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 256.000000 256.000000"
      preserveAspectRatio="xMidYMid meet"
    >
      <g transform="translate(0.000000,256.000000) scale(0.100000,-0.100000)" fill="#45c427" stroke="none">
        <path d="M2095 1939 c-20 -5 -194 -174 -527 -506 -273 -273 -501 -493 -505 -489 -91 94 -469 453 -481 458 -32 13 -87 8 -118 -11 -32 -20 -64 -74 -64 -110 0 -50 43 -101 317 -373 l289 -288 59 0 60 0 562 562 c586 586 592 592 576 657 -10 45 -51 89 -91 101 -42 11 -37 11 -77 -1z"/>
      </g>
    </svg>
  {%- when 'icon-unavailable' -%}
    <svg
      version="1.0"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 256.000000 256.000000"
      preserveAspectRatio="xMidYMid meet"
    >
      <g transform="translate(0.000000,256.000000) scale(0.100000,-0.100000)" fill="#DE3618" stroke="none">
        <path d="M610 2023 c-28 -10 -69 -56 -75 -83 -3 -14 -1 -40 5 -56 6 -16 140 -158 298 -316 l286 -288 -291 -292 c-223 -225 -292 -300 -298 -325 -15 -71 57 -143 128 -128 25 6 100 75 325 298 l292 291 288 -286 c158 -158 300 -292 316 -298 39 -15 78 -5 109 28 32 34 41 70 27 107 -6 17 -138 157 -298 318 l-287 287 287 288 c158 158 292 300 298 316 15 39 5 78 -28 109 -34 32 -70 41 -107 27 -17 -6 -157 -138 -317 -298 l-288 -287 -287 287 c-159 158 -301 292 -316 297 -30 12 -44 13 -67 4z"/>
      </g>
    </svg>
  {%- when 'icon-view' -%}
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 122.88 68.18" class="w-full h-full">
      <defs>
          <style>.cls-1{fill-rule:evenodd;}</style>
      </defs>
      <path class="cls-1"
          d="M61.44,13.81a20.31,20.31,0,1,1-14.34,6,20.24,20.24,0,0,1,14.34-6ZM1.05,31.31A106.72,106.72,0,0,1,11.37,20.43C25.74,7.35,42.08.36,59,0s34.09,5.92,50.35,19.32a121.91,121.91,0,0,1,12.54,12,4,4,0,0,1,.25,5,79.88,79.88,0,0,1-15.38,16.41A69.53,69.53,0,0,1,63.43,68.18,76,76,0,0,1,19.17,53.82,89.35,89.35,0,0,1,.86,36.44a3.94,3.94,0,0,1,.19-5.13Zm15.63-5A99.4,99.4,0,0,0,9.09,34,80.86,80.86,0,0,0,23.71,47.37,68.26,68.26,0,0,0,63.4,60.3a61.69,61.69,0,0,0,38.41-13.72,70.84,70.84,0,0,0,12-12.3,110.45,110.45,0,0,0-9.5-8.86C89.56,13.26,74.08,7.58,59.11,7.89S29.63,14.48,16.68,26.27Zm39.69-7.79a7.87,7.87,0,1,1-7.87,7.87,7.86,7.86,0,0,1,7.87-7.87Z" />
    </svg>
  {%- when 'icon-calendar' -%}
    <svg
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      x="0px"
      y="0px"
      viewBox="0 0 117 123"
      style="enable-background:new 0 0 117 123;"
      xml:space="preserve"
    >
      <g>
        <path d="M107.4,112.3H7.4V24.8h12.5v74.9h74.9V24.8h12.5V112.3z M26.2,62.2V49.7h12.5v12.4H26.2z M26.2,87.2V74.7h12.5v12.5H26.2z
            M32.4,30.9V18.4c0-1.7,0.6-3.2,1.9-4.4c1.3-1.2,2.8-1.8,4.5-1.8c1.7,0,3.2,0.6,4.5,1.8c1.3,1.2,1.8,2.7,1.7,4.4v12.5
          c0,1.8-0.6,3.3-1.8,4.5c-1.2,1.2-2.7,1.8-4.4,1.8c-1.8,0-3.2-0.6-4.4-1.8C33.1,34.2,32.4,32.7,32.4,30.9z M51.2,62.2V49.7h12.5
          v12.4H51.2z M51.2,87.2V74.7h12.5v12.5H51.2z M69.9,30.9V18.4c0-1.7,0.6-3.2,1.8-4.4c1.2-1.2,2.7-1.8,4.4-1.8
          c1.8,0,3.3,0.6,4.5,1.8c1.3,1.2,1.9,2.7,1.8,4.4v12.5c0,1.8-0.6,3.3-1.8,4.5c-1.2,1.2-2.7,1.8-4.4,1.8c-1.7,0-3.2-0.6-4.5-1.8
          C70.5,34.2,69.9,32.7,69.9,30.9z M76.2,62.2V49.7h12.5v12.4H76.2z M76.2,87.2V74.7h12.5L76.2,87.2z"/>
      </g>
    </svg>
  {%- when 'icon-email' -%}
    <svg
      version="1.1"
      id="Layer_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      x="0px"
      y="0px"
      viewBox="0 0 197.8 197.8"
      style="enable-background:new 0 0 197.8 197.8;"
      xml:space="preserve"
    >
      <g>
        <g>
          <path class="st0" d="M171,32.1c5.8,0,10.7,2,14.7,5.9s5.9,8.9,5.9,14.7v92.8c0,5.7-2,10.6-5.9,14.7c-4,4.1-8.9,6.1-14.7,5.9H26.3
            c-5.8,0-10.7-2-14.7-5.9c-4-4-5.9-8.9-5.9-14.7V52.8c0-5.8,2-10.7,5.9-14.7s8.9-5.9,14.7-5.9H171z M171,52.8H26.3v2.2L94,114.4
            c1.2,0.9,2.7,1.4,4.6,1.4c1.8,0,3.3-0.5,4.4-1.4l68-59.5V52.8z M26.3,138.4l37.1-37.5L26.3,68.6V138.4z M87.5,122.1l-16.3-14.3
            l-37.7,37.7h129.8l-37.7-37.7l-16.1,14.3c-3.2,2.6-6.9,4-11.1,4C94.4,126.1,90.7,124.8,87.5,122.1z M133.7,100.9l37.3,37.3V68.6
            L133.7,100.9z"/>
        </g>
      </g>
    </svg>
  {%- when 'icon-location' -%}
    <svg
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      x="0px"
      y="0px"
      viewBox="0 0 197.8 197.8"
      style="enable-background:new 0 0 197.8 197.8;"
      xml:space="preserve"
    >
      <g>
        <path class="st0" d="M98.3,3.4c22.3,0,41.1,7.7,56.6,23c15.5,15.3,23.3,33.9,23.4,55.6c0.1,21.7-7.7,40.3-23.4,55.6l-56.6,55.6
          l-56.2-55.6c-15.7-15.3-23.6-33.9-23.6-55.6c0-21.7,7.9-40.3,23.6-55.6C57.8,11.1,76.5,3.4,98.3,3.4z M98.3,23.4
          c-16.4,0-30.5,5.8-42.4,17.3c-11.6,11.3-17.5,25-17.5,41.2c0,16.2,5.8,30.1,17.5,41.4l42.4,42l42.8-42
          c11.5-11.3,17.3-25.1,17.3-41.4c0-16.4-5.8-30.1-17.3-41.2C129.2,29.1,114.9,23.4,98.3,23.4z M98.3,48.3c9.6,0,17.8,3.4,24.7,10.2
          c6.9,6.8,10.4,15,10.4,24.7c0,9.7-3.5,18-10.4,24.7c-6.9,6.8-15.2,10.2-24.7,10.4c-9.6,0.1-17.8-3.3-24.6-10.4
          c-6.8-7-10.2-15.3-10.4-24.7c-0.1-9.5,3.3-17.7,10.4-24.7C80.8,51.5,88.9,48.1,98.3,48.3z M98.3,58.3c-6.7,0-12.5,2.5-17.5,7.5
          c-5,5-7.5,10.9-7.5,17.6c0,6.8,2.5,12.7,7.5,17.6c4.9,4.7,10.7,7.1,17.6,7.1s12.8-2.4,17.6-7.1c5-5,7.5-10.9,7.5-17.6
          c0-6.8-2.5-12.7-7.5-17.6C111.1,60.8,105.2,58.3,98.3,58.3z"/>
      </g>
    </svg>
  {%- when 'icon-location-1' -%}
    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="18" viewBox="0 0 15 18" fill="none" style="fill:none">
      <path d="M13.4115 7.48801C13.4115 10.7628 7.23506 16.3821 7.23506 16.3821C7.23506 16.3821 1.05859 10.7628 1.05859 7.48801C1.05859 4.21328 3.8239 1.55859 7.23506 1.55859C10.6462 1.55859 13.4115 4.21328 13.4115 7.48801Z" stroke="currentColor" stroke-width="1.8"/>
      <path d="M7.23587 8.14901C7.72317 8.14901 8.11822 7.7803 8.11822 7.32548C8.11822 6.87066 7.72317 6.50195 7.23587 6.50195C6.74856 6.50195 6.35352 6.87066 6.35352 7.32548C6.35352 7.7803 6.74856 8.14901 7.23587 8.14901Z" fill="none" stroke="currentColor" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  {%- when 'icon-phone' -%}
    <svg
      version="1.1"
      id="Layer_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      x="0px"
      y="0px"
      viewBox="0 0 197.8 197.8"
      style="enable-background:new 0 0 197.8 197.8;"
      xml:space="preserve"
    >
      <g>
        <g>
          <path class="st0" d="M178.8,29.3c3.7,3.7,6.9,8.2,9.4,13.4c2.5,5.2,4.2,11.8,4.9,19.8c0.7,8,0,16.3-2.2,24.8
            c-2.2,8.6-6.7,18.4-13.4,29.6c-6.8,11.1-15.8,22.3-27,33.6c-28.7,28.7-56.1,43-82.3,43c-16.2,0-29.2-4.9-39-14.7l-16.9-16.9
            C6.7,156.3,4,149.6,4,141.8c0-7.9,2.8-14.5,8.3-19.9l15.1-15.1c5.6-5.6,12.2-8.3,19.9-8.3c7.7,0,14.4,2.8,19.9,8.3l13.1,12.9
            l39.5-39.2l-13.2-13.1c-5.6-5.7-8.3-12.4-8.3-20.1c0-7.7,2.8-14.4,8.3-20.1l15.1-14.9c5.4-5.4,12.1-8.2,20-8.2
            c7.9,0,14.6,2.7,20,8.2L178.8,29.3z M25.5,148.5l10.3,10.2l28.5-28.3l-10.5-10.2c-1.8-1.8-4-2.7-6.5-2.7s-4.7,0.9-6.5,2.7
            l-15.2,14.9c-1.8,1.8-2.7,4.1-2.7,6.7C22.8,144.4,23.7,146.7,25.5,148.5z M137.1,137.2c9.8-9.9,17.7-19.5,23.6-28.8
            c5.9-9.3,9.8-17.3,11.6-24.1c1.8-6.8,2.5-13.1,2-19c-0.5-5.9-1.5-10.6-3.1-14.1c-1.6-3.5-3.5-6.3-5.8-8.5L137.1,71l2.7,2.7
            c1.8,1.8,2.7,4.1,2.7,6.7c0,2.7-0.9,4.8-2.7,6.5L87,140c-1.8,1.8-4.1,2.7-6.7,2.7c-2.7,0-4.9-0.9-6.7-2.7l-2.7-3.1l-28.3,28.7
            c6,6,14.6,9.1,25.6,9.1C89.2,174.6,112.2,162.1,137.1,137.2z M120,40.6c-1.8,1.8-2.7,4.1-2.7,6.7c0,2.7,0.9,4.9,2.7,6.7l10.2,10.2
            l28.5-28.3l-10.3-10.2c-1.8-1.8-4-2.7-6.6-2.7c-2.6,0-4.8,0.9-6.6,2.7L120,40.6z"/>
        </g>
      </g>
    </svg>
  {% when 'icon-grid' %}
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="100%"
      height="100%"
      viewBox="0 0 18 18"
      fill="none"
      style="fill: none;"
    >
      <path d="M10.7998 16.56V11.34C10.7998 11.0418 11.0415 10.8 11.3398 10.8H16.5598C16.8581 10.8 17.0998 11.0418 17.0998 11.34V16.56C17.0998 16.8583 16.8581 17.1 16.5598 17.1H11.3398C11.0415 17.1 10.7998 16.8583 10.7998 16.56Z" stroke="currentcolor" stroke-width="1.5"/>
      <path d="M0.899902 16.56V11.34C0.899902 11.0418 1.14167 10.8 1.4399 10.8H6.6599C6.95814 10.8 7.1999 11.0418 7.1999 11.34V16.56C7.1999 16.8583 6.95814 17.1 6.6599 17.1H1.4399C1.14167 17.1 0.899902 16.8583 0.899902 16.56Z" stroke="currentcolor" stroke-width="1.5"/>
      <path d="M10.7998 6.6599V1.4399C10.7998 1.14167 11.0415 0.899902 11.3398 0.899902H16.5598C16.8581 0.899902 17.0998 1.14167 17.0998 1.4399V6.6599C17.0998 6.95814 16.8581 7.1999 16.5598 7.1999H11.3398C11.0415 7.1999 10.7998 6.95814 10.7998 6.6599Z" stroke="currentcolor" stroke-width="1.5"/>
      <path d="M0.899902 6.6599V1.4399C0.899902 1.14167 1.14167 0.899902 1.4399 0.899902H6.6599C6.95814 0.899902 7.1999 1.14167 7.1999 1.4399V6.6599C7.1999 6.95814 6.95814 7.1999 6.6599 7.1999H1.4399C1.14167 7.1999 0.899902 6.95814 0.899902 6.6599Z" stroke="currentcolor" stroke-width="1.5"/>
    </svg>
  {% when 'icon-list' %}
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="100%"
      height="100%"
      viewBox="0 0 18 18"
      fill="none"
      style="fill: none;"
    >
      <path d="M11.6997 16.2H17.0997" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M11.6997 11.7H17.0997" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M0.899902 16.56V11.34C0.899902 11.0418 1.14167 10.8 1.4399 10.8H6.6599C6.95814 10.8 7.1999 11.0418 7.1999 11.34V16.56C7.1999 16.8583 6.95814 17.1 6.6599 17.1H1.4399C1.14167 17.1 0.899902 16.8583 0.899902 16.56Z" stroke="currentcolor" stroke-width="1.5"/>
      <path d="M11.6997 6.2998H17.0997" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M11.6997 1.7998H17.0997" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M0.899902 6.6599V1.4399C0.899902 1.14167 1.14167 0.899902 1.4399 0.899902H6.6599C6.95814 0.899902 7.1999 1.14167 7.1999 1.4399V6.6599C7.1999 6.95814 6.95814 7.1999 6.6599 7.1999H1.4399C1.14167 7.1999 0.899902 6.95814 0.899902 6.6599Z" stroke="currentcolor" stroke-width="1.5"/>
    </svg>
  {% when 'icon-gift' %}
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="100%"
      height="100%"
      viewBox="0 0 18 18"
      fill="none"
      style="fill: none;"
    >
      <path d="M14.9401 9V15.9795C14.9401 16.2256 14.7406 16.425 14.4946 16.425H3.50556C3.25952 16.425 3.06006 16.2256 3.06006 15.9795V9" stroke="currentcolor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M15.9797 5.2876H2.0207C1.77465 5.2876 1.5752 5.48706 1.5752 5.7331V8.5546C1.5752 8.80066 1.77465 9.0001 2.0207 9.0001H15.9797C16.2258 9.0001 16.4252 8.80066 16.4252 8.5546V5.7331C16.4252 5.48706 16.2258 5.2876 15.9797 5.2876Z" stroke="currentcolor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M9 16.4251V5.2876" stroke="currentcolor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M9.00023 5.2877H5.65898C5.16668 5.2877 4.69453 5.09213 4.34642 4.74401C3.9983 4.3959 3.80273 3.92375 3.80273 3.43145C3.80273 2.93914 3.9983 2.46699 4.34642 2.11888C4.69453 1.77076 5.16668 1.5752 5.65898 1.5752C8.25773 1.5752 9.00023 5.2877 9.00023 5.2877Z" stroke="currentcolor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M9 5.2877H12.3413C12.8335 5.2877 13.3057 5.09213 13.6538 4.74401C14.0019 4.3959 14.1975 3.92375 14.1975 3.43145C14.1975 2.93914 14.0019 2.46699 13.6538 2.11888C13.3057 1.77076 12.8335 1.5752 12.3413 1.5752C9.7425 1.5752 9 5.2877 9 5.2877Z" stroke="currentcolor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  {% when 'icon-add-complementary' %}
    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 14 14" fill="none">
      <path d="M6 14V0H8V14H6Z"/>
      <path d="M0 6H14V8H0V6Z"/>
    </svg>
  {% when 'icon-clock' %}
    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="18" viewBox="0 0 17 18" fill="none" style="fill:none">
      <g>
        <path d="M11.9548 10.652H8.78711V7.48438" stroke="currentColor" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M3.41211 2.97219L4.94795 1.82031" stroke="currentColor" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M14.1628 2.97219L12.627 1.82031" stroke="currentColor" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M8.74024 17.083C12.5308 17.083 15.6035 14.0103 15.6035 10.2197C15.6035 6.42925 12.5308 3.35645 8.74024 3.35645C4.94975 3.35645 1.87695 6.42925 1.87695 10.2197C1.87695 14.0103 4.94975 17.083 8.74024 17.083Z" stroke="currentColor" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
      </g>
    </svg>
  {% when 'icon-comparison' %}
    <svg width="37" height="37" viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="1" y="1" width="35" height="34.9988" rx="17.4994" fill="transparent" fill-opacity="0.1" stroke="white" stroke-width="2"/>
      <path d="M13.25 22.5L8.75 18L13.25 13.5" stroke="white" stroke-width="2" stroke-linecap="round" fill="transparent" stroke-linejoin="round"/>
      <path d="M24.2383 22.5L28.7383 18L24.2383 13.5" stroke="white" stroke-width="2" stroke-linecap="round" fill="transparent" stroke-linejoin="round"/>
    </svg>
  {% when 'icon-file' %}
    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg="">
      <path fill="currentColor" d="M320 496c26.5 0 48-21.5 48-48V179.9c0-1.3-.1-2.6-.2-3.9H248c-22.1 0-40-17.9-40-40V16.2c-1.3-.2-2.6-.2-3.9-.2H64C37.5 16 16 37.5 16 64V448c0 26.5 21.5 48 48 48H320zm41.1-336c-.8-1-1.6-1.9-2.4-2.7L226.7 25.4c-.9-.9-1.8-1.7-2.7-2.4V136c0 13.3 10.7 24 24 24H361.1zM0 64C0 28.7 28.7 0 64 0H204.1c12.7 0 24.9 5.1 33.9 14.1L369.9 145.9c9 9 14.1 21.2 14.1 33.9V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V64z"></path>
    </svg>
  {% when 'icon-file-upload' %}
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="100%"
      height="100%"
      viewBox="0 0 38 32"
      fill="none"
      style="fill:none"
    >
      <path d="M19 31V17M19 17L25 22.4444M19 17L13 22.4444" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M32.0909 25C34.5351 24.1001 37 22.05 37 17.9152C37 11.7642 31.5455 10.2265 28.8182 10.2265C28.8182 7.15097 28.8182 1 19 1C9.18182 1 9.18182 7.15097 9.18182 10.2265C6.45454 10.2265 1 11.7642 1 17.9152C1 22.05 3.46482 24.1001 5.90909 25" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  {% when 'icon-up-right' %}
    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
      <path d="M5.16602 11.8337L11.8327 5.16699" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
      <path d="M5.16602 5.16699H11.8327V11.8337" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
    </svg>
{%- endcase -%}
