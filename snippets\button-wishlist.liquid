{% comment %}
  Shopify Wishlist
  Usage:
    - Markup: {%- render 'button-wishlist', product: product -%}
    - Place this snippet inside your existing product card snippet
  Parameters:
    - product: <Shopify product> (required)
{% endcomment %}

{%- if product.handle != blank -%}
  <wishlist-item>
    <button
      wishlist-button
      class="wishlist__button {{ className }} {% if tooltip %} product--tooltip {% endif %}"
      type="button"
      aria-label="{{ 'products.product.add_to_wishlist' | t }}"
      data-product-handle="{{ product.handle }}"
    >
      <span title="{{ 'products.product.add_to_wishlist' | t }}" class="add__wishlist">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
          <path fill="currentColor" d="M462.3 62.7c-54.5-46.4-136-38.7-186.6 13.5L256 96.6l-19.7-20.3C195.5 34.1 113.2 8.7 49.7 62.7c-62.8 53.6-66.1 149.8-9.9 207.8l193.5 199.8c6.2 6.4 14.4 9.7 22.6 9.7 8.2 0 16.4-3.2 22.6-9.7L472 270.5c56.4-58 53.1-154.2-9.7-207.8zm-13.1 185.6L256.4 448.1 62.8 248.3c-38.4-39.6-46.4-115.1 7.7-161.2 54.8-46.8 119.2-12.9 142.8 11.5l42.7 44.1 42.7-44.1c23.2-24 88.2-58 142.8-11.5 54 46 46.1 121.5 7.7 161.2z" />
        </svg>
      </span>
      <span class="loading__wishlist"></span>
      <span title="{{ 'products.product.remove_from_wishlist' | t }}" class="remove__wishlist">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
          <path fill="currentColor" d="M417.84 448a15.94 15.94 0 01-11.35-4.72L40.65 75.26a16 16 0 0122.7-22.56l365.83 368a16 16 0 01-11.34 27.3zM364.92 80c-48.09 0-80 29.55-96.92 51-16.88-21.48-48.83-51-96.92-51a107.37 107.37 0 00-31 4.55L168 112c22.26 0 45.81 9 63.94 26.67a123 123 0 0121.75 28.47 16 16 0 0028.6 0 123 123 0 0121.77-28.51C322.19 121 342.66 112 364.92 112c43.15 0 78.62 36.33 79.07 81 .54 53.69-22.75 99.55-57.38 139.52l22.63 22.77c3-3.44 5.7-6.64 8.14-9.6 40-48.75 59.15-98.8 58.61-153C475.37 130.52 425.54 80 364.92 80zM268 432C180.38 372.51 91 297.6 92 193a83.69 83.69 0 012.24-18.39L69 149.14a115.1 115.1 0 00-9 43.49c-.54 54.22 18.63 104.27 58.61 153 18.77 22.87 52.8 59.45 131.39 112.8a31.84 31.84 0 0036 0c20.35-13.81 37.7-26.5 52.58-38.11l-22.66-22.81C300.25 409.6 284.09 421.05 268 432z"/>
        </svg>
      </span>
      {% if tooltip %}
        <div class="product--tooltip-label {{ tooltip_position }}">
          <span class="product__card--add-wishlist">{{ 'products.product.add_to_wishlist' | t }}</span>
          <span class="product__card--remove-wishlist">
            {{ 'products.product.remove_from_wishlist' | t }}
          </span>
        </div>
      {% endif %}
    </button>
  </wishlist-item>
{%- endif -%}
