<div {{ block.shopify_attributes }}>
  {%- if product != blank -%}
    {%- liquid
      assign gift_card_recipient_feature_active = false
      if block.settings.show_gift_card_recipient and product.gift_card?
        assign gift_card_recipient_feature_active = true
      endif

      assign show_dynamic_checkout = false
      if block.settings.show_dynamic_checkout and gift_card_recipient_feature_active == false
        assign show_dynamic_checkout = true
      endif
    -%}
    <product-form
      class="product-form mb-20 product_buy_button_form"
      data-hide-errors="{{ gift_card_recipient_feature_active }}"
    >
      {%- liquid
        assign gift_card_recipient_feature_active = false
        if block.settings.show_gift_card_recipient and product.gift_card?
          assign gift_card_recipient_feature_active = true
        endif

        assign show_dynamic_checkout = false
        if block.settings.show_dynamic_checkout and gift_card_recipient_feature_active == false
          assign show_dynamic_checkout = true
        endif
      -%}

      <div class="product-form__error-message-wrapper no-js-inline" role="alert" hidden>
        <svg
          aria-hidden="true"
          focusable="false"
          role="presentation"
          class="icon icon-error"
          viewBox="0 0 13 13"
        >
          <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
          <circle cx="6.5" cy="6.5" r="5.5" fill="#EB001B" stroke="#EB001B" stroke-width="0.7"/>
          <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
          <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="#EB001B" stroke-width="0.7">
        </svg>
        <span class="product-form__error-message"></span>
      </div>

      {%- form 'product',
        product,
        id: product_form_id,
        class: 'form',
        novalidate: 'novalidate',
        data-type: 'add-to-cart-form'
      -%}
        <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
        {%- if gift_card_recipient_feature_active -%}
          {%- render 'gift-card-recipient-form', product: product, form: form, section: section -%}
        {%- endif -%}
        <div class="product-form__buttons">
          <div class="product-form__cart--box d-flex align-items-end">
            {%- if block.settings.quantity__button -%}
              <div class="product-form__input product-form__quantity">
                <label class="form__label" for="Quantity-{{ section.id }}">
                  <strong>{{ 'products.product.quantity.label' | t }}</strong>
                </label>

                <quantity-input class="quantity">
                  <button class="quantity__button no-js-hidden" name="minus" type="button">
                    <span class="visually-hidden">
                      {{- 'products.product.quantity.decrease' | t: product: product.title | escape -}}
                    </span>
                    {% render 'icon-minus' %}
                  </button>
                  <input
                    class="quantity__input"
                    type="number"
                    name="quantity"
                    id="Quantity-{{ section.id }}"
                    min="1"
                    value="1"
                    form="product-form-{{ section.id }}"
                  >
                  <button class="quantity__button no-js-hidden" name="plus" type="button">
                    <span class="visually-hidden">
                      {{- 'products.product.quantity.increase' | t: product: product.title | escape -}}
                    </span>
                    {% render 'icon-plus' %}
                  </button>
                </quantity-input>
              </div>
            {%- endif -%}

            <div class="product__add__cart__button">
              <button
                type="submit"
                name="add"
                class="product-form__submit button {% if block.settings.show_dynamic_checkout and product.selling_plan_groups == empty %}button--secondary{% else %}button--primary{% endif %} button--medium"
                {% if product.selected_or_first_available_variant.available == false %}
                  disabled
                {% endif %}
              >
                {%- if current_variant.available -%}
                  {%- if current_variant.inventory_quantity <= 0
                    and current_variant.inventory_policy == 'continue'
                    and block.settings.preorder_button
                  -%}
                    {% if block.settings.cart_icon_type != 'none' %}
                      {% render 'icon-cart', icon: block.settings.cart_icon_type %}
                    {% endif %}
                    <span class="cart--button-text">{{ 'products.product.pre_order' | t }}</span>
                  {%- else -%}
                    {% if block.settings.cart_icon_type != 'none' %}
                      {% render 'icon-cart', icon: block.settings.cart_icon_type %}
                    {% endif %}
                    <span class="cart--button-text">{{ 'products.product.add_to_cart' | t }}</span>
                  {%- endif -%}
                {%- else -%}
                  <span class="cart--button-text">{{ 'products.product.sold_out' | t }}</span>
                {%- endif -%}
              </button>
            </div>
          </div>

          {%- if show_dynamic_checkout -%}
            {{ form | payment_button }}
          {%- endif -%}
        </div>
      {%- endform -%}
      {%- liquid
        assign preorder_button_show = false
        if block.settings.preorder_button
          assign preorder_button_show = true
        endif
      -%}
      <script>
        window.preorder_button = {{ preorder_button_show }};
      </script>
    </product-form>
  {%- else -%}
    <div class="product-form">
      <div class="product-form__buttons form">
        <button
          type="submit"
          name="add"
          class="product-form__submit button button--full-width button--primary"
          disabled
        >
          {{ 'products.product.sold_out' | t }}
        </button>
      </div>
    </div>
  {%- endif -%}

  <div
    id="notify__me--available-{{ section.id }}"
    class="notify__me--available-{{ section.id }} {% if current_variant.available == true %}no-js-inline{% endif %}"
  >
    <modal-opener
      class="product-popup-modal__opener"
      data-modal="#PopupModal-4"
      {{ block.shopify_attributes }}
    >
      <button
        id="ProductPopup-notify"
        class="button__notify--product mb-30 "
        type="button"
        aria-haspopup="dialog"
      >
        {{ 'products.product.back_in_stock_notify.button' | t }}
      </button>
    </modal-opener>
  </div>
  {{ 'component-pickup-availability.css' | asset_url | stylesheet_tag }}

  {%- assign pick_up_availabilities = product.selected_or_first_available_variant.store_availabilities
    | where: 'pick_up_enabled', true
  -%}

  <pickup-availability
    class="product__pickup-availabilities no-js-hidden mb-20"
    {% if product.selected_or_first_available_variant.available and pick_up_availabilities.size > 0 %}
      available
    {% endif %}
    data-root-url="{{ routes.root_url }}"
    data-variant-id="{{ product.selected_or_first_available_variant.id }}"
    data-has-only-default-variant="{{ product.has_only_default_variant }}"
  >
    <template>
      <pickup-availability-preview class="pickup-availability-preview">
        {% render 'icon-unavailable' %}
        <div class="pickup-availability-info">
          <p class="caption-large">{{ 'products.product.pickup_availability.unavailable' | t }}</p>
          <button class="pickup-availability-button link link--text underlined-link">
            {{ 'products.product.pickup_availability.refresh' | t }}
          </button>
        </div>
      </pickup-availability-preview>
    </template>
  </pickup-availability>
</div>

<script src="{{ 'pickup-availability.js' | asset_url }}" defer="defer"></script>

{% if block.settings.wishlist_btn %}
  {%- render 'text-button-wishlist' -%}
{% endif %}

{% if block.settings.compare_btn %}
  {%- render 'text-button-compare' %}
{% endif %}

{%- if block.settings.guarantee_safe_checkout -%}
  <div class="guarantee__safe__checkout">
    <p>{{ block.settings.safe_checkout_text }}</p>
    <ul class="list d-flex product__payment mb-20" role="list">
      {%- for type in shop.enabled_payment_types -%}
        <li class="product__payment__item">
          {{ type | payment_type_svg_tag: class: 'icon icon--full-color' }}
        </li>
      {%- endfor -%}
    </ul>
  </div>
{%- endif -%}
