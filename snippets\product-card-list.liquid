{%- liquid
  assign variant = product_card_product.selected_or_first_available_variant

  assign first_variant_featured_media_check = false
  if variant.featured_media == null
    assign first_variant_featured_media_check = true
  endif

  assign second_img_position = 1

  unless show_secondary_image
    assign second_image_class = 'second--image__hide'
  endunless

  for variant in product_card_product.variants
    assign current_total_stock = current_total_stock | plus: variant.inventory_quantity
  endfor
-%}

<div class="product__card--list  {% if corner_radius %} product--corner-radius-true{% endif %} color-{{ color_scheme }}">
  <div class="product__card-list__thumbnail {{ second_image_class }}">
    {%- if show_badge -%}
      {%- render 'product-badge', product: product_card_product -%}
    {%- endif -%}

    {%- if product_card_product.featured_media -%}
      {%- liquid
        assign featured_media_aspect_ratio = product_card_product.featured_media.aspect_ratio

        if product_card_product.featured_media.aspect_ratio == null
          assign featured_media_aspect_ratio = 1
        endif
      -%}
      <a
        href="{{ product_card_product.url | default: '#' }}"
        class="d-block product__media_thumbnail product__card--link"
      >
        {%- render 'product-card-media',
          product_card_product: product_card_product,
          variant: variant,
          media_size: media_size,
          featured_media_aspect_ratio: featured_media_aspect_ratio,
          second_img_position: second_img_position,
          show_secondary_image: show_secondary_image,
          first_variant_featured_media_check: first_variant_featured_media_check
        -%}
      </a>

    {%- else -%}
      <div class="card__content">
        <a href="{{ product_card_product.url | default: '#' }}" class="d-block">
          <div class="placeholder placeholder_svg_parent" style="padding-bottom: 100%">
            {{ 'product-1' | placeholder_svg_tag: 'placeholder-svg-2' }}
          </div>
        </a>
      </div>
    {%- endif -%}
  </div>

  <div class="product__card--list__content {% if spacing %} product--card-spacing-true{% endif %}">
    {%- if show_vendor -%}
      <span class="visually-hidden">{{ 'accessibility.vendor' | t }}</span>
      <div class="product__vendor">{{ product_card_product.vendor }}</div>
    {%- endif -%}

    {%- if show_title -%}
      <h3 class="product__card__title {{ settings.product_title_size }}">
        <a class="product__card-title--link" href="{{ product_card_product.url | default: '#' }}">
          {{- product_card_product.title -}}
        </a>
      </h3>
    {%- endif -%}

    <div class="product-card-list--price-cart">
      {%- if show_price -%}
        {% render 'price', product: product_card_product, price_class: price_class %}
      {%- endif -%}

      {%- if show_cart_button -%}
        <div class="product-card-list-action-buttons ">
          {% render 'add-to-cart-form-2',
            product_card_product: product_card_product,
            variant: variant,
            tooltip: true,
            tooltip_position: 'tooltip--top',
            tooltip_desktop: true
          %}
        </div>
      {%- endif -%}
    </div>

    {%- if show_rating and product_card_product.metafields.reviews.rating.value != blank -%}
      <div class="product-card--rating">
        {% liquid
          assign rating_decimal = 0
          assign decimal = product_card_product.metafields.reviews.rating.value.rating | modulo: 1
          if decimal >= 0.3 and decimal <= 0.7
            assign rating_decimal = 0.5
          elsif decimal > 0.7
            assign rating_decimal = 1
          endif
        %}
        <div
          class="rating"
          role="img"
          aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: product_card_product.metafields.reviews.rating.value, rating_max: product_card_product.metafields.reviews.rating.value.scale_max }}"
        >
          <span
            aria-hidden="true"
            class="rating-star color-icon-{{ settings.accent_icons }}"
            style="--rating: {{ product_card_product.metafields.reviews.rating.value.rating | floor }}; --rating-max: {{ product_card_product.metafields.reviews.rating.value.scale_max }}; --rating-decimal: {{ rating_decimal }};"
          ></span>
        </div>
        <p class="rating-text caption">
          <span aria-hidden="true">
            {{- product_card_product.metafields.reviews.rating.value }} /
            {{ product_card_product.metafields.reviews.rating.value.scale_max -}}
          </span>
        </p>
        <p class="rating-count caption">
          <span aria-hidden="true">({{ product_card_product.metafields.reviews.rating_count }})</span>
          <span class="visually-hidden">
            {{- product_card_product.metafields.reviews.rating_count }}
            {{ 'accessibility.total_reviews' | t -}}
          </span>
        </p>
      </div>
    {%- endif -%}

    <div class="product--inventory-stock">
      {% if product_card_product.selected_or_first_available_variant.available %}
        <span class="product--inventory-stocky-label badge-stock badge-stock-in color-{{ section.settings.stock_color_scheme }}">
          <span class="badge-stock-dot"></span>
          {{ 'products.product.inventory_status.stock' | t }}
          {%- if current_total_stock > 0 -%}: {{ current_total_stock }}{%- endif -%}
        </span>
      {% else %}
        <span class="product--inventory-stocky-label badge-stock badge-out-of-stock color-{{ section.settings.stock_out_color_scheme }}">
          <span class="badge-stock-dot"></span>
          {{ 'products.product.out_of_stock' | t }}
        </span>
      {% endif %}
    </div>
  </div>
</div>
