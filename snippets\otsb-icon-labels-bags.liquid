{%- if icon != 'none' -%}
  <svg style="fill: none" width="100%" height="100%" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg"> 
    {% case icon %}
      {% when "1st-medal" %}
        <path d="M62.724 44.0025L79.5 6M40.422 44.844L21 6M55.929 42.216L38.2575 6M63.12 6L58.44 17.25M25.5 69C25.5 76.1608 28.3446 83.0284 33.4081 88.0919C38.4716 93.1554 45.3392 96 52.5 96C59.6608 96 66.5284 93.1554 71.5919 88.0919C76.6554 83.0284 79.5 76.1608 79.5 69C79.5 61.8392 76.6554 54.9716 71.5919 49.9081C66.5284 44.8446 59.6608 42 52.5 42C45.3392 42 38.4716 44.8446 33.4081 49.9081C28.3446 54.9716 25.5 61.8392 25.5 69Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M45.75 64.5L54.75 57.75V80.25" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "award" %}
        <path d="M49.6365 63.273C65.4521 63.273 78.273 50.4521 78.273 34.6365C78.273 18.821 65.4521 6 49.6365 6C33.821 6 21 18.821 21 34.6365C21 50.4521 33.821 63.273 49.6365 63.273Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M34.1317 58.7318L29.1816 96.0002L49.6363 83.7274L70.091 96.0002L65.1409 58.6909" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "bicycle" %}
        <path d="M21.3636 81.3181C30.401 81.3181 37.7273 73.9917 37.7273 64.9545C37.7273 55.9172 30.401 48.5908 21.3636 48.5908C12.3262 48.5908 5 55.9172 5 64.9545C5 73.9917 12.3262 81.3181 21.3636 81.3181Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M35.6826 34.2727H60.2281M60.2281 34.2727H62.2735L78.6372 64.9545M60.2281 34.2727L68.4099 22M68.4099 22H58.1826M68.4099 22H76.5917" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M21.3643 64.9548L35.6824 34.2729L50.0006 60.8639H62.2733" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M35.6824 34.2727C34.3188 30.1818 29.5461 22 21.3643 22" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M78.6361 81.3181C87.6733 81.3181 94.9997 73.9917 94.9997 64.9545C94.9997 55.9172 87.6733 48.5908 78.6361 48.5908C69.5989 48.5908 62.2725 55.9172 62.2725 64.9545C62.2725 73.9917 69.5989 81.3181 78.6361 81.3181Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "box" %}
        <path d="M90 67.2132V31.6576C89.9982 30.0988 89.5871 28.5678 88.8067 27.2183C88.0267 25.8688 86.9053 24.7481 85.5556 23.9687L54.4444 6.19089C53.0933 5.41076 51.5604 5 50 5C48.4396 5 46.9067 5.41076 45.5556 6.19089L14.4444 23.9687C13.0945 24.7481 11.9732 25.8688 11.1932 27.2183C10.4131 28.5678 10.0016 30.0988 10 31.6576V67.2132C10.0016 68.7718 10.4131 70.3029 11.1932 71.6523C11.9732 73.002 13.0945 74.1225 14.4444 74.902L45.5556 92.6798C46.9067 93.4598 48.4396 93.8705 50 93.8705C51.5604 93.8705 53.0933 93.4598 54.4444 92.6798L85.5556 74.902C86.9053 74.1225 88.0267 73.002 88.8067 71.6523C89.5871 70.3029 89.9982 68.7718 90 67.2132Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M11.1982 27.0356L49.9981 49.4801L88.7981 27.0356" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M50 94.2355V49.4355" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "briefcase" %}
        <path d="M86 27H14C9.02943 27 5 31.0294 5 36V81C5 85.9707 9.02943 90 14 90H86C90.9707 90 95 85.9707 95 81V36C95 31.0294 90.9707 27 86 27Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M68 90V18C68 15.6131 67.0518 13.3239 65.3639 11.6361C63.6759 9.94819 61.3868 9 59 9H41C38.6131 9 36.3239 9.94819 34.6361 11.6361C32.9482 13.3239 32 15.6131 32 18V90" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "calendar" %}
        <path d="M82 14H19C14.0294 14 10 18.0294 10 23V86C10 90.9707 14.0294 95 19 95H82C86.9707 95 91 90.9707 91 86V23C91 18.0294 86.9707 14 82 14Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M68.5 5V23" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M32.5 5V23" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M10 41H91" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M76 76.6667C76 77.8159 75.5435 78.9181 74.7308 79.7308C73.9181 80.5435 72.8159 81 71.6667 81C70.5174 81 69.4152 80.5435 68.6025 79.7308C67.7899 78.9181 67.3333 77.8159 67.3333 76.6667C67.3333 75.5174 67.7899 74.4152 68.6025 73.6025C69.4152 72.7899 70.5174 72.3333 71.6667 72.3333C72.8159 72.3333 73.9181 72.7899 74.7308 73.6025C75.5435 74.4152 76 75.5174 76 76.6667ZM76 59.3333C76 60.4826 75.5435 61.5848 74.7308 62.3975C73.9181 63.2101 72.8159 63.6667 71.6667 63.6667C70.5174 63.6667 69.4152 63.2101 68.6025 62.3975C67.7899 61.5848 67.3333 60.4826 67.3333 59.3333C67.3333 58.1841 67.7899 57.0819 68.6025 56.2692C69.4152 55.4565 70.5174 55 71.6667 55C72.8159 55 73.9181 55.4565 74.7308 56.2692C75.5435 57.0819 76 58.1841 76 59.3333ZM54.3333 76.6667C54.3333 77.8159 53.8768 78.9181 53.0641 79.7308C52.2515 80.5435 51.1493 81 50 81C48.8507 81 47.7485 80.5435 46.9359 79.7308C46.1232 78.9181 45.6667 77.8159 45.6667 76.6667C45.6667 75.5174 46.1232 74.4152 46.9359 73.6025C47.7485 72.7899 48.8507 72.3333 50 72.3333C51.1493 72.3333 52.2515 72.7899 53.0641 73.6025C53.8768 74.4152 54.3333 75.5174 54.3333 76.6667ZM54.3333 59.3333C54.3333 60.4826 53.8768 61.5848 53.0641 62.3975C52.2515 63.2101 51.1493 63.6667 50 63.6667C48.8507 63.6667 47.7485 63.2101 46.9359 62.3975C46.1232 61.5848 45.6667 60.4826 45.6667 59.3333C45.6667 58.1841 46.1232 57.0819 46.9359 56.2692C47.7485 55.4565 48.8507 55 50 55C51.1493 55 52.2515 55.4565 53.0641 56.2692C53.8768 57.0819 54.3333 58.1841 54.3333 59.3333ZM32.6667 76.6667C32.6667 77.8159 32.2101 78.9181 31.3975 79.7308C30.5848 80.5435 29.4826 81 28.3333 81C27.1841 81 26.0819 80.5435 25.2692 79.7308C24.4565 78.9181 24 77.8159 24 76.6667C24 75.5174 24.4565 74.4152 25.2692 73.6025C26.0819 72.7899 27.1841 72.3333 28.3333 72.3333C29.4826 72.3333 30.5848 72.7899 31.3975 73.6025C32.2101 74.4152 32.6667 75.5174 32.6667 76.6667Z" fill="currentColor"/>
      {% when "camera" %}
        <path d="M95 78.4545C95 80.6244 94.138 82.7054 92.6035 84.2399C91.069 85.7744 88.988 86.6364 86.8182 86.6364H13.1818C11.0119 86.6364 8.93079 85.7744 7.39641 84.2399C5.862 82.7054 5 80.6244 5 78.4545V33.4545C5 31.2846 5.862 29.2035 7.39641 27.6691C8.93079 26.1347 11.0119 25.2727 13.1818 25.2727H29.5455L37.7273 13H62.2727L70.4545 25.2727H86.8182C88.988 25.2727 91.069 26.1347 92.6035 27.6691C94.138 29.2035 95 31.2846 95 33.4545V78.4545Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M50.0004 70.2727C59.0376 70.2727 66.364 62.9463 66.364 53.909C66.364 44.8718 59.0376 37.5454 50.0004 37.5454C40.963 37.5454 33.6367 44.8718 33.6367 53.909C33.6367 62.9463 40.963 70.2727 50.0004 70.2727Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "chat-bubble" %}
        <path d="M69.0561 71.5842L52.7777 89.547C51.587 90.8609 49.5225 90.8609 48.3317 89.547L32.0534 71.5842C31.4847 70.9567 30.6792 70.5987 29.8323 70.5987H15.4351C9.7946 70.5987 6.92826 65.3306 6.09227 62.3866C6.02658 62.1552 6 61.925 6 61.6845V18.1656C6 10.8892 12.2901 8.35669 15.4351 8H81.4809C91.9644 8 93.8864 14.7771 93.5369 18.1656C93.8864 30.1147 94.3756 55.6178 93.5369 62.0382C92.6982 68.4586 88.6446 70.4204 86.7227 70.5987H71.2791C70.4322 70.5987 69.6248 70.9567 69.0561 71.5842Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/>
        <path d="M25 31H49" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/>
        <path d="M25 46H71" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/>
      {% when "check-mark" %}
        <path d="M95 15L37.1429 85L5 57" stroke="currentColor" stroke-width="8" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "clock" %}
        <path d="M50 95C74.8526 95 95 74.8526 95 50C95 25.1472 74.8526 5 50 5C25.1472 5 5 25.1472 5 50C5 74.8526 25.1472 95 50 95Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M50 23V50L68 59" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "cloud-rain" %}
        <path d="M66.3877 54.0889V86.8161" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M33.6602 54.0889V86.8161" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M50.0244 62.2705V94.9978" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>          <path d="M82.7513 68.7351C87.0519 66.8516 90.5736 63.5461 92.7253 59.3733C94.877 55.2005 95.5277 50.4145 94.5683 45.8186C93.6088 41.2228 91.0976 37.0969 87.456 34.1336C83.8145 31.1702 79.2644 29.5498 74.5695 29.5443H69.415C68.1182 24.5228 65.6466 19.8813 62.204 16.0025C58.7613 12.1238 54.4461 9.11868 49.614 7.235C44.782 5.35133 39.5714 4.64299 34.4119 5.16837C29.2523 5.69375 24.2915 7.43781 19.9383 10.2568C15.5851 13.0758 11.9642 16.889 9.37398 21.3821C6.78379 25.8752 5.29849 30.9195 5.04052 36.0993C4.78254 41.2791 5.75928 46.4461 7.89021 51.1743C10.0211 55.9026 13.2453 60.0567 17.2968 63.2942" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "coffee" %}
        <path d="M73.5713 36H77.857C82.4036 36 86.7639 37.8061 89.9788 41.021C93.1937 44.2359 94.9999 48.5963 94.9999 53.1429C94.9999 57.6894 93.1937 62.0498 89.9788 65.2647C86.7639 68.4796 82.4036 70.2857 77.857 70.2857H73.5713" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M5 36H73.5714V74.5714C73.5714 79.118 71.7653 83.4783 68.5504 86.6933C65.3355 89.9082 60.9751 91.7143 56.4286 91.7143H22.1429C17.5963 91.7143 13.2359 89.9082 10.021 86.6933C6.80612 83.4783 5 79.118 5 74.5714V36Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M22.1426 6V18.8571" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M39.2861 6V18.8571" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M56.4287 6V18.8571" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "coin" %}
        <path d="M69.5555 54.8891C56.0552 54.8891 45.111 49.4168 45.111 42.6668C45.111 35.9165 56.0552 30.4446 69.5555 30.4446C83.0557 30.4446 94 35.9165 94 42.6668C94 49.4168 83.0557 54.8891 69.5555 54.8891Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M45.111 62.2224C45.111 68.9723 56.0552 74.4447 69.5555 74.4447C83.0557 74.4447 94 68.9723 94 62.2224" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M6 37.7778C6 44.5282 16.9443 50.0001 30.4445 50.0001C35.9485 50.0001 41.0272 49.0909 45.1133 47.5556" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M6 54.8892C6 61.6391 16.9443 67.111 30.4445 67.111C35.9472 67.111 41.0256 66.2019 45.1109 64.6677" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M6 18.2222V71.9999C6 78.7498 16.9443 84.2222 30.4445 84.2222C35.9476 84.2222 41.0256 83.3122 45.1109 81.7776" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M54.889 32.889V18.2222" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M45.111 42.6667V81.7777C45.111 88.5276 56.0552 93.9999 69.5555 93.9999C83.0557 93.9999 94 88.5276 94 81.7777V42.6667" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M30.4445 30.4445C16.9443 30.4445 6 24.9722 6 18.2223C6 11.4723 16.9443 6 30.4445 6C43.9448 6 54.889 11.4723 54.889 18.2223C54.889 24.9722 43.9448 30.4445 30.4445 30.4445Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "credit-card" %}
        <path d="M36.1412 25L74.3581 11.9967C76.083 11.4098 78.0207 12.0524 78.7379 13.7274C80.0959 16.8991 80.891 21.1759 78.28 24.0888C77.6966 24.7397 76.814 25 75.9399 25H30" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/>
        <path d="M94 35C94 29.4772 89.5228 25 84 25H16C10.4772 25 6 29.4772 6 35V79C6 84.5228 10.4772 89 16 89H84C89.5228 89 94 84.5228 94 79V35Z" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M30 43C30 39.6863 27.3137 37 24 37V37C20.6863 37 18 39.6863 18 43V47C18 50.3137 20.6863 53 24 53V53C27.3137 53 30 50.3137 30 47V43Z" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M22 77H18" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M42 77H30" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M82 77C84.2091 74.7909 84.2091 71.2091 82 69L81 68C79.3431 66.3431 76.6569 66.3431 75 68V68C73.3431 69.6569 70.6569 69.6569 69 68V68C67.3431 66.3431 64.6569 66.3431 63 68L62 69C59.7909 71.2091 59.7909 74.7909 62 77L63 78C64.6569 79.6569 67.3431 79.6569 69 78V78C70.6569 76.3431 73.3431 76.3431 75 78V78C76.6569 79.6569 79.3431 79.6569 81 78L82 77Z" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "delivery-truck" %}
        <path d="M21 16h39m4.6 55.22V26.14M34 73h36M8.33 63.2v5.28a5.35 5.35 0 0 0 5.42 4.75c3.96.08 3.78.04 3.2 0m47.68-47.15V20.8a5.35 5.35 0 0 0-5.43-4.75c-3.95-.09-3.78-.04-3.2 0M23.4 16h-4.85c-1.43.18-4.29 1.6-4.37 5.91-.07 4.31-.03 4.12 0 3.49" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/><path d="M79.2 65.8a7.47 7.47 0 1 1 0 14.95 7.47 7.47 0 0 1 0-14.95Z" stroke="currentColor" stroke-width="6.5"/><path d="M89 59h2" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/><path d="M26.16 65.33a7.47 7.47 0 1 1 0 14.95 7.47 7.47 0 0 1 0-14.95Z" stroke="currentColor" stroke-width="6.5"/><path d="M9.3 54.44h6.2M6 43.88h12.21m-6.78-10.56h14.73m43.22 1.9h13.98a3 3 0 0 1 2.72 1.73l7.64 16.46a3 3 0 0 1 .28 1.26v15.14a3 3 0 0 1-3 3h-2.23" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/>
      {% when "dollar-sign" %}
        <path d="M49.5459 5V95" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M70 21.3638H39.3182C35.5208 21.3638 31.8789 22.8723 29.1937 25.5575C26.5085 28.2426 25 31.8845 25 35.682C25 39.4794 26.5085 43.1213 29.1937 45.8064C31.8789 48.4916 35.5208 50.0001 39.3182 50.0001H59.7727C63.5701 50.0001 67.212 51.5087 69.8972 54.1938C72.5824 56.879 74.0909 60.5209 74.0909 64.3183C74.0909 68.1157 72.5824 71.7576 69.8972 74.4428C67.212 77.128 63.5701 78.6365 59.7727 78.6365H25" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "earth" %}
        <path d="M6 50a44 44 0 1 0 88 0 44 44 0 0 0-88 0Z" stroke="currentColor" stroke-width="6.5"/><path d="M23.6 17.93c3.43 3.12 10.5 11.67 11.33 20.84C35.7 47.34 41.36 54.3 50 54.4c3.32.04 6.68-2.36 6.67-5.68 0-1.03-.17-2.08-.44-3.05a6.2 6.2 0 0 1 .37-4.47c2.68-5.53 7.96-7.02 12.14-10.02 1.86-1.34 3.55-2.74 4.3-3.87 2.06-3.13 4.12-9.38 3.09-12.51M94 54.4c-1.45 4.1-2.47 14.85-18.84 15.02 0 0-14.5 0-18.84 8.2-3.48 6.55-1.45 13.65 0 16.38" stroke="currentColor" stroke-width="6.5"/>
      {% when "eye" %}
        <path d="M5 49.7273C5 49.7273 21.3636 17 50 17C78.6364 17 95 49.7273 95 49.7273C95 49.7273 78.6364 82.4545 50 82.4545C21.3636 82.4545 5 49.7273 5 49.7273Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M50.0003 62C56.7783 62 62.273 56.5054 62.273 49.7273C62.273 42.9493 56.7783 37.4546 50.0003 37.4546C43.2222 37.4546 37.7275 42.9493 37.7275 49.7273C37.7275 56.5054 43.2222 62 50.0003 62Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "feather" %}
        <path d="M67.9738 32.0262L5 95M74.7209 63.5131H36.4869M52.995 81.5056C54.1784 81.5054 55.3502 81.2716 56.4432 80.8178C57.5361 80.3639 58.5288 79.6989 59.3643 78.8607L87.0458 51.0983C92.11 46.0341 94.955 39.1656 94.955 32.0038C94.955 24.8419 92.11 17.9734 87.0458 12.9092C81.9816 7.84503 75.1131 5 67.9513 5C60.7894 5 53.9209 7.84503 48.8567 12.9092L21.1303 40.6357C19.443 42.3224 18.4949 44.6102 18.4944 46.996V77.0075C18.4944 78.2005 18.9683 79.3446 19.8118 80.1882C20.6554 81.0317 21.7995 81.5056 22.9925 81.5056H52.995Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "fire" %}
        <path d="M50 94c-19.3 0-35-11.4-35-30.8v-.4c0-9.3 5.2-16.8 13.1-21.6 7.6-4.6 12-13.2 11-22L36.9 6l8 3.5a73 73 0 0 1 34.8 31A41 41 0 0 1 85 60.9v2.4q-.2 10.4-5.3 17.4" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M50 94c-7.2 0-13.1-6.3-13.1-14 0-6.2 4.4-11.2 8.3-15.7l4.8-5.5 4.8 5.5C58.7 68.8 63 73.8 63 80c.1 7.7-5.8 14-13 14" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "flower" %}
        <path d="M49.9982 62.2705C56.7764 62.2705 62.2709 56.776 62.2709 49.9978C62.2709 43.22 56.7764 37.7251 49.9982 37.7251C43.2204 37.7251 37.7256 43.22 37.7256 49.9978C37.7256 56.776 43.2204 62.2705 49.9982 62.2705Z" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M54.0901 37.7273C54.0901 37.7273 58.181 29.5455 58.181 21.3636C58.181 13.1818 49.9992 5 49.9992 5C49.9992 5 41.8174 13.1818 41.8174 21.3636C41.8174 29.5455 45.9083 37.7273 45.9083 37.7273" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M37.7273 45.9093C37.7273 45.9093 29.5455 41.8184 21.3636 41.8184C13.1818 41.8184 5 50.0002 5 50.0002C5 50.0002 13.1818 58.182 21.3636 58.182C29.5455 58.182 37.7273 54.0911 37.7273 54.0911" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M54.0901 62.2725C54.0901 62.2725 58.181 70.4543 58.181 78.6361C58.181 86.8179 49.9992 94.9997 49.9992 94.9997C49.9992 94.9997 41.8174 86.8179 41.8174 78.6361C41.8174 70.4543 45.9083 62.2725 45.9083 62.2725" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M62.2725 45.9093C62.2725 45.9093 70.4543 41.8184 78.6361 41.8184C86.8179 41.8184 94.9997 50.0002 94.9997 50.0002C94.9997 50.0002 86.8179 58.182 78.6361 58.182C70.4543 58.182 62.2725 54.0911 62.2725 54.0911" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M44.2151 38.4301C44.2151 38.4301 41.3223 29.752 35.5369 23.9666C29.7515 18.1812 18.1807 18.1812 18.1807 18.1812C18.1807 18.1812 18.1807 29.752 23.9661 35.5374C29.7515 41.3228 38.4296 44.2156 38.4296 44.2156" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M38.4287 55.7852C38.4287 55.7852 29.7505 58.6778 23.9651 64.4632C18.1797 70.2486 18.1797 81.8197 18.1797 81.8197C18.1797 81.8197 29.7505 81.8197 35.5359 76.0343C41.3214 70.2486 44.2139 61.5705 44.2139 61.5705" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M61.5715 55.7852C61.5715 55.7852 70.2495 58.6778 76.0349 64.4632C81.8203 70.2486 81.8203 81.8197 81.8203 81.8197C81.8203 81.8197 70.2495 81.8197 64.4642 76.0343C58.6788 70.2486 55.7861 61.5705 55.7861 61.5705" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M55.7861 38.4301C55.7861 38.4301 58.6788 29.752 64.4642 23.9666C70.2495 18.1812 81.8207 18.1812 81.8207 18.1812C81.8207 18.1812 81.8207 29.752 76.0353 35.5374C70.2495 41.3228 61.5715 44.2156 61.5715 44.2156" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "gift" %}
        <path d="M87.5 48.4v36A6.6 6.6 0 0 1 81 91H19a6.5 6.5 0 0 1-6.5-6.6v-36m35.9-28a12 12 0 0 0-7-10.5 11.3 11.3 0 0 0-15.6 8.3A12 12 0 0 0 30.6 30q2.9 2 6.4 2h11.4m0-11.5V32m0-11.5a12 12 0 0 1 7-10.6A11.3 11.3 0 0 1 71 18.2 12 12 0 0 1 66 30q-2.9 2-6.3 2H48.4m0 0v59M10.9 48.4H89a5 5 0 0 0 4.9-5V37a5 5 0 0 0-4.9-5H11a5 5 0 0 0-5 5v6.5a5 5 0 0 0 4.9 5" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "globe" %}
        <path d="M50 6a44 44 0 1 0 0 88 44 44 0 0 0 0-88Z" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="10"/><path d="M50 6C37.7 6 26.2 25.7 26.2 50S37.7 94 50 94s23.8-19.7 23.8-44S62.3 6 50 6Z" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="10"/><path d="M20.7 20.7c8 5.7 18.2 9.1 29.3 9.1a51 51 0 0 0 29.3-9.1m0 58.6c-8-5.7-18.2-9.1-29.3-9.1a51 51 0 0 0-29.3 9.1" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M50 6v88m44-44H6" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="10"/>
      {% when "heart" %}
        <path d="M70.3 11.3c12 0 21.4 9.4 21.5 21.9 0 7.6-3.4 14.8-9.9 22.8-6.5 8.2-16 16.8-27.6 27.6l-4.3 4-4.3-4A299 299 0 0 1 18 56c-6.5-8-9.9-15.2-9.9-22.8 0-12.5 9.4-22 21.6-22 6.8 0 13.3 3.2 17.7 8.5l2.5 3 2.5-3a23 23 0 0 1 17.8-8.4Z" stroke="currentColor" stroke-width="6.5"/>
      {% when "iron" %}
        <path d="M40.3564 65.4583H40.4069M35.3055 20H72.9803C76.5672 20.0003 80.0377 21.2729 82.7744 23.5916C85.5111 25.9102 87.3365 29.1245 87.926 32.6627L90.8404 50.159L94.9316 74.7318C95.0518 75.4546 95.0132 76.1949 94.8186 76.9014C94.6241 77.6078 94.2781 78.2635 93.8048 78.8229C93.3314 79.3822 92.742 79.8319 92.0775 80.1407C91.413 80.4495 90.6892 80.61 89.9564 80.611H5C5 71.2339 8.72504 62.2409 15.3557 55.6103C21.9863 48.9796 30.9793 45.2546 40.3564 45.2546H89.8554M60.5601 65.4583H60.6106" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "leaf" %}
        <path d="M31.5098 76.9921C85.3308 76.9921 89.6617 31.2072 90.0084 9.44915C90.0115 8.85729 89.8959 8.2708 89.6684 7.7244C89.4409 7.178 89.1061 6.68278 88.6839 6.26806C88.2616 5.85334 87.7604 5.52754 87.21 5.30993C86.6596 5.09233 86.0711 4.98736 85.4794 5.00121C9 6.40583 9 43.484 9 76.9921V95" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M9 76.9923C9 76.9923 9 49.9805 45.0157 45.4785" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "lock" %}
        <path d="M70.3466 35.6471C70.3466 31.9028 72.1518 18.1284 65.5327 11.8396C62.2531 8.72364 54.8715 6 49.2858 6C43.7002 6 39.5468 7.48739 35.5971 10.135C31.6475 12.7825 29.4286 16.3734 29.4286 20.1177V37.0588" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M75.2 38.6858H24.8C18.8353 38.6858 14 43.0561 14 48.4471V84.2388C14 89.6298 18.8353 94.0001 24.8 94.0001H75.2C81.1647 94.0001 86 89.6298 86 84.2388V48.4471C86 43.0561 81.1647 38.6858 75.2 38.6858Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "map-pin" %}
        <path d="M50.001 6.25C68.6451 6.25024 83.751 21.3417 83.75 39.9463C83.75 48.2506 80.7297 55.838 75.7334 61.7178L50.5723 91.3105C50.2728 91.6628 49.7282 91.6627 49.4287 91.3105L24.2656 61.7188C19.2695 55.8378 16.25 48.2512 16.25 39.9473C16.25 21.3413 31.3567 6.25 50.001 6.25ZM50.001 20.2305C39.8063 20.2305 31.5343 28.4835 31.5342 38.6748C31.5342 48.8663 39.8063 57.1201 50.001 57.1201C60.1955 57.1199 68.4668 48.8661 68.4668 38.6748C68.4666 28.4836 60.1954 20.2307 50.001 20.2305Z" stroke="currentColor" stroke-width="6.5"/>
      {% when "megaphone" %}
        <path d="M58.8889 60.4088V24.8533M58.8889 60.4088L86.0102 75.9066C87.788 76.9226 90 75.639 90 73.5915V11.6706C90 9.62307 87.788 8.33943 86.0102 9.3553L58.8889 24.8533M58.8889 60.4088H27.7778C17.9594 60.4088 10 52.4492 10 42.631C10 32.8126 17.9594 24.8533 27.7778 24.8533H58.8889" stroke="currentColor" stroke-width="6.5"/>
        <path d="M31.1425 83.9647L27.7773 60.4087H45.5551L48.5649 81.4758C49.3222 86.7771 45.2085 91.5198 39.8536 91.5198C35.4745 91.5198 31.7618 88.2998 31.1425 83.9647Z" stroke="currentColor" stroke-width="6.5"/>
      {% when "message-text" %}
        <path d="M27.7764 50H72.2208" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M27.7764 32.2227H54.443" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M10 86.8422V18.8889C10 13.9797 13.9797 10 18.8889 10H81.1111C86.0204 10 90 13.9797 90 18.8889V63.3333C90 68.2427 86.0204 72.2222 81.1111 72.2222H32.05C29.3497 72.2222 26.7958 73.4498 25.1089 75.5582L14.749 88.508C13.1743 90.4765 10 89.3631 10 86.8422Z" stroke="currentColor" stroke-width="6.5"/>
      {% when "music" %}
        <path d="M35 80V15L95 5V70" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M20 95C28.2843 95 35 88.2843 35 80C35 71.7157 28.2843 65 20 65C11.7157 65 5 71.7157 5 80C5 88.2843 11.7157 95 20 95Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M80 85C88.2843 85 95 78.2843 95 70C95 61.7157 88.2843 55 80 55C71.7157 55 65 61.7157 65 70C65 78.2843 71.7157 85 80 85Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "packages" %}
        <path d="M40.9999 63L41 81C41 85.9707 36.9706 90 32 90H14C9.02943 90 5 85.9707 5 81V63C5 58.0293 9.02943 54 14 54H31.9999C36.9705 54 40.9999 58.0293 40.9999 63Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M68 18V36C68 40.9707 63.9707 45 59 45H41C36.0294 45 32 40.9707 32 36V18C32 13.0294 36.0294 9 41 9H59C63.9702 9 68 13.0294 68 18Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M95 63V81C95 85.9707 90.9707 90 86 90H68C63.0293 90 59 85.9707 59 81V63C59 58.0293 63.0293 54 68 54H86C90.9703 54 95 58.0293 95 63Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M23 67.5V54" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M50 22.5V9" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M77 67.5V54" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "pants" %}
        <path d="M18.1626 10.9475C18.0737 9.34675 19.3477 8 20.9509 8H80.2065C81.8101 8 83.0837 9.34675 82.9952 10.9475L78.6513 89.1396C78.5688 90.6193 77.3447 91.7771 75.8627 91.7771H62.3704C60.9545 91.7771 59.7625 90.7172 59.5976 89.3107L53.3524 36.2286C52.9656 32.9402 48.1919 32.9402 47.8052 36.2286L41.5602 89.3107C41.395 90.7172 40.203 91.7771 38.787 91.7771H25.2949C23.8128 91.7771 22.5888 90.6193 22.5066 89.1396L18.1626 10.9475Z" stroke="currentColor" stroke-width="6.5"/>
        <path d="M18 28.9443H24.9814C30.1224 28.9443 34.29 24.7767 34.29 19.6357V8" stroke="currentColor" stroke-width="6.5"/>
        <path d="M80.8338 28.9443H76.1796C71.0386 28.9443 66.8711 24.7767 66.8711 19.6357V8" stroke="currentColor" stroke-width="6.5"/>
      {% when "percent" %}
        <path d="m9.4 90.6 80.1-80.1M76.3 93a16.7 16.7 0 1 0 0-33.4 16.7 16.7 0 0 0 0 33.4M23.7 40.4a16.7 16.7 0 1 0 0-33.4 16.7 16.7 0 0 0 0 33.4" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "piggy-bank" %}
        <path d="M81.999 26.375H83.1768L81.4512 33.3223C80.8985 35.5481 81.6323 37.7559 83.0449 39.2676C84.4806 40.804 85.7078 42.5156 86.6904 44.375C87.7459 46.3722 89.8501 47.8749 92.3799 47.875H94.75V68.25H88.7158C86.718 68.25 84.9574 69.2008 83.7959 70.583C82.9537 71.5854 82.0362 72.5105 81.0498 73.3516C79.6883 74.5124 78.7491 76.2604 78.749 78.2451V89.75H69.249V85.25C69.249 81.7983 66.4507 79.0002 62.999 79H47.665C44.2133 79 41.415 81.7982 41.415 85.25V89.75H31.9141V78.2432C31.9141 76.2626 30.979 74.5125 29.6143 73.3506C25.1407 69.5418 22.0797 64.1392 21.3936 58.0078C21.1617 55.9366 19.932 53.9779 18.0684 52.9258C20.1195 52.2377 21.6478 50.5277 22.2393 48.5527C25.1557 38.8145 34.1065 31.75 44.665 31.75H65.999C66.5568 31.75 67.1324 31.798 67.7744 31.8779C69.9007 32.1426 71.8725 31.3184 73.2373 29.9893C75.5394 27.7474 78.6058 26.3751 81.999 26.375ZM73.999 46.75C70.7137 46.75 68.082 49.4382 68.082 52.6875C68.082 55.9368 70.7137 58.625 73.999 58.625C77.2843 58.625 79.916 55.9368 79.916 52.6875C79.916 49.4382 77.2843 46.75 73.999 46.75ZM52.665 10.25C58.5749 10.2501 63.5766 14.3267 65.0059 19.875H44.665C43.1602 19.875 41.6881 19.9829 40.25 20.1758C41.5733 14.4745 46.6488 10.25 52.665 10.25Z" stroke="currentColor" stroke-width="6.5"/>
      {% when "plane" %}
        <path d="M93 7L46.25 53.75" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M93 7L63.25 92L46.25 53.75L8 36.75L93 7Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "planet" %}
        <g clip-path="url(#clip0_9445_105)">
          <path d="M59.536 86.4225C79.6557 81.6668 92.1108 61.5012 87.355 41.3814C82.5993 21.2616 62.4337 8.80657 42.3139 13.5623C22.1941 18.3181 9.73908 38.4837 14.4948 58.6034C19.2506 78.7232 39.4162 91.1783 59.536 86.4225Z" stroke="currentColor" stroke-width="6.5"/>
          <path d="M69.8856 18.3369C79.7704 13.4104 87.3385 11.9216 89.9758 15.0868C94.8827 20.9764 80.858 40.7496 58.6506 59.2511C36.4431 77.7531 14.4627 87.9771 9.55585 82.0876C6.92515 78.9302 9.73605 71.7815 16.3324 62.9783" stroke="currentColor" stroke-width="6.5"/>
        </g>
        <defs>
          <clipPath id="clip0_9445_105">
            <rect width="100" height="100" fill="white"/>
          </clipPath>
        </defs>
      {% when "question-mark" %}
        <path d="M50 95C74.8526 95 95 74.8526 95 50C95 25.1472 74.8526 5 50 5C25.1472 5 5 25.1472 5 50C5 74.8526 25.1472 95 50 95Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M36.9043 36.5006C37.9623 33.4931 40.0505 30.9571 42.7991 29.3417C45.5477 27.7263 48.7792 27.1358 51.9215 27.6748C55.0639 28.2138 57.9142 29.8475 59.9671 32.2865C62.0204 34.7255 63.1441 37.8125 63.1391 41.0006C63.1391 50.0006 49.6391 54.5006 49.6391 54.5006" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M50 72.5H50.045" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "rocket" %}
        <path d="M92.5 8.2a1 1 0 0 0-.6-.7c-11.3-2.8-37.2 7-51.3 21.1a61 61 0 0 0-6.9 8 27 27 0 0 0-12.4 1.6C11 42.8 8 54.8 7 60a2 2 0 0 0 2 2l16.8-1.8.2 3.8q.2 1.3 1.1 2.3l6.5 6.5q1 1 2.3 1 1.9.3 3.8.3L37.8 91a2 2 0 0 0 2.2 2c5-1 17-4 21.7-14.3Q64 73 63.3 66.3q4.4-3 8-6.8C85.6 45.4 95.4 20 92.6 8.2m-33.7 33a8.8 8.8 0 1 1 12.5-12.5 8.8 8.8 0 0 1-12.5 12.5" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M19.7 69.9q-3.1.4-5.4 2.6c-3.6 3.7-4 17.2-4 17.2s13.6-.3 17.2-4a9 9 0 0 0 2.7-5.4" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "rulers" %}
        <path d="M68.5 12 12 68.6a10 10 0 0 0 0 14l5.3 5.4a10 10 0 0 0 14.1 0L88 31.5a10 10 0 0 0 0-14L82.6 12a10 10 0 0 0-14.1 0M38 81.4 30.7 74M53 66.5l-7.5-7.4m21.6-6.7-7.4-7.5M81 38.4 73.6 31" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "scissors" %}
        <path d="M24.1175 36.2351C31.9144 36.2351 38.2351 29.9144 38.2351 22.1175C38.2351 14.3207 31.9144 8 24.1175 8C16.3207 8 10 14.3207 10 22.1175C10 29.9144 16.3207 36.2351 24.1175 36.2351Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M24.1175 92.7053C31.9144 92.7053 38.2351 86.3849 38.2351 78.5877C38.2351 70.7906 31.9144 64.4702 24.1175 64.4702C16.3207 64.4702 10 70.7906 10 78.5877C10 86.3849 16.3207 92.7053 24.1175 92.7053Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M90.0001 12.7056L34.0947 68.611" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M63.9741 62.0229L89.9974 87.9992" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M34.0947 32.0947L52.3533 50.3534" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "settings" %}
        <path d="M50 62.5C56.9036 62.5 62.5 56.9036 62.5 50C62.5 43.0964 56.9036 37.5 50 37.5C43.0964 37.5 37.5 43.0964 37.5 50C37.5 56.9036 43.0964 62.5 50 62.5Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M80.8333 62.4998C80.2787 63.7566 80.1132 65.1506 80.3583 66.5023C80.6034 67.8539 81.2477 69.1012 82.2083 70.0832L82.4583 70.3332C83.2331 71.1071 83.8478 72.0262 84.2671 73.0378C84.6865 74.0495 84.9024 75.1339 84.9024 76.229C84.9024 77.3241 84.6865 78.4085 84.2671 79.4202C83.8478 80.4318 83.2331 81.3509 82.4583 82.1248C81.6844 82.8996 80.7653 83.5143 79.7536 83.9337C78.742 84.353 77.6576 84.5689 76.5625 84.5689C75.4673 84.5689 74.3829 84.353 73.3713 83.9337C72.3596 83.5143 71.4406 82.8996 70.6666 82.1248L70.4166 81.8748C69.4346 80.9143 68.1874 80.2699 66.8357 80.0248C65.4841 79.7797 64.09 79.9452 62.8333 80.4998C61.6009 81.028 60.5499 81.905 59.8096 83.0229C59.0693 84.1408 58.672 85.4507 58.6666 86.7915V87.4998C58.6666 89.71 57.7887 91.8296 56.2259 93.3924C54.663 94.9552 52.5434 95.8332 50.3333 95.8332C48.1232 95.8332 46.0035 94.9552 44.4407 93.3924C42.8779 91.8296 42 89.71 42 87.4998V87.1248C41.9677 85.7457 41.5213 84.4082 40.7188 83.2861C39.9162 82.164 38.7947 81.3094 37.5 80.8332C36.2432 80.2785 34.8492 80.1131 33.4975 80.3582C32.1459 80.6032 30.8986 81.2476 29.9166 82.2082L29.6666 82.4582C28.8927 83.233 27.9736 83.8476 26.962 84.267C25.9503 84.6864 24.8659 84.9022 23.7708 84.9022C22.6757 84.9022 21.5913 84.6864 20.5796 84.267C19.568 83.8476 18.6489 83.233 17.875 82.4582C17.1002 81.6842 16.4855 80.7652 16.0661 79.7535C15.6468 78.7419 15.4309 77.6575 15.4309 76.5623C15.4309 75.4672 15.6468 74.3828 16.0661 73.3712C16.4855 72.3595 17.1002 71.4404 17.875 70.6665L18.125 70.4165C19.0855 69.4345 19.7299 68.1873 19.975 66.8356C20.2201 65.484 20.0546 64.0899 19.5 62.8332C18.9718 61.6008 18.0948 60.5498 16.9769 59.8095C15.859 59.0691 14.5491 58.6719 13.2083 58.6665H12.5C10.2898 58.6665 8.17021 57.7885 6.6074 56.2257C5.0446 54.6629 4.16663 52.5433 4.16663 50.3332C4.16663 48.123 5.0446 46.0034 6.6074 44.4406C8.17021 42.8778 10.2898 41.9998 12.5 41.9998H12.875C14.2541 41.9676 15.5916 41.5212 16.7137 40.7186C17.8358 39.9161 18.6904 38.7946 19.1666 37.4998C19.7213 36.2431 19.8867 34.849 19.6416 33.4974C19.3966 32.1457 18.7522 30.8985 17.7916 29.9165L17.5416 29.6665C16.7668 28.8926 16.1522 27.9735 15.7328 26.9618C15.3134 25.9502 15.0976 24.8658 15.0976 23.7707C15.0976 22.6755 15.3134 21.5912 15.7328 20.5795C16.1522 19.5679 16.7668 18.6488 17.5416 17.8748C18.3156 17.1 19.2346 16.4854 20.2463 16.066C21.2579 15.6466 22.3423 15.4308 23.4375 15.4308C24.5326 15.4308 25.617 15.6466 26.6286 16.066C27.6403 16.4854 28.5594 17.1 29.3333 17.8748L29.5833 18.1248C30.5653 19.0854 31.8125 19.7298 33.1642 19.9749C34.5158 20.2199 35.9099 20.0545 37.1666 19.4998H37.5C38.7323 18.9717 39.7834 18.0947 40.5237 16.9768C41.264 15.8589 41.6613 14.5489 41.6666 13.2082V12.4998C41.6666 10.2897 42.5446 8.17008 44.1074 6.60728C45.6702 5.04448 47.7898 4.1665 50 4.1665C52.2101 4.1665 54.3297 5.04448 55.8925 6.60728C57.4553 8.17008 58.3333 10.2897 58.3333 12.4998V12.8748C58.3386 14.2156 58.7359 15.5256 59.4762 16.6434C60.2166 17.7613 61.2676 18.6383 62.5 19.1665C63.7567 19.7212 65.1508 19.8866 66.5024 19.6415C67.8541 19.3964 69.1013 18.7521 70.0833 17.7915L70.3333 17.5415C71.1072 16.7667 72.0263 16.152 73.038 15.7327C74.0496 15.3133 75.134 15.0974 76.2291 15.0974C77.3243 15.0974 78.4087 15.3133 79.4203 15.7327C80.432 16.152 81.351 16.7667 82.125 17.5415C82.8998 18.3154 83.5144 19.2345 83.9338 20.2462C84.3532 21.2578 84.569 22.3422 84.569 23.4373C84.569 24.5325 84.3532 25.6169 83.9338 26.6285C83.5144 27.6402 82.8998 28.5592 82.125 29.3332L81.875 29.5832C80.9144 30.5652 80.27 31.8124 80.0249 33.1641C79.7799 34.5157 79.9453 35.9098 80.5 37.1665V37.4998C81.0281 38.7322 81.9052 39.7832 83.023 40.5236C84.1409 41.2639 85.4509 41.6612 86.7916 41.6665H87.5C89.7101 41.6665 91.8297 42.5445 93.3925 44.1073C94.9553 45.6701 95.8333 47.7897 95.8333 49.9998C95.8333 52.21 94.9553 54.3296 93.3925 55.8924C91.8297 57.4552 89.7101 58.3332 87.5 58.3332H87.125C85.7842 58.3385 84.4742 58.7358 83.3564 59.4761C82.2385 60.2164 81.3615 61.2675 80.8333 62.4998Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "shirt" %}
        <path d="M83.5556 92H17.4444C14.9396 92 12.5374 91.005 10.7662 89.2338C8.99504 87.4626 8 85.0604 8 82.5556V11.7222C8 8.88889 9.88889 7 12.7222 7H69.3889C72.2222 7 74.1111 8.88889 74.1111 11.7222V82.5556C74.1111 85.0604 75.1062 87.4626 76.8773 89.2338C78.6485 91.005 81.0507 92 83.5556 92ZM83.5556 92C86.0604 92 88.4626 91.005 90.2338 89.2338C92.005 87.4626 93 85.0604 93 82.5556V25.8889C93 23.0556 91.1111 21.1667 88.2778 21.1667H74.1111" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M26.8887 7V11.7222C26.8887 15.4795 28.3812 19.0828 31.038 21.7396C33.6948 24.3963 37.2981 25.8889 41.0553 25.8889C44.8126 25.8889 48.4159 24.3963 51.0727 21.7396C53.7295 19.0828 55.222 15.4795 55.222 11.7222V7M41.0553 44.7778H41.1026M41.0553 63.6667H41.1026" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "shop-alt" %}
        <path d="M9.8 40.7v42c0 5.1 4 9.3 8.9 9.3h62.6c5 0 9-4.2 9-9.3v-42" stroke="currentColor" stroke-width="6.5"/><path d="M62.7 92V64a9 9 0 0 0-9-9.3h-9a9 9 0 0 0-8.9 9.3v28" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="16"/><path d="M93.9 37.7 86.3 10q-.6-1.9-2.6-2h-18l2 26.6Q68 36 69 36.7c1.8 1.1 5.2 3.1 7.8 4 4.6 1.4 11.2.9 15 .4 1.6-.2 2.5-1.8 2.1-3.4Z" stroke="currentColor" stroke-width="6.5"/><path d="M59 40.7c2.5-.8 5.7-2.7 7.5-3.8q1.4-1 1.2-2.6L65.7 8H34.3l-2.1 26.3Q32 36 33.5 37c1.8 1 5 3 7.6 3.8a26 26 0 0 0 17.8 0Z" stroke="currentColor" stroke-width="6.5"/><path d="M13.7 10 6 37.7c-.4 1.6.5 3.2 2.1 3.4 3.8.5 10.4 1 15-.4 2.6-.9 6-2.9 7.8-4q1.1-.7 1.2-2L34.4 8H16.3q-2 .1-2.6 2Z" stroke="currentColor" stroke-width="6.5"/>
      {% when "shopping-bag" %}
        <path d="M14.5769 40.495C14.9456 35.923 18.7764 32.3999 23.3791 32.3999H76.6211C81.2236 32.3999 85.0543 35.923 85.4229 40.495L88.9711 84.495C89.3839 89.6166 85.3249 93.9999 80.1688 93.9999H19.8311C14.6753 93.9999 10.6159 89.6166 11.0289 84.495L14.5769 40.495Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M67.6611 45.6V23.6C67.6611 13.8798 59.7538 6 50 6C40.246 6 32.3389 13.8798 32.3389 23.6V45.6" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "shopping-cart" %}
        <path d="M6 6H17.4155C18.4322 6 19.2871 6.76284 19.4025 7.77302L21.2307 23.7778M21.2307 23.7778L27.7413 66.5234C27.8901 67.5004 28.7302 68.2223 29.7184 68.2223H83.5572C84.512 68.2223 85.3336 67.5473 85.5191 66.6107L93.5271 26.1662C93.7719 24.9297 92.8257 23.7778 91.5652 23.7778H21.2307Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <circle cx="72" cy="89" r="3.75" stroke="currentColor" stroke-width="6.5"/>
        <circle cx="43" cy="89" r="3.75" stroke="currentColor" stroke-width="6.5"/>
      {% when "smile" %}
        <path d="M50 95C74.8526 95 95 74.8526 95 50C95 25.1472 74.8526 5 50 5C25.1472 5 5 25.1472 5 50C5 74.8526 25.1472 95 50 95Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M32 59C32 59 38.75 68 50 68C61.25 68 68 59 68 59" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M36.5 36.5H36.545" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M63.5 36.5H63.545" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "star" %}
        <path d="M34.9844 32.9628L46.4135 9.28018C47.8805 6.23994 52.1195 6.23994 53.5864 9.28018L65.0154 32.9628L90.5746 36.784C93.8539 37.2742 95.1607 41.3973 92.7869 43.7623L74.2955 62.184L78.6594 88.2085C79.2199 91.5507 75.7901 94.0991 72.8558 92.5207L50 80.2268L27.1441 92.5207C24.2098 94.0991 20.78 91.5507 21.3404 88.2085L25.7043 62.184L7.21331 43.7623C4.83916 41.3973 6.14618 37.2742 9.42549 36.784L34.9844 32.9628Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "sun" %}
        <mask id="mask0_9451_1823" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="5" y="5" width="90" height="90">
          <path d="M8.25 91.75V8.25H91.75V91.75H8.25Z" fill="white" stroke="white" stroke-width="6.5"/>
        </mask>
        <g mask="url(#mask0_9451_1823)">
          <path d="M50 68.75C60.3553 68.75 68.75 60.3553 68.75 50C68.75 39.6447 60.3553 31.25 50 31.25C39.6447 31.25 31.25 39.6447 31.25 50C31.25 60.3553 39.6447 68.75 50 68.75Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M50 8.75V16.25V8.75Z" fill="currentColor"/>
          <path d="M50 8.75V16.25" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M50 83.75V91.25V83.75Z" fill="currentColor"/>
          <path d="M50 83.75V91.25" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M20.8242 20.8257L26.1492 26.1507L20.8242 20.8257Z" fill="currentColor"/>
          <path d="M20.8242 20.8257L26.1492 26.1507" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M73.8516 73.8496L79.1766 79.1746L73.8516 73.8496Z" fill="currentColor"/>
          <path d="M73.8516 73.8496L79.1766 79.1746" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M8.75 50H16.25H8.75Z" fill="currentColor"/>
          <path d="M8.75 50H16.25" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M83.75 50H91.25H83.75Z" fill="currentColor"/>
          <path d="M83.75 50H91.25" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M20.8242 79.1746L26.1492 73.8496L20.8242 79.1746Z" fill="currentColor"/>
          <path d="M20.8242 79.1746L26.1492 73.8496" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M73.8516 26.1507L79.1766 20.8257L73.8516 26.1507Z" fill="currentColor"/>
          <path d="M73.8516 26.1507L79.1766 20.8257" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        </g>
      {% when "support" %}
        <path d="M14.22 49.65V39.6c0-8.91 3.77-17.46 10.48-23.77A37 37 0 0 1 50 6c9.49 0 18.6 3.54 25.3 9.84a32.6 32.6 0 0 1 10.48 23.77v10.04" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/><path d="M12.62 49.65h8.81V73.2h-8.8a5.8 5.8 0 0 1-3.98-1.55A5 5 0 0 1 7 67.92V54.93c0-1.4.6-2.74 1.65-3.73a5.8 5.8 0 0 1 3.97-1.55ZM87.38 73.2h-8.81V49.66h8.8c1.5 0 2.93.55 3.98 1.54A5 5 0 0 1 93 54.93v13c0 1.4-.6 2.74-1.65 3.73a5.8 5.8 0 0 1-3.97 1.55Zm-.45.68v8.8c0 1.87-.8 3.66-2.2 4.98a7.8 7.8 0 0 1-5.3 2.06H58.55m-3.77-4.65h-7.69c-2.07 0-3.74 1.58-3.74 3.52v1.89c0 1.94 1.67 3.52 3.74 3.52h7.69c2.07 0 3.75-1.58 3.75-3.52v-1.89c0-1.94-1.68-3.52-3.75-3.52Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/>
      {% when "tag" %}
        <path d="M6 45.5729V15.6855C6 10.3363 10.3363 6 15.6855 6H45.5729C48.1415 6 50.605 7.02041 52.4215 8.83683L91.1634 47.5788C94.9455 51.3609 94.9455 57.4938 91.1634 61.2759L61.2759 91.1634C57.4938 94.9455 51.3609 94.9455 47.5788 91.1634L8.83683 52.4215C7.02041 50.605 6 48.1415 6 45.5729Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <circle cx="26.3395" cy="26.3396" r="3.25" stroke="currentColor" stroke-width="5.12256"/>
      {% when "telephone" %}
        <path fill-rule="evenodd" clip-rule="evenodd" d="M7.82921 9.24791C7.99769 8.37568 8.7401 7.72275 9.64067 7.65476L31.406 6.01171C33.5223 5.85193 35.397 7.3452 35.6766 9.41343L38.2651 28.5638C38.4291 29.7769 38.0089 30.9958 37.1286 31.8611L28.6106 40.2338C27.0488 41.7689 27.0488 44.2579 28.6106 45.793L55.0602 71.7914C56.622 73.3265 59.1542 73.3265 60.716 71.7914L69.3472 63.3074C70.1627 62.5058 71.2907 62.0893 72.4415 62.1648L90.2671 63.335C92.3833 63.4739 94.0212 65.2126 93.9998 67.2973L93.7609 90.3243C93.7511 91.2631 93.0674 92.064 92.1286 92.2364C68.7056 96.5379 43.53 93.713 24.9312 75.4315C6.30365 57.1217 3.3794 32.2838 7.82921 9.24791Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "truck" %}
        <path d="M82.014 81.4682C82.9266 81.4249 83.7816 81.3819 84.5788 81.3392C89.4574 81.0791 93.3634 77.4062 93.8209 72.5421C94.3446 66.9748 94.9026 58.806 94.9883 48.7181C95.0031 46.9622 94.6465 45.2166 93.8269 43.6637C91.7033 39.6399 86.7368 31.4649 79.2625 26.5346C77.6811 25.4914 75.8067 25.0363 73.9135 24.9726C68.4795 24.7899 62.4917 24.654 56.4004 24.5537" stroke="currentColor" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M5.03186 52.1426C5.01116 53.301 5 54.4714 5 55.6483C5 63.1393 5.45204 69.0527 5.94076 73.257C6.46807 77.7934 10.1829 81.0538 14.7429 81.3049C15.7279 81.3592 16.8088 81.4149 17.9856 81.4706" stroke="currentColor" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M72.1487 35.3834C76.8229 38.0336 80.2299 43.139 82.2236 46.8425C83.4471 49.1154 82.0562 51.7756 79.4846 51.9976C75.9714 52.3008 72.7507 52.0781 70.4809 51.7979C68.5095 51.5542 67.1426 49.8378 67.1426 47.8514V38.4697C67.1426 36.5534 68.6959 35 70.6123 35C71.1489 35 71.6818 35.1187 72.1487 35.3834Z" stroke="currentColor" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M56.4286 52.1429H30.7143H5V28.5714C5 22.1924 5.20179 17.2929 5.44154 13.7231C5.74799 9.15986 9.26649 5.71865 13.8318 5.44458C17.7758 5.20779 23.3384 5 30.7143 5C38.0902 5 43.6526 5.20779 47.5968 5.44458C52.1621 5.71865 55.6805 9.15986 55.9869 13.7231C56.2267 17.2929 56.4286 22.1924 56.4286 28.5714V52.1429Z" stroke="currentColor" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M41.3779 82.1011C44.0901 82.1283 46.9641 82.1437 50.0001 82.1437C53.0372 82.1437 55.9121 82.1283 58.6251 82.1011" stroke="currentColor" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M58.5713 83.2144C58.5713 89.7236 63.8479 95.0001 70.357 95.0001C76.8661 95.0001 82.1427 89.7236 82.1427 83.2144C82.1427 76.7053 76.8661 71.4287 70.357 71.4287C63.8479 71.4287 58.5713 76.7053 58.5713 83.2144Z" stroke="currentColor" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M17.8564 83.2144C17.8564 89.7236 23.133 95.0001 29.6422 95.0001C36.1513 95.0001 41.4279 89.7236 41.4279 83.2144C41.4279 76.7053 36.1513 71.4287 29.6422 71.4287C23.133 71.4287 17.8564 76.7053 17.8564 83.2144Z" stroke="currentColor" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "wallet" %}
        <path d="M83.8462 27.9541H16.1538C10.546 27.9541 6 32.5093 6 38.1284V78.8257C6 84.4449 10.546 89.0001 16.1538 89.0001H83.8462C89.454 89.0001 94 84.4449 94 78.8257V38.1284C94 32.5093 89.454 27.9541 83.8462 27.9541Z" stroke="currentColor" stroke-width="6.5" stroke-linejoin="round"/>
        <path d="M82.8646 27.9538V21.5948C82.8641 20.0352 82.5201 18.4949 81.8571 17.0839C81.1941 15.6728 80.2285 14.4258 79.0291 13.4317C77.8298 12.4376 76.4262 11.7211 74.9187 11.3331C73.4111 10.9452 71.8367 10.8954 70.3077 11.1874L14.5969 20.7152C12.1776 21.1772 9.99502 22.4706 8.4256 24.3725C6.85618 26.2744 5.99833 28.6655 6 31.1333V41.5196" stroke="currentColor" stroke-width="6.5" stroke-linejoin="round"/>
        <path d="M73.0071 55.0117C73.6884 54.8759 74.3952 54.9454 75.0374 55.2119C75.6795 55.4785 76.229 55.9308 76.6165 56.5117C77.004 57.0929 77.2112 57.7773 77.2112 58.4775C77.2111 59.4164 76.8394 60.3156 76.179 60.9775C75.5185 61.6393 74.6238 62.0097 73.6926 62.0098C72.998 62.0098 72.3181 61.8034 71.7395 61.416C71.161 61.0286 70.7088 60.4772 70.4417 59.8311C70.1746 59.1849 70.1043 58.4735 70.2405 57.7871C70.3768 57.1005 70.7136 56.4702 71.2063 55.9766C71.6989 55.4831 72.3258 55.1475 73.0071 55.0117Z" stroke="currentColor" stroke-width="6.5"/>
      {% when "washing" %}
        <path d="M89.9996 14.8888L90 86C90 90.9092 86.0204 94.8888 81.1112 94.8888H18.8889C13.9797 94.8888 10 90.9092 10 86V14.8889C10 9.97968 13.9797 6 18.8889 6H81.1108C86.02 6 89.9996 9.97968 89.9996 14.8888Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M76.6655 19.3693L76.7027 19.3281" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M50.0014 81.5563C64.7286 81.5563 76.6677 69.6175 76.6677 54.8899C76.6677 40.1622 64.7286 28.2231 50.0014 28.2231C35.2736 28.2231 23.3345 40.1622 23.3345 54.8899C23.3345 69.6175 35.2736 81.5563 50.0014 81.5563Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M49.9986 68.2219C42.6346 68.2219 36.6655 62.2527 36.6655 54.8887" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "yoga" %}
        <path d="M62.8605 65.0176L67.1467 74.2427C67.1467 74.2427 86.432 78.3387 86.432 88.0807C86.432 94.9997 77.8606 94.9997 77.8606 94.9997H55.0035L43.7534 88.7498" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M37.1432 65.0176L32.8575 74.2427C32.8575 74.2427 13.5718 78.3387 13.5718 88.0807C13.5718 94.9997 22.1432 94.9997 22.1432 94.9997H32.5003L43.7503 88.7498L57.5007 79.9996" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M5 69.6297C5 69.6297 15.7143 67.3235 22.1429 65.0169C28.5715 32.7287 47.8569 35.0351 50.0002 35.0351C52.1431 35.0351 71.4285 32.7287 77.8571 65.0169C84.2857 67.3235 95 69.6297 95 69.6297" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M50.0005 25C55.5236 25 60.0004 20.5228 60.0004 15C60.0004 9.47718 55.5236 5 50.0005 5C44.4774 5 40.0005 9.47718 40.0005 15C40.0005 20.5228 44.4774 25 50.0005 25Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "activity" %}
        <path d="M7 54.8889H18.1033C18.5439 54.8889 18.9327 55.1773 19.0604 55.599L29.677 90.6532C29.9684 91.6155 31.3383 91.5942 31.5997 90.6233L53.3646 9.77627C53.6337 8.77657 55.0583 8.79457 55.3021 9.80075L66.0413 54.1244C66.1499 54.573 66.5516 54.8889 67.0131 54.8889H78.0718" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/>
        <rect x="78.25" y="46.25" width="15.5" height="15.5" rx="7.75" stroke="currentColor" stroke-width="6.5"/>
      {% when "archive" %}
        <path d="M86.818 33.4546L86.818 86.6364L13.1816 86.6364L13.1816 33.4546" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M95 13L5 13L5 33.4545L95 33.4545L95 13Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M41.8184 49.8179L58.182 49.8179" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "arrow-down-cricle" %}
        <path d="M50 5C25.1474 4.99999 5.00001 25.1474 5 50C5 74.8528 25.1474 95 50 95C74.8528 95 95 74.8528 95 50C95 25.1474 74.8528 5 50 5Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M32 50L50 68L68 50" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M50 32L50 68" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "arrow-left" %}
        <path d="M95 50L5 50" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M35 80L5 50L35 20" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "arrow-left-circle" %}
        <path d="M95 50C95 25.1474 74.8526 5 50 5C25.1472 5 5 25.1474 5 50C5 74.8528 25.1472 95 50 95C74.8526 95 95 74.8528 95 50Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M50 32L32 50L50 68" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M68 50L32 50" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "arrow-right" %}
        <path d="M5 50H95" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M65 20L95 50L65 80" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "arrow-right-circle" %}
        <path d="M5 50C5 74.8526 25.1474 95 50 95C74.8528 95 95 74.8526 95 50C95 25.1472 74.8528 5 50 5C25.1474 5 5 25.1472 5 50Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M50 68L68 50L50 32" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M32 50L68 50" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "arrow-up-circle" %}
        <path d="M50 95C74.8526 95 95 74.8526 95 50C95 25.1472 74.8526 5 50 5C25.1472 5 5 25.1472 5 50C5 74.8526 25.1472 95 50 95Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M68 50L50 32L32 50" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M50 68V32" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "chevron-left" %}
        <path d="M72.0001 95L27 49.9999L72.0001 5" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "chevron-right" %}
        <path d="M26.9999 5L72 50.0001L26.9999 95" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "trending-down" %}
        <path d="M23 90H77" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/>
        <path d="M90.098 69.8082L58.5439 28.6674C56.1422 25.5361 51.4237 25.5361 49.022 28.6674L39.4307 41.1728C37.029 44.3041 32.3105 44.3041 29.9088 41.1728L6 10" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M75.1533 73.3768H94.1002V54.6436" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "trending-up" %}
        <path d="M23 90H77" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/>
        <path d="M90.5198 13L58.7196 53.0075C56.3174 56.0296 51.7278 56.0296 49.3256 53.0075L39.5105 40.6592C37.1084 37.6371 32.5187 37.6371 30.1166 40.6592L6 71" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M75.6045 10H94.1045V28.5" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "tv" %}
        <path d="M69.1765 10L50.3529 28.8235L31.5294 10M64.4706 28.8235V90M78.5882 66.4706V66.5177M78.5882 52.3529V52.4M8 38.2353C8 35.7391 8.99159 33.3452 10.7566 31.5802C12.5217 29.8151 14.9156 28.8235 17.4118 28.8235H83.2941C85.7903 28.8235 88.1842 29.8151 89.9492 31.5802C91.7143 33.3452 92.7059 35.7391 92.7059 38.2353V80.5882C92.7059 83.0844 91.7143 85.4783 89.9492 87.2434C88.1842 89.0084 85.7903 90 83.2941 90H17.4118C14.9156 90 12.5217 89.0084 10.7566 87.2434C8.99159 85.4783 8 83.0844 8 80.5882V38.2353Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when "zap" %}
        <path d="M55 5L10 59H50.5L46 95L91 41H50.5L55 5Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when 'faq' %}
        <mask id="path-1-outside-1_9555_27" maskUnits="userSpaceOnUse" x="4" y="4" width="92" height="93" fill="black">
          <rect fill="white" x="4" y="4" width="92" height="93"/>
          <path d="M39.6571 5C35.5803 5 31.9282 7.93452 31.9282 11.9338V40.8095H12.0256C8.33948 40.8095 5 43.47 5 47.1348V81.0482C5 84.7131 8.33948 87.3735 12.0256 87.3735C12.312 87.3736 12.5957 87.3173 12.8604 87.2078C13.1251 87.0982 13.3656 86.9376 13.5682 86.7351C13.7708 86.5326 13.9315 86.2921 14.0411 86.0275C14.1508 85.7628 14.2072 85.4792 14.2072 85.1927C14.2072 84.9063 14.1508 84.6226 14.0411 84.358C13.9315 84.0933 13.7708 83.8529 13.5682 83.6504C13.3656 83.4479 13.1251 83.2872 12.8604 83.1777C12.5957 83.0681 12.312 83.0118 12.0256 83.0119C10.343 83.0119 9.36178 81.9788 9.36178 81.0482V47.1348C9.36178 46.2043 10.343 45.1696 12.0256 45.1696H31.9282V50.7813C31.9282 54.7806 35.5803 57.7091 39.6571 57.7091H56.2547V81.0482C56.2547 81.9788 55.2746 83.0119 53.5924 83.0119H24.3245C24.0375 83.012 23.7533 83.0687 23.4883 83.1788C23.2233 83.2888 22.9826 83.4502 22.78 83.6535L15.1761 91.2574C14.9647 91.4578 14.7956 91.6985 14.6787 91.9653C14.5619 92.2322 14.4997 92.5197 14.4958 92.8109C14.4919 93.1022 14.5464 93.3913 14.6561 93.6611C14.7658 93.931 14.9284 94.1761 15.1344 94.3821C15.3403 94.588 15.5855 94.7507 15.8553 94.8603C16.1251 94.97 16.4142 95.0245 16.7055 95.0206C16.9967 95.0167 17.2843 94.9546 17.5511 94.8377C17.8179 94.7209 18.0586 94.5518 18.259 94.3404L25.2275 87.3735H53.5924C57.2784 87.3735 60.6165 84.7131 60.6165 81.0482V57.7091H72.2799L80.3495 65.7833C80.5499 65.9947 80.7906 66.1638 81.0574 66.2806C81.3243 66.3974 81.6118 66.4596 81.903 66.4635C82.1943 66.4674 82.4834 66.4129 82.7532 66.3032C83.0231 66.1935 83.2682 66.0309 83.4742 65.8249C83.6801 65.6189 83.8427 65.3738 83.9524 65.104C84.0621 64.8341 84.1166 64.545 84.1127 64.2538C84.1088 63.9625 84.0466 63.675 83.9298 63.4082C83.8129 63.1414 83.6438 62.9007 83.4325 62.7003L74.7213 53.989C74.3137 53.5799 73.7605 53.3492 73.183 53.3475H39.6571C37.5842 53.3475 36.2886 52.0462 36.2886 50.7813V11.9338C36.2886 10.6689 37.5842 9.36163 39.6571 9.36163H87.2714C89.3444 9.36163 90.6383 10.6689 90.6383 11.9338V50.7813C90.6383 52.0462 89.3444 53.3475 87.2714 53.3475C86.6936 53.3483 86.1397 53.5784 85.7314 53.9873C85.3231 54.3962 85.0938 54.9504 85.0938 55.5283C85.0938 56.1061 85.3231 56.6604 85.7314 57.0692C86.1397 57.4781 86.6936 57.7083 87.2714 57.7091C91.348 57.7091 95 54.7806 95 50.7813V11.9338C95 7.93452 91.348 5 87.2714 5H39.6571ZM63.4605 15.0003C58.9592 15.0003 55.2677 18.6936 55.2677 23.1947C55.2597 23.486 55.3102 23.7759 55.4162 24.0473C55.5222 24.3187 55.6815 24.5662 55.8847 24.775C56.0879 24.9838 56.331 25.1498 56.5994 25.2631C56.8678 25.3765 57.1563 25.4349 57.4476 25.4349C57.739 25.4349 58.0274 25.3765 58.2959 25.2631C58.5643 25.1498 58.8073 24.9838 59.0105 24.775C59.2138 24.5662 59.3731 24.3187 59.479 24.0473C59.585 23.7759 59.6355 23.486 59.6276 23.1947C59.6276 21.0506 61.3163 19.362 63.4605 19.362C65.6045 19.362 67.2992 21.0506 67.2992 23.1947C67.2992 25.2429 65.7519 26.8775 63.7446 27.02C63.6415 27.0058 63.5375 26.999 63.4335 26.9996C63.1294 27.0039 62.8295 27.0717 62.5532 27.1988C62.2768 27.3258 62.0301 27.5093 61.8289 27.7373C61.605 27.9804 61.4394 28.2712 61.3444 28.5877C61.2493 28.9042 61.2275 29.2381 61.2805 29.5643V34.1213C61.2847 34.6969 61.5163 35.2474 61.9248 35.653C62.3334 36.0585 62.8856 36.2861 63.4612 36.2861C64.0369 36.2861 64.5891 36.0585 64.9976 35.653C65.4061 35.2474 65.6378 34.6969 65.642 34.1213V31.0878C69.0963 30.1229 71.6609 26.9415 71.6609 23.1954C71.6609 18.6943 67.9615 15.0003 63.4605 15.0003ZM63.4605 38.06C63.0297 38.0599 62.6031 38.1446 62.205 38.3093C61.8069 38.4741 61.4451 38.7157 61.1404 39.0202C60.8357 39.3248 60.5939 39.6864 60.4289 40.0844C60.2639 40.4824 60.179 40.909 60.1789 41.3399C60.1788 41.7708 60.2636 42.1976 60.4285 42.5958C60.5934 42.9939 60.8351 43.3557 61.1399 43.6604C61.4446 43.9652 61.8064 44.2069 62.2046 44.3717C62.6028 44.5366 63.0296 44.6213 63.4605 44.6212C64.3306 44.621 65.165 44.2752 65.7801 43.6598C66.3951 43.0444 66.7406 42.2099 66.7404 41.3399C66.7402 40.4701 66.3946 39.6359 65.7795 39.0209C65.1645 38.4058 64.3304 38.0602 63.4605 38.06ZM18.2681 63.471C17.6857 63.4711 17.1091 63.5859 16.5711 63.8088C16.0331 64.0318 15.5443 64.3585 15.1326 64.7703C14.7208 65.1822 14.3943 65.6711 14.1715 66.2092C13.9487 66.7472 13.8341 67.3239 13.8342 67.9063C13.8344 69.0821 14.3016 70.2098 15.1331 71.0412C15.9646 71.8726 17.0922 72.3398 18.2681 72.34C19.4442 72.3402 20.5722 71.8732 21.404 71.0417C22.2357 70.2102 22.7031 69.0824 22.7033 67.9063C22.7034 67.3238 22.5888 66.747 22.3659 66.2088C22.1431 65.6707 21.8164 65.1817 21.4045 64.7698C20.9926 64.358 20.5036 64.0313 19.9655 63.8084C19.4273 63.5856 18.8505 63.4709 18.2681 63.471ZM32.8043 63.471C31.6282 63.4712 30.5003 63.9386 29.6688 64.7704C28.8373 65.6021 28.3703 66.7302 28.3705 67.9063C28.3707 69.0821 28.8379 70.2097 29.6694 71.0412C30.5008 71.8726 31.6285 72.3398 32.8043 72.34C33.3867 72.3401 33.9633 72.2255 34.5014 72.0027C35.0395 71.78 35.5284 71.4534 35.9402 71.0417C36.3521 70.63 36.6788 70.1412 36.9018 69.6032C37.1247 69.0652 37.2395 68.4886 37.2396 67.9063C37.2397 67.3238 37.1251 66.747 36.9022 66.2088C36.6794 65.6707 36.3526 65.1817 35.9408 64.7698C35.5289 64.358 35.0399 64.0313 34.5017 63.8084C33.9636 63.5856 33.3868 63.4709 32.8043 63.471ZM47.3405 63.471C46.7581 63.4709 46.1813 63.5856 45.6431 63.8084C45.105 64.0313 44.616 64.358 44.2041 64.7698C43.7923 65.1817 43.4656 65.6707 43.2427 66.2088C43.0198 66.747 42.9052 67.3238 42.9053 67.9063C42.9055 69.0824 43.3729 70.2102 44.2047 71.0417C45.0364 71.8732 46.1644 72.3402 47.3405 72.34C48.5164 72.3398 49.644 71.8726 50.4755 71.0412C51.3069 70.2097 51.7741 69.0821 51.7743 67.9063C51.7745 66.7302 51.3075 65.6021 50.476 64.7704C49.6445 63.9386 48.5166 63.4712 47.3405 63.471Z"/>
        </mask>
        <path d="M39.6571 5C35.5803 5 31.9282 7.93452 31.9282 11.9338V40.8095H12.0256C8.33948 40.8095 5 43.47 5 47.1348V81.0482C5 84.7131 8.33948 87.3735 12.0256 87.3735C12.312 87.3736 12.5957 87.3173 12.8604 87.2078C13.1251 87.0982 13.3656 86.9376 13.5682 86.7351C13.7708 86.5326 13.9315 86.2921 14.0411 86.0275C14.1508 85.7628 14.2072 85.4792 14.2072 85.1927C14.2072 84.9063 14.1508 84.6226 14.0411 84.358C13.9315 84.0933 13.7708 83.8529 13.5682 83.6504C13.3656 83.4479 13.1251 83.2872 12.8604 83.1777C12.5957 83.0681 12.312 83.0118 12.0256 83.0119C10.343 83.0119 9.36178 81.9788 9.36178 81.0482V47.1348C9.36178 46.2043 10.343 45.1696 12.0256 45.1696H31.9282V50.7813C31.9282 54.7806 35.5803 57.7091 39.6571 57.7091H56.2547V81.0482C56.2547 81.9788 55.2746 83.0119 53.5924 83.0119H24.3245C24.0375 83.012 23.7533 83.0687 23.4883 83.1788C23.2233 83.2888 22.9826 83.4502 22.78 83.6535L15.1761 91.2574C14.9647 91.4578 14.7956 91.6985 14.6787 91.9653C14.5619 92.2322 14.4997 92.5197 14.4958 92.8109C14.4919 93.1022 14.5464 93.3913 14.6561 93.6611C14.7658 93.931 14.9284 94.1761 15.1344 94.3821C15.3403 94.588 15.5855 94.7507 15.8553 94.8603C16.1251 94.97 16.4142 95.0245 16.7055 95.0206C16.9967 95.0167 17.2843 94.9546 17.5511 94.8377C17.8179 94.7209 18.0586 94.5518 18.259 94.3404L25.2275 87.3735H53.5924C57.2784 87.3735 60.6165 84.7131 60.6165 81.0482V57.7091H72.2799L80.3495 65.7833C80.5499 65.9947 80.7906 66.1638 81.0574 66.2806C81.3243 66.3974 81.6118 66.4596 81.903 66.4635C82.1943 66.4674 82.4834 66.4129 82.7532 66.3032C83.0231 66.1935 83.2682 66.0309 83.4742 65.8249C83.6801 65.6189 83.8427 65.3738 83.9524 65.104C84.0621 64.8341 84.1166 64.545 84.1127 64.2538C84.1088 63.9625 84.0466 63.675 83.9298 63.4082C83.8129 63.1414 83.6438 62.9007 83.4325 62.7003L74.7213 53.989C74.3137 53.5799 73.7605 53.3492 73.183 53.3475H39.6571C37.5842 53.3475 36.2886 52.0462 36.2886 50.7813V11.9338C36.2886 10.6689 37.5842 9.36163 39.6571 9.36163H87.2714C89.3444 9.36163 90.6383 10.6689 90.6383 11.9338V50.7813C90.6383 52.0462 89.3444 53.3475 87.2714 53.3475C86.6936 53.3483 86.1397 53.5784 85.7314 53.9873C85.3231 54.3962 85.0938 54.9504 85.0938 55.5283C85.0938 56.1061 85.3231 56.6604 85.7314 57.0692C86.1397 57.4781 86.6936 57.7083 87.2714 57.7091C91.348 57.7091 95 54.7806 95 50.7813V11.9338C95 7.93452 91.348 5 87.2714 5H39.6571ZM63.4605 15.0003C58.9592 15.0003 55.2677 18.6936 55.2677 23.1947C55.2597 23.486 55.3102 23.7759 55.4162 24.0473C55.5222 24.3187 55.6815 24.5662 55.8847 24.775C56.0879 24.9838 56.331 25.1498 56.5994 25.2631C56.8678 25.3765 57.1563 25.4349 57.4476 25.4349C57.739 25.4349 58.0274 25.3765 58.2959 25.2631C58.5643 25.1498 58.8073 24.9838 59.0105 24.775C59.2138 24.5662 59.3731 24.3187 59.479 24.0473C59.585 23.7759 59.6355 23.486 59.6276 23.1947C59.6276 21.0506 61.3163 19.362 63.4605 19.362C65.6045 19.362 67.2992 21.0506 67.2992 23.1947C67.2992 25.2429 65.7519 26.8775 63.7446 27.02C63.6415 27.0058 63.5375 26.999 63.4335 26.9996C63.1294 27.0039 62.8295 27.0717 62.5532 27.1988C62.2768 27.3258 62.0301 27.5093 61.8289 27.7373C61.605 27.9804 61.4394 28.2712 61.3444 28.5877C61.2493 28.9042 61.2275 29.2381 61.2805 29.5643V34.1213C61.2847 34.6969 61.5163 35.2474 61.9248 35.653C62.3334 36.0585 62.8856 36.2861 63.4612 36.2861C64.0369 36.2861 64.5891 36.0585 64.9976 35.653C65.4061 35.2474 65.6378 34.6969 65.642 34.1213V31.0878C69.0963 30.1229 71.6609 26.9415 71.6609 23.1954C71.6609 18.6943 67.9615 15.0003 63.4605 15.0003ZM63.4605 38.06C63.0297 38.0599 62.6031 38.1446 62.205 38.3093C61.8069 38.4741 61.4451 38.7157 61.1404 39.0202C60.8357 39.3248 60.5939 39.6864 60.4289 40.0844C60.2639 40.4824 60.179 40.909 60.1789 41.3399C60.1788 41.7708 60.2636 42.1976 60.4285 42.5958C60.5934 42.9939 60.8351 43.3557 61.1399 43.6604C61.4446 43.9652 61.8064 44.2069 62.2046 44.3717C62.6028 44.5366 63.0296 44.6213 63.4605 44.6212C64.3306 44.621 65.165 44.2752 65.7801 43.6598C66.3951 43.0444 66.7406 42.2099 66.7404 41.3399C66.7402 40.4701 66.3946 39.6359 65.7795 39.0209C65.1645 38.4058 64.3304 38.0602 63.4605 38.06ZM18.2681 63.471C17.6857 63.4711 17.1091 63.5859 16.5711 63.8088C16.0331 64.0318 15.5443 64.3585 15.1326 64.7703C14.7208 65.1822 14.3943 65.6711 14.1715 66.2092C13.9487 66.7472 13.8341 67.3239 13.8342 67.9063C13.8344 69.0821 14.3016 70.2098 15.1331 71.0412C15.9646 71.8726 17.0922 72.3398 18.2681 72.34C19.4442 72.3402 20.5722 71.8732 21.404 71.0417C22.2357 70.2102 22.7031 69.0824 22.7033 67.9063C22.7034 67.3238 22.5888 66.747 22.3659 66.2088C22.1431 65.6707 21.8164 65.1817 21.4045 64.7698C20.9926 64.358 20.5036 64.0313 19.9655 63.8084C19.4273 63.5856 18.8505 63.4709 18.2681 63.471ZM32.8043 63.471C31.6282 63.4712 30.5003 63.9386 29.6688 64.7704C28.8373 65.6021 28.3703 66.7302 28.3705 67.9063C28.3707 69.0821 28.8379 70.2097 29.6694 71.0412C30.5008 71.8726 31.6285 72.3398 32.8043 72.34C33.3867 72.3401 33.9633 72.2255 34.5014 72.0027C35.0395 71.78 35.5284 71.4534 35.9402 71.0417C36.3521 70.63 36.6788 70.1412 36.9018 69.6032C37.1247 69.0652 37.2395 68.4886 37.2396 67.9063C37.2397 67.3238 37.1251 66.747 36.9022 66.2088C36.6794 65.6707 36.3526 65.1817 35.9408 64.7698C35.5289 64.358 35.0399 64.0313 34.5017 63.8084C33.9636 63.5856 33.3868 63.4709 32.8043 63.471ZM47.3405 63.471C46.7581 63.4709 46.1813 63.5856 45.6431 63.8084C45.105 64.0313 44.616 64.358 44.2041 64.7698C43.7923 65.1817 43.4656 65.6707 43.2427 66.2088C43.0198 66.747 42.9052 67.3238 42.9053 67.9063C42.9055 69.0824 43.3729 70.2102 44.2047 71.0417C45.0364 71.8732 46.1644 72.3402 47.3405 72.34C48.5164 72.3398 49.644 71.8726 50.4755 71.0412C51.3069 70.2097 51.7741 69.0821 51.7743 67.9063C51.7745 66.7302 51.3075 65.6021 50.476 64.7704C49.6445 63.9386 48.5166 63.4712 47.3405 63.471Z" fill="currentColor"/>
        <path d="M39.6571 5C35.5803 5 31.9282 7.93452 31.9282 11.9338V40.8095H12.0256C8.33948 40.8095 5 43.47 5 47.1348V81.0482C5 84.7131 8.33948 87.3735 12.0256 87.3735C12.312 87.3736 12.5957 87.3173 12.8604 87.2078C13.1251 87.0982 13.3656 86.9376 13.5682 86.7351C13.7708 86.5326 13.9315 86.2921 14.0411 86.0275C14.1508 85.7628 14.2072 85.4792 14.2072 85.1927C14.2072 84.9063 14.1508 84.6226 14.0411 84.358C13.9315 84.0933 13.7708 83.8529 13.5682 83.6504C13.3656 83.4479 13.1251 83.2872 12.8604 83.1777C12.5957 83.0681 12.312 83.0118 12.0256 83.0119C10.343 83.0119 9.36178 81.9788 9.36178 81.0482V47.1348C9.36178 46.2043 10.343 45.1696 12.0256 45.1696H31.9282V50.7813C31.9282 54.7806 35.5803 57.7091 39.6571 57.7091H56.2547V81.0482C56.2547 81.9788 55.2746 83.0119 53.5924 83.0119H24.3245C24.0375 83.012 23.7533 83.0687 23.4883 83.1788C23.2233 83.2888 22.9826 83.4502 22.78 83.6535L15.1761 91.2574C14.9647 91.4578 14.7956 91.6985 14.6787 91.9653C14.5619 92.2322 14.4997 92.5197 14.4958 92.8109C14.4919 93.1022 14.5464 93.3913 14.6561 93.6611C14.7658 93.931 14.9284 94.1761 15.1344 94.3821C15.3403 94.588 15.5855 94.7507 15.8553 94.8603C16.1251 94.97 16.4142 95.0245 16.7055 95.0206C16.9967 95.0167 17.2843 94.9546 17.5511 94.8377C17.8179 94.7209 18.0586 94.5518 18.259 94.3404L25.2275 87.3735H53.5924C57.2784 87.3735 60.6165 84.7131 60.6165 81.0482V57.7091H72.2799L80.3495 65.7833C80.5499 65.9947 80.7906 66.1638 81.0574 66.2806C81.3243 66.3974 81.6118 66.4596 81.903 66.4635C82.1943 66.4674 82.4834 66.4129 82.7532 66.3032C83.0231 66.1935 83.2682 66.0309 83.4742 65.8249C83.6801 65.6189 83.8427 65.3738 83.9524 65.104C84.0621 64.8341 84.1166 64.545 84.1127 64.2538C84.1088 63.9625 84.0466 63.675 83.9298 63.4082C83.8129 63.1414 83.6438 62.9007 83.4325 62.7003L74.7213 53.989C74.3137 53.5799 73.7605 53.3492 73.183 53.3475H39.6571C37.5842 53.3475 36.2886 52.0462 36.2886 50.7813V11.9338C36.2886 10.6689 37.5842 9.36163 39.6571 9.36163H87.2714C89.3444 9.36163 90.6383 10.6689 90.6383 11.9338V50.7813C90.6383 52.0462 89.3444 53.3475 87.2714 53.3475C86.6936 53.3483 86.1397 53.5784 85.7314 53.9873C85.3231 54.3962 85.0938 54.9504 85.0938 55.5283C85.0938 56.1061 85.3231 56.6604 85.7314 57.0692C86.1397 57.4781 86.6936 57.7083 87.2714 57.7091C91.348 57.7091 95 54.7806 95 50.7813V11.9338C95 7.93452 91.348 5 87.2714 5H39.6571ZM63.4605 15.0003C58.9592 15.0003 55.2677 18.6936 55.2677 23.1947C55.2597 23.486 55.3102 23.7759 55.4162 24.0473C55.5222 24.3187 55.6815 24.5662 55.8847 24.775C56.0879 24.9838 56.331 25.1498 56.5994 25.2631C56.8678 25.3765 57.1563 25.4349 57.4476 25.4349C57.739 25.4349 58.0274 25.3765 58.2959 25.2631C58.5643 25.1498 58.8073 24.9838 59.0105 24.775C59.2138 24.5662 59.3731 24.3187 59.479 24.0473C59.585 23.7759 59.6355 23.486 59.6276 23.1947C59.6276 21.0506 61.3163 19.362 63.4605 19.362C65.6045 19.362 67.2992 21.0506 67.2992 23.1947C67.2992 25.2429 65.7519 26.8775 63.7446 27.02C63.6415 27.0058 63.5375 26.999 63.4335 26.9996C63.1294 27.0039 62.8295 27.0717 62.5532 27.1988C62.2768 27.3258 62.0301 27.5093 61.8289 27.7373C61.605 27.9804 61.4394 28.2712 61.3444 28.5877C61.2493 28.9042 61.2275 29.2381 61.2805 29.5643V34.1213C61.2847 34.6969 61.5163 35.2474 61.9248 35.653C62.3334 36.0585 62.8856 36.2861 63.4612 36.2861C64.0369 36.2861 64.5891 36.0585 64.9976 35.653C65.4061 35.2474 65.6378 34.6969 65.642 34.1213V31.0878C69.0963 30.1229 71.6609 26.9415 71.6609 23.1954C71.6609 18.6943 67.9615 15.0003 63.4605 15.0003ZM63.4605 38.06C63.0297 38.0599 62.6031 38.1446 62.205 38.3093C61.8069 38.4741 61.4451 38.7157 61.1404 39.0202C60.8357 39.3248 60.5939 39.6864 60.4289 40.0844C60.2639 40.4824 60.179 40.909 60.1789 41.3399C60.1788 41.7708 60.2636 42.1976 60.4285 42.5958C60.5934 42.9939 60.8351 43.3557 61.1399 43.6604C61.4446 43.9652 61.8064 44.2069 62.2046 44.3717C62.6028 44.5366 63.0296 44.6213 63.4605 44.6212C64.3306 44.621 65.165 44.2752 65.7801 43.6598C66.3951 43.0444 66.7406 42.2099 66.7404 41.3399C66.7402 40.4701 66.3946 39.6359 65.7795 39.0209C65.1645 38.4058 64.3304 38.0602 63.4605 38.06ZM18.2681 63.471C17.6857 63.4711 17.1091 63.5859 16.5711 63.8088C16.0331 64.0318 15.5443 64.3585 15.1326 64.7703C14.7208 65.1822 14.3943 65.6711 14.1715 66.2092C13.9487 66.7472 13.8341 67.3239 13.8342 67.9063C13.8344 69.0821 14.3016 70.2098 15.1331 71.0412C15.9646 71.8726 17.0922 72.3398 18.2681 72.34C19.4442 72.3402 20.5722 71.8732 21.404 71.0417C22.2357 70.2102 22.7031 69.0824 22.7033 67.9063C22.7034 67.3238 22.5888 66.747 22.3659 66.2088C22.1431 65.6707 21.8164 65.1817 21.4045 64.7698C20.9926 64.358 20.5036 64.0313 19.9655 63.8084C19.4273 63.5856 18.8505 63.4709 18.2681 63.471ZM32.8043 63.471C31.6282 63.4712 30.5003 63.9386 29.6688 64.7704C28.8373 65.6021 28.3703 66.7302 28.3705 67.9063C28.3707 69.0821 28.8379 70.2097 29.6694 71.0412C30.5008 71.8726 31.6285 72.3398 32.8043 72.34C33.3867 72.3401 33.9633 72.2255 34.5014 72.0027C35.0395 71.78 35.5284 71.4534 35.9402 71.0417C36.3521 70.63 36.6788 70.1412 36.9018 69.6032C37.1247 69.0652 37.2395 68.4886 37.2396 67.9063C37.2397 67.3238 37.1251 66.747 36.9022 66.2088C36.6794 65.6707 36.3526 65.1817 35.9408 64.7698C35.5289 64.358 35.0399 64.0313 34.5017 63.8084C33.9636 63.5856 33.3868 63.4709 32.8043 63.471ZM47.3405 63.471C46.7581 63.4709 46.1813 63.5856 45.6431 63.8084C45.105 64.0313 44.616 64.358 44.2041 64.7698C43.7923 65.1817 43.4656 65.6707 43.2427 66.2088C43.0198 66.747 42.9052 67.3238 42.9053 67.9063C42.9055 69.0824 43.3729 70.2102 44.2047 71.0417C45.0364 71.8732 46.1644 72.3402 47.3405 72.34C48.5164 72.3398 49.644 71.8726 50.4755 71.0412C51.3069 70.2097 51.7741 69.0821 51.7743 67.9063C51.7745 66.7302 51.3075 65.6021 50.476 64.7704C49.6445 63.9386 48.5166 63.4712 47.3405 63.471Z" stroke="currentColor" stroke-width="2" mask="url(#path-1-outside-1_9555_27)"/>
      {% when 'dna' %} 
        <path d="m39.52 39.52 20.96 20.96m-6.29-25.15 10.48 10.48m-29.34 8.38 10.48 10.48" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M70.95 6c-4.19 14.67 14.67 46.1-2.1 62.86-8.19 8.2-19.89 7.87-31.42 5.9M6 70.95c5.3-1.51 12.77-.02 20.95 1.71" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/><path d="M29.05 94c4.19-14.67-14.67-46.1 2.1-62.86 8.19-8.2 19.89-7.87 31.42-5.9M94 29.05c-5.3 1.51-12.77.02-20.95-1.71" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/><path d="m81.43 16.48 2.1 2.1M16.48 81.43l2.1 2.1" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when 'information' %}
        <path d="M50 6a44 44 0 1 0 0 88 44 44 0 0 0 0-88Z" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="10"/><path d="M50 41v30m0-42v1" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/>
      {% when 'help' %}
        <path d="M50 6a44 44 0 1 0 0 88 44 44 0 0 0 0-88Z" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="10"/><path d="M36 36.6s.2-4.4 4.9-8.2c2.8-2.2 6.1-2.9 9.1-2.9 2.7 0 5.2.4 6.6 1.1C59.1 27.8 64 30.7 64 37c0 6.5-4.2 9.4-9 12.7-5 3.2-6.2 6.8-6.2 10.4" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="10" stroke-linecap="round"/><path d="M48.5 78a5 5 0 1 0 0-10 5 5 0 0 0 0 10" fill="currentColor"/>
      {% when 'moon' %}       
        <path d="M30.6923 23.6154C30.6923 18.9157 31.1945 14.1748 32.3986 9.79433C32.8801 8.04274 31.114 6.38307 29.5446 7.29778C15.9036 15.2481 7 30.3603 7 47.3077C7 72.5421 27.4579 93 52.6923 93C69.6397 93 84.7519 84.0964 92.7022 70.4554C93.6169 68.886 91.9573 67.1199 90.2057 67.6014C85.8252 68.8055 81.0843 69.3077 76.3846 69.3077C51.1502 69.3077 30.6923 48.8498 30.6923 23.6154Z" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when 'water' %}    
        <path d="M41.1 94.9c-15 0-27.1-12.2-27.1-27.2a28 28 0 0 1 6-17.2L41 24l21.3 26.5a28 28 0 0 1 5.9 17.2A27 27 0 0 1 41 94.8m-19.6-27c0 10.8 8.7 19.6 19.6 19.6m20.6-63.8a13.7 13.7 0 1 0 27.4 0q0-4.8-3.4-8.6L75.4 1.4 65.1 15a13 13 0 0 0-3.4 8.6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      {% when 'blink' %}
        <path d="m84 15 .5.5.5.5-1 1-.5-.5-.3-.3-.2-.2.5-.5zM53 26q.7 3.9 1.8 6.7a25 25 0 0 0 15.5 15.5q2.8 1 6.7 1.8-3.9.7-6.7 1.8a25 25 0 0 0-15.5 15.5Q53.8 70 53 74q-.7-3.9-1.8-6.7a25 25 0 0 0-6-9.5l-.4-.4a25 25 0 0 0-9-5.6q-3-1-6.8-1.8 3.9-.7 6.7-1.8a25 25 0 0 0 15.5-15.5q1-2.8 1.8-6.7ZM16 83l.5.5.5.5-1 1-.5-.5-.3-.3-.2-.2.5-.5z" stroke="currentColor" stroke-width="6.5"/> 
      {% when 'free-delivery' %}  
        <path d="M21.12 16H62.5m2.1 55.22V26.14M34 73h36M8.33 63.2v5.28a5.35 5.35 0 0 0 5.42 4.75c3.96.08 3.78.04 3.2 0m47.68-47.15V20.8a5.35 5.35 0 0 0-5.43-4.75c-3.95-.09-3.78-.04-3.2 0M23.4 16h-4.85c-1.43.18-4.29 1.6-4.37 5.91-.07 4.31-.03 4.12 0 3.49" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/><path d="M9.84 37.78 7.37 52H4.03l2.46-14.22zm4.34 5.92-.46 2.64H7.39l.46-2.64zm1.56-5.92-.46 2.65H8.4l.47-2.65zm3.36 0 5.26.01a7 7 0 0 1 2.4.47q1.09.45 1.71 1.38.63.91.53 2.35a4.3 4.3 0 0 1-1.72 3.36 9 9 0 0 1-1.77 1l-1.2.63h-4.74l.45-2.65 3.22.02q.64 0 1.14-.25.5-.24.83-.7t.4-1.09q.09-.52-.03-.94a1.2 1.2 0 0 0-.45-.66 1.6 1.6 0 0 0-.88-.27L22 40.43 19.97 52h-3.34zM24.26 52l-1.96-6.29 3.39-.02 2.14 6.14V52zm17-2.64L40.8 52h-7.37l.46-2.64zM37.1 37.78 34.63 52H31.3l2.46-14.22zm4.24 5.65-.44 2.54h-6.43l.45-2.54zm1.95-5.65-.46 2.65h-7.4l.47-2.65zm10.83 11.58L53.66 52H46.3l.46-2.64zm-4.16-11.58L47.5 52h-3.34l2.46-14.22zm4.24 5.65-.44 2.54h-6.43l.45-2.54zm1.95-5.65-.46 2.65h-7.4l.47-2.65z" fill="currentColor"/><path d="M79.2 65.8a7.47 7.47 0 1 1 0 14.95 7.47 7.47 0 0 1 0-14.95Z" stroke="currentColor" stroke-width="6.5"/><path d="M89 59h2" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/><path d="M26.16 65.33a7.47 7.47 0 1 1 0 14.95 7.47 7.47 0 0 1 0-14.95Z" stroke="currentColor" stroke-width="6.5"/><path d="M69.38 35.22h13.98a3 3 0 0 1 2.72 1.73l7.64 16.46a3 3 0 0 1 .28 1.26v15.14a3 3 0 0 1-3 3h-2.23" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/>      
      {% when 'hot-sale' %}   
        <path d="M29.7 47.9h-4.3c-2.4 0-4.4 2-4.4 4.3s2 4.4 4.4 4.4c2.3 0 4.3 2 4.3 4.3s-2 4.4-4.3 4.4H21m17.4 0v-13c0-2.4 2-4.4 4.4-4.4 2.3 0 4.3 2 4.3 4.3v13m-8.7-8.6h8.7m8.7-8.7v17.4h8.7M79 47.9h-5.8v17.4H79m0-8.7h-5.8" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/><path d="m50 6 22 25.1 9.4-12.5S94 34.3 94 56.3 75.1 94 50 94 6 78.3 6 56.3s12.6-37.7 12.6-37.7L28 31z" stroke="currentColor" stroke-width="6.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
      {% when 'dark-mode' %}  
        <circle cx="50" cy="50" r="40.8" stroke="currentColor" stroke-width="6.5"/><path d="M53 79V20M22.3 50c-1-9 3.7-27.5 29.9-30m0 59.9c-9 1-27.6-3.7-30-30" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/>
      {% when 'light-mode' %}  
        <path d="M90.75 50A40.75 40.75 0 1 1 50 9.25 40.75 40.75 0 0 1 90.75 50" stroke="currentColor" stroke-width="6.5" stroke-linecap="round" stroke-linejoin="round"/><path d="m48.9 21.04-2.05 58.97M78.6 51.07c.63 9.01-4.68 27.42-30.92 29m2.07-59.88c9-.63 27.41 4.68 29 30.92" stroke="currentColor" stroke-width="6.5" stroke-linecap="round"/>
    {% endcase %}
  </svg> 
{%- endif -%}