{{ 'component-grid.css' | asset_url | stylesheet_tag }}
{{ 'timeline.css' | asset_url | stylesheet_tag }}

{%- style -%}
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
     --padding-top: {{ section.settings.mobile_padding_top }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
      --padding-top: {{ section.settings.padding_top }}px;
    }
  }
  #MainContent > :first-child .section--top-space-{{ section.id }} {
    padding-top: calc(var(--header-height) * var(--transparent-header-show) + var(--padding-top));
  }
{%- endstyle -%}

{% liquid
  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif
-%}

<div
  class="timeline--wrapper section--top-space-{{ section.id }} section-{{ section.id }}-padding color-{{ section.settings.color_scheme }}"
  data-section-id="{{ section.id }}"
  data-section-type="timeline"
  id="Slider-{{ section.id }}"
  data-slider-autoplay="{{ section.settings.autorotate }}"
  data-slider-delay="{{ section.settings.autorotate_speed }}000"
  data-slider-loop="{%- if section.blocks.size > 1 -%}true {%- else -%} false {%- endif -%}"
>
  <div class="{{ container }}">
    <div class="section-heading text-{{ section.settings.alignment }} mobile-text-{{ section.settings.mobile_alignment }}   {% if section.settings.heading != blank or section.settings.subtitle != blank %} mb-50 {% endif %}">
      <h2 class="section-heading__title {{ section.settings.heading_size }} rte">
        {{- section.settings.heading -}}
      </h2>
      <span class="section-heading__sub_title">{{- section.settings.subtitle -}}</span>
    </div>

    <div class="timeline__navigation">
      {%- for block in section.blocks -%}
        {% case block.type %}
          {% when 'timeline' %}
            <button
              class="timeline__bullet {% if forloop.first == true %}active{% endif %}"
              data-index="{{ forloop.index0 }}"
            >
              <span class="timeline__bullet--solid--btn">{{ forloop.index }}</span>
              {% if block.settings.navigation_label != blank %}
                <span class="timeline__bullet--text h6 mb-0">{{ block.settings.navigation_label }}</span>
              {% endif %}
            </button>
        {% endcase %}
      {% endfor %}
    </div>

    <div class="timeline__container">
      <div class="swiper timeline--slider">
        <div class="swiper-wrapper">
          {%- for block in section.blocks -%}
            {% case block.type %}
              {% when 'timeline' %}
                <div class="swiper-slide" tabindex="-1">
                  {% liquid
                    assign content_position = ''
                    if block.settings.content_position == 'middle'
                      assign content_position = 'align-items-center'
                    elsif block.settings.content_position == 'bottom'
                      assign content_position = 'align-items-end'
                    endif

                    assign content_alignment = ''
                    if block.settings.content_alignment == 'center'
                      assign content_alignment = 'text-center'
                    elsif block.settings.content_alignment == 'right'
                      assign content_alignment = 'text-right'
                    endif
                  %}
                  <div class="timeline__card">
                    <div class="timeline__card--inner grid grid--1-col grid--2-col-tablet {% if section.settings.layout == "text_first" %}desktop-row-reverse {% endif %} ">
                      <div class="timeline__card--media-wrapper grid__item">
                        <div
                          class="timeline__card--media timeline__card--media--{{ section.settings.height }} {% if block.settings.image != blank %}media {% if block.settings.transparent_media %}transparent--media{% endif %}{% else %}placeholder{% endif %} {% if section.settings.round_corner %} rounded--image {% endif %}"
                          {% if section.settings.height == 'adapt' and block.settings.image != blank %}
                            style="padding-bottom: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%;"
                          {% endif %}
                        >
                          {%- if block.settings.image != blank -%}
                            {%- capture sizes -%}(min-width: {{ settings.container_lg_width }}px) {% if section.settings.full_width %}{{ settings.container_lg_width | minus: 30 }}px{% else %}{{ settings.container_lg_width | minus: 60 | divided_by: 2 }}px{% endif %},(min-width: 750px) {% if section.settings.full_width %} calc(100vw - 30px){% else %} calc((100vw - 130px) / 2) {% endif %}{%- endcapture -%}
                            {{
                              block.settings.image
                              | image_url: width: 1500
                              | image_tag: loading: 'lazy', sizes: sizes, widths: '165, 360, 535, 750, 1070, 1500'
                            }}
                          {%- else -%}
                            {{ 'image' | placeholder_svg_tag: 'placeholder-svg-2' }}
                          {%- endif -%}
                        </div>
                      </div>

                      <div class="timeline__card--text-wrapper grid__item {{ content_position }} d-flex">
                        <div class="timeline__card--text-inner large--space {{ section.settings.layout }}  {{ content_alignment }}">
                          <h3 class="timeline__card--heading mb-0 {{ block.settings.heading_size }}">
                            {{ block.settings.heading }}
                          </h3>
                          <div class="timeline__card--subheading">{{ block.settings.subheading }}</div>
                          <div class="timeline__card--heading-two {{ block.settings.heading_two_size }}">
                            {{ block.settings.heading_two }}
                          </div>
                          <div class="timeline__card--text">{{ block.settings.text | escape }}</div>
                          {% liquid
                            assign button_class = ''
                            case block.settings.button_style
                              when 'primary'
                                assign button_class = 'button button--primary'
                              when 'secondary'
                                assign button_class = 'button button--secondary'
                              when 'icon'
                                assign button_class = 'link with--icon'
                              else
                                assign button_class = 'link'
                            endcase
                          %}

                          {%- if block.settings.button_label != blank -%}
                            <div class="timeline__card--button">
                              <a
                                {% if block.settings.button_link == blank %}
                                  role="link" aria-disabled="true"
                                {% else %}
                                  href="{{ block.settings.button_link }}"
                                {% endif %}
                                class="{{ button_class }} {% unless block.settings.button_style == "icon" %} button--{{ block.settings.button_size }} {% endunless %}  {% if block.settings.button_icon %} button--with-icon{% endif %}"
                                {{ block.shopify_attributes }}
                              >
                                {{ block.settings.button_label | escape }}
                                {% if block.settings.button_icon %}
                                  <span class="button--icon button--icon-right"> {% render 'icon-arrow-right' %} </span>
                                {% endif %}
                              </a>
                            </div>
                          {%- endif -%}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
            {% endcase %}
          {% endfor %}
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Timeline",
   "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
     {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
     {
      "type": "header",
      "content": "Section header"
    },
    {
        "type": "text",
        "id": "heading",
        "default": "Timeline",
        "label": "t:sections.featured-collection.settings.title.label"
    },
    {
    "type": "select",
    "id": "heading_size",
    "options": [
        {
          "value": "h0",
          "label": "Large"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h2",
          "label": "Small"
        }
     ],
     "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "textarea",
      "id": "subtitle",
      "default": "Share information about your brand with your customers.",
      "label": "Subheading"
    },
    {
      "type": "select",
      "id": "alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left",
      "label": "Desktop heading alignment"
    },
    {
      "type": "select",
      "id": "mobile_alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center",
      "label": "Mobile heading alignment"
    },
    {
      "type": "header",
      "content": "Timeline card"
    },
    {
      "type": "select",
      "id": "height",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.image-with-text.settings.height.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.image-with-text.settings.height.options__2.label"
        },
		{
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "t:sections.image-with-text.settings.height.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.image-with-text.settings.height.label"
    },
    {
      "type": "select",
      "id": "layout",
      "options": [
        {
          "value": "image_first",
          "label": "t:sections.image-with-text.settings.layout.options__1.label"
        },
        {
          "value": "text_first",
          "label": "t:sections.image-with-text.settings.layout.options__2.label"
        }
      ],
      "default": "image_first",
      "label": "t:sections.image-with-text.settings.layout.label",
      "info": "t:sections.image-with-text.settings.layout.info"
    },
    {
        "type": "header",
        "content": "Section padding"
      },
      {
          "type": "paragraph",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 50
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 50
        },
		{
          "type": "paragraph",
          "content": "Mobile"
        },
		{
          "type": "range",
          "id": "mobile_padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 50
        },
        {
          "type": "range",
          "id": "mobile_padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 50
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
        "type": "color_scheme",
        "id": "color_scheme",
        "label": "t:sections.all.colors.label",
        "default": "background-1"
      }
  ],
   "blocks": [
    {
      "type": "timeline",
      "name": "Timeline",
      "limit": 6,
      "settings": [
        {
          "type": "text",
          "id": "navigation_label",
          "default": "Title",
          "label": "Navigation label"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.multicolumn.blocks.column.settings.image.label"
        },
        {
          "type": "checkbox",
          "id": "transparent_media",
          "label": "Enable transparent image",
          "default": false
        },
        {
          "type": "text",
          "id": "heading",
          "default": "Title 1",
          "label": "t:sections.multicolumn.blocks.column.settings.title.label"
        },
        {
         "type": "select",
         "id": "heading_size",
         "options": [
           {
             "value": "h4",
             "label": "Small"
           },
           {
             "value": "h3",
             "label": "Medium"
           },
           {
             "value": "h2",
             "label": "Large"
           }
         ],
         "default": "h3",
         "label": "Heading size"
       },
        {
          "type": "text",
          "id": "heading_two",
          "default": "Title 2",
          "label": "Heading two"
        },
        {
         "type": "select",
         "id": "heading_two_size",
         "options": [
           {
             "value": "h4",
             "label": "Small"
           },
           {
             "value": "h3",
             "label": "Medium"
           },
           {
             "value": "h2",
             "label": "Large"
           }
         ],
         "default": "h3",
         "label": "Heading two size"
       },
        {
          "type": "text",
          "id": "subheading",
          "default": "Sub title",
          "label": "Subheading"
        },
        {
          "type": "textarea",
          "id": "text",
          "default": "Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.",
          "label": "t:sections.multicolumn.blocks.column.settings.text.label"
        },
         {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "t:sections.image-with-text.blocks.button.settings.button_label.label",
          "info": "t:sections.image-with-text.blocks.button.settings.button_label.info"
        },
         {
             "type": "checkbox",
             "id": "button_icon",
             "label": "Use arrow icon",
             "default": true
           },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.image-with-text.blocks.button.settings.button_link.label"
        },
		{
              "type": "select",
              "id": "button_style",
              "label": "Button style",
              "default": "primary",
              "options": [
                {
                  "value": "secondary",
                  "label": "Secondary"
                },
                {
                  "value": "primary",
                  "label": "Primary"
                },
                {
                  "value": "icon",
                  "label": "Link button"
                }
              ]
            },
			{
              "type": "select",
              "id": "button_size",
              "label": "Button size",
              "default": "small",
              "options": [
                {
                  "value": "large",
                  "label": "Large"
                },
                {
                  "value": "medium",
                  "label": "Medium"
                },
				        {
                  "value": "small",
                  "label": "Small"
                }
              ]
            },
         {
          "type": "select",
          "id": "content_position",
          "options": [
            {
              "value": "top",
              "label": "t:sections.image-with-text.settings.desktop_content_position.options__1.label"
            },
            {
              "value": "middle",
              "label": "t:sections.image-with-text.settings.desktop_content_position.options__2.label"
            },
            {
              "value": "bottom",
              "label": "t:sections.image-with-text.settings.desktop_content_position.options__3.label"
            }
          ],
          "default": "middle",
          "label": "Content position"
       },
      {
        "type": "select",
        "id": "content_alignment",
        "options": [
          {
            "value": "left",
            "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__1.label"
          },
          {
            "value": "center",
            "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__2.label"
          },
          {
            "value": "right",
            "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__3.label"
          }
        ],
        "default": "left",
        "label": "Content alignment"
       }

      ]
    }
  ],
  "presets": [
  {
    "name": "Timeline",
     "blocks":[
    		{
                 "type": "timeline"
            },
    		{
               "type": "timeline"
             },
             {
               "type": "timeline"
             },
             {
               "type": "timeline"
             }
		]
    }
  ]
}
{% endschema %}
