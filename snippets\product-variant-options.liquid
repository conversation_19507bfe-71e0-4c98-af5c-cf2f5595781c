{% comment %}
  Renders product variant options

  Accepts:
  - product: {Object} product object.
  - option: {Object} current product_option object.
  - block: {Object} block object.


  Usage:
  {% render 'product-variant-options',
    product: product,
    option: option,
    block: block,
    picker_type: block.settings.picker_type
  %}
{% endcomment %}

{%- liquid
  assign variants_available_arr = product.variants | map: 'available'
  assign variants_option1_arr = product.variants | map: 'option1'
  assign variants_option2_arr = product.variants | map: 'option2'
  assign variants_option3_arr = product.variants | map: 'option3'

  assign product_form_id = 'product-form-' | append: section.id
-%}

{%- for value in option.values -%}
  {%- liquid
    assign option_name = option.name | downcase
    assign option_disabled = true

    for option1_name in variants_option1_arr
      case option.position
        when 1
          if variants_option1_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
            assign option_disabled = false
          endif
        when 2
          if option1_name == product.selected_or_first_available_variant.option1 and variants_option2_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
            assign option_disabled = false
          endif
        when 3
          if option1_name == product.selected_or_first_available_variant.option1 and variants_option2_arr[forloop.index0] == product.selected_or_first_available_variant.option2 and variants_option3_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
            assign option_disabled = false
          endif
      endcase
    endfor
  -%}

  {%- if picker_type == 'button' -%}
    <input
      type="radio"
      id="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
      name="{{ option.name }}"
      value="{{ value | escape }}"
      form="{{ product_form_id }}"
      {% if option.selected_value == value %}
        checked
      {% endif %}
      {% if option_disabled %}
        class="disabled"
      {% endif %}
    >
    {% if choose_option == option_name %}
      <label
        class="variant--swatch-custom {% if swatch_type == "image" %}variant--swatch-image{% else %}variant--swatch-color{% endif %} {% if swatch_button_style == "round" %}swatch-button-circle{% endif %}"
        for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
        data-value="{{ value | escape }}"
      >
        <span class="swatch--variant-tooltip">{{ value }}</span>
        <span class="visually-hidden">{{ value }}</span>
      </label>
    {% else %}
      <label for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}">
        {{ value -}}
        <span class="visually-hidden"> {{ value -}}</span>
      </label>
    {% endif %}
  {%- elsif picker_type == 'dropdown' -%}
    <option
      value="{{ value | escape }}"
      {% if option.selected_value == value %}
        selected="selected"
      {% endif %}
    >
      {% if option_disabled -%}
        {{- 'products.product.value_unavailable' | t: option_value: value -}}
      {%- else -%}
        {{- value -}}
      {%- endif %}
    </option>
  {%- endif -%}
{%- endfor -%}
