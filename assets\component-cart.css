.cart {
  position: relative;
  display: block;
}

.cart__empty-text,
.is-empty .cart__contents,
cart-items.is-empty .title-wrapper-with-link,
.is-empty .cart__footer {
  display: none;
}

.is-empty .cart__empty-text,
.is-empty .cart__warnings {
  display: block;
}

.cart__warnings {
  display: none;
  text-align: center;
}

.cart__empty-text {
  margin: 4.5rem 0 2rem;
}

.cart__contents > * + * {
  margin-top: 2.5rem;
}

.cart__login-title {
  margin: 5.5rem 0 0.5rem;
}

.cart__login-paragraph {
  margin-top: 0.8rem;
}

.cart__login-paragraph a {
  font-size: inherit;
}

@media screen and (min-width: 990px) {
  .cart__empty-text {
    margin: 0 0 3rem;
  }
}

cart-items {
  display: block;
}

.cart__items {
  position: relative;
  padding-bottom: 3rem;
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
}

.cart__items--disabled {
  pointer-events: none;
}

.cart__footer {
  padding: 4rem 0 5rem;
}

.cart__footer-wrapper:last-child .cart__footer {
  padding-bottom: 5rem;
}

.cart__footer > div:only-child {
  margin-left: auto;
}

.cart__footer > * + * {
  margin-top: 4rem;
}

.cart__footer .discounts {
  margin-top: 1rem;
}

.cart__note {
  display: block;
}

.cart__note label {
  display: flex;
  align-items: flex-end;
  line-height: 1;
  height: 1.8rem;
  margin-bottom: 2rem;
  color: rgba(var(--color-foreground), 0.75);
}

.cart__note .field__input {
  padding: 1rem;
}

@media screen and (min-width: 750px) {
  .cart__items {
    grid-column-start: 1;
    grid-column-end: 3;
    padding-bottom: 4rem;
  }

  .cart__contents > * + * {
    margin-top: 0;
  }

  .cart__items + .cart__footer {
    grid-column: 2;
  }

  .cart__footer {
    display: flex;
    justify-content: space-between;
    border: 0;
  }

  .cart__footer-wrapper:last-child {
    padding-top: 0;
  }

  .cart__footer > * {
    width: 35rem;
  }

  .cart__footer > * + * {
    margin-left: 4rem;
    margin-top: 0;
  }
}

.cart__ctas button {
  width: 100%;
}

.cart__ctas > *:not(noscript:first-child) + * {
  margin-top: 1rem;
}

.cart__update-button {
  margin-bottom: 1rem;
}

.cart__dynamic-checkout-buttons {
  max-width: 36rem;
  margin: 0 auto;
}

.cart__blocks > * + * {
  margin-top: 1rem;
}

.cart__dynamic-checkout-buttons div[role='button'] {
  border-radius: 0 !important;
}

.cart-note__label {
  display: inline-block;
  margin-bottom: 1rem;
  line-height: 2;
}

.tax-note {
  margin: 2.2rem 0 1.6rem auto;
  text-align: center;
  display: block;
}

.cart__checkout-button {
  max-width: 36rem;
}

.cart__ctas {
  text-align: center;
}

@media screen and (min-width: 750px) {
  .cart-note {
    max-width: 35rem;
  }

  .cart__update-button {
    margin-bottom: 0;
    margin-right: 0.8rem;
  }

  .tax-note {
    margin-bottom: 2.2rem;
    text-align: right;
  }

  [data-shopify-buttoncontainer] {
    justify-content: flex-end;
  }

  .cart__ctas {
    display: flex;
    gap: 1rem;
  }
}

/* Shipping calc css */
.action_drawer_body {
    display: flex;
    gap: 2rem;
    justify-content: flex-start;
    flex-direction: column;
}
.action_drawer_body > div {
    width: 25%;
}
.coupon__title {
    margin-bottom: 1.5rem;
    color: rgba(var(--color-foreground),.75);
    display: block;
}
input#coupon_code_cart,.cart__note .field__input{
	border-radius: 5px;
}
.shipping_calculator {
    border: .1rem solid rgba(var(--color-foreground),.2);
    padding: 5rem 3rem;
    margin-top: 2rem;
    position: relative;
}
.action_drawer_heading {
    position: relative;
    top: 0;
    left: 0;
    background: transparent;
    transform: inherit;
    padding: 0;
    white-space: nowrap;
    text-align: left;
    padding-bottom: 20px;
}
.cart__footer--wrapper .input__field, input[type=email], .cart__footer--wrapper input[type=text],.cart__footer--wrapper .select__field_form select {
    border-color: rgba(var(--color-foreground),.2);
}
.cart__footer--wrapper .text-area{
	box-shadow: 0 0 0 .1rem rgba(var(--color-foreground),.2);
}

@media only screen and (max-width: 749px){
  .action_drawer_heading {
    top: -15px;
  }	
  .action_drawer_heading h3 {
    font-size: 1.8rem;
  }
  .action_drawer_body > div {
    width: 100%;
  }
  .action_drawer_body {
    flex-wrap: wrap;
  }
}

@media only screen and (min-width: 750px) and (max-width: 991px){
  .action_drawer_body {
    flex-wrap: wrap;
  }
  .action_drawer_body > div {
    width: 33.33%;
  }
}

.shipping_calculator .button.loading:after,.shipping_calculator button.loading:after {
    left: 50%;
    transform: translateX(-50%);
}