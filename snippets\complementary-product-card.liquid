{%- liquid
  assign variant = product_card_product.selected_or_first_available_variant

  assign productCountdown = product_card_product.metafields.meta.product_countdown.value
  assign todayDate = 'now' | date: '%s'
  assign countDownDate = productCountdown | date: '%s'

  assign first_variant_featured_media_check = false
  if variant.featured_media == null
    assign first_variant_featured_media_check = true
  endif

  assign second_img_position = 1

  unless show_secondary_image
    assign second_image_class = 'second--image__hide'
  endunless
-%}

<div class="product-grid-item {{ className }}">
  <div class="product-grid-item__thumbnail {{ second_image_class }}">
    {%- if show_badge -%}
      {%- render 'product-badge', product: product_card_product -%}
    {%- endif -%}

    {%- if product_card_product.featured_media -%}
      {%- liquid
        assign featured_media_aspect_ratio = product_card_product.featured_media.aspect_ratio

        if product_card_product.featured_media.aspect_ratio == null
          assign featured_media_aspect_ratio = 1
        endif
      -%}
      <a href="{{ product_card_product.url | default: '#' }}" class="d-block product__media_thumbnail">
        {%- render 'product-card-media',
          product_card_product: product_card_product,
          variant: variant,
          media_size: media_size,
          featured_media_aspect_ratio: featured_media_aspect_ratio,
          second_img_position: second_img_position,
          show_secondary_image: show_secondary_image,
          first_variant_featured_media_check: first_variant_featured_media_check,
          round_image: round_image
        -%}
      </a>

    {%- else -%}
      <div class="card__content">
        <a href="{{ product_card_product.url | default: '#' }}" class="d-block">
          <div class="placeholder placeholder_svg_parent" style="padding-bottom: 100%">
            {{ 'product-1' | placeholder_svg_tag: 'placeholder-svg' }}
          </div>
        </a>
      </div>
    {%- endif -%}
  </div>

  <div class="complementary--product-card-content">
    <div class="complementary--product-content-grid">
      {%- if show_vendor -%}
        <span class="visually-hidden">{{ 'accessibility.vendor' | t }}</span>
        <div class="product__vendor">{{ product_card_product.vendor }}</div>
      {%- endif -%}

      {%- if show_title -%}
        <h3 class="product-grid-item__title h6 mb-0">
          <a class="complementary--product__title--link" href="{{ product_card_product.url | default: '#' }}">
            {{- product_card_product.title -}}
          </a>
        </h3>
      {%- endif -%}

      {%- if show_price -%}
        {% render 'price', product: product_card_product, price_class: price_class %}
      {%- endif -%}

      {%- if show_rating and product_card_product.metafields.reviews.rating.value != blank -%}
        {% liquid
          assign rating_decimal = 0
          assign decimal = product_card_product.metafields.reviews.rating.value.rating | modulo: 1
          if decimal >= 0.3 and decimal <= 0.7
            assign rating_decimal = 0.5
          elsif decimal > 0.7
            assign rating_decimal = 1
          endif
        %}

        <div class="complementary-product__card--rating">
          <div
            class="rating"
            role="img"
            aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: product_card_product.metafields.reviews.rating.value, rating_max: product_card_product.metafields.reviews.rating.value.scale_max }}"
          >
            <span
              aria-hidden="true"
              class="rating-star color-icon-{{ settings.accent_icons }}"
              style="--rating: {{ product_card_product.metafields.reviews.rating.value.rating | floor }}; --rating-max: {{ product_card_product.metafields.reviews.rating.value.scale_max }}; --rating-decimal: {{ rating_decimal }};"
            ></span>
          </div>
          <p class="rating-text caption">
            <span aria-hidden="true">
              {{- product_card_product.metafields.reviews.rating.value }} /
              {{ product_card_product.metafields.reviews.rating.value.scale_max -}}
            </span>
          </p>
          <p class="rating-count caption">
            <span aria-hidden="true">({{ product_card_product.metafields.reviews.rating_count }})</span>
            <span class="visually-hidden">
              {{- product_card_product.metafields.reviews.rating_count }}
              {{ 'accessibility.total_reviews' | t -}}
            </span>
          </p>
        </div>
      {%- endif -%}
    </div>

    {%- if show_cart_button -%}
      <div class="complementary--product__cart--btn">
        {%- if show_cart_button -%}
          {% if product_card_product.has_only_default_variant %}
            <product-form>
              <form action="/cart/add" method="post" enctype="multipart/form-data" data-type="add-to-cart-form">
                <input type="hidden" name="id" value="{{ variant.id }}">
                <button
                  type="submit"
                  name="add"
                  class="complementary__product--cart-btn"
                  {% if product_card_product.selected_or_first_available_variant.available == false %}
                    disabled
                  {% endif %}
                >
                  {%- if product_card_product.selected_or_first_available_variant.available -%}
                    <span class="complementary__product--cart-btn-inner">
                      <span class="complementary__action--btn-label">
                        {{ 'products.product.add_to_cart' | t }}
                      </span>
                    </span>
                  {%- else -%}
                    <span class="complementary__product--cart-btn-inner">
                      <span class="complementary__action--btn-label">
                        {{ 'products.product.sold_out' | t }}
                      </span>
                    </span>
                  {%- endif -%}
                </button>
              </form>
            </product-form>
          {%- else -%}
            <quick-view-modal>
              <button
                aria-haspopup="dialog"
                type="button"
                class="complementary__product--cart-btn"
                data-product-handle="{{ product_card_product.handle }}"
              >
                <span class="complementary__product--cart-btn-inner">
                  <span class="complementary__action--btn-label">
                    {{ 'products.product.select_options' | t }}
                  </span>
                </span>
              </button>
            </quick-view-modal>
          {%- endif -%}
        {%- endif -%}
      </div>
    {%- endif -%}
  </div>
</div>
