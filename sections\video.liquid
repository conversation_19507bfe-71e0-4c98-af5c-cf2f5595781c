{{ 'video-section.css' | asset_url | stylesheet_tag }}
{{ 'component-deferred-media.css' | asset_url | stylesheet_tag }}
{{ 'section-title.css' | asset_url | stylesheet_tag }}
<style>
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
</style>

{%- liquid
  assign video_id = section.settings.video.id | default: section.settings.video_url.id
  assign video_alt = section.settings.video.alt | default: section.settings.description
  assign alt = 'sections.video.load_video' | t: description: video_alt | escape
  assign poster = section.settings.cover_image

  if section.settings.video != null
    assign ratio_diff = section.settings.video.aspect_ratio | minus: poster.aspect_ratio | abs
    if ratio_diff < 0.01 and ratio_diff > 0
      assign fix_ratio = true
    endif
  endif

  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif
-%}

{%- capture sizes -%}
  {% if section.settings.full_width -%}
    100vw
  {%- else -%}
    (min-width: {{ settings.container_lg_width }}px) {{ settings.container_lg_width | minus: 100 }}px, (min-width: 750px)
    calc(100vw - 10rem), 100vw
  {%- endif %}
{%- endcapture -%}

<div class="video-section section-{{ section.id }}-padding">
  <div class="{{ container }} {% unless section.settings.show_offset %}p-0{% endunless %}">
    {% if section.settings.heading != blank or section.settings.subtitle != blank %}
      <div class="section-heading text-{{ section.settings.text_center }} mb-70">
        <span class="section-heading__sub_title h6">{{- section.settings.subtitle -}}</span>
        <h2 class="section-heading__title">
          {{- section.settings.heading | replace: 'p>', 'span>' -}}
        </h2>
      </div>
    {% endif %}
    <noscript>
      <div
        class="video-section__media video--banner__media--{{ section.settings.height }} {% if section.settings.round_corner %}rounded--image-1 {% endif %}"
        {% if poster != null and section.settings.height == 'adapt' %}
          style="--ratio-percent: {{ 1 | divided_by: poster.aspect_ratio | times: 100 }}%;"
        {% endif %}
      >
        {%- if section.settings.video == null and section.settings.video_url != null -%}
          <a
            href="{{ section.settings.video_url }}"
            class="video-section__poster media media--transparent{% if poster == null %} video-section__placeholder{% endif %}"
          >
            {%- if poster != null -%}
              {{
                poster
                | image_url: width: 3840
                | image_tag:
                  loading: 'lazy',
                  sizes: sizes,
                  widths: '375, 750, 1100, 1500, 1780, 2000, 3000, 3840',
                  alt: alt
              }}
            {%- else -%}
              {{ 'collection-2' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
            {%- endif -%}
          </a>
        {%- else -%}
          {{
            section.settings.video
            | video_tag: image_size: '1100x', loop: section.settings.enable_video_looping, controls: true, muted: false
          }}
        {%- endif -%}
      </div>
    </noscript>

    <deferred-media
      class="video-section__media deferred-media no-js-hidden gradient video--banner__media--{{ section.settings.height }} {% if poster == null and section.settings.height == 'adapt' %} video-section__placeholder{% endif %}  {% if fix_ratio %} media-fit-cover{% endif %} {% if section.settings.round_corner %}rounded--image-1 {% endif %}"
      data-media-id="{{ video_id }}"
      {% if poster != null and section.settings.height == 'adapt' %}
        style="--ratio-percent: {{ 1 | divided_by: poster.aspect_ratio | times: 100 }}%;"
      {% endif %}
    >
      <button
        id="Deferred-Poster-Modal-{{ video_id }}"
        class="video-section__poster media deferred-media__poster media--landscape"
        type="button"
        aria-label="{{ alt }}"
      >
        {%- if poster != null -%}
          {{
            poster
            | image_url: width: 3840
            | image_tag: loading: 'lazy', sizes: sizes, widths: '375, 750, 1100, 1500, 1780, 2000, 3000, 3840', alt: alt
          }}
        {%- else -%}
          {{ 'collection-2' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
        {%- endif -%}
        <span class="deferred-media__poster-button motion-reduce banner__bideo--play">
          <span class="banner__bideo--play__icon">
            {%- render 'icon-play' -%}
          </span>
        </span>
      </button>
      <template>
        {%- if section.settings.video == null and section.settings.video_url != null -%}
          {%- liquid
            assign loop = ''
            if section.settings.enable_video_looping
              assign loop = '&loop=1&playlist=' | append: video_id
            endif
          -%}
          {%- if section.settings.video_url.type == 'youtube' -%}
            <iframe
              src="https://www.youtube.com/embed/{{ video_id }}?enablejsapi=1&autoplay=1{{ loop }}"
              class="js-youtube"
              allow="autoplay; encrypted-media"
              allowfullscreen
              title="{{ section.settings.description | escape }}"
            ></iframe>
          {%- else -%}
            <iframe
              src="https://player.vimeo.com/video/{{ video_id }}?autoplay=1{{ loop }}"
              class="js-vimeo"
              allow="autoplay; encrypted-media"
              allowfullscreen
              title="{{ section.settings.description | escape }}"
            ></iframe>
          {%- endif -%}
        {%- else -%}
          {{
            section.settings.video
            | video_tag:
              image_size: '1100x',
              autoplay: true,
              loop: section.settings.enable_video_looping,
              controls: true,
              muted: false
          }}
        {%- endif -%}
      </template>
    </deferred-media>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.video.name",
  "tag": "section",
  "class": "spaced-section spaced-section--full-width",
  "settings": [
    {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
     {
       "type": "checkbox",
       "id": "show_offset",
       "label": "Enable Offset (left & right)",
       "default": true
     },
    {
     "type": "header",
     "content": "Section header"
   },
   {
     "type": "textarea",
     "id": "subtitle",
     "label": "Subheading"
   },
    {
      "type": "richtext",
      "id": "heading",
      "default": "<p>Video</p>",
      "label": "t:sections.featured-collection.settings.title.label"
    },
    {
      "type": "select",
      "id": "text_center",
      "options": [
        {
          "value": "right",
          "label": "Right"
        },
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        }
      ],
      "default": "center",
      "label": "Heading alignment"
    },
     {
      "type": "header",
      "content": "t:sections.video.settings.header__1.content"
    },
    {
      "type": "video",
      "id": "video",
      "label": "t:sections.video.settings.video.label"
    },
    {
      "type": "header",
      "content": "t:sections.video.settings.header__2.content"
    },
    {
      "type": "paragraph",
      "content": "t:sections.video.settings.paragraph.content"
    },
    {
      "type": "video_url",
      "id": "video_url",
      "accept": [
        "youtube",
        "vimeo"
      ],
      "default": "https:\/\/www.youtube.com\/watch?v=_9VUPq3SxOc",
      "label": "t:sections.video.settings.video_url.label",
      "placeholder": "t:sections.video.settings.video_url.placeholder",
      "info": "t:sections.video.settings.video_url.info"
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "label": "t:sections.video.settings.enable_video_looping.label",
      "default": false
    },
    {
      "type": "image_picker",
      "id": "cover_image",
      "label": "t:sections.video.settings.cover_image.label"
    },
	 {
        "type": "select",
        "id": "height",
        "options": [
          {
            "value": "adapt",
            "label": "t:sections.image-with-text.settings.height.options__1.label"
          },
          {
            "value": "small",
            "label": "t:sections.image-with-text.settings.height.options__2.label"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
          {
            "value": "large",
            "label": "t:sections.image-with-text.settings.height.options__3.label"
          }
        ],
        "default": "adapt",
        "label": "t:sections.image-with-text.settings.height.label"
      },
    {
      "type": "text",
      "id": "description",
      "label": "t:sections.video.settings.description.label",
      "info": "t:sections.video.settings.description.info"
    },
    {
      "type": "checkbox",
      "id": "round_corner",
      "label": "Round corner",
      "default": false
    },
	{
      "type": "header",
      "content": "Section padding"
    },
    {
          "type": "paragraph",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        },
		{
          "type": "paragraph",
          "content": "Mobile"
        },
		{
          "type": "range",
          "id": "mobile_padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "mobile_padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        }
  ],
  "presets": [
    {
      "name": "t:sections.video.presets.name"
    }
  ],
  "disabled_on": {
    "groups": ["header","footer"]
  }
}
{% endschema %}
