{% comment %}
  Joining Process Section - Converted from React to Liquid
  Displays a 4-step process timeline with responsive design
{% endcomment %}

{%- assign steps = section.blocks -%}

<section
  id="program"
  class="joining-process-section"
>
  <div class="joining-process-container">
    {% if section.settings.heading != blank %}
      <div style="text-align: center; margin-bottom: 3rem;">
        <h2 class="joining-process-heading">
          {{ section.settings.heading }}
          {% if section.settings.heading_highlight != blank %}
            <span class="joining-process-heading-highlight">{{ section.settings.heading_highlight }}</span>
          {% endif %}
        </h2>
      </div>
    {% endif %}

    <!-- Interactive Timeline - Desktop -->
    <div class="joining-process-desktop">
      <!-- Timeline Steps Container -->
      <div style="position: relative;">
        <!-- Progress Flow Line - Positioned to align with step circles -->
        <div class="joining-process-progress-line"></div>

        <!-- Timeline Steps Grid -->
        <div class="joining-process-steps-grid" style="grid-template-columns: repeat({{ steps.size | default: 4 }}, 1fr);">
          {% for step in steps %}
            {%- assign step_index = forloop.index0 -%}
            <div class="joining-process-step">
              <!-- Step Circle Container - Precisely positioned -->
              <div class="joining-process-circle-container">
                <div class="joining-process-circle {{ step.settings.bg_color_class | default: 'joining-process-blue' }} {{ step.settings.border_color_class | default: 'joining-process-blue' }}">
                  {% if step.settings.icon != blank %}
                    <span class="joining-process-icon">
                      {{ step.settings.icon }}
                    </span>
                  {% endif %}

                  <!-- Step Number Badge -->
                  <div class="joining-process-badge {{ step.settings.gradient_class | default: 'joining-process-blue-gradient' }}">
                    {{ step.settings.step_number | default: forloop.index | prepend: '0' | slice: -2, 2 }}
                  </div>
                </div>
              </div>

              <!-- Content Card -->
              <div class="joining-process-card {{ step.settings.bg_color_class | default: 'joining-process-blue' }} {{ step.settings.border_color_class | default: 'joining-process-blue' }}">
                <!-- Title and Description -->
                <div>
                  <!-- Category Label -->
                  {% if step.settings.category != blank %}
                    <div class="joining-process-category {{ step.settings.text_color_class | default: 'joining-process-blue-text' }}">
                      {{ step.settings.category }}
                    </div>
                  {% endif %}

                  <!-- Title -->
                  {% if step.settings.title != blank %}
                    <h3 class="joining-process-title joining-process-title--large">
                      {{ step.settings.title }}
                    </h3>
                  {% endif %}

                  <!-- Description -->
                  {% if step.settings.description != blank %}
                    <p class="joining-process-description joining-process-description--large">
                      {{ step.settings.description }}
                    </p>
                  {% endif %}
                </div>

                <!-- Duration and Action - Bottom aligned -->
                {% if step.settings.duration != blank or step.settings.action != blank %}
                  <div class="joining-process-meta">
                    <div class="joining-process-meta-content">
                      {% if step.settings.duration != blank %}
                        <span class="joining-process-duration">
                          <span style="font-size: 0.875rem;">⏱️</span> {{ step.settings.duration }}
                        </span>
                      {% endif %}
                      {% if step.settings.action != blank %}
                        <span class="joining-process-action">
                          {{ step.settings.action }}
                        </span>
                      {% endif %}
                    </div>
                  </div>
                {% endif %}
              </div>

              <!-- Connection Arrow - Precisely aligned with flow line -->
              {% unless forloop.last %}
                <div class="joining-process-arrow">
                  <svg width="32" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: #6b7280;">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </div>
              {% endunless %}
            </div>
          {% endfor %}
        </div>
      </div>
    </div>

    <!-- Mobile & Tablet Timeline -->
    <div class="joining-process-mobile">
      <div class="joining-process-mobile-steps">
        {% for step in steps %}
          <div class="joining-process-mobile-step">
            <!-- Vertical Connection Line - Properly aligned with step circles -->
            {% unless forloop.last %}
              <div class="joining-process-mobile-line"></div>
              <!-- Mobile Arrow -->
              <div class="joining-process-mobile-arrow">
                <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: #6b7280;">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
              </div>
            {% endunless %}

            <div class="joining-process-mobile-content">
              <!-- Step Circle -->
              <div class="joining-process-mobile-circle">
                <div class="joining-process-circle {{ step.settings.bg_color_class | default: 'joining-process-blue' }} {{ step.settings.border_color_class | default: 'joining-process-blue' }}">
                  {% if step.settings.icon != blank %}
                    <span class="joining-process-icon">{{ step.settings.icon }}</span>
                  {% endif %}

                  <!-- Step Number Badge -->
                  <div class="joining-process-badge {{ step.settings.gradient_class | default: 'joining-process-blue-gradient' }}">
                    {{ step.settings.step_number | default: forloop.index | prepend: '0' | slice: -2, 2 }}
                  </div>
                </div>
              </div>

              <!-- Content -->
              <div class="joining-process-mobile-card-container">
                <div class="joining-process-mobile-card {{ step.settings.bg_color_class | default: 'joining-process-blue' }} {{ step.settings.border_color_class | default: 'joining-process-blue' }}">
                  <!-- Title and Description -->
                  <div>
                    <!-- Category Label -->
                    {% if step.settings.category != blank %}
                      <div class="joining-process-category {{ step.settings.text_color_class | default: 'joining-process-blue-text' }}">
                        {{ step.settings.category }}
                      </div>
                    {% endif %}

                    <!-- Title -->
                    {% if step.settings.title != blank %}
                      <h3 class="joining-process-title joining-process-title--large">
                        {{ step.settings.title }}
                      </h3>
                    {% endif %}

                    <!-- Description -->
                    {% if step.settings.description != blank %}
                      <p class="joining-process-description joining-process-description--large">
                        {{ step.settings.description }}
                      </p>
                    {% endif %}
                  </div>

                  <!-- Duration and Action - Bottom aligned -->
                  {% if step.settings.duration != blank or step.settings.action != blank %}
                    <div class="joining-process-mobile-meta">
                      <div class="joining-process-mobile-meta-content">
                        {% if step.settings.duration != blank %}
                          <span class="joining-process-duration">
                            {{ step.settings.duration }}
                          </span>
                        {% endif %}
                        {% if step.settings.action != blank %}
                          <span class="joining-process-action">
                            {{ step.settings.action }}
                          </span>
                        {% endif %}
                      </div>
                    </div>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>

    <!-- Process Flow Visual -->
    <div style="max-width: 64rem; margin: 0 auto 0.75rem;"></div>
  </div>
</section>

<style>
  /* Additional styles for timeline effects */
  .joining-process-timeline {
    position: relative;
  }
  
  .joining-process-step {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .joining-process-step:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }
</style>

{% schema %}
{
  "name": "Joining Process Timeline",
  "tag": "section",
  "class": "section joining-process-section",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "Section Heading",
      "default": "Ready to Fill Your Centre?"
    },
    {
      "type": "text",
      "id": "heading_highlight",
      "label": "Heading Highlight Text",
      "default": "Here's How!"
    }
  ],
  "blocks": [
    {
      "type": "step",
      "name": "Process Step",
      "settings": [
        {
          "type": "text",
          "id": "step_number",
          "label": "Step Number",
          "default": "01"
        },
        {
          "type": "text",
          "id": "category",
          "label": "Category Label",
          "default": "ATTRACT",
          "info": "Small uppercase label above the title"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Step Title",
          "default": "Step Title"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Step Description",
          "default": "Step description goes here."
        },
        {
          "type": "text",
          "id": "icon",
          "label": "Icon (Emoji)",
          "default": "📞",
          "info": "Use an emoji for the step icon"
        },
        {
          "type": "text",
          "id": "duration",
          "label": "Duration",
          "default": "15 min"
        },
        {
          "type": "text",
          "id": "action",
          "label": "Action Text",
          "default": "No commitment required"
        },
        {
          "type": "select",
          "id": "gradient_class",
          "label": "Gradient Color",
          "default": "joining-process-blue-gradient",
          "options": [
            {
              "value": "joining-process-blue-gradient",
              "label": "Blue"
            },
            {
              "value": "joining-process-teal-gradient",
              "label": "Teal"
            },
            {
              "value": "joining-process-orange-gradient",
              "label": "Orange"
            },
            {
              "value": "joining-process-yellow-gradient",
              "label": "Yellow"
            },
            {
              "value": "joining-process-purple-gradient",
              "label": "Purple"
            },
            {
              "value": "joining-process-green-gradient",
              "label": "Green"
            }
          ]
        },
        {
          "type": "select",
          "id": "bg_color_class",
          "label": "Background Color",
          "default": "joining-process-blue",
          "options": [
            {
              "value": "joining-process-blue",
              "label": "Blue"
            },
            {
              "value": "joining-process-teal",
              "label": "Teal"
            },
            {
              "value": "joining-process-orange",
              "label": "Orange"
            },
            {
              "value": "joining-process-yellow",
              "label": "Yellow"
            },
            {
              "value": "joining-process-purple",
              "label": "Purple"
            },
            {
              "value": "joining-process-green",
              "label": "Green"
            }
          ]
        },
        {
          "type": "select",
          "id": "border_color_class",
          "label": "Border Color",
          "default": "joining-process-blue",
          "options": [
            {
              "value": "joining-process-blue",
              "label": "Blue"
            },
            {
              "value": "joining-process-teal",
              "label": "Teal"
            },
            {
              "value": "joining-process-orange",
              "label": "Orange"
            },
            {
              "value": "joining-process-yellow",
              "label": "Yellow"
            },
            {
              "value": "joining-process-purple",
              "label": "Purple"
            },
            {
              "value": "joining-process-green",
              "label": "Green"
            }
          ]
        },
        {
          "type": "select",
          "id": "text_color_class",
          "label": "Text Color",
          "default": "joining-process-blue-text",
          "options": [
            {
              "value": "joining-process-blue-text",
              "label": "Blue"
            },
            {
              "value": "joining-process-teal-text",
              "label": "Teal"
            },
            {
              "value": "joining-process-orange-text",
              "label": "Orange"
            },
            {
              "value": "joining-process-yellow-text",
              "label": "Yellow"
            },
            {
              "value": "joining-process-purple-text",
              "label": "Purple"
            },
            {
              "value": "joining-process-green-text",
              "label": "Green"
            }
          ]
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Joining Process Timeline",
      "blocks": [
        {
          "type": "step",
          "settings": {
            "step_number": "01",
            "category": "ATTRACT",
            "title": "Book Call",
            "description": "Fill the form, reviewed within 48 hours.",
            "icon": "📞",
            "gradient_class": "joining-process-blue-gradient",
            "bg_color_class": "joining-process-blue",
            "border_color_class": "joining-process-blue",
            "text_color_class": "joining-process-blue-text",
            "duration": "15 min",
            "action": "No commitment required"
          }
        },
        {
          "type": "step",
          "settings": {
            "step_number": "02",
            "category": "ENGAGE",
            "title": "Strategy Session",
            "description": "Map your Money Plan.",
            "icon": "💡",
            "gradient_class": "joining-process-teal-gradient",
            "bg_color_class": "joining-process-teal",
            "border_color_class": "joining-process-teal",
            "text_color_class": "joining-process-teal-text",
            "duration": "45 min",
            "action": "Free ROI analysis"
          }
        },
        {
          "type": "step",
          "settings": {
            "step_number": "03",
            "category": "CONVERT",
            "title": "Mutual Acceptance",
            "description": "If suitable, we onboard you.",
            "icon": "✅",
            "gradient_class": "joining-process-orange-gradient",
            "bg_color_class": "joining-process-orange",
            "border_color_class": "joining-process-orange",
            "text_color_class": "joining-process-orange-text",
            "duration": "24 hours",
            "action": "Decision time"
          }
        },
        {
          "type": "step",
          "settings": {
            "step_number": "04",
            "category": "MAXIMIZE",
            "title": "Onboarding & Campaign Launch",
            "description": "Leads start in week 1.",
            "icon": "🚀",
            "gradient_class": "joining-process-yellow-gradient",
            "bg_color_class": "joining-process-yellow",
            "border_color_class": "joining-process-yellow",
            "text_color_class": "joining-process-yellow-text",
            "duration": "Week 1",
            "action": "Growth begins"
          }
        }
      ]
    }
  ]
}
{% endschema %}