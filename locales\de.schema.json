{"settings_schema": {"colors": {"name": "<PERSON><PERSON>", "settings": {"colors_solid_button_labels": {"label": "Beschriftung für durchgehende Schaltfläche", "info": "Wird als Vordergrundfarbe für Akzentfarben verwendet."}, "colors_accent_1": {"label": "Akzent 1", "info": "Wird als Hintergrund für durchgehende Schaltflächen verwendet."}, "colors_accent_2": {"label": "Akzent 2"}, "header__1": {"content": "Primärfarben"}, "header__2": {"content": "Sekundärfarben"}, "colors_text": {"label": "Text", "info": "Wird als Vordergrundfarbe für Hintergrundfarben verwendet."}, "colors_outline_button_labels": {"label": "Umriss-Schaltfläche", "info": "Wird auch für Textlinks verwendet."}, "colors_background_1": {"label": "Hintergrund 1"}, "colors_background_2": {"label": "Hintergrund 2"}}}, "typography": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"type_header_font": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Die Auswahl einer anderen Schriftart kann sich auf die Geschwindigkeit deines Shops auswirken. [Weitere Informationen zu Systemschriftarten.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)"}, "header__1": {"content": "Überschriften"}, "header__2": {"content": "Nachricht"}, "type_body_font": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Die Auswahl einer anderen Schriftart kann sich auf die Geschwindigkeit deines Shops auswirken. [Weitere Informationen zu Systemschriftarten.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)"}}}, "styles": {"name": "Stile", "settings": {"sold_out_badge_color_scheme": {"options__1": {"label": "Hintergrund 1"}, "options__2": {"label": "Invertiert"}, "label": "Farbschema für Ausverkauft-Badges"}, "header__1": {"content": "Badges"}, "header__2": {"content": "Dekorative Elemente"}, "sale_badge_color_scheme": {"options__1": {"label": "Hintergrund 2"}, "options__2": {"label": "Akzent 1"}, "options__3": {"label": "Akzent 2"}, "label": "Farbschema für Sale-Badges"}, "accent_icons": {"options__1": {"label": "Akzent 1"}, "options__2": {"label": "Akzent 2"}, "options__3": {"label": "Umriss-Schaltfläche"}, "options__4": {"label": "Text"}, "label": "Akzent-Symbole"}}}, "social-media": {"name": "Social Media", "settings": {"share_facebook": {"label": "Auf Facebook teilen"}, "share_twitter": {"label": "Auf Twitter twittern"}, "share_pinterest": {"label": "<PERSON><PERSON>nen"}, "header__1": {"content": "Social-Sharing-Optionen"}, "header__2": {"content": "Social-Media-Konten"}, "social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://vimeo.com/shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "http://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "Social-Media-Konten"}}}, "currency_format": {"name": "Währungsformat", "settings": {"content": "Währungscodes", "currency_code_enabled": {"label": "Währungscodes anzeigen"}, "paragraph": "Warenkorb- und Checkout-Preise zeigen immer Währungscodes an. Beispiel: 1,00 USD."}}, "favicon": {"name": "Favicon", "settings": {"favicon": {"label": "Favicon-Bild", "info": "Wird auf 32 x 32 Pixel verkleinert"}}}, "layout": {"name": "Layout", "settings": {"page_width": {"label": "Maximale Breite", "options__1": {"label": "1200 px"}, "options__2": {"label": "1600 px"}}}}, "search_input": {"name": "Sucheingabe", "settings": {"header": {"content": "Produktvorschläge"}, "predictive_search_enabled": {"label": "Produktvorschläge aktivieren"}, "predictive_search_show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen", "info": "<PERSON><PERSON><PERSON>, wenn Produktvorschläge aktiviert sind."}, "predictive_search_show_price": {"label": "<PERSON><PERSON> anzeigen", "info": "<PERSON><PERSON><PERSON>, wenn Produktvorschläge aktiviert sind."}}}}, "sections": {"announcement-bar": {"name": "Ankündigungsleiste", "blocks": {"announcement": {"name": "Ankündigung", "settings": {"text": {"label": "Text"}, "color_scheme": {"label": "Farbschema", "options__1": {"label": "Hintergrund 1"}, "options__2": {"label": "Hintergrund 2"}, "options__3": {"label": "Invertiert"}, "options__4": {"label": "Akzent 1"}, "options__5": {"label": "Akzent 2"}}, "link": {"label": "Link"}}}}}, "collage": {"name": "Collage", "settings": {"heading": {"label": "Überschrift"}, "desktop_layout": {"label": "Desktop-Layout", "options__1": {"label": "Großer Block links"}, "options__2": {"label": "Großer Block rechts"}}, "mobile_layout": {"label": "Mobiles Layout", "options__1": {"label": "Collage"}, "options__2": {"label": "<PERSON>lt<PERSON>"}}}, "blocks": {"image": {"name": "Bild", "settings": {"image": {"label": "Bild"}, "image_padding": {"label": "Bild-Padding hinzufügen", "info": "<PERSON><PERSON><PERSON>e Bild-Padding aus, wenn du nicht möchtest, dass dein Bild abgeschnitten wird."}, "color_scheme": {"options__1": {"label": "Akzent 1"}, "options__2": {"label": "Akzent 2"}, "options__3": {"label": "Hintergrund 1"}, "options__4": {"label": "Hintergrund 2"}, "options__5": {"label": "Invertiert"}, "label": "Farbschema", "info": "<PERSON><PERSON>hle Bild-Padding aus, um Farbe sichtbar zu machen."}}}, "product": {"name": "Produkt", "settings": {"product": {"label": "Produkt"}, "secondary_background": {"label": "Sekundären Hintergrund anzeigen"}, "second_image": {"label": "Hover-Effekt mit zweitem Bild"}, "image_padding": {"label": "Bild-Padding hinzufügen", "info": "<PERSON><PERSON><PERSON><PERSON> Bild-<PERSON><PERSON> aus, wenn du nicht möchtest, dass deine Bilder abgeschnitten werden."}}}, "collection": {"name": "<PERSON><PERSON><PERSON>", "settings": {"collection": {"label": "<PERSON><PERSON><PERSON>"}, "image_padding": {"label": "Bild-Padding hinzufügen", "info": "<PERSON><PERSON><PERSON>e Bild-Padding aus, wenn du nicht möchtest, dass dein Bild abgeschnitten wird."}, "color_scheme": {"options__1": {"label": "Akzent 1"}, "options__2": {"label": "Akzent 2"}, "options__3": {"label": "Hintergrund 1"}, "options__4": {"label": "Hintergrund 2"}, "options__5": {"label": "Invertiert"}, "label": "Farbschema"}}}, "video": {"name": "Video", "settings": {"cover_image": {"label": "Titelbild"}, "video_url": {"label": "URL", "info": "Video wird in einem Pop-up abgespielt, wenn der Abschnitt andere Blöcke enthält.", "placeholder": "YouTube- oder Vimeo-URL verwenden"}, "image_padding": {"label": "Bild-Padding hinzufügen", "info": "<PERSON><PERSON><PERSON>e Bild-Padding aus, wenn du nicht möchtest, dass dein Titelbild abgeschnitten wird."}, "description": {"label": "Video-Alt-Text", "info": "Beschreibe das Video, um Kunden mit Bildschirmlesegeräten den Zugriff zu ermöglichen."}}}}, "presets": {"name": "Collage"}}, "collection-list": {"name": "Kategorieliste", "settings": {"title": {"label": "Überschrift"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}, "info": "Bearbeite deine Kategorien, um Bilder hinzuzufügen. [Mehr Informationen](https://help.shopify.com/en/manual/products/collections)"}, "color_scheme": {"options__1": {"label": "Akzent 1"}, "options__2": {"label": "Akzent 2"}, "options__3": {"label": "Hintergrund 1"}, "options__4": {"label": "Hintergrund 2"}, "options__5": {"label": "Invertiert"}, "label": "Farbschema"}, "swipe_on_mobile": {"label": "Durchziehen für mobiles Gerät aktivieren"}, "image_padding": {"label": "Bild-Padding hinzufügen"}, "show_view_all": {"label": "Aktiviere die Schaltfläche \"Alle anzeigen\", wenn die Liste mehr Kategorien enthält, als angezeigt werden"}}, "blocks": {"featured_collection": {"name": "<PERSON><PERSON><PERSON>", "settings": {"collection": {"label": "<PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "Kategorieliste"}}, "contact-form": {"name": "Kontaktformular", "presets": {"name": "Kontaktformular"}}, "custom-liquid": {"name": "Benutzerdefiniertes Liquid", "settings": {"custom_liquid": {"label": "Benutzerdefiniertes Liquid", "info": "Füge App-Snippets oder anderen Liquid-Code hinzu, um fortgeschrittene Anpassungen vorzunehmen."}}, "presets": {"name": "Benutzerdefiniertes Liquid"}}, "featured-blog": {"name": "Blog-Beiträge", "settings": {"heading": {"label": "Überschrift"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Blog-Beiträge"}, "show_view_all": {"label": "Aktiviere die Schaltfläche \"Alle anzeigen\", wenn der Blog mehr Blog-Beiträge enthält, als angezeigt werden"}, "show_image": {"label": "Feature-<PERSON><PERSON><PERSON> anzeigen", "info": "Verwende für Bilder ein Seitenverhältnis von 2:3 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "soft_background": {"label": "Sekundären Hintergrund anzeigen"}, "show_date": {"label": "<PERSON><PERSON> anzeigen"}, "show_author": {"label": "<PERSON><PERSON> <PERSON>"}}, "blocks": {"title": {"name": "Titel", "settings": {"show_date": {"label": "<PERSON><PERSON> anzeigen"}, "show_author": {"label": "<PERSON><PERSON> <PERSON>"}}}, "summary": {"name": "Auszug"}, "link": {"name": "Link"}}, "presets": {"name": "Blog-Beiträge"}}, "featured-collection": {"name": "Vorgestellte Kategorie", "settings": {"title": {"label": "Überschrift"}, "collection": {"label": "<PERSON><PERSON><PERSON>"}, "products_to_show": {"label": "Maximal anzuzeigende Produkte"}, "show_view_all": {"label": "Aktiviere die Schaltfläche \"Alle anzeigen\", wenn die Kategorie mehr Produkte enthält, als angezeigt werden"}, "swipe_on_mobile": {"label": "Durchziehen für mobiles Gerät aktivieren"}, "header": {"content": "Produktkarte"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "add_image_padding": {"label": "Bild-Padding hinzufügen"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "show_image_outline": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> anzeigen"}}, "presets": {"name": "Vorgestellte Kategorie"}}, "footer": {"name": "Fußzeile", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Überschrift", "info": "Überschrift ist erforderlich, um das Menü anzuzeigen."}, "menu": {"label": "<PERSON><PERSON>", "info": "<PERSON>eigt nur Top-Level-Menüpunkte an."}}}, "text": {"name": "Text", "settings": {"heading": {"label": "Überschrift"}, "subtext": {"label": "Subtext"}}}}, "settings": {"color_scheme": {"options__1": {"label": "Akzent 1"}, "options__2": {"label": "Akzent 2"}, "options__3": {"label": "Hintergrund 1"}, "options__4": {"label": "Hintergrund 2"}, "options__5": {"label": "Invertiert"}, "label": "Farbschema"}, "newsletter_enable": {"label": "E-Mail-Anmeldung anzeigen"}, "newsletter_heading": {"label": "Überschrift"}, "header__1": {"content": "E-Mail-Anmeldung", "info": "<PERSON><PERSON><PERSON><PERSON>, die automatisch zu deiner Kundenliste \"Akzeptiert Marketing\" hinzugefügt wurden. [Mehr Informationen](https://help.shopify.com/en/manual/customers/manage-customers)"}, "header__2": {"content": "Social Media-Symbole", "info": "Um deine Social Media-Konten anzuzeigen, verlinke sie in deinen Theme-Einstellungen."}, "show_social": {"label": "Social-Media-Symbole anzeigen"}, "header__3": {"content": "Auswahl für Land/Region"}, "header__4": {"info": "Gehe zu den [Zahlungseinstellungen](/admin/settings/payments), um ein Land / eine Region hinzuzufügen."}, "enable_country_selector": {"label": "Auswahl für Land/Region aktivieren"}, "header__5": {"content": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "header__6": {"info": "Gehe zu den [Spracheinstellungen](/admin/settings/languages), um eine Sprache hinzuzufügen."}, "enable_language_selector": {"label": "Sprachauswahl aktivieren"}, "header__7": {"content": "Zahlungsmethoden"}, "payment_enable": {"label": "Zahlungssymbole anzeigen"}}}, "header": {"name": "Header", "settings": {"logo": {"label": "Logo-Bild"}, "logo_width": {"unit": "Pixel", "label": "Breite des benutzerdefinierten Logos"}, "logo_position": {"label": "Logopositionierung auf großen Bildschirmen", "options__1": {"label": "Mitte links"}, "options__2": {"label": "Oben links"}, "options__3": {"label": "<PERSON><PERSON>"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "Trennlinie anzeigen"}, "enable_sticky_header": {"label": "Fixierten Header aktivieren", "info": "Der Header wird auf dem Bildschirm angezeigt, wenn der Kunde nach oben scrollt."}}}, "image-banner": {"name": "Bild-Banner", "settings": {"image": {"label": "<PERSON><PERSON><PERSON> Bild"}, "image_2": {"label": "Zweites Bild"}, "desktop_text_box_position": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Unten"}, "label": "Desktop-Textposition"}, "color_scheme": {"options__1": {"label": "Akzent 1"}, "options__2": {"label": "Akzent 2"}, "options__3": {"label": "Hintergrund 1"}, "options__4": {"label": "Hintergrund 2"}, "options__5": {"label": "Invertiert"}, "label": "Farbschema", "info": "<PERSON><PERSON><PERSON>, wenn Textfeld angezeigt wird"}, "stack_images_on_mobile": {"label": "Gestapelte Bilder auf Mobilgeräten"}, "adapt_height_first_image": {"label": "Abschnittshöhe an Größe des ersten Bildes anpassen"}, "show_text_box": {"label": "Textfeld auf dem Desktop anzeigen"}, "image_overlay_opacity": {"label": "Deckkraft der Bildüberlagerung"}, "header": {"content": "Mobiles Layout"}, "show_text_below": {"label": "Text unter Bildern anzeigen"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift"}, "heading_size": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Schriftgröße der Überschrift"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Beschreibung"}}}, "buttons": {"name": "Schaltflächen", "settings": {"button_label_1": {"label": "Erste Beschriftung der Schaltfläche", "info": "Lasse die Beschriftung leer, um die Schaltfläche auszublenden."}, "button_link_1": {"label": "Erster Link der Schaltfläche"}, "button_style_secondary_1": {"label": "Umriss-Stil für Schaltfläche verwenden"}, "button_label_2": {"label": "Zweite Beschriftung der Schaltfläche", "info": "Lasse die Beschriftung leer, um die Schaltfläche auszublenden."}, "button_link_2": {"label": "Zweiter Link der Schaltfläche"}, "button_style_secondary_2": {"label": "Umriss-Stil für Schaltfläche verwenden"}}}}, "presets": {"name": "Bild-Banner"}}, "image-with-text": {"name": "Bild mit Text", "settings": {"image": {"label": "Bild"}, "height": {"options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Bildverhältnis"}, "color_scheme": {"options__1": {"label": "Hintergrund 1"}, "options__2": {"label": "Hintergrund 2"}, "options__3": {"label": "Invertiert"}, "options__4": {"label": "Akzent 1"}, "options__5": {"label": "Akzent 2"}, "label": "Farbschema"}, "layout": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Text zu<PERSON>t"}, "label": "Desktop-Layout", "info": "Das Standardlayout für Mobilgeräte ist \"Bild zuerst\"."}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Beschreibung"}}}, "button": {"name": "Schaltfläche", "settings": {"button_label": {"label": "Schaltflächenbeschriftung", "info": "Lasse die Beschriftung leer, um die Schaltfläche auszublenden."}, "button_link": {"label": "Schaltflächenlink"}}}}, "presets": {"name": "Bild mit Text"}}, "main-article": {"name": "Blog-Beitrag", "blocks": {"featured_image": {"name": "Feature-Bild", "settings": {"image_height": {"label": "<PERSON><PERSON><PERSON> des Feature-Bildes", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Verwende für Bilder ein Seitenverhältnis von 16:9 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "title": {"name": "Titel", "settings": {"blog_show_date": {"label": "<PERSON><PERSON> anzeigen"}, "blog_show_author": {"label": "<PERSON><PERSON> <PERSON>"}}}, "content": {"name": "Inhalt"}, "social_sharing": {"name": "Social-Sharing-Schaltflächen"}, "share": {"name": "Teilen", "settings": {"featured_image_info": {"content": "<PERSON><PERSON> <PERSON> e<PERSON> Link in Social-Media-Posts einfügst, wird das Feature-Bild der Seite als Vorschaubild angezeigt. [Mehr Informationen](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Ein Titel und eine Beschreibung des Shops sind im Vorschaubild enthalten. [Mehr Informationen](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Text"}}}}}, "main-blog": {"name": "Blog-Beiträge", "settings": {"header": {"content": "Blog-Beitrags-Karte"}, "show_image": {"label": "Feature-<PERSON><PERSON><PERSON> anzeigen", "info": "Verwende für Bilder ein Seitenverhältnis von 2:3 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "paragraph": {"content": "Bearbeite deine Blog-Beiträge, um Auszüge zu ändern. [Mehr Informationen](https://help.shopify.com/en/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "show_date": {"label": "<PERSON><PERSON> anzeigen"}, "show_author": {"label": "<PERSON><PERSON> <PERSON>"}}, "blocks": {"title": {"name": "Titel", "settings": {"show_date": {"label": "<PERSON><PERSON> anzeigen"}, "show_author": {"label": "<PERSON><PERSON> <PERSON>"}}}, "summary": {"name": "Auszug"}, "link": {"name": "Link"}}}, "main-cart-footer": {"name": "Zwischensumme", "settings": {"show_cart_note": {"label": "Warenkorbanmerkung aktivieren"}}, "blocks": {"subtotal": {"name": "Zwischensumme"}, "buttons": {"name": "Checkout-Schaltfläche"}}}, "main-cart-items": {"name": "Artikel", "settings": {"show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}}}, "main-collection-banner": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"paragraph": {"content": "Bearbeite deine Kategorien, um eine Beschreibung oder ein Bild hinzuzufügen. [Mehr Informationen](https://help.shopify.com/en/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Kategoriebeschreibung anzeigen"}, "show_collection_image": {"label": "Kategoriebild anzeigen", "info": "Verwende für Bilder ein Seitenverhältnis von 16:9 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-collection-product-grid": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"products_per_page": {"label": "Produkte pro Seite"}, "enable_filtering": {"label": "Filtern aktivieren", "info": "[Filter](/admin/menus) anpassen"}, "enable_sorting": {"label": "Sortieren aktivieren"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "add_image_padding": {"label": "Bild-Padding hinzufügen"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "header__1": {"content": "Filtern und Sortieren"}, "header__3": {"content": "Produktkarte"}, "enable_tags": {"label": "Filtern aktivieren", "info": "[Filter anpassen](/admin/menus)"}, "show_image_outline": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> anzeigen"}, "enable_sort": {"label": "Sortieren aktivieren"}, "collapse_on_larger_devices": {"label": "Auf größeren Bildschirmen minimieren"}}}, "main-list-collections": {"name": "Listenseite für Kategorien", "settings": {"title": {"label": "Überschrift"}, "sort": {"label": "<PERSON><PERSON><PERSON> sortieren nach:", "options__1": {"label": "Alphabetisch, A-Z"}, "options__2": {"label": "Alphabetisch, Z-A"}, "options__3": {"label": "<PERSON><PERSON>, neu zu alt"}, "options__4": {"label": "<PERSON><PERSON>, alt zu neu"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hoch zu niedrig"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ni<PERSON><PERSON> zu hoch"}}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}, "info": "Bearbeite deine Kategorien, um Bilder hinzuzufügen. [Mehr Informationen](https://help.shopify.com/en/manual/products/collections)"}, "color_scheme": {"options__1": {"label": "Akzent 1"}, "options__2": {"label": "Akzent 2"}, "options__3": {"label": "Hintergrund 1"}, "options__4": {"label": "Hintergrund 2"}, "options__5": {"label": "Invertiert"}, "label": "Farbschema"}, "image_padding": {"label": "Bild-Padding hinzufügen"}}}, "main-page": {"name": "Seite"}, "main-password-footer": {"name": "Passwort-Fußzeile", "settings": {"color_scheme": {"options__1": {"label": "Akzent 1"}, "options__2": {"label": "Akzent 2"}, "options__3": {"label": "Hintergrund 1"}, "options__4": {"label": "Hintergrund 2"}, "options__5": {"label": "Invertiert"}, "label": "Farbschema"}}}, "main-password-header": {"name": "Passwort-Header", "settings": {"logo": {"label": "Logo-Bild"}, "logo_max_width": {"label": "Breite des benutzerdefinierten Logos", "unit": "Pixel"}, "color_scheme": {"options__1": {"label": "Akzent 1"}, "options__2": {"label": "Akzent 2"}, "options__3": {"label": "Hintergrund 1"}, "options__4": {"label": "Hintergrund 2"}, "options__5": {"label": "Invertiert"}, "label": "Farbschema"}}}, "main-product": {"blocks": {"text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Textstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}, "title": {"name": "Titel"}, "price": {"name": "Pre<PERSON>"}, "quantity_selector": {"name": "Men<PERSON>aus<PERSON><PERSON>"}, "variant_picker": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"picker_type": {"label": "Art", "options__1": {"label": "Dropdown"}, "options__2": {"label": "Schaltfläche"}}}}, "buy_buttons": {"name": "Buy Buttons", "settings": {"show_dynamic_checkout": {"label": "Dynamischer Checkout-<PERSON><PERSON> anzeigen", "info": "Wenn sie die Zahlungsmethoden verwenden, die in deinem Shop verfügbar sind, sehen <PERSON> ihre bevorzugte Option, z. B. PayPal oder Apple Pay. [Mehr Informationen](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "pickup_availability": {"name": "Verfügbarkeit von Abholungen"}, "description": {"name": "Beschreibung"}, "share": {"name": "Teilen", "settings": {"featured_image_info": {"content": "<PERSON><PERSON> <PERSON> e<PERSON> Link in Social-Media-Posts einfügst, wird das Feature-Bild der Seite als Vorschaubild angezeigt. [Mehr Informationen](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Ein Titel und eine Beschreibung des Shops sind im Vorschaubild enthalten. [Mehr Informationen](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "Text"}}}, "collapsible_tab": {"name": "Minimierbarer <PERSON>", "settings": {"heading": {"info": "Füge eine Überschrift ein, die den Inhalt erklärt.", "label": "Überschrift"}, "content": {"label": "Tab-Inhalt"}, "page": {"label": "Tab-Inhalt der Seite"}, "icon": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Chat-Blase"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__5": {"label": "<PERSON><PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON>"}, "options__7": {"label": "<PERSON><PERSON>"}, "options__8": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__9": {"label": "<PERSON><PERSON>"}, "options__10": {"label": "<PERSON><PERSON>"}, "options__11": {"label": "<PERSON><PERSON><PERSON>"}, "options__12": {"label": "<PERSON><PERSON> mit Pin"}, "options__13": {"label": "Hose"}, "options__14": {"label": "Flugzeug"}, "options__15": {"label": "<PERSON><PERSON><PERSON>"}, "options__16": {"label": "Fragezeichen"}, "options__17": {"label": "Rückgabe"}, "options__18": {"label": "Lineal"}, "options__19": {"label": "<PERSON><PERSON><PERSON>"}, "options__20": {"label": "<PERSON><PERSON><PERSON>"}, "options__21": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__22": {"label": "Stern"}, "options__23": {"label": "Lieferwagen"}, "options__24": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Symbol"}}}, "popup": {"name": "Pop-up", "settings": {"link_label": {"label": "Link-Label"}, "page": {"label": "Seite"}}}, "custom_liquid": {"name": "Benutzerdefiniertes Liquid", "settings": {"custom_liquid": {"label": "Benutzerdefiniertes Liquid", "info": "Füge App-Snippets oder anderen Liquid-Code hinzu, um fortgeschrittene Anpassungen vorzunehmen."}}}}, "settings": {"header": {"content": "Medien", "info": "Mehr Informationen über [Medienarten](https://help.shopify.com/manual/products/product-media)."}, "enable_video_looping": {"label": "Videoschleife aktivieren"}, "enable_sticky_info": {"label": "Fixierte Produktinformationen auf großen Bildschirmen aktivieren"}, "hide_variants": {"label": "Medien anderer <PERSON> aus<PERSON>, sobald eine Variante ausgewählt wurde"}}, "name": "Produktinformationen"}, "main-search": {"name": "Suchergebnisse", "settings": {"image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "add_image_padding": {"label": "Bild-Padding hinzufügen"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "header__1": {"content": "Produktkarte"}, "header__2": {"content": "Blog-Karte"}, "article_show_date": {"label": "<PERSON><PERSON> anzeigen"}, "article_show_author": {"label": "<PERSON><PERSON> <PERSON>"}, "show_image_outline": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> anzeigen"}}}, "multicolumn": {"name": "<PERSON>t mehreren <PERSON>", "settings": {"title": {"label": "Überschrift"}, "image_width": {"label": "Bildbreite", "options__1": {"label": "Drittelbreite der Spalte"}, "options__2": {"label": "Halbe Breite der Spalte"}, "options__3": {"label": "Ganze Breite der Spalte"}}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}, "options__4": {"label": "Kreis"}}, "column_alignment": {"label": "Spaltenausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "background_style": {"label": "Sekundärer Hintergrund", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Als Spaltenhintergrund anzeigen"}, "options__3": {"label": "Als Abschnittshintergrund anzeigen"}}, "button_label": {"label": "Schaltflächenbeschriftung"}, "button_link": {"label": "Schaltflächenlink"}, "swipe_on_mobile": {"label": "Durchziehen für mobiles Gerät aktivieren"}}, "blocks": {"column": {"name": "<PERSON>lt<PERSON>", "settings": {"image": {"label": "Bild"}, "title": {"label": "Überschrift"}, "text": {"label": "Beschreibung"}}}}, "presets": {"name": "<PERSON>t mehreren <PERSON>"}}, "newsletter": {"name": "E-Mail-Anmeldung", "settings": {"color_scheme": {"label": "Farbschema", "options__1": {"label": "Akzent 1"}, "options__2": {"label": "Akzent 2"}, "options__3": {"label": "Hintergrund 1"}, "options__4": {"label": "Hintergrund 2"}, "options__5": {"label": "Invertiert"}}, "full_width": {"label": "Abschnitt über die gesamte Breite"}, "paragraph": {"content": "Durch jedes E-Mail-Abonnement wird ein Kundenkonto erstellt. [Mehr Informationen](https://help.shopify.com/en/manual/customers)"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift"}}}, "paragraph": {"name": "Unter-Überschrift", "settings": {"paragraph": {"label": "Beschreibung"}}}, "email_form": {"name": "E-Mail-Formular"}}, "presets": {"name": "E-Mail-Anmeldung"}}, "page": {"name": "Seite", "settings": {"page": {"label": "Seite"}}, "presets": {"name": "Seite"}}, "product-recommendations": {"name": "Produktempfehlungen", "settings": {"heading": {"label": "Überschrift"}, "header__1": {"content": "Produktempfehlungen"}, "header__2": {"content": "Produktkarte"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "add_image_padding": {"label": "Bild-Padding hinzufügen"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "paragraph__1": {"content": "Dynamische Empfehlungen nutzen Bestell- und Produktinformationen, um sich mit der Zeit zu verändern und zu verbessern. [Weitere Informationen](https://help.shopify.com/en/themes/development/recommended-products)"}, "show_image_outline": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> anzeigen"}}}, "rich-text": {"name": "Rich Text", "settings": {"color_scheme": {"options__1": {"label": "Akzent 1"}, "options__2": {"label": "Akzent 2"}, "options__3": {"label": "Hintergrund 1"}, "options__4": {"label": "Hintergrund 2"}, "options__5": {"label": "Invertiert"}, "label": "Farbschema"}, "full_width": {"label": "Abschnitt über die gesamte Breite"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift"}, "heading_size": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Schriftgröße der Überschrift", "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "text": {"name": "Text", "settings": {"text": {"label": "Beschreibung"}}}, "button": {"name": "Schaltfläche", "settings": {"button_label": {"label": "Schaltflächenbeschriftung"}, "button_link": {"label": "Schaltflächenlink"}, "button_style_secondary": {"label": "Umriss-Stil für Schaltfläche verwenden"}}}}, "presets": {"name": "Rich Text"}}, "apps": {"name": "Apps", "settings": {"include_margins": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> so gestalten wie das Theme"}}, "presets": {"name": "Apps"}}, "video": {"name": "Video", "settings": {"heading": {"label": "Titel"}, "cover_image": {"label": "Titelbild"}, "video_url": {"label": "URL", "placeholder": "YouTube- oder Vimeo-URL verwenden", "info": "Video wird auf der Seite abgespielt."}, "description": {"label": "Video-Alt-Text", "info": "Beschreibe das Video, um Kunden mit Bildschirmlesegeräten den Zugriff zu ermöglichen."}, "image_padding": {"label": "Bild-Padding hinzufügen", "info": "<PERSON><PERSON><PERSON>e Bild-Padding aus, wenn du nicht möchtest, dass dein Titelbild abgeschnitten wird."}, "full_width": {"label": "Abschnitt über die gesamte Breite"}}, "presets": {"name": "Video"}}, "featured-product": {"name": "Vorgestelltes Produkt", "blocks": {"text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Textstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}, "title": {"name": "Titel"}, "price": {"name": "Pre<PERSON>"}, "quantity_selector": {"name": "Men<PERSON>aus<PERSON><PERSON>"}, "variant_picker": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"picker_type": {"label": "Art", "options__1": {"label": "Dropdown"}, "options__2": {"label": "Schaltfläche"}}}}, "buy_buttons": {"name": "Buy Buttons", "settings": {"show_dynamic_checkout": {"label": "Dynamischen Checkout-<PERSON><PERSON> anzeigen", "info": "Wenn sie die Zahlungsmethoden verwenden, die in deinem Shop verfügbar sind, sehen <PERSON> ihre bevorzugte Option, z. B. PayPal oder Apple Pay. [Mehr Informationen](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Beschreibung"}, "share": {"name": "Teilen", "settings": {"featured_image_info": {"content": "<PERSON><PERSON> <PERSON> e<PERSON> Link in Social-Media-Posts einfügst, wird das Feature-Bild der Seite als Vorschaubild angezeigt. [Mehr Informationen](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Ein Titel und eine Beschreibung des Shops sind im Vorschaubild enthalten. [Mehr Informationen](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Text"}}}, "custom_liquid": {"name": "Benutzerdefiniertes Liquid", "settings": {"custom_liquid": {"label": "Benutzerdefiniertes Liquid"}}}}, "settings": {"product": {"label": "Produkt"}, "secondary_background": {"label": "Sekundären Hintergrund anzeigen"}, "header": {"content": "Medien", "info": "Mehr Informationen zu [Medienarten](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Videoschleife aktivieren"}}, "presets": {"name": "Vorgestelltes Produkt"}}}}