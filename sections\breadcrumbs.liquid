{% liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
%}

{{ 'page-title.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
  {% if section.settings.custom_colors %}
    .custom-colors-{{ section.id }}{
      --color-foreground: {{ section.settings.foreground.red }}, {{ section.settings.foreground.green }}, {{ section.settings.foreground.blue }};
      --color-background: {{ section.settings.background.red }}, {{ section.settings.background.green }}, {{ section.settings.background.blue }};
    }
    {% endif %}

  {% if section.settings.image != blank %}
      .breadcrumb--banner-media-wrapper::before {
        position: absolute;
        content: "";
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
    	opacity: {{ section.settings.image_overlay_opacity | divided_by: 100.0 }};
        background-color: rgba(0,0,0,1);
        z-index: 1;
    }
    {% endif %}



    .breadcrumb--content .space-between {
        display: flex;
        justify-content: space-between;
        flex-direction: row-reverse;
        align-items: center;
    }
    .breadcrumb--content .space-between h1.page_header__title_label.h3 {
        margin: 0;
        padding: 0;
    }



  
{%- endstyle -%}

{%- liquid
  assign product_columns = section.settings.text_center
  assign textAlignClass = ''

  case product_columns
    when 'left'
      assign textAlignClass = 'text-left'
    when 'right'
      assign textAlignClass = 'text-right'
    when 'space-between'
      assign textAlignClass = 'space-between'
    else
      assign textAlignClass = 'text-center'
  endcase

  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif
-%}
{% if theme_rtl %}
  {{ 'breadcrumbs-rtl.css' | asset_url | stylesheet_tag }}
{% endif %}
{%- assign t = template | split: '.' | first -%}
{%- if section.settings.show_page_title or section.settings.show_breadcrumb_navigation -%}
  <div class="breadcrumbs {% if section.settings.image != blank %} breadcrumb--banner-media-wrapper {% endif %} color-{{ section.settings.color_scheme }} {% if section.settings.custom_colors  %} custom-colors-{{ section.id }} {% endif %}  section-{{ section.id }}-padding">
    {%- if section.settings.image != blank -%}
      {% liquid
        assign height = section.settings.image.width | divided_by: section.settings.image.aspect_ratio
      %}
      <div class="breadcrumb--banner__media media">
        {{
          section.settings.image
          | image_url: width: 3840
          | image_tag:
            loading: 'lazy',
            height: height,
            sizes: '100vw',
            widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'
        }}
      </div>
    {%- endif -%}

    <div class="{{ container }} breadcrumb--content">
      <div class="row">
        <div class="col-12 {{ textAlignClass }}">

          {%- if section.settings.show_breadcrumb_navigation -%}
            {%- render 'breadcrumb-nav', t: t' -%}
          {%- endif -%}
          {%- if section.settings.show_page_title -%}
            <div class="page_header__title bread_p_t">
              {%- case t -%}
                {%- when 'page' -%}
                  <h1 class="page_header__title_label {{ section.settings.heading_size }}">{{ page.title }}</h1>
                {%- when 'product' -%}
                  <h1 class="page_header__title_label {{ section.settings.heading_size }}">{{ product.title }}</h1>
                {%- when 'collection' -%}
                  <h1 class="page_header__title_label {{ section.settings.heading_size }}">{{ collection.title }}</h1>
                {%- when 'blog' -%}
                  <h1 class="page_header__title_label {{ section.settings.heading_size }}">{{ blog.title }}</h1>
                {%- when 'article' -%}
                  <h1 class="page_header__title_label {{ section.settings.heading_size }}">{{ article.title }}</h1>
                {%- else -%}
                  <h1 class="page_header__title_label {{ section.settings.heading_size }}">{{ page_title }}</h1>
              {%- endcase -%}
            </div>
          {%- endif -%}


        </div>
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
 {
   "name": "Page title banner",
   "settings": [
       {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
       {
         "type": "image_picker",
         "id": "image",
         "label": "Background image"
       },
       {
         "type": "range",
         "id": "image_overlay_opacity",
         "min": 0,
         "max": 100,
         "step": 10,
         "unit": "%",
         "label": "Image overlay opacity",
         "default": 0
       },
      {
     "type": "header",
     "content": "Section header"
   },
	{
         "type": "checkbox",
         "id": "show_page_title",
         "default": true,
         "label": "Show page heading"
       },
	{
             "type": "select",
             "id": "heading_size",
             "options": [
               {
                 "value": "h3",
                 "label": "Small"
               },
               {
                 "value": "h2",
                 "label": "Medium"
               },
               {
                 "value": "h1",
                 "label": "Large"
               }
             ],
             "default": "h1",
             "label": "Heading size"
       },
	{
         "type": "checkbox",
         "id": "show_breadcrumb_navigation",
         "default": true,
         "label": "Show Breadcrumb Navigation"
       },
	{
         "type": "select",
         "id": "text_center",
         "label": "Text Align",
         "options": [
           {
             "label": "Left",
             "value": "left"
           },
           {
             "label": "Right",
             "value": "right"
           },
		   {
             "label": "Center",
             "value": "center"
           },
		   {
             "label": "Space Between ",
             "value": "space-between"
           }
         ],
         "default": "center"
       },

	 {
     "type": "header",
     "content": "Section padding"
   },
{
         "type": "paragraph",
         "content": "Desktop"
       },
       {
         "type": "range",
         "id": "padding_top",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding top",
         "default": 50
       },
       {
         "type": "range",
         "id": "padding_bottom",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding bottom",
         "default": 50
       },
	{
         "type": "paragraph",
         "content": "Mobile"
       },
	{
         "type": "range",
         "id": "mobile_padding_top",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding top",
         "default": 50
       },
       {
         "type": "range",
         "id": "mobile_padding_bottom",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding bottom",
         "default": 50
       },
     {
     "type": "header",
     "content": "Colors"
   },
     
     {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:sections.all.colors.label",
          "default": "background-2"
        },
     
     {
      "type": "checkbox",
      "id": "custom_colors",
      "label": "Replace with your custom colors",
      "default": false
    },
    {
      "type": "color",
      "id": "foreground",
      "default": "#121212",
      "label": "Foreground color"
    },
    {
      "type": "color",
      "id": "background",
      "default": "#F7F8FC",
      "label": "Background color"
    }
]
 }
{% endschema %}
