<!-- Minimal scroll target section - 1px x 1px invisible anchor point -->
<div 
  id="{{ section.settings.target_id | default: 'scroll-target' }}" 
  class="scroll-target-section"
  style="
    width: 1px; 
    height: 1px; 
    visibility: hidden; 
    position: relative;
    margin: 0;
    padding: 0;
    overflow: hidden;
  "
  data-scroll-target="{{ section.settings.target_id | default: 'scroll-target' }}"
>
  <!-- Invisible content for scroll positioning -->
  <span style="font-size: 0; line-height: 0;">&nbsp;</span>
</div>

{% schema %}
{
  "name": "Scroll Target",
  "tag": "section",
  "class": "section-scroll-target",
  "settings": [
    {
      "type": "text",
      "id": "target_id",
      "label": "Target ID",
      "default": "scroll-target",
      "info": "Unique ID for the scroll target. Use this ID in your customize button's onclick handler."
    },
    {
      "type": "header",
      "content": "Usage Instructions"
    },
    {
      "type": "paragraph",
      "content": "This section creates an invisible 1px × 1px scroll target. Add this section to your product page where you want the customize button to scroll to. Then use JavaScript like: document.getElementById('your-target-id').scrollIntoView({behavior: 'smooth'});"
    }
  ],
  "presets": [
    {
      "name": "Scroll Target"
    }
  ]
}
{% endschema %}