{%- liquid

    assign variant = product_card_product.selected_or_first_available_variant 

	assign productCountdown = product_card_product.metafields.meta.product_countdown.value
    assign todayDate = "now" | date: "%s" 
    assign countDownDate = productCountdown | date: '%s'
-%}

<div class="product-grid-item col mb-30">
  <table>
    <tbody>
    <tr>
       <td class="product-grid-item__thumbnail" data-pid="{{ product_card_product.id }}">
         
         <button class="cp_remove--button compare__remove--btn mb-20" data-product-handle="{{ product_card_product.handle }}" data-pid="{{ product_card_product.id }}"> 
           <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path></svg>
         </button>
      
      <div class="product__card--media-wrapper"> 
        {%- if product_card_product.featured_media -%}
        {%- liquid
              assign featured_media_aspect_ratio = product_card_product.featured_media.aspect_ratio
  
              if product_card_product.featured_media.aspect_ratio == nil
                assign featured_media_aspect_ratio = 1
              endif
            -%}
        <a href="{{ product_card_product.url | default: '#' }}" class="d-block">
          <div class="media media--transparent media--{{ media_size }} media--hover-effect"
               {% if media_size == 'adapt' and product_card_product.featured_media %} style="padding-bottom: {{ 1 | divided_by: featured_media_aspect_ratio | times: 100 }}%;"{% endif %}
               >
            <img
                 srcset="{%- if product_card_product.featured_media.width >= 165 -%}{{ product_card_product.featured_media | img_url: '165x' }} 165w,{%- endif -%}
                         {%- if product_card_product.featured_media.width >= 360 -%}{{ product_card_product.featured_media | img_url: '360x' }} 360w,{%- endif -%}
                         {%- if product_card_product.featured_media.width >= 533 -%}{{ product_card_product.featured_media | img_url: '533x' }} 533w,{%- endif -%}
                         {%- if product_card_product.featured_media.width >= 720 -%}{{ product_card_product.featured_media | img_url: '720x' }} 720w,{%- endif -%}
                         {%- if product_card_product.featured_media.width >= 940 -%}{{ product_card_product.featured_media | img_url: '940x' }} 940w,{%- endif -%}
                         {%- if product_card_product.featured_media.width >= 1066 -%}{{ product_card_product.featured_media | img_url: '1066x' }} 1066w{%- endif -%}"
                 src="{{ product_card_product.featured_media | img_url: '533x' }}"
                 sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 130 | divided_by: 4 }}px, (min-width: 990px) calc((100vw - 130px) / 4), (min-width: 750px) calc((100vw - 120px) / 3), calc((100vw - 35px) / 2)"
                 alt="{{ product_card_product.featured_media.alt | escape }}"
                 loading="lazy"
                 class="motion-reduce"
                 width="{{ product_card_product.featured_media.width }}"
                 height="{{ product_card_product.featured_media.height }}"
                 >
  
            {%- if product_card_product.media[1] != nil and show_secondary_image -%}
            <img
                 srcset="{%- if product_card_product.media[1].width >= 165 -%}{{ product_card_product.media[1] | img_url: '165x' }} 165w,{%- endif -%}
                         {%- if product_card_product.media[1].width >= 360 -%}{{ product_card_product.media[1] | img_url: '360x' }} 360w,{%- endif -%}
                         {%- if product_card_product.media[1].width >= 533 -%}{{ product_card_product.media[1] | img_url: '533x' }} 533w,{%- endif -%}
                         {%- if product_card_product.media[1].width >= 720 -%}{{ product_card_product.media[1] | img_url: '720x' }} 720w,{%- endif -%}
                         {%- if product_card_product.media[1].width >= 940 -%}{{ product_card_product.media[1] | img_url: '940x' }} 940w,{%- endif -%}
                         {%- if product_card_product.media[1].width >= 1066 -%}{{ product_card_product.media[1] | img_url: '1066x' }} 1066w{%- endif -%}"
                 src="{{ product_card_product.media[1] | img_url: '533x' }}"
                 sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 130 | divided_by: 4 }}px, (min-width: 990px) calc((100vw - 130px) / 4), (min-width: 750px) calc((100vw - 120px) / 3), calc((100vw - 35px) / 2)"
                 alt="{{ product_card_product.media[1].alt | escape }}"
                 loading="lazy"
                 class="motion-reduce"
                 width="{{ product_card_product.media[1].width }}"
                 height="{{ product_card_product.media[1].height }}"
                 >
            {%- endif -%}
          </div>
        </a>
        {%- else -%}
        <div class="card__content"><h2 class="card__text h3">{{ product_card_product.title }}</h2></div>
        {%- endif -%}
        
        {%- if show_wishlist -%}
        {%- render 'button-wishlist', product: product_card_product, className: "compare--product__wishlist" -%}
        {%- endif -%}
      </div>
      
      <div class="compare__product--footer">
          <h3 class="product-grid-item__title h6"><a href="{{ product_card_product.url | default: '#'  }}">{{ product_card_product.title | truncatewords: 10 }}</a></h3>
      </div>
    </td>
      
      <td class="cp_prd-price" data-pid="{{ product_card_product.id }}">
        {% render 'price', product: product_card_product, price_class: 'product-grid-item__price' %}
      </td> 
      
       <td class="cp_prd-availability" data-pid="{{ product_card_product.id }}">
        {% if product_card_product.available %}
        <span class="ml-1">{{ 'in stock' }}</span>
        {% else %}
        <span class="ml-1">{{ 'unavailable'}}</span>
        {% endif %}
      </td> 
      
      <td class="cp_prd-brand" data-pid="{{ product_card_product.id }}">
       {{ product_card_product.vendor }} 
      </td> 
      
      <td class="cp_prd_description" data-pid="{{ product_card_product.id }}">
       {{ product_card_product.description | strip_html | truncatewords: 30 }}
      </td> 


       <td class="cp_prd_buy--proudct" data-pid="{{ product_card_product.id }}">
         {% if product_card_product.has_only_default_variant %}
          <product-form>         
            <form action="/cart/add" method="post" enctype="multipart/form-data" data-type="add-to-cart-form">
    
              <input type="hidden" name="id" value="{{ variant.id }}">
              <button
                      type="submit"
                      name="add"
                      class="button button--medium"
                      {% if product_card_product.selected_or_first_available_variant.available == false %}disabled{% endif %}
                      >
                {%- if product_card_product.selected_or_first_available_variant.available -%}
                {{ 'products.product.add_to_cart' | t }}
                {%- else -%}
                {{ 'products.product.sold_out' | t }}
                {%- endif -%}
              </button>
            </form>
          </product-form>
          {%- else -%}
          {%- render 'compare-quick-view', product_card_product: product_card_product -%}
          {%- endif -%}
      </td> 
      
    </tr>
    </tbody>
  </table>
  
  
  <div class="product__details ">

    <div class="product__details_single_info">
      <div class="product__details_info_head">
        {{ 'Type' }}
      </div>
      <div class="product__details_info_container">
        <p>{{ product.type }}</p>
      </div>
    </div>


    {% unless product.has_only_default_variant %}
    {%- for option in product_card_product.options_with_values -%}
    <div class="product__details_single_info">
      <div class="product__details_info_head">
        {{ option.name }}
      </div>
      <div class="product__details_info_container">
        {% for value in option.values %}
        {%- if forloop.last == true  -%}
        {{ value }}
        {%- else -%}
        {{ value | append: ',' }}
        {%- endif -%}
        {% endfor %}
      </div>
    </div>
    {% endfor %}
    {%- endunless -%}
  </div>
</div>

