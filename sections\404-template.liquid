<style type="text/css">
  .template-404 .title + * {
    margin-top: 1rem;
  }

  @media screen and (min-width: 750px) {
    .template-404 .title + * {
      margin-top: 2rem;
    }
  }
  .template-404{
  	height: 70vh;
  }
   .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.mobile_padding_top }}px;
      padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
    }
    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top }}px;
        padding-bottom: {{ section.settings.padding_bottom }}px;
      }
    }
</style>

<div class="template-404 text-center color-{{ section.settings.color_scheme }} section-{{ section.id }}-padding d-flex align-items-center">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <h1 class="title">
          {{ 'templates.404.title' | t }}
        </h1>
        <p>
          {{ 'templates.404.subtext' | t }}
        </p>
        <a href="{{ routes.all_products_collection_url }}" class="button">
          {{ 'general.continue_shopping' | t }}
        </a>
      </div>
    </div>
  </div>
</div>
{% schema %}
 {
   "name": "404",
   "settings": [
     {
       "type": "color_scheme",
       "id": "color_scheme",
       "label": "t:sections.all.colors.label",
       "default": "background-1"
     },

        {
     "type": "header",
     "content": "Section padding"
   },
   {
         "type": "paragraph",
         "content": "Desktop"
       },
       {
         "type": "range",
         "id": "padding_top",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding top",
         "default": 0
       },
       {
         "type": "range",
         "id": "padding_bottom",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding bottom",
         "default": 0
       },
	{
         "type": "paragraph",
         "content": "Mobile"
       },
	{
         "type": "range",
         "id": "mobile_padding_top",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding top",
         "default": 0
       },
       {
         "type": "range",
         "id": "mobile_padding_bottom",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding bottom",
         "default": 0
       }
]
 }
{% endschema %}
