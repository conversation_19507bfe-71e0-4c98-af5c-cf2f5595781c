{%- liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
-%}

{{ 'component-image-with-text.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  assign column_width = section.settings.desktop_image_width

  case column_width
    when 'small'
      assign flex_image_grow = '0'
      assign flex_content_grow = '1'
    when 'medium'
      assign flex_image_grow = '1'
      assign flex_content_grow = '1'
    else
      assign flex_image_grow = '1'
      assign flex_content_grow = '0'
  endcase

  assign desktop_content_position_class = section.settings.desktop_content_position

  case desktop_content_position_class
    when 'top'
      assign desktop_content_position_class_assign = 'align-items-start'
    when 'bottom'
      assign desktop_content_position_class_assign = 'align-items-end'
    else
      assign desktop_content_position_class_assign = 'align-items-center'
  endcase

  assign desktop_content_alignment_class = section.settings.desktop_content_alignment

  case desktop_content_alignment_class
    when 'right'
      assign desktop_content_alignment_class_assign = 'justify-content-end'
    when 'center'
      assign desktop_content_alignment_class_assign = 'justify-content-center'
    else
      assign desktop_content_alignment_class_assign = 'justify-content-start'
  endcase

  assign mobile_content_alignment_class = section.settings.mobile_content_alignment

  case mobile_content_alignment_class
    when 'left'
      assign mobile_content_alignment_class_assign = 'mobile__text-left'
    when 'center'
      assign mobile_content_alignment_class_assign = 'mobile__text-center'
    else
      assign mobile_content_alignment_class_assign = 'mobile__text-right'
  endcase

  if section.settings.full_width
    assign column_class = 'col-12'
  else
    assign column_class = 'col-md-4'
  endif

  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif
-%}

{% if theme_rtl %}
  {{ 'component-image-with-text-rtl.css' | asset_url | stylesheet_tag }}
{% endif %}

<div class="image-with-text section-{{ section.id }}-padding color-{{ section.settings.color_scheme }}">
  <div class="{{ container }}">
    <div class="image-with-text__grid {% if section.settings.layout == "text_first" %}desktop-row-reverse {% endif %} d-flex flex-wrap color-{{ section.settings.color_scheme }}">
      <div class="{{ column_class }} flex-grow-{{ flex_image_grow }} image-with-text__media-item">
        <div class="image-with-text-media--container">
          <div
            class="image-with-text__media image-with-text__media--{{ section.settings.height }} global-media-settings {% if section.settings.image != blank %}media {% if section.settings.transparent_media %}media--transparent{% endif %}{% else %}image-with-text__media--placeholder placeholder {% endif %}"
            {% if section.settings.height == 'adapt' and section.settings.image != blank %}
              style="padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%;"
            {% endif %}
          >
            {%- if section.settings.image != blank -%}
              <img
                srcset="
                  {%- if section.settings.image.width >= 165 -%}{{ section.settings.image | image_url: width: 165 }} 165w,{%- endif -%}
                  {%- if section.settings.image.width >= 360 -%}{{ section.settings.image | image_url: width: 360 }} 360w,{%- endif -%}
                  {%- if section.settings.image.width >= 535 -%}{{ section.settings.image | image_url: width: 535 }} 535w,{%- endif -%}
                  {%- if section.settings.image.width >= 750 -%}{{ section.settings.image | image_url: width: 750 }} 750w,{%- endif -%}
                  {%- if section.settings.image.width >= 1070 -%}{{ section.settings.image | image_url: width: 1070 }} 1070w,{%- endif -%}
                  {%- if section.settings.image.width >= 1500 -%}{{ section.settings.image | image_url: width: 1500 }} 1500w,{%- endif -%}
                  {{ section.settings.image | image_url }} {{ section.settings.image.width }}w
                "
                src="{{ section.settings.image | image_url: width: 1500 }}"
                sizes="(min-width: {{ settings.container_lg_width }}px) {{ settings.container_lg_width | minus: 100 | divided_by: 2 }}px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
                alt="{{ section.settings.image.alt | escape }}"
                loading="lazy"
                width="{{ section.settings.image.width }}"
                height="{{ section.settings.image.height }}"
              >
            {%- else -%}
              {{ 'detailed-apparel-1' | placeholder_svg_tag: 'placeholder-svg-new' }}
            {%- endif -%}
          </div>
        </div>
      </div>
      <div class="{{ column_class }}  flex-grow-{{ flex_content_grow }} image-with-text__text-item d-flex {{ desktop_content_position_class_assign }}">
        <div class="image-with-text__content {% unless section.settings.card_content_padding == "none" %}image--content-padded-{{ section.settings.card_content_padding }}{% endunless %} text-{{ section.settings.desktop_content_alignment }}  {{ mobile_content_alignment_class_assign }} color-{{ section.settings.color_scheme }}">
          {%- for block in section.blocks -%}
            {% case block.type %}
              {%- when 'heading' -%}
                <h2
                  class="image-with-text__heading {{ block.settings.heading_size }} mb-0"
                  {{ block.shopify_attributes }}
                >
                  {{ block.settings.heading | escape }}
                </h2>
              {%- when 'caption' -%}
                <p
                  class="image-with-text__text image-with-text__text--caption {{ block.settings.text_style }} {{ block.settings.text_style }}--{{ block.settings.text_size }} {{ block.settings.text_style }}"
                  {{ block.shopify_attributes }}
                >
                  {{ block.settings.caption | escape }}
                </p>
              {%- when 'text' -%}
                <div class="image-with-text__text rte {{ block.settings.text_style }}" {{ block.shopify_attributes }}>
                  {{ block.settings.text }}
                </div>
              {%- when 'list' -%}
                <div
                  class="feature__list"
                  {% if block.settings.full_width %}
                    style="--feature-list-width: 100%;"
                  {% endif %}
                >
                  <div class="feature-list--inner">
                    {%- if block.settings.feature_image_icon != blank or block.settings.icon != 'none' -%}
                      <div class="feature-list-icon">
                        {%- if block.settings.feature_image_icon != blank -%}
                          <span class="feature-list-image--icon">
                            <img
                              src="{{ block.settings.feature_image_icon | image_url: width: 100 }}"
                              alt="{{ block.settings.feature_image_icon.alt | escape }}"
                              width="100"
                              height="{{ 100 | divided_by: block.settings.feature_image_icon.aspect_ratio | ceil }}"
                              loading="lazy"
                            >
                          </span>
                        {% else %}
                          {% if block.settings.icon != 'none' %}
                            {% if block.settings.card_solid_button_icon %}
                              <span
                                class="icon--solid-button color-{{ block.settings.color_scheme }}"
                              >
                            {%- endif %}
                            {%- render 'icon-featured-promotion', icon: block.settings.icon -%}
                            {% if block.settings.card_solid_button_icon %} </span>{% endif %}
                          {%- endif -%}
                        {%- endif -%}
                      </div>
                    {%- endif -%}

                    <div class="feature-list-content">
                      <div class="feature-list-heading h5">{{ block.settings.heading }}</div>
                      <p class="feature-list-subheading">{{ block.settings.subheading }}</p>
                    </div>
                  </div>
                </div>
              {%- when 'button' -%}
                {% liquid
                  assign button_class = ''
                  case block.settings.button_style
                    when 'primary'
                      assign button_class = 'button button--primary'
                    when 'secondary'
                      assign button_class = 'button button--secondary'
                    when 'icon'
                      assign button_class = 'link with--icon'
                    else
                      assign button_class = 'link'
                  endcase
                %}

                {%- if block.settings.button_label != blank -%}
                  <div class="button__wrapper">
                    <a
                      {% if block.settings.button_link == blank %}
                        role="link" aria-disabled="true"
                      {% else %}
                        href="{{ block.settings.button_link }}"
                      {% endif %}
                      class="{{ button_class }} {% unless block.settings.button_style == "icon" %} button--{{ block.settings.button_size }} {% endunless %}  {% if block.settings.button_icon %} button--with-icon{% endif %}"
                      {{ block.shopify_attributes }}
                    >
                      {{ block.settings.button_label | escape }}
                      {% if block.settings.button_icon %}
                        <span class="button--icon button--icon-right"> {% render 'icon-arrow-right' %} </span>
                      {% endif %}
                    </a>
                  </div>
                {%- endif -%}
            {%- endcase -%}
          {%- endfor -%}
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.image-with-text.name",
  "class": "section",
  "settings": [
    {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.image-with-text.settings.image.label"
    },
    {
          "type": "checkbox",
          "id": "transparent_media",
          "label": "Enable transparent image",
          "default": false
        },
    {
      "type": "select",
      "id": "height",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.image-with-text.settings.height.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.image-with-text.settings.height.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.image-with-text.settings.height.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.image-with-text.settings.height.label"
    },
    {
      "type": "select",
      "id": "desktop_image_width",
      "options": [
        {
          "value": "small",
          "label": "t:sections.image-with-text.settings.desktop_image_width.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.image-with-text.settings.desktop_image_width.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.image-with-text.settings.desktop_image_width.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.image-with-text.settings.desktop_image_width.label",
      "info": "t:sections.image-with-text.settings.desktop_image_width.info"
    },
    {
      "type": "select",
      "id": "layout",
      "options": [
        {
          "value": "image_first",
          "label": "t:sections.image-with-text.settings.layout.options__1.label"
        },
        {
          "value": "text_first",
          "label": "t:sections.image-with-text.settings.layout.options__2.label"
        }
      ],
      "default": "image_first",
      "label": "t:sections.image-with-text.settings.layout.label",
      "info": "t:sections.image-with-text.settings.layout.info"
    },
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "top",
          "label": "t:sections.image-with-text.settings.desktop_content_position.options__1.label"
        },
        {
          "value": "middle",
          "label": "t:sections.image-with-text.settings.desktop_content_position.options__2.label"
        },
        {
          "value": "bottom",
          "label": "t:sections.image-with-text.settings.desktop_content_position.options__3.label"
        }
      ],
      "default": "top",
      "label": "t:sections.image-with-text.settings.desktop_content_position.label"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__3.label"
        }
      ],
      "default": "left",
      "label": "t:sections.image-with-text.settings.desktop_content_alignment.label"
    },
    {
        "type": "select",
        "id": "card_content_padding",
        "label": "Desktop content padded",
         "info": "The padding is automatically optimized for mobile",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "small",
            "label": "Small"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
          {
            "value": "large",
            "label": "Large"
          }
        ],
        "default": "none"
      },
	{
      "type": "checkbox",
      "id": "full_width",
      "label": "Make section full width",
      "default": false
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "background-1"
    },
    {
      "type": "header",
      "content": "Mobile layout"
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.image-with-text.settings.mobile_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.image-with-text.settings.mobile_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.image-with-text.settings.mobile_content_alignment.options__3.label"
        }
      ],
      "default": "left",
      "label": "t:sections.image-with-text.settings.mobile_content_alignment.label"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
          "type": "paragraph",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        },
		{
          "type": "paragraph",
          "content": "Mobile"
        },
		{
          "type": "range",
          "id": "mobile_padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "mobile_padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "t:sections.image-with-text.blocks.heading.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Image with text",
          "label": "t:sections.image-with-text.blocks.heading.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            }
          ],
          "default": "h1",
          "label": "t:sections.all.heading_size.label"
        }
      ]
    },
    {
      "type": "caption",
      "name": "t:sections.image-with-text.blocks.caption.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "caption",
          "default": "Add a tagline",
          "label": "t:sections.image-with-text.blocks.caption.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "subtitle",
              "label": "t:sections.image-with-text.blocks.caption.settings.text_style.options__1.label"
            },
            {
              "value": "caption-with-letter-spacing",
              "label": "t:sections.image-with-text.blocks.caption.settings.text_style.options__2.label"
            }
          ],
          "default": "caption-with-letter-spacing",
          "label": "t:sections.image-with-text.blocks.caption.settings.text_style.label"
        },
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "small",
              "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.options__1.label"
            },
            {
              "value": "medium",
              "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.options__2.label"
            },
            {
              "value": "large",
              "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.options__3.label"
            }
          ],
          "default": "medium",
          "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.label"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.image-with-text.blocks.text.name",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
          "label": "t:sections.image-with-text.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.image-with-text.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.image-with-text.blocks.text.settings.text_style.options__2.label"
            }
          ],
          "default": "body",
          "label": "t:sections.image-with-text.blocks.text.settings.text_style.label"
        }
      ]
    },
    {
      "type": "list",
      "name": "Feature list",
      "settings": [
         {
           "type": "text",
           "id": "heading",
           "default": "Heading",
           "label": "Heading"
         },
	  {
         "type": "textarea",
         "id": "subheading",
         "default": "Pair text with an icon to focus on your store's features.",
         "label": "Subheading"
       },
        {
       "type": "checkbox",
       "id": "card_solid_button_icon",
       "label": "Icon solid button",
       "default": false
     },
	   {
           "type": "select",
           "id": "icon",
           "options": [
             {
               "value": "none",
               "label": "None"
             },
             {
               "value": "truck",
               "label": "Truck"
             },
             {
               "value": "return",
               "label": "Return"
             },
             {
               "value": "payment",
               "label": "Payment"
             },
             {
               "value": "gift",
               "label": "Gift"
             },
             {
               "value": "chat",
               "label": "Chat"
             },
               {
                  "value": "computer",
                  "label": "Computer"
                },
                {
                  "value": "camera",
                  "label": "Camera"
                },
                {
                  "value": "keyboard",
                  "label": "Keyboard"
                },
                {
                  "value": "phone",
                  "label": "Phone"
                },
                 {
                  "value": "headphone",
                  "label": "Headphone"
                },
                {
                  "value": "watch",
                  "label": "Watch"
                },
               {
                  "value": "battery",
                  "label": "Battery"
                },
                {
                  "value": "battery_charge",
                  "label": "Battery charge"
                },
                {
                  "value": "wirless",
                  "label": "Wirless"
                },
                {
                  "value": "bluetooth",
                  "label": "Bluetooth"
                },
                {
                  "value": "radio_outline",
                  "label": "Radio outline"
                },
                {
                  "value": "printers",
                  "label": "Printers"
                },
                {
                  "value": "apple",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
                },
                {
                  "value": "banana",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
                },
                {
                  "value": "bottle",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
                },
                {
                  "value": "box",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
                },
                {
                  "value": "carrot",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
                },
                {
                  "value": "chat_bubble",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
                },
                {
                  "value": "check_mark",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
                },
                {
                  "value": "clipboard",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
                },
                {
                  "value": "dairy",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
                },
                {
                  "value": "dairy_free",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
                },
                {
                  "value": "dryer",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
                },
                {
                  "value": "eye",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
                },
                {
                  "value": "fire",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
                },
                {
                  "value": "gluten_free",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
                },
                {
                  "value": "heart",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
                },
                {
                  "value": "iron",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
                },
                {
                  "value": "leaf",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
                },
                {
                  "value": "leather",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
                },
                {
                  "value": "lightning_bolt",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
                },
                {
                  "value": "lipstick",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
                },
                {
                  "value": "lock",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
                },
                {
                  "value": "map_pin",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
                },
                {
                  "value": "nut_free",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
                },
                {
                  "value": "pants",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
                },
                {
                  "value": "paw_print",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
                },
                {
                  "value": "pepper",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
                },
                {
                  "value": "perfume",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
                },
                {
                  "value": "plane",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
                },
                {
                  "value": "plant",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
                },
                {
                  "value": "price_tag",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
                },
                {
                  "value": "question_mark",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
                },
                {
                  "value": "recycle",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
                },
                {
                  "value": "ruler",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
                },
                {
                  "value": "serving_dish",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
                },
                {
                  "value": "shirt",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
                },
                {
                  "value": "shoe",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
                },
                {
                  "value": "silhouette",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
                },
                {
                  "value": "snowflake",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
                },
                {
                  "value": "star",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
                },
                {
                  "value": "stopwatch",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
                },
                {
                  "value": "washing",
                  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
                }
           ],
           "default": "truck",
           "label": "Icon"
         },
        {
           "type": "image_picker",
           "id": "feature_image_icon",
           "label": "Image icon"
         },
        {
          "type": "checkbox",
          "id": "full_width",
          "label": "Make section full width",
          "default": false
        },
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:sections.all.colors.label",
          "default": "background-1"
        }
      ]
    },
    {
      "type": "button",
      "name": "t:sections.image-with-text.blocks.button.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "t:sections.image-with-text.blocks.button.settings.button_label.label",
          "info": "t:sections.image-with-text.blocks.button.settings.button_label.info"
        },
         {
             "type": "checkbox",
             "id": "button_icon",
             "label": "Use arrow icon",
             "default": true
           },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.image-with-text.blocks.button.settings.button_link.label"
        },
		{
              "type": "select",
              "id": "button_style",
              "label": "Button style",
              "default": "primary",
              "options": [
                {
                  "value": "secondary",
                  "label": "Secondary"
                },
                {
                  "value": "primary",
                  "label": "Primary"
                },
                {
                  "value": "icon",
                  "label": "Link button"
                }
              ]
            },
			{
              "type": "select",
              "id": "button_size",
              "label": "Button size",
              "default": "small",
              "options": [
                {
                  "value": "large",
                  "label": "Large"
                },
                {
                  "value": "medium",
                  "label": "Medium"
                },
				        {
                  "value": "small",
                  "label": "Small"
                }
              ]
            }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.image-with-text.presets.name",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "button"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header","footer"]
  }
}
{% endschema %}
