.grid {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 2rem;
  padding: 0;
  list-style: none;
  column-gap: 10px;
  row-gap: 10px;
}

@media screen and (min-width: 750px) {
  .product__info-container--sticky {
    position: sticky;
    top: 50px;
    z-index: 2;
  }
}

/* Product form */

.product-form {
  display: block;
}

/* Form Elements */

variant-radios,
variant-selects {
  display: block;
}

.no-js .product-form__submit.button--secondary {
  --color-button: var(--color-base-accent-1);
  --color-button-text: var(--color-base-solid-button-labels);
  --alpha-button-background: 1;
}

.product-form__submit[disabled] + .shopify-payment-button {
  display: none;
}

/* Overrides */
.shopify-payment-button__more-options {
  color: rgb(var(--color-foreground));
}

.shopify-payment-button__button {
  font-size: calc(var(--button-font-size) * 1.6rem);
}

/* Product info */
.product .price--sold-out .price__badge-sold-out {
  background: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
  border: 1px solid rgb(var(--color-background));
}

.product .price--sold-out .price__badge-sale {
  display: none;
}

.product--no-media .product__title,
.product--no-media .product__text,
.product--no-media noscript .product-form__input {
  text-align: center;
}

.product--no-media noscript .product-form__input,
.product--no-media .share-button {
  max-width: 100%;
}

.product--no-media fieldset.product-form__input,
.product--no-media .product-form__quantity,
.product--no-media .product-form__input--dropdown,
.product--no-media .share-button,
.product--no-media .product__view-details,
.product--no-media .store-availability-container,
.product--no-media .product-form {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.product--no-media .product-form > .form {
  max-width: 30rem;
  width: 100%;
}

.product--no-media .product-form__quantity,
.product--no-media .product-form__input--dropdown {
  flex-direction: column;
  max-width: 100%;
}

.product--no-media fieldset.product-form__input {
  flex-wrap: wrap;
  margin: 0 auto 1.2rem auto;
}

.product--no-media .product__info-container > modal-opener {
  display: block;
  text-align: center;
}

.product--no-media .product-popup-modal__button {
  padding-right: 0;
}

.product--no-media .price {
  justify-content: center;
}

.product--no-media .product__info-wrapper {
  padding-left: 0;
}

/* Product media */
.product__media-item--variant {
  display: none;
}

.product__media-item--variant:first-child {
  display: block;
}

@media screen and (min-width: 750px) and (max-width: 989px) {
  .product__media-list .product__media-item:first-child {
    padding-left: 0;
  }

  .product__media-list .product__media-item {
    padding: 0 0 0.5rem;
    width: 100%;
  }
}

.product__media-icon .icon {
  width: 2.5rem;
}

.product__media-icon, .thumbnail__badge {
    background-color: rgba(var(--primary-button-hover-background));
    border-radius: 50%;
    border: .1rem solid rgba(var(--primary-button-hover-background));
    color: rgb(var(--color-button-text));
    display: flex;
    align-items: center;
    justify-content: center;
    height: 5rem;
    width: 5rem;
    position: absolute;
    left: 1.5rem;
    top: 1.5rem;
    z-index: 1;
    transition: color var(--duration-short) ease,opacity var(--duration-short) ease;
}
.product__media-video .product__media-icon {
  opacity: 1;
}

.product__modal-opener--image .product__media-toggle:hover {
  cursor: zoom-in;
}

.product__modal-opener:hover .product__media-icon {
  border: 0.1rem solid rgba(var(--color-foreground), 0.1);
}

@media screen and (min-width: 750px) {
  .product--grid__item.product__media-item--full {
    width: 100%;
  }
}

@media screen and (min-width: 990px) {
  .product--stacked .product__media-item {
    max-width: calc(50% - 5px);
  }

  .product__media-list .product__media-item:first-child,
  .product__media-list .product__media-item--full {
    width: 100%;
    max-width: 100%;
  }

  .product__modal-opener .product__media-icon {
    opacity: 0;
  }

  .product__modal-opener:hover .product__media-icon,
  .product__modal-opener:focus .product__media-icon {
    opacity: 1;
  }
  .product--columns
    .product__media-item:not(.product__media-item--single):not(:only-child) {
    max-width: calc(50% - 5px);
  }
}

.product__media-item > * {
  display: block;
  position: relative;
}
.product__media-toggle {
  background-color: transparent;
  border: none;
  cursor: pointer;
  display: block;
  margin: 0;
  padding: 0;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  height: 100%;
  width: 100%;
}

.product-media-modal {
  background-color: rgb(var(--color-background));
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  visibility: hidden;
  opacity: 0;
  z-index: -1;
}

.product-media-modal[open] {
  visibility: visible;
  opacity: 1;
  z-index: 999;
}

.product-media-modal__dialog {
  display: flex;
  align-items: center;
  height: 100vh;
}

.product-media-modal__content {
  max-height: 100vh;
  width: 100%;
  overflow: auto;
}

.product-media-modal__content > *:not(.active),
.product__media-list .deferred-media {
  display: none;
}

@media screen and (min-width: 750px) {
  .product-media-modal__content {
    padding-bottom: 2rem;
  }

  .product-media-modal__content > *:not(.active) {
    display: block;
  }

  .product__modal-opener:not(.product__modal-opener--image) {
    display: none;
  }

  .product__media-list .deferred-media {
    display: block;
  }
}

.product-media-modal__content > * {
  display: block;
  height: auto;
  margin: auto;
  border: 0.1rem solid rgba(var(--color-foreground), 0.04);
}

.product-media-modal__content .media {
  background: none;
}

.product-media-modal__model {
  width: 100%;
}

.product-media-modal__toggle {
  background-color: rgb(var(--color-background));
  border: 0.1rem solid rgba(var(--color-foreground), 0.1);
  border-radius: 50%;
  color: rgba(var(--color-foreground), 0.55);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  right: 2rem;
  padding: 1.2rem;
  position: fixed;
  z-index: 2;
  top: 2rem;
  width: 4rem;
}

.product-media-modal__content .deferred-media {
  width: 100%;
}

@media screen and (min-width: 750px) {
  .product-media-modal__content {
    padding: 2rem 11rem;
  }

  .product-media-modal__content > * {
    width: 100%;
  }

  .product-media-modal__content > * + * {
    margin-top: 2rem;
  }

  .product-media-modal__toggle {
    right: 5rem;
    top: 2.2rem;
  }
}

@media screen and (min-width: 990px) {
  .product-media-modal__content {
    padding: 2rem 11rem;
  }

  .product-media-modal__content > * + * {
    margin-top: 1.5rem;
  }

  .product-media-modal__content {
    padding-bottom: 1.5rem;
  }

  .product-media-modal__toggle {
    right: 5rem;
  }
}
@media screen and (min-width: 1200px) {
  .product-media-modal__content > * {
    width: 100%;
    max-width: var(--container-lg-width);
  }
}

.product-media-modal__toggle:hover {
  color: rgba(var(--color-foreground), 0.75);
}

.product-media-modal__toggle .icon {
  height: auto;
  margin: 0;
  width: 2.2rem;
}

/* Product popup */

.product-popup-modal {
  box-sizing: border-box;
  opacity: 0;
  position: fixed;
  visibility: hidden;
  z-index: -1;
  margin: 0 auto;
  top: 0;
  left: 0;
  overflow: auto;
  width: 100%;
  background: rgba(var(--color-foreground), 0.5);
  height: 100%;
}

.product-popup-modal[open] {
  opacity: 1;
  visibility: visible;
  z-index: 101;
}

.product-popup-modal__content {
  background-color: rgb(var(--color-background));
  overflow: auto;
  height: 80%;
  margin: 0 auto;
  left: 50%;
  transform: translateX(-50%);
}

@media screen and (min-width: 750px) {
  .product-popup-modal__content {
    padding-right: 1.5rem;
    width: 70%;
    padding: 0 3rem;
  }
}

.product-popup-modal__content img {
  max-width: 100%;
}

@media screen and (max-width: 749px) {
  .product-popup-modal__content table {
    display: block;
    max-width: fit-content;
    overflow-x: auto;
    white-space: nowrap;
    margin: 0;
  }
}
.product-popup-modal__button {
  font-size: 20px;
  padding-right: 15px;
  padding-left: 0;
  text-decoration: none;
}

.product-popup-modal__content-info > * {
  height: auto;
  margin: 0 auto;
  max-width: 100%;
  width: 100%;
}

@media screen and (max-width: 749px) {
  .product-popup-modal__content-info > * {
    max-height: 100%;
  }
}
.product-popup-modal__toggle {
  background-color: rgb(var(--color-background));
  border: 0.1rem solid rgba(var(--color-foreground), 0.1);
  border-radius: 50%;
  color: rgba(var(--color-foreground), 0.55);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  top: 20px;
  width: 40px;
  height: 40px;
  margin: 0 0 0 auto;
  padding: 10px;
}
.product-popup-modal__toggle:hover {
  color: rgba(var(--color-foreground), 0.75);
}

.product-popup-modal__toggle .icon {
  height: auto;
  margin: 0;
  width: 2.2rem;
}

/*  Proudct Info Csutom css */
.product__media-item {
  max-width: 100%;
  width: 100%;
}
.media > a > img {
  object-fit: cover;
  object-position: center center;
  transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: zoom-in;
}
.discount__sale__text {
    background: rgba(var(--color-foreground));
    color: rgba(var(--color-background));
    padding: 3px 7px;
    border-radius: 3px;
    font-size: 1.3rem;
    line-height: 1;
}
.price__box_wrapper {
  display: flex;
  align-items: center;
}

.save__disoucnt {
  margin-left: 5px;
}
.product-popup-modal__content {
  top: 50%;
  transform: translate(-50%, -120%);
  margin: 0;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
  padding: 0;
  position: relative;
  border-radius: 0.3rem;
}
.product-popup-modal[open] .product-popup-modal__content {
  transform: translate(-50%, -50%);
}
@media screen and (min-width: 992px) {
  .product-popup-modal__content.modal-sm {
    max-width: 550px;
    width: 500px;
  }
}
.modal-header {
  display: flex;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.1);
  align-items: center;
}
.modal__title {
  margin: 0;
}
.ask_about_product input,
.ask_about_product textarea {
  width: 100%;
  border: 0.1rem solid rgba(var(--color-foreground), 0.1);
  padding: 10px 20px;
  line-height: 25px;
  font-size: 15px;
}
.product-popup-modal__content-info {
  padding: 0 30px 25px;
}
@media screen and (min-width: 992px) {
  .product-popup-modal__content.modal-md {
    max-width: 800px;
    width: 800px;
  }
}
.select__select option:disabled {
  background: #eee;
  color: #aaa;
}

.select__select option {
  font-size: 16px;
}
.product-form__buttons {
  margin: 30px 0 0 0 !important;
}
.single_product_action_btn.loading::after {
  top: 10px;
  left: 10px;
}
.product__payment {
  justify-content: flex-start;
  padding: 0;
  margin-top: 0;
}
li.product__payment__item {
  list-style: none;
  line-height: 1;
}

.product__payment .product__payment__item + .product__payment__item {
  margin-left: 10px;
}
/* Countdown css */
.product__countdown {
  display: flex;
}
.product__details_countdown .product__countdown > div + div {
  margin-left: 10px;
}

.product__countdown > * span {
  line-height: 1.8rem;
}
.product__details_countdown .product__countdown .countdown__inner {
  display: flex;
  flex-direction: column;
}
.product__details_countdown .product__countdown .countdown-item {
  position: relative;
}

.product__details_countdown .product__countdown > * {
  text-align: center;
  font-size: 1.5rem;
}
.product__details_countdown span.countdown__number {
  background: var(--countdown-background);
  color: var(--countdown-foreground);
  padding: 0 1rem;
  border-radius: 10px 0;
  height: 3.5rem;
  font-weight: 700;
  display: inline-flex;
  align-items: center;
  line-height: 1;
  margin-bottom: 0.5rem;
  min-width: 5rem;
  text-align: center;
  justify-content: center;
}
span.countdown__label {
  margin-bottom: 1.2rem;
}
.timer__icon {
  width: 2.2rem;
  margin-right: 0.8rem;
}
.product__countdown {
  background-color: transparent;
}
.stock_countdown_progress {
    width: 100%;
    background: var(--progress-bar-background);
    height: 6px;
    border-radius: 3px;
    margin-top: 7px;
}
span.stock_progress_bar {
  display: block;
  background: var(--progress-bar-foreground);
  background-attachment: fixed;
  height: 100%;
  transition: width 2.5s ease;
  border-radius: 3px 0 0 3px;
}
.stock__inventgory--status {
  margin-bottom: 5px;
}
.input__field_form + .input__field_form {
  margin-top: 1.5rem;
}
span.input__field--label {
  display: block;
}
.product__info-container > * + * {
  margin-top: 1.5rem;
}
h1.product__title {
  margin-top: 0;
}
@media only screen and (min-width: 992px) {
  .product__info-container {
    padding: 0 3rem 0 0;
  }
}
@media only screen and (max-width: 767px) {
.product__info-container {
    padding-top: 3.5rem;
}
}

.product__media {
  cursor: crosshair;
}

span.product__media-icon.motion-reduce > a {
  display: block;
  line-height: 1;
}
@media only screen and (min-width: 992px) {
  .product-popup-modal.popup__contact--form .product-popup-modal__content,
  .product-popup-modal.back__in--stock-popup .product-popup-modal__content {
    height: auto;
  }
}
.product-form__quantity .quantity {
  height: 48px;
}
.product-form__cart--box.d-flex {
  gap: 20px;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}
@media only screen and (max-width: 750px) {
  .product-media-modal__content > * {
    max-width: 100%;
  }
}

/*  Additional css  */
@media screen and (min-width: 750px) {
  .product--thumbnail .product__media-gallery,
  .product--thumbnail_slider .product__media-gallery,
  .product--stacked .product__info-container--sticky {
    display: block;
    position: sticky;
    top: 3rem;
    z-index: 2;
  }

  .product--thumbnail .thumbnail-list {
    padding-right: var(--media-shadow-horizontal-offset);
  }

  .product__info-wrapper {
    padding-left: 5rem;
  }

  .product__info-wrapper--extra-padding {
    padding-left: 8rem;
  }

  .product__media-container .slider-buttons {
    display: none;
  }
}

/* Product media */
@media screen and (max-width: 749px) {
  /* .product__media-list {
    margin-left: -2.5rem;
    padding-bottom: 3rem;
    margin-bottom: 0;
    width: calc(100% + 4rem);
  } */

  .product__media-list {
    margin-bottom: 3rem;
  }
  .product__media-wrapper slider-component:not(.thumbnail-slider--no-slide) {
    margin-left: -1.5rem;
    margin-right: -1.5rem;
  }

  .slider.product__media-list::-webkit-scrollbar {
    height: 0.2rem;
    width: 0.2rem;
  }

  .product__media-list::-webkit-scrollbar-thumb {
    background-color: rgb(var(--color-foreground));
  }

  .product__media-list::-webkit-scrollbar-track {
    background-color: rgba(var(--color-foreground), 0.2);
  }

  .product__media-list .product__media-item {
    width: 100%;
  }
}

@media screen and (min-width: 750px) {
  .product--thumbnail .product__media-item:not(.is-active),
  .product--thumbnail_slider .product__media-item:not(.is-active) {
    display: none;
  }

  .product-media-modal__content
    > .product__media-item--variant.product__media-item--variant {
    display: none;
  }

  .product-media-modal__content > .product__media-item--variant:first-child {
    display: block;
  }
}

.product__media-item.product__media-item--variant {
  display: none;
}

.product__media-item--variant:first-child {
  display: block;
}

@media screen and (max-width: 749px) {
  .product--thumbnail_slider
    .mobile__Match--height
    .product__media-item:not(.is-active),
  .product--stacked .mobile__Match--height .product__media-item:not(.is-active),
  .product--thumbnail
    .mobile__Match--height
    .product__media-item:not(.is-active) {
    display: none;
  }
  .product--thumbnail_slider .product__media-list,
  .product--stacked .product__media-list,
  .product--thumbnail .product__media-list {
    margin-bottom: 0;
  }
  .product__media-item--variant:first-child {
    padding-right: 1.5rem;
  }
}

@media screen and (min-width: 750px) and (max-width: 989px) {
  .product__media-list .product__media-item:first-child {
    padding-left: 0;
  }

  .product--thumbnail_slider .product__media-list {
    margin-left: 0;
  }

  .product__media-list .product__media-item {
    width: 100%;
  }
}
.product__modal-opener--image .product__media-toggle:hover {
  cursor: zoom-in;
}

.product__modal-opener:hover .product__media-icon {
  border: 0.1rem solid rgba(var(--color-foreground), 0.1);
}

@media screen and (min-width: 750px) {
  .product--grid__item.product__media-item--full {
    width: 100%;
  }
}

@media screen and (min-width: 990px) {
  .product--stacked .product__media-item {
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
  }

  .product__media-list .product__media-item:first-child,
  .product__media-list .product__media-item--full {
    width: 100%;
    max-width: 100%;
  }

  .product__modal-opener .product__media-icon {
    opacity: 0;
  }

  .product__modal-opener:hover .product__media-icon,
  .product__modal-opener:focus .product__media-icon {
    opacity: 1;
  }
  .product--columns
    .product__media-item:not(.product__media-item--single):not(:only-child) {
    max-width: calc(50% - 5px);
  }
}

.product__media-item > * {
  display: block;
  position: relative;
}

.product__media-toggle {
  display: flex;
  border: none;
  background-color: transparent;
  color: currentColor;
  padding: 0;
}

.product__media-toggle::after {
  content: "";
  cursor: pointer;
  display: block;
  margin: 0;
  padding: 0;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  height: 100%;
  width: 100%;
}

.product__media-toggle:focus-visible {
  outline: 0;
  box-shadow: none;
}

.product__media-toggle.focused {
  outline: 0;
  box-shadow: none;
}

.product__media-toggle:focus-visible:after {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0rem 0.5rem rgba(var(--color-foreground), 0.5);
  border-radius: var(--media-radius) - var(--media-border-width);
}

.product__media-toggle.focused:after {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0rem 0.5rem rgba(var(--color-foreground), 0.5);
  border-radius: var(--media-radius);
}

.product__media-toggle:focus-visible:after {
  border-radius: var(--media-radius);
}

@media screen and (max-width: 749px) {
  .product--thumbnail
    .is-active
    > .product__modal-opener:not(.product__modal-opener--image),
  .product--thumbnail_slider
    .is-active
    > .product__modal-opener:not(.product__modal-opener--image) {
    display: none;
  }

  .product--thumbnail .is-active .deferred-media,
  .product--thumbnail_slider .is-active .deferred-media {
    display: block;
    width: 100%;
  }
}

.product__media-list .media > * {
  overflow: hidden;
}

.thumbnail-list {
  flex-wrap: wrap;
  grid-gap: 1rem;
}

@media screen and (min-width: 750px) {
  .product--stacked .thumbnail-list {
    display: none;
  }

  .thumbnail-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
  }
}

.thumbnail-list_item--variant:not(:first-child) {
  display: none;
}

@media screen and (min-width: 990px) {
  .thumbnail-list {
    grid-template-columns: repeat(4, 1fr);
  }

  .product--medium .thumbnail-list {
    grid-template-columns: repeat(5, 1fr);
  }

  .product--large .thumbnail-list {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media screen and (max-width: 749px) {
  .product__media-item {
    display: flex;
    align-items: center;
  }

  .product__modal-opener {
    width: 100%;
  }

  .thumbnail-slider {
    display: flex;
    align-items: center;
  }

  .thumbnail-slider .thumbnail-list.slider {
    display: flex;
    padding: 0.5rem;
    flex: 1;
    scroll-padding-left: 0.5rem;
  }

  .thumbnail-list__item.slider__slide {
    width: calc(33% - 0.6rem);
  }
}

@media screen and (min-width: 750px) {
  .product--thumbnail_slider .thumbnail-slider {
    display: flex;
    align-items: center;
  }

  .thumbnail-slider .thumbnail-list.slider--tablet-up {
    display: flex;
    padding: 0.5rem 0;
    flex: 1;
    scroll-padding-left: 0.5rem;
  }

  .product__media-wrapper .slider-mobile-gutter .slider-button {
    display: none;
  }

  .thumbnail-list.slider--tablet-up .thumbnail-list__item.slider__slide {
    width: calc(25% - 0.8rem);
  }

  .product--thumbnail_slider .slider-mobile-gutter .slider-button {
    display: flex;
  }
  .product.media__postion--right {
    flex-direction: row-reverse;
  }
}

@media screen and (min-width: 900px) {
  .product--small
    .thumbnail-list.slider--tablet-up
    .thumbnail-list__item.slider__slide {
    width: calc(25% - 0.8rem);
  }

  .thumbnail-list.slider--tablet-up .thumbnail-list__item.slider__slide {
    width: calc(20% - 0.8rem);
  }
}
.thumbnail {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  height: 100%;
  width: 100%;
  padding: 0;
  color: rgb(var(--color-base-text));
  cursor: pointer;
  background: none;
  border: 1px solid rgb(var(--color-foreground), 0.15);
}
.thumbnail > img {
  border: 1px solid rgb(var(--color-foreground), 0.15);
  padding: 0.5rem;
}
.thumbnail:hover {
  opacity: 0.7;
}

.thumbnail.global-media-settings img {
  border-radius: 0;
}

.thumbnail[aria-current] {
  border-color: rgb(var(--color-foreground));
}

.thumbnail[aria-current]:focus-visible {
  box-shadow: 0 0 0 0.1rem rgb(var(--color-background)),
    0 0 0rem 0.1rem rgba(var(--color-foreground), 0.5);
}

.thumbnail[aria-current]:focus,
.thumbnail.focused {
  outline: 0;
  box-shadow: 0 0 0 0.1rem rgb(var(--color-background)),
    0 0 0rem 0.1rem rgba(var(--color-foreground), 0.5);
}

.thumbnail[aria-current]:focus:not(:focus-visible) {
  outline: 0;
  box-shadow: 0 0 0 0.1rem rgb(var(--color-foreground));
}

.thumbnail img {
  pointer-events: none;
}

.thumbnail--narrow img {
  height: 100%;
  width: auto;
  max-width: 100%;
}

.thumbnail--wide img {
  height: auto;
  width: 100%;
}

.thumbnail__badge .icon {
  width: 1rem;
  height: 1rem;
}

.thumbnail__badge .icon-3d-model {
  width: 1.2rem;
  height: 1.2rem;
}

.thumbnail__badge {
  color: rgb(var(--color-foreground));
  height: 2rem;
  width: 2rem;
  left: auto;
  right: 0;
  top: 0;
}
.thumbnail-slider .slider-button .icon {
    height: 9px;
}
@media screen and (min-width: 750px) {
  .product:not(.product--small) .thumbnail__badge {
    height: 3rem;
    width: 3rem;
  }

  .product:not(.product--small) .thumbnail__badge .icon {
    width: 1.2rem;
    height: 1.2rem;
  }

  .product:not(.product--small) .thumbnail__badge .icon-3d-model {
    width: 1.4rem;
    height: 1.4rem;
  }
}

.thumbnail-list__item {
  position: relative;
}

.thumbnail-list__item::before {
  content: "";
  display: block;
  padding-bottom: 100%;
}

.product:not(.featured-product) .product__view-details {
  display: none;
}

.product__view-details {
  display: block;
  text-decoration: none;
}

.product__view-details:hover {
  text-decoration: underline;
  text-underline-offset: 0.3rem;
}

.product__view-details .icon {
  width: 1.2rem;
  margin-left: 1.2rem;
  flex-shrink: 0;
}
.thumbnail-slider .slider-button {
    color: rgb(var(--color-button-text));
    background: rgba(var(--color-button),var(--alpha-button-background));
    width: 3.9rem;
    height: 4rem;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 9;
    border-radius: 50%;
}
.thumbnail-slider .slider-button.slider-button--prev {
  left: -30px;
}
.thumbnail-slider .slider-button[disabled] .icon,
.thumbnail-slider .slider-button:not([disabled]):hover {
  color: rgb(var(--color-button-text));
}
.thumbnail-slider .slider-button {
  opacity: 0;
}
.thumbnail-slider:hover .slider-button {
  opacity: 1;
}
.thumbnail-slider:hover button:disabled {
  opacity: 0.5;
}
@media only screen and (min-width: 750px) {
  .product--thumbnail .thumbnail-slider button:disabled,
  .product--stacked .thumbnail-slider button:disabled,
  .product--stacked .thumbnail-slider button {
    display: none;
  }
}
@media screen and (max-width: 749px) {
  .product--mobile-columns .product__media-item {
    width: calc(50% - 1.5rem - 5px);
  }
  .product__media-icon,
  .thumbnail__badge {
    height: 4rem;
    width: 4rem;
  }
  .product__media-icon .icon {
    width: 2rem;
  }
}
.product--thumbnail_slider .thumbnail-slider {
  overflow: hidden;
}
.thumbnail-slider .slider-button.slider-button--next {
  left: auto;
  right: -30px;
}
.thumbnail-slider:hover .slider-button.slider-button--prev {
  left: 10px;
}
.thumbnail-slider:hover .slider-button.slider-button--next {
  right: 10px;
}
.wishlist__button--text > span > svg {
  width: 18px;
  margin-right: 9px;
}
.wishlist__button--text.link {
  text-decoration: none;
}
.wishlist__button.loading.adding .remove__wishlist.wishlist__button--text {
  display: none;
}
.wishlist__button.active .remove__wishlist.wishlist__button--text {
  display: inline-flex;
}
.wishlist__button.wishlist__button--text {
  position: relative;
}
.wishlist__button.wishlist__button--text.loading.adding {
  pointer-events: none;
  padding-right: 3rem;
}
.wishlist__button.wishlist__button--text.loading:after {
  top: 0;
}
.product-popup-modal__button > svg {
  width: 20px;
}
.product-popup-modal__button > span {
  font-size: 1.8rem;
  margin-left: 1rem;
}
button.product-form__submit.loading:after {
  top: 10px;
  left: 62px;
}
.main-product__description {
  padding-top: 1.5rem;
}
.main-product__description {
  padding-top: 1.5rem;
  margin-top: 0;
}
.deferred-media__poster-button.video--button {
  position: absolute;
  background: rgba(var(--color-background));
  width: 6rem;
  height: 6rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(var(--color-foreground));
}
.product-form__submit.button > svg {
  width: 2.2rem;
}
.product-form__submit.button {
  gap: 0.7rem;
  width: 100%;
}
.product--thumbnail_slider .thumbnail-slider .thumbnail,.product--thumbnail_slider .thumbnail-slider .thumbnail.global-media-settings img {
  border-radius: 10px;
}
.thumbnail-slider .slider-button {
    color: rgb(var(--color-button-text));
    background: rgba(var(--primary-button-hover-background));
}
.thumbnail-slider .slider-button:hover {
    color: rgb(var(--color-button-text));
    background: rgba(var(--color-button),var(--alpha-button-background));
}
.product-form__cart--box .product__add__cart__button button.product-form__submit.button.button--secondary {
background: rgba(var(--color-button-text),var(--alpha-button-border));
color: rgba(var(--secondary-button-hover-text));
}
.product-form__cart--box .product__add__cart__button button.product-form__submit.button.button--secondary svg {
fill: rgba(var(--secondary-button-hover-text));
}
.product-form__cart--box .product__add__cart__button {
    width: 100%;
}
.product-form__cart--box.d-flex {
    flex-wrap: nowrap;
}
.product-form__cart--box .product__add__cart__button button.product-form__submit.button.button--secondary:hover {
    background: rgba(var(--primary-button-hover-background));
    border-color: rgba(var(--primary-button-hover-background));
}
.main-product__description {
    padding-top: 0;
    margin-top: 0;
    padding-bottom: 0px;
}
.discount__sale__text {
    border-radius: 1rem;
}
.product__inventory,.price__regular {
    font-weight: 700;
}
.product-variant-inventory--inner {
    padding-top: 5px;
}
.product__info-container div#shopify-block-AUDQza0hyRmxXOVRBR__trustshop_reviews_widget_product_rating_dWmBtm {
    margin-top: -8px;
    margin-bottom: 19px;
}
.product__media_container 
.product__media-item .product__media {
    border-radius: 1rem;
}









