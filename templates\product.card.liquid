{% comment %}
  Product recommendation slider
{% endcomment %}
{%- layout none -%}
{%- liquid
  # Parse URL parameters for section-specific settings
  assign url_params = request.url | split: '?' | last

  # Helper function to get URL parameter value
  assign get_param = ''

  # Parse card_style
  assign card_style_param = url_params | split: 'card_style=' | last | split: '&' | first
  if card_style_param != blank and card_style_param != url_params
    assign card_style_to_use = card_style_param
  else
    assign card_style_to_use = settings.recent_viewed_proudct_card
  endif

  # Parse image_ratio
  assign image_ratio_param = url_params | split: 'image_ratio=' | last | split: '&' | first
  if image_ratio_param != blank and image_ratio_param != url_params
    assign image_ratio_to_use = image_ratio_param
  else
    assign image_ratio_to_use = settings.recent_viewed_proudct_media_size
  endif

  # Parse boolean parameters
  assign show_secondary_image_param = url_params | split: 'show_secondary_image=' | last | split: '&' | first
  if show_secondary_image_param == 'true'
    assign show_secondary_image_to_use = true
  else
    assign show_secondary_image_to_use = false
  endif

  assign color_swatches_param = url_params | split: 'color_swatches=' | last | split: '&' | first
  if color_swatches_param == 'true'
    assign color_swatches_to_use = true
  else
    assign color_swatches_to_use = false
  endif

  assign show_badges_param = url_params | split: 'show_badges=' | last | split: '&' | first
  if show_badges_param == 'true'
    assign show_badges_to_use = true
  else
    assign show_badges_to_use = settings.show_badges
  endif

  assign show_cart_button_param = url_params | split: 'show_cart_button=' | last | split: '&' | first
  if show_cart_button_param == 'true'
    assign show_cart_button_to_use = true
  else
    assign show_cart_button_to_use = settings.show_cart_button
  endif

  assign show_preorder_button_param = url_params | split: 'show_preorder_button=' | last | split: '&' | first
  if show_preorder_button_param == 'true'
    assign show_preorder_button_to_use = true
  else
    assign show_preorder_button_to_use = false
  endif

  assign show_quick_view_param = url_params | split: 'show_quick_view=' | last | split: '&' | first
  if show_quick_view_param == 'true'
    assign show_quick_view_to_use = true
  else
    assign show_quick_view_to_use = settings.show_quick_view_button
  endif

  assign show_compare_param = url_params | split: 'show_compare=' | last | split: '&' | first
  if show_compare_param == 'true'
    assign show_compare_to_use = true
  else
    assign show_compare_to_use = settings.show_compare_view_button
  endif

  assign show_wishlist_param = url_params | split: 'show_wishlist=' | last | split: '&' | first
  if show_wishlist_param == 'true'
    assign show_wishlist_to_use = true
  else
    assign show_wishlist_to_use = settings.show_wishlist_button
  endif

  assign show_title_param = url_params | split: 'show_title=' | last | split: '&' | first
  if show_title_param == 'true'
    assign show_title_to_use = true
  else
    assign show_title_to_use = true
  endif

  assign show_price_param = url_params | split: 'show_price=' | last | split: '&' | first
  if show_price_param == 'true'
    assign show_price_to_use = true
  else
    assign show_price_to_use = true
  endif

  assign show_vendor_param = url_params | split: 'show_vendor=' | last | split: '&' | first
  if show_vendor_param == 'true'
    assign show_vendor_to_use = true
  else
    assign show_vendor_to_use = false
  endif

  assign show_countdown_param = url_params | split: 'show_countdown=' | last | split: '&' | first
  if show_countdown_param == 'true'
    assign show_countdown_to_use = true
  else
    assign show_countdown_to_use = false
  endif

  assign show_rating_param = url_params | split: 'show_rating=' | last | split: '&' | first
  if show_rating_param == 'true'
    assign show_rating_to_use = true
  else
    assign show_rating_to_use = settings.recent_viewd_product_rating
  endif

  assign inventory_status_param = url_params | split: 'inventory_status=' | last | split: '&' | first
  if inventory_status_param == 'true'
    assign inventory_status_to_use = true
  else
    assign inventory_status_to_use = settings.recent_viewd_inventory_status
  endif

  assign card_radius_param = url_params | split: 'card_radius=' | last | split: '&' | first
  if card_radius_param == 'true'
    assign card_radius_to_use = true
  else
    assign card_radius_to_use = settings.product_card_radius
  endif

  assign card_spacing_param = url_params | split: 'card_spacing=' | last | split: '&' | first
  if card_spacing_param == 'true'
    assign card_spacing_to_use = true
  else
    assign card_spacing_to_use = settings.product_card_spacing
  endif

  # Parse color_scheme
  assign color_scheme_param = url_params | split: 'color_scheme=' | last | split: '&' | first
  if color_scheme_param != blank and color_scheme_param != url_params
    assign color_scheme_to_use = color_scheme_param
  else
    assign color_scheme_to_use = settings.product_card_color_scheme
  endif
-%}
{%- render 'product-card',
  product_card_product: product,
  className: 'swiper-slide',
  media_size: image_ratio_to_use,
  show_secondary_image: show_secondary_image_to_use,
  show_vendor: show_vendor_to_use,
  show_badge: show_badges_to_use,
  show_cart_button: show_cart_button_to_use,
  show_preorder_button: show_preorder_button_to_use,
  show_quick_view: show_quick_view_to_use,
  show_quick_compare: show_compare_to_use,
  show_wishlist: show_wishlist_to_use,
  show_countdown: show_countdown_to_use,
  show_title: show_title_to_use,
  show_price: show_price_to_use,
  show_rating: show_rating_to_use,
  card_style: card_style_to_use,
  color_swatches: color_swatches_to_use,
  color_scheme: color_scheme_to_use,
  spacing: card_spacing_to_use,
  corner_radius: card_radius_to_use,
  inventory_status: inventory_status_to_use
-%}
