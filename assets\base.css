/* Color custom properties */
:root {
  --alpha-button-background: 1;
  --alpha-button-border: 1;
  --alpha-link: 0.85;
  --alpha-badge-border: 0.1;
}

.button--tertiary {
  --color-button: var(--color-base-outline-button-labels);
  --color-button-text: var(--color-base-outline-button-labels);
  --alpha-button-background: 0;
  --alpha-button-border: 0.2;
}

/* base CSS */

.no-js:not(html) {
  display: none !important;
}

html.no-js .no-js:not(html) {
  display: block !important;
}

.no-js-inline {
  display: none !important;
}

html.no-js .no-js-inline {
  display: inline-block !important;
}

html.no-js .no-js-hidden {
  display: none !important;
}

/*  page width   */

.page-width {
  max-width: var(--page-width);
  margin: 0 auto;
  padding: 0 1.5rem;
}

.page-width-desktop {
  padding: 0;
  margin: 0 auto;
}

@media screen and (min-width: 750px) {
  .page-width {
    padding: 0 5rem;
  }

  .page-width--narrow {
    padding: 0 9rem;
  }

  .page-width-desktop {
    padding: 0;
  }
}

@media screen and (min-width: 990px) {
  .page-width--narrow {
    max-width: 72.6rem;
    padding: 0;
  }

  .page-width-desktop {
    max-width: var(--page-width);
    padding: 0 5rem;
  }
}

.element-margin {
  margin-top: 5rem;
}
:root {
  --duration-short: 0.1s;
  --duration-default: 0.2s;
  --duration-long: 0.5s;
}
.page-margin,
.shopify-challenge__container {
  margin: 7rem auto;
}

.rte-width {
  max-width: 82rem;
  margin: 0 auto 2rem;
}

.list-unstyled {
  margin: 0;
  padding: 0;
  list-style: none;
}

.hidden {
  display: none !important;
}
[hidden] {
  display: none !important;
}

.visually-hidden {
  position: absolute !important;
  overflow: hidden;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
  clip: rect(0 0 0 0);
  word-wrap: normal !important;
}

.visually-hidden--inline {
  margin: 0;
  height: 1em;
}

.overflow-hidden {
  overflow: hidden;
}

.skip-to-content-link:focus {
  z-index: 9999;
  position: inherit;
  overflow: auto;
  width: auto;
  height: auto;
  clip: auto;
}

.full-width-link {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
}

/* Heading Tag Style */
h1,
h2,
h3,
h4,
h5,
h6,
.h0,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-family: var(--font-heading-family);
  font-style: var(--font-heading-style);
  font-weight: var(--font-heading-weight);
  letter-spacing: var(--heading-letter-spacing, 0);
  color: rgb(var(--color-foreground));
  line-height: calc(1 + 0.3 / max(1, var(--font-heading-size)));
  margin: 0 0 15px;
  text-transform: var(--header-text-case);
}

.h0 {
  font-size: calc(var(--font-heading-size) * 3rem);
}

@media only screen and (min-width: 992px) {
  .h0 {
    font-size: calc(var(--font-heading-size) * 5rem);
  }
}

h1,
.h1 {
  font-size: calc(var(--font-heading-size) * 2.5rem);
}
@media only screen and (min-width: 750px) {
  h1,
  .h1 {
    font-size: calc(var(--font-heading-size) * 3.6rem);
  }
}

@media only screen and (min-width: 992px) {
  h1,
  .h1 {
    font-size: calc(var(--font-heading-size) * 4rem);
  }
}
h2,
.h2 {
  font-size: calc(var(--font-heading-size) * 2.5rem);
}

@media only screen and (min-width: 750px) {
  h2,
  .h2 {
    font-size: calc(var(--font-heading-size) * 3rem);
  }
}
@media only screen and (min-width: 992px) {
  h2,
  .h2 {
    font-size: calc(var(--font-heading-size) * 3.2rem);
  }
}

h3,
.h3 {
  font-size: calc(var(--font-heading-size) * 2.2rem);
}
@media only screen and (min-width: 750px) {
  h3,
  .h3 {
    font-size: calc(var(--font-heading-size) * 2.8rem);
  }
}

h4,
.h4 {
  font-size: calc(var(--font-heading-size) * 2rem);
}
@media only screen and (min-width: 750px) {
  h4,
  .h4 {
    font-size: calc(var(--font-heading-size) * 2.4rem);
  }
}

h5,
.h5 {
  font-size: calc(var(--font-heading-size) * 1.8rem);
}

@media only screen and (min-width: 750px) {
  h5,
  .h5 {
    font-size: calc(var(--font-heading-size) * 2rem);
  }
}

h6,
.h6 {
  font-size: calc(var(--font-heading-size) * 1.6rem);
}
/*  End Heading Tag Style */

/*  Others Common Style */
p {
  margin-top: 0;
  margin-bottom: 15px;
}
p:last-child {
  margin-bottom: 0;
}

blockquote {
    font-style: italic;
    color: rgb(var(--color-button-text));
    border-left: 0 solid rgba(var(--color-foreground),.2);
    padding-left: 1rem;
    background: rgba(var(--primary-button-hover-background));
    padding: 25px 30px;
    border-radius: 1rem;
}
.article-template__content img {
    width: 100%;
}
.article-template__content img {
    width: 100%;
    border-radius: 1rem;
}
.caption {
  font-size: 1rem;
  letter-spacing: 0.07rem;
  line-height: 1.7;
}

@media screen and (min-width: 750px) {
  .caption {
    font-size: 1.2rem;
  }
}

.caption-with-letter-spacing {
  font-size: 1.6rem;
  letter-spacing: 0.13rem;
  line-height: 1.2;
  text-transform: uppercase;
}

.caption-large,
.customer .field input,
.customer select,
.field__input,
.form__label,
.select__select {
  font-size: 16px;
  line-height: 1.5;
}

.color-foreground {
  color: rgb(var(--color-foreground));
}

table:not([class]) {
  table-layout: fixed;
  border-collapse: collapse;
  font-size: 1.4rem;
  border-style: hidden;
  box-shadow: 0 0 0 0.1rem rgba(var(--color-foreground), 0.2);
  /* draws the table border  */
}

table:not([class]) td,
table:not([class]) th {
  padding: 1em;
  border: 0.1rem solid rgba(var(--color-foreground), 0.2);
}
.error {
  color: red;
}
.hidden {
  display: none !important;
}

a:empty,
ul:empty,
dl:empty,
section:empty,
article:empty,
div:empty,
p:empty,
h1:empty,
h2:empty,
h3:empty,
h4:empty,
h5:empty,
h6:empty {
  display: none;
}

.link,
.customer a {
  cursor: pointer;
  display: inline-block;
  border: none;
  box-shadow: none;
  text-decoration: none;
  text-underline-offset: 0.3rem;
  color: rgb(var(--color-link));
  background-color: transparent;
  font-size: 16px;
  font-family: inherit;
}

.link--text {
  color: rgb(var(--color-foreground));
}

.link--text:hover {
  color: rgba(var(--primary-button-hover-background)) !important;
}

hr {
  border: none;
  height: 0.1rem;
  background-color: rgba(var(--color-foreground), 0.2);
  display: block;
  margin: 5rem 0;
}

@media screen and (min-width: 750px) {
  hr {
    margin: 7rem 0;
  }
}
.placeholder-new {
  position: relative;
}
.placeholder {
  background-color: rgba(var(--placeholder-background));
  color: rgba(var(--placeholder-foreground), 0.85);
  fill: rgba(var(--placeholder-foreground), 0.85);
  position: relative;
}
.placeholder-svg-new {
  position: absolute;
  left: 0;
  height: 100%;
  top: 0;
  width: 100%;
  fill: currentColor;
}
details > * {
  box-sizing: border-box;
}

.break {
  word-break: break-word;
}

.visibility-hidden {
  visibility: hidden;
}

.customer a {
  color: rgba(var(--color-link), var(--alpha-link));
  text-underline-offset: 0.3rem;
  text-decoration-thickness: 0.1rem;
  transition: text-decoration-thickness ease 100ms;
}
.customer a:hover {
    color: rgba(var(--primary-button-hover-background));
    text-decoration-thickness: .2rem;
}
a,
button {
  line-height: inherit;
  display: inline-block;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  color: inherit;
  font-family: var(--font-body-family);
}
/*  End Others Common Style */

/* Display: None css */

@media screen and (max-width: 749px) {
  .small-hide {
    display: none !important;
  }
}

@media screen and (min-width: 750px) and (max-width: 989px) {
  .medium-hide {
    display: none !important;
  }
}

@media screen and (min-width: 990px) {
  .large-up-hide {
    display: none !important;
  }
}

.d-none {
  display: none;
}
@media only screen and (max-width: 991px) {
  .d-md-none {
    display: none !important;
  }
}

@media only screen and (min-width: 992px) {
  .d-md-only-visible {
    display: none !important;
  }
}

@media only screen and (min-width: 750px) {
  .d-sm-only-visible {
    display: none !important;
  }
}

@media only screen and (max-width: 749px) {
  .d-sm-none {
    display: none !important;
  }
}
@media only screen and (max-width: 575px) {
  .d-xs-none {
    display: none !important;
  }
}

.d-block {
  display: block;
}
@media only screen and (min-width: 992px) {
  .d-md-block {
    display: block !important;
  }
}

@media only screen and (min-width: 750px) {
  .d-sm-block {
    display: block !important;
  }
}

@media only screen and (min-width: 575px) {
  .d-xs-block {
    display: block !important;
  }
}

@media only screen and (max-width: 991px) {
  .d-md-only-block {
    display: block !important;
  }
}

@media only screen and (max-width: 749px) {
  .d-sm-only-block {
    display: block !important;
  }
}
@media only screen and (max-width: 575px) {
  .d-xs-only-block {
    display: block !important;
  }
}

/* End Display: None css */

/* Text Align Center css */
.center {
  text-align: center;
}

.right {
  text-align: right;
}

.uppercase {
  text-transform: uppercase;
}
/* End Text Align Center css */

/* Details summary Css */
summary {
  cursor: pointer;
  list-style: none;
  position: relative;
}

summary .icon-caret {
  position: absolute;
  height: 0.6rem;
  right: 1.5rem;
  top: calc(50% - 0.2rem);
}

summary::-webkit-details-marker {
  display: none;
}

.disclosure-has-popup {
  position: relative;
}

.disclosure-has-popup[open] > summary::before {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: block;
  cursor: default;
  content: " ";
  background: transparent;
}

.disclosure-has-popup > summary::before {
  display: none;
}

.disclosure-has-popup[open] > summary + * {
  z-index: 100;
}

@media screen and (min-width: 750px) {
  .disclosure-has-popup[open] > summary + * {
    z-index: 2;
  }
}

/* base-focus */
/*
  Focus ring - default (with offset)
*/

input:focus {
  box-shadow: 0 0 5px 0px rgba(var(--color-foreground), 0.1);
}

*:focus-visible {
  box-shadow: 0 0 5px 0px rgba(var(--color-foreground), 0.1);
}

/* Fallback - for browsers that don't support :focus-visible, a fallback is set for :focus */
.focused,
.no-js *:focus {
  outline: 0.1rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 0.3rem;
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}

/* Negate the fallback side-effect for browsers that support :focus-visible */
.no-js *:focus:not(:focus-visible) {
  outline: 0;
  box-shadow: none;
}

/*
  Focus ring - inset
*/

.focus-inset:focus-visible {
  outline: 0.1rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: -0.2rem;
  box-shadow: 0 0 0.2rem 0 rgba(var(--color-foreground), 0.3);
}

.focused.focus-inset,
.no-js .focus-inset:focus {
  outline: 0.1rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: -0.2rem;
  box-shadow: 0 0 0.2rem 0 rgba(var(--color-foreground), 0.3);
}

.no-js .focus-inset:focus:not(:focus-visible) {
  outline: 0;
  box-shadow: none;
}

/*
  Focus ring - none
*/

/* Dangerous for a11y - Use with care */
.focus-none {
  box-shadow: none !important;
  outline: 0 !important;
}

.focus-offset:focus-visible {
  outline: 0.1rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 1rem;
  box-shadow: 0 0 0 1rem rgb(var(--color-background)),
    0 0 0.2rem 1.2rem rgba(var(--color-foreground), 0.3);
}

.focus-offset.focused,
.no-js .focus-offset:focus {
  outline: 0.1rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 1rem;
  box-shadow: 0 0 0 1rem rgb(var(--color-background)),
    0 0 0.2rem 1.2rem rgba(var(--color-foreground), 0.3);
}

.no-js .focus-offset:focus:not(:focus-visible) {
  outline: 0;
  box-shadow: none;
}

/* component-media */
.media {
  display: block;
  background-color: rgba(var(--color-foreground), 0.1);
  position: relative;
  overflow: hidden;
}

.media--transparent {
  background-color: transparent;
}
.media
  > *:not(
    .zoom,
    .deferred-media__poster-button,
    .lookbook__shop--product-wrapper,
    quick-view-modal
  ),
.media model-viewer {
  display: block;
  max-width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}
.media > img {
  object-fit: cover;
  object-position: center center;
  transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.media--square {
  padding-bottom: 100%;
}

.media--portrait {
  padding-bottom: 125%;
}

.media--landscape {
  padding-bottom: 66.6%;
}

.media--cropped {
  padding-bottom: 56%;
}

.media--16-9 {
  padding-bottom: 56.25%;
}

.media--circle {
  padding-bottom: 100%;
  border-radius: 50%;
}

.media.media--hover-effect > img + img {
  opacity: 0;
}

@media screen and (min-width: 990px) {
  .media--cropped {
    padding-bottom: 63%;
  }
}

deferred-media {
  display: block;
}

/* End component-media */

/* Button default style */
.button, .customer button, .shopify-challenge__button {
    cursor: pointer;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    font-size: calc(var(--button-font-size) * 1.7rem);
    padding: 1rem 3rem;
    text-decoration: none;
    border: var(--button-border-width) solid transparent;
    background-color: rgba(163, 87, 65, 1);
    color: rgb(var(--color-button-text));
    transition: box-shadow var(--duration-short) ease;
    -webkit-appearance: none;
    appearance: none;
    border-radius: var(--button-border-radius);
    letter-spacing: var(--button-letter-spacing);
    text-transform: var(--button-text-case);
    transition: .3s;
    position: relative;
}
.skip--button {
  padding: 0 1.5rem;
  background-color: rgba(var(--color-button), var(--alpha-button-background));
  color: rgb(var(--color-button-text));
  transition: box-shadow var(--duration-short) ease;
}
.button:focus-visible,
.skip--button:focus-visible {
  box-shadow: 0 0 0 0.1rem rgba(var(--color-button), var(--alpha-button-border)),
    0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}
.button--primary:hover {
    background-color: rgba(var(--color-button),var(--alpha-button-background));
    color: rgba(var(--primary-button-hover-text));
}
.button--secondary,
.button--tertiary {
  --color-button: var(--color-secondary-button);
  --color-button-text: var(--color-secondary-button-text);
}

.button--secondary {
  --alpha-button-background: 0;
  border-width: var(--button-border-width);
  border-color: rgba(var(--color-button-text), var(--alpha-button-border));
}

.button--tertiary {
  --alpha-button-background: 0;
  --alpha-button-border: 0.2;
}

.button--secondary:hover {
  background-color: rgba(var(--secondary-button-hover-background));
  color: rgba(var(--secondary-button-hover-text));
  border-color: rgba(var(--secondary-button-hover-background));
}
.button-label {
  font-size: 1.5rem;
  letter-spacing: 0.1rem;
  line-height: 1.2;
}

.button--tertiary {
  font-size: 1.2rem;
  padding: 1rem 1.5rem;
  min-width: 9rem;
  min-height: 3.5rem;
}
.button--large {
  padding-left: 5rem !important;
  padding-right: 5rem !important;
}
.button--medium {
  padding-left: 3.5rem !important;
  padding-right: 3.5rem !important;
}
.button--small {
  padding: 1rem 2rem;
}
.button--extra-small {
  font-size: 1.6rem;
  padding: 1rem 1.5rem;
}
.button--with-icon {
  align-items: center;
}
.button--icon {
  line-height: 0;
}
.button--icon-right {
  margin-left: 0.5rem;
}
.button--icon > svg {
  width: 2.2rem;
  display: inline-block;
}
.button--icon-left {
  margin-right: 0.5rem;
}
/* Button - other */

.button:disabled,
.button[aria-disabled="true"],
.button.disabled,
button:disabled,
.customer button:disabled,
.customer button[aria-disabled="true"],
.customer button.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.button--full-width {
  display: flex;
  width: 100%;
}

.button.loading,
button.loading {
  color: transparent !important;
}

.button.loading:after,
button.loading:after {
  animation: loading var(--duration-long) infinite linear;
  border: 2.5px solid rgba(var(--color-foreground));
  border-left: 2.5px solid transparent;
  border-radius: 100%;
  box-sizing: content-box;
  content: "";
  display: block;
  height: 20px;
  position: absolute;
  width: 20px;
  top: 5px;
  left: 5px;
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Button default style End */

/* component-form */
.field__input,
.select__select,
.customer .field input {
  -webkit-appearance: none;
  appearance: none;
  background-color: transparent;
  border: 0;
  border-radius: 0;
  color: rgb(var(--color-foreground));
  font-size: 1.6rem;
  width: 100%;
  box-shadow: 0 0 0 0.1rem rgba(var(--color-foreground), 0.55);
  height: 4.5rem;
  box-sizing: border-box;
  transition: box-shadow var(--duration-short) ease;
}

.select__select {
  font-family: var(--font-body-family);
  font-style: var(--font-body-style);
  font-weight: var(--font-body-weight);
  font-size: 1.2rem;
  color: rgba(var(--color-foreground), 0.75);
}

.field__input:hover,
.select__select:hover,
.customer .field input:hover,
.localization-form__select:hover {
  box-shadow: 0 0 0 0.2rem rgba(var(--color-foreground), 0.55);
}

.field__input:focus,
.select__select:focus,
.customer .field input:focus,
.localization-form__select:focus {
  box-shadow: 0 0 0 0.2rem rgba(var(--color-foreground), 0.75);
  outline: transparent;
}

.text-area,
.select {
  display: inline-block;
  position: relative;
  width: 100%;
}

/* Select */

.select .icon-caret,
.customer select + svg,
.select__field_form select + svg {
  height: 0.6rem;
  pointer-events: none;
  position: absolute;
  top: calc(50% - 0.2rem);
  right: 1.5rem;
}

.select__select,
.customer select {
  cursor: pointer;
  line-height: 1.6;
  padding: 0 4rem 0 1.5rem;
}
.select_box {
  appearance: none;
  -webkit-appearance: none;
  height: 50px;
  padding: 0 20px;
  min-width: 200px;
  border-color: rgba(var(--color-foreground), 0.55);
  color: rgba(var(--color-foreground), 1);
  background: rgba(var(--color-background));
}
.select_box option {
  font-size: 16px;
}
select option[disabled] {
  background: #ddd;
}
/* Field */

.field {
  position: relative;
  width: 100%;
  display: flex;
}

.customer .field {
  display: block;
}

.field--with-error {
  flex-wrap: wrap;
}

.field__input,
.customer .field input {
  flex-grow: 1;
  text-align: left;
  padding: 1.5rem;
}

.field__label,
.customer .field label {
  font-size: 1.6rem;
  left: 1.5rem;
  top: 1rem;
  margin-bottom: 0;
  pointer-events: none;
  position: absolute;
  transition: top var(--duration-short) ease,
    font-size var(--duration-short) ease;
  color: rgba(var(--color-foreground), 0.75);
  letter-spacing: 0.1rem;
  line-height: 1.5;
}

.field__input:focus ~ .field__label,
.field__input:not(:placeholder-shown) ~ .field__label,
.field__input:-webkit-autofill ~ .field__label,
.customer .field input:focus ~ label,
.customer .field input:not(:placeholder-shown) ~ label,
.customer .field input:-webkit-autofill ~ label {
  font-size: 1rem;
  top: 0.3em;
  letter-spacing: 0.04rem;
}

.field__input:focus,
.field__input:not(:placeholder-shown),
.field__input:-webkit-autofill,
.customer .field input:focus,
.customer .field input:not(:placeholder-shown),
.customer .field input:-webkit-autofill {
  padding: 2.2rem 1.5rem 0.8rem;
}

.field__input::-webkit-search-cancel-button,
.customer .field input::-webkit-search-cancel-button {
  display: none;
}

.field__input::placeholder,
.customer .field input::placeholder {
  opacity: 0;
}

.field__button {
  align-items: center;
  background-color: transparent;
  border: 0;
  color: currentColor;
  cursor: pointer;
  display: flex;
  height: 4.4rem;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 4.4rem;
}

.field__button > svg {
  height: 2.5rem;
  width: 2.5rem;
}

.field__input:-webkit-autofill ~ .field__button,
.field__input:-webkit-autofill ~ .field__label,
.customer .field input:-webkit-autofill ~ label {
  color: rgb(0, 0, 0);
}

/* Text area */

.text-area {
    font-family: var(--font-body-family);
    font-style: var(--font-body-style);
    font-weight: var(--font-body-weight);
    padding: 1.2rem;
    min-height: 10rem;
    resize: none;
    border: .1rem solid rgba(var(--color-foreground),.15);
    font-size: 16px;
}

.text-area--resize-vertical {
  resize: vertical;
}

input[type="checkbox"] {
  display: inline-block;
  width: auto;
  margin-right: 0.5rem;
}

/* Form global */

.form__label {
  display: block;
  margin-bottom: 0.6rem;
}

.form__message {
  align-items: center;
  display: flex;
  font-size: 1.4rem;
  line-height: 1;
  margin-top: 1rem;
}

.form__message--large {
  font-size: 1.6rem;
}

.customer .field .form__message {
  font-size: 1.4rem;
  text-align: left;
}

.form__message .icon,
.customer .form__message svg {
  flex-shrink: 0;
  height: 1.3rem;
  margin-right: 0.5rem;
  width: 1.3rem;
}

.form__message--large .icon,
.customer .form__message svg {
  height: 1.5rem;
  width: 1.5rem;
  margin-right: 1rem;
}

.customer .field .form__message svg {
  align-self: start;
}

.form-status {
  margin: 0;
  font-size: 1.6rem;
}

.form-status-list {
  padding: 0;
  margin: 2rem 0 4rem;
}

.form-status-list li {
  list-style-position: inside;
}

.form-status-list .link::first-letter {
  text-transform: capitalize;
}

/* component-quantity */

.quantity {
  border: 1.5px solid rgba(var(--color-foreground), 0.08);
  position: relative;
  height: 45px;
  width: 120px;
  display: flex;
  background: rgba(var(--color-background));
  border-radius: 25px;
}
.quantity__input {
  color: currentColor;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  background-color: transparent;
  border: 0;
  padding: 10px;
  width: 100%;
  flex-grow: 1;
  -webkit-appearance: none;
  appearance: none;
}
.quantity__button {
  width: 40px;
  flex-shrink: 0;
  border: 0;
  background-color: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgb(var(--color-foreground));
  padding: 0;
}

.quantity__button svg {
  width: 1rem;
  pointer-events: none;
}

.quantity__input:-webkit-autofill,
.quantity__input:-webkit-autofill:hover,
.quantity__input:-webkit-autofill:active {
  box-shadow: 0 0 0 10rem rgb(var(--color-background)) inset !important;
  -webkit-box-shadow: 0 0 0 10rem rgb(var(--color-background)) inset !important;
}

.quantity__input::-webkit-outer-spin-button,
.quantity__input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.quantity__input[type="number"] {
  -moz-appearance: textfield;
}

/* component-modal */
.modal__toggle {
  list-style-type: none;
}

.no-js details[open] .modal__toggle {
  position: absolute;
  z-index: 2;
}

.modal__toggle-close {
  display: none;
}

.no-js details[open] svg.modal__toggle-close {
  display: flex;
  z-index: 1;
  height: 1.7rem;
  width: 1.7rem;
}

.modal__toggle-open {
  display: flex;
}

.no-js details[open] .modal__toggle-open {
  display: none;
}

.no-js .modal__close-button.link {
  display: none;
}

.modal__close-button.link {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0rem;
  height: 20px;
  width: 20px;
  background-color: transparent;
}

.modal__close-button .icon {
  width: 1.7rem;
  height: 1.7rem;
}

.modal__content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgb(var(--color-background));
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.media-modal {
  cursor: zoom-out;
}

.media-modal .deferred-media {
  cursor: initial;
}

/* Announcement bar end */

details-disclosure > details {
  position: relative;
}

@keyframes animateMenuOpen {
  0% {
    opacity: 0;
    transform: translateY(-1.5rem);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.overflow-hidden-mobile,
.overflow-hidden-tablet {
  overflow: hidden;
}

@media screen and (min-width: 750px) {
  .overflow-hidden-mobile {
    overflow: auto;
  }
}

@media screen and (min-width: 990px) {
  .overflow-hidden-tablet {
    overflow: auto;
  }
}
.badge {
    border: 1px solid transparent;
    display: inline-block;
    font-size: 13px;
    line-height: 1;
    padding: 4px 10px;
    text-align: center;
    background-color: rgb(var(--color-badge-background));
    border-color: rgba(var(--color-badge-border), var(--alpha-badge-border));
    color: rgb(var(--color-foreground));
    word-break: break-word;
    border-radius: 1rem;
}
@media only screen and (max-width: 575px) {
  .badge {
    padding: 4px 7px;
  }
}
.gradient {
  background: rgb(var(--color-background));
  background: var(--gradient-background);
  background-attachment: fixed;
}
/*  container width || pag width  */

/* HT own code start */
.container,
.container-fluid {
  max-width: 100%;
  margin-right: auto;
  margin-left: auto;
}
.container {
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}
.container-fluid {
  --offset-fluid: 1.5rem;
  padding-right: var(--offset-fluid);
  padding-left: var(--offset-fluid);
}

@media only screen and (min-width: 992px) {
  .container-fluid {
    --offset-fluid: 3rem;
  }
}

@media only screen and (min-width: 1366px) {
  .container-fluid {
    --offset-fluid: calc(var(--container-fluid-offset) / 4.5);
  }
}

@media only screen and (min-width: 1600px) {
  .container-fluid {
    --offset-fluid: calc(var(--container-fluid-offset) / 2.5);
  }
}

@media only screen and (min-width: 1800px) {
  .container-fluid {
    --offset-fluid: var(--container-fluid-offset);
  }
}

.px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

@media (min-width: 1200px) {
  .container {
    max-width: var(--container-lg-width);
  }
}
.row {
  --bs-gutter-x: 20px;
  --bs-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--bs-gutter-y));
  margin-right: calc(-0.5 * var(--bs-gutter-x));
  margin-left: calc(-0.5 * var(--bs-gutter-x));
}
.row > * {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-top: var(--bs-gutter-y);
}
.g-0,
.gy-0 {
  --bs-gutter-y: 0;
}
.g-0,
.gx-0 {
  --bs-gutter-x: 0;
}
/* HT own code end */

/* HT - Grid Column CSS Start */

.col {
  flex: 1 0 0%;
}

.row-cols-auto > * {
  flex: 0 0 auto;
  width: auto;
}

.row-cols-1 > * {
  flex: 0 0 auto;
  width: 100%;
}

.row-cols-2 > * {
  flex: 0 0 auto;
  width: 50%;
}

.row-cols-3 > * {
  flex: 0 0 auto;
  width: 33.3333333333%;
}

.row-cols-4 > * {
  flex: 0 0 auto;
  width: 25%;
}

.row-cols-5 > * {
  flex: 0 0 auto;
  width: 20%;
}

.row-cols-6 > * {
  flex: 0 0 auto;
  width: 16.6666666667%;
}
@media (min-width: 750px) {
  .col-md {
    flex: 1 0 0%;
  }

  .row-cols-md-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-md-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-md-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-md-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }

  .row-cols-md-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-md-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-md-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
}
@media (min-width: 992px) {
  .col-lg {
    flex: 1 0 0%;
  }

  .row-cols-lg-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-lg-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-lg-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-lg-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }

  .row-cols-lg-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-lg-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-lg-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    flex: 1 0 0%;
  }

  .row-cols-xl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-xl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-xl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-xl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }

  .row-cols-xl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-xl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-xl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
}
@media (min-width: 1500px) {
  .row-cols-xxl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-xxl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-xxl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }

  .row-cols-xxl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-xxl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
}
.col-auto {
  flex: 0 0 auto;
  width: auto;
}

.col-1 {
  flex: 0 0 auto;
  width: 8.33333333%;
}

.col-2 {
  flex: 0 0 auto;
  width: 16.66666667%;
}

.col-3 {
  flex: 0 0 auto;
  width: 25%;
}

.col-4 {
  flex: 0 0 auto;
  width: 33.33333333%;
}

.col-5 {
  flex: 0 0 auto;
  width: 41.66666667%;
}

.col-6 {
  flex: 0 0 auto;
  width: 50%;
}

.col-7 {
  flex: 0 0 auto;
  width: 58.33333333%;
}

.col-8 {
  flex: 0 0 auto;
  width: 66.66666667%;
}

.col-9 {
  flex: 0 0 auto;
  width: 75%;
}

.col-10 {
  flex: 0 0 auto;
  width: 83.33333333%;
}

.col-11 {
  flex: 0 0 auto;
  width: 91.66666667%;
}

.col-12 {
  flex: 0 0 auto;
  width: 100%;
}

@media (min-width: 750px) {
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-md-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-md-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-md-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-md-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-md-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-md-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-md-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-md-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
}
@media (min-width: 992px) {
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-lg-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-lg-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-lg-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-lg-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-lg-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-lg-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-lg-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
}
@media (min-width: 1200px) {
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-xl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-xl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-xl-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-xl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-xl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-xl-6 {
    flex: 0 0 auto;
    width: 50%;
  }
}
@media (min-width: 1400px) {
  .col-xxl-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-xxl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-xxl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-xxl-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-xxl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-xxl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-xxl-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-xxl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
}
.d-flex {
  display: flex;
}
.flex-wrap {
  flex-wrap: wrap;
}
.align-items-center {
  align-items: center;
}
.align-items-end {
  align-items: flex-end;
}
.align-items-start {
  align-items: flex-start;
}
.align-self-center {
  align-self: center;
}

.justify-content-between {
  justify-content: space-between;
}
.justify-content-around {
  justify-content: space-around;
}
.justify-content-center {
  justify-content: center;
}
.justify-content-end {
  justify-content: flex-end;
}
.justify-content-start {
  justify-content: flex-start;
}
.flex-row-reverse {
  flex-direction: row-reverse;
}
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.flex-grow-1 {
  flex-grow: 1;
}
.flex-direction-column {
  flex-direction: column;
}
.inline-y-center {
  display: flex;
  align-items: center;
}
.inline-x-center {
  display: flex;
  justify-content: center;
}
.inline-xy-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
/* TH Grid Column CSS End  */

/* Wishlist Button Css */
.wishlist__button {
  cursor: pointer;
}
.wishlist__button.loading.adding .remove__wishlist,
.wishlist__button.wishlist__button.loading.adding .add__wishlist,
.wishlist__button.active .add__wishlist,
.loading__wishlist,
.remove__wishlist {
  display: none;
}
.wishlist__button.active .remove__wishlist,
.wishlist__button.loading .loading__wishlist {
  display: inline-block;
}
.wishlist__button.loading.adding {
  pointer-events: none;
}
.wishlist__button svg {
  width: 2rem;
  height: auto;
  display: inline-block;
}
.remove__wishlist svg {
  width: 2rem;
}
/* Added overlay Css */
.added__overlay,
.added__overlay_search,
.added__overlay_filter {
  overflow: hidden;
}
.added__overlay::before,
.added__overlay_search::before,
.added__overlay_filter::before {
  position: absolute;
  content: "";
  background: #000;
  width: 100%;
  height: 100%;
  z-index: 3;
  opacity: 0.5;
  cursor: crosshair;
  z-index: 99;
}

/* HT - Input Field */
.input__field, input[type=email], input[type=text], .select__field_form select {
    height: 50px;
    width: 100%;
    padding: 0 15px;
    border: 1px solid rgba(var(--color-foreground),.15);
    border-radius: 5px;
    font-size: 16px;
    color: rgba(var(--color-foreground));
    background: #fff;
    -webkit-appearance: none;
    appearance: none;
}
.input__field_form {
  position: relative;
}
.input__field_form_button {
  position: absolute;
  right: 0;
  background: 0;
  border: none;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  line-height: 1;
}
.input__field:hover {
  appearance: none;
}
textarea {
  padding: 15px;
}

input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(var(--color-foreground), 0.55);
}
input::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(var(--color-foreground), 0.55);
}
input:-ms-input-placeholder {
  /* IE 10+ */
  color: rgba(var(--color-foreground), 0.55);
}
input:-moz-placeholder {
  /* Firefox 18- */
  color: rgba(var(--color-foreground), 0.55);
}
.select__field_form {
  position: relative;
}

/* Tab */
.tab_content {
  display: block;
}

.tab_pane {
  display: none;
  transition: var(--transition);
}
.tab_pane:not(.show) {
  opacity: 0;
}
.tab_pane.show {
  opacity: 1;
}
.tab_pane.active {
  display: block;
}

/*  Spacing Helper css */
.mb-30 {
  margin-bottom: 30px;
}
.mb-40 {
  margin-bottom: 40px;
}
.pt-25 {
  padding-top: 25px !important;
}
.mb-15 {
  margin-bottom: 15px;
}
.mb-20 {
  margin-bottom: 20px;
}
.mt-40 {
  margin-top: 40px;
}
.mr-20 {
  margin-right: 20px;
}
.mb-80 {
  margin-bottom: 80px;
}
.p-0 {
  padding: 0;
}
.pt--0 {
  padding-top: 0;
}
.pb--0 {
  padding-bottom: 0;
}
.pb-80 {
  padding-bottom: 80px;
}
.mt-15 {
  margin-top: 15px;
}
.mb-0 {
  margin-bottom: 0 !important;
}
.mt-0 {
  margin-top: 0 !important;
}
.mb-50 {
  margin-bottom: 50px;
}
.mt-30-minus {
  margin-top: -30px;
}
.mt-50 {
  margin-top: 50px;
}
.mt-30 {
  margin-top: 30px;
}
.mt-20 {
  margin-top: 20px;
}
.max-w-500 {
  max-width: 50rem;
}
.h-100 {
  height: 100%;
}
.y_scroll {
  overflow-y: scroll;
}
.product-form__error-message-wrapper svg,
.drawer_cart-item__error svg {
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 0.7rem;
}
.product-form__error-message-wrapper,
.drawer_cart-item__error {
  background: #f8d7da;
  color: #721c24;
  padding: 5px 10px;
  border-radius: 3px;
  display: flex;
  align-items: center;
}
.drawer_cart-item__error {
  flex: 0 0 100%;
  margin-top: 20px;
}
.mt-10 {
  margin-top: 10px;
}
.placeholder-svg-2 {
  position: absolute;
  left: 50%;
  max-width: 80rem;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  fill: currentColor;
}
.placeholder_svg_parent {
  background-color: rgba(var(--placeholder-background));
  color: rgba(var(--placeholder-foreground));
  position: relative;
}

.relative {
  position: relative;
}
.loading-bar:after {
  content: "";
  /* width: 40px; */
  height: 3px;
  background: #000;

  position: absolute;
  animation: loader 2s;
  -webkit-animation: loader 2s;
  animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  transition-timing-function: linear;
  -webkit-transition-timing-function: linear;
  top: 0px;

  margin-left: 0;
}

@keyframes loader {
  0% {
    width: 0%;
    left: 0;
    right: 0;
  }
  50% {
    width: 100%;
    left: 0;
    right: 0;
  }
  99% {
    width: 0%;
    left: 100%;
    right: 0;
  }
}

@-webkit-keyframes loader {
  0% {
    width: 0%;
    left: 0;
    right: 0;
  }
  50% {
    width: 100%;
    left: 0;
    right: 0;
  }
  99% {
    width: 0%;
    left: 100%;
    right: 0;
  }
}

@-webkit-keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
  }
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}

@-webkit-keyframes animate {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }
  40% {
    -webkit-box-shadow: 0 0 0 50px rgba(255, 193, 7, 0);
    box-shadow: 0 0 0 50px rgba(255, 193, 7, 0);
  }
  80% {
    -webkit-box-shadow: 0 0 0 50px rgba(255, 193, 7, 0);
    box-shadow: 0 0 0 50px rgba(255, 193, 7, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 rgba(255, 193, 7, 0);
    box-shadow: 0 0 0 rgba(255, 193, 7, 0);
  }
}

@keyframes animate {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }
  40% {
    -webkit-box-shadow: 0 0 0 50px rgba(255, 193, 7, 0);
    box-shadow: 0 0 0 50px rgba(255, 193, 7, 0);
  }
  80% {
    -webkit-box-shadow: 0 0 0 50px rgba(255, 193, 7, 0);
    box-shadow: 0 0 0 50px rgba(255, 193, 7, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 rgba(255, 193, 7, 0);
    box-shadow: 0 0 0 rgba(255, 193, 7, 0);
  }
}

.animate-fadeInDown {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
}

.button__icon--arrow_svg {
  max-width: 2.5rem;
}
/* Theme global css */
.position__relative {
  position: relative;
}
.swiper:hover .swiper__nav--btn {
  opacity: 1;
  visibility: visible;
}

.swiper__nav--btn {
  width: 4rem;
  height: 4rem;
  background: rgba(var(--color-button), var(--alpha-button-background));
  color: rgb(var(--color-button-text));
  border-radius: 50%;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: var(--transition);
  transition: var(--transition);
  font-size: 2rem;
}

@media only screen and (max-width: 749px) {
  .swiper__nav--btn {
    width: 3.5rem;
    height: 3.5rem;
  }
}

.swiper__nav--btn:hover {
  background: rgba(var(--primary-button-hover-background));
  color: rgba(var(--primary-button-hover-text));
}

.swiper__nav--btn:after {
  font-size: 1.8rem;
}
.swiper__nav--btn.swiper-button-prev {
  left: 0;
}

.swiper__nav--btn.swiper-button-next {
  right: 0;
}

.hero__slider--activation:hover .swiper__nav--btn.swiper-button-next {
right: 10px;
}

.hero__slider--activation:hover .swiper__nav--btn.swiper-button-prev {
left: 10px;
}
.component--slider-wrapper:hover .swiper-button-next.component--slider--nav {
    right: 24px !important;
}
.component--slider-wrapper:hover .swiper-button-prev.component--slider--nav {
    left: 24px !important;
}


.swiper:hover .swiper__nav--btn.swiper-button-disabled {
  opacity: 0.5;
}
.swiper-button-next.swiper-button-disabled,
.swiper-button-prev.swiper-button-disabled {
  pointer-events: initial;
}
@media only screen and (max-width: 749px) {
  .mobile--text-align-center {
    text-align: center !important;
  }
  .shopify-policy__container {
    margin: 6.5rem auto;
  }
}
@media only screen and (min-width: 750px) {
  .shopify-policy__container {
    margin: 9rem auto;
  }
}
.link.button--not-underline {
  text-decoration: none;
}
.rounded--image-1 {
  border-radius: 1rem;
}
.section-heading__title {
    font-weight: 700;
}
.header__actions_btn_cart_num {
    top: -4px!important;
    right: 1px!important;
    width: 19px!important;
    height: 19px!important;
    padding-left: 1px !important;
}
span.empty_c_icon {
    padding: 45px 0 17px;
    display: block;
}

/* .collection__list--slider .product__items--thumbnail:hover img{
  animation: toBottomFromTop .7s forwards;

} */
@keyframes toBottomFromTop {

  49% {
  -webkit-transform: translateY(-100%);
  transform: translateY(-100%);
  opacity: 1;
  }
  50% {
  -webkit-transform: translateY(100%);
  transform: translateY(100%);
  opacity: 0;
  }
  51% {
  opacity: 1;
  }
}

.collection__list--slider .product__items--thumbnail img {
    transition: .5s;
}
.collection__list--slider .product__items--thumbnail:hover img {
    filter: grayscale(1);
}
body.body_class {
    background: #fff;
}
.deals--product-cart-button.button {
    padding: 6px 28px;
}
.image-with-text__media,.shop__the--look.image-with-text__media--placeholder.placeholder{
  border-radius: 1rem;
}
.swiper-button-next, .swiper-button-prev {
    width: 5rem!important;
    height: 5rem!important;
}
.product__card   .trustshop-collection-rating--item, .deals--product__card .trustshop-collection-rating--item {
    text-align: center;
    justify-content: center;
    display: flex;
    margin-bottom: 6px;
}
.product__card .trustshop-collection-rating--item p.trustshop-collection-rating--count,.deals--product__card .trustshop-collection-rating--item p.trustshop-collection-rating--count {
    display: none;
}
.trustshop-collection-rating--item .trustshop-rating-star--container svg {
    height: 14px!important;
    width: 14px!important;
}
.trustshop-rating-star--container {
    gap: 3px !important;
}
.product__card-title--link:hover {
    color: rgba(var(--color-button),var(--alpha-button-background))!important;
}
.featured--collection-card-content-inner button.button {
    padding: 10px 40px;
}

.swiper-pagination-bullet {
width: 1rem;
height: 1rem;
border: 0 solid transparent;
opacity: 1;
border-radius: 100%;
background: transparent;
position: relative;
transition: var(--transition);
}
.swiper-pagination-bullet.swiper-pagination-bullet-active:before {
background: rgba(var(--pagnation-color));
}
.header__menu_li:hover .header__menu_item {
    color: rgba(var(--color-button),var(--alpha-button-background))!important;
}
.product-form__submit.button svg {
    width: 2.2rem;
    height: 2.2rem;
}
.blog__post {
    box-shadow: #6363630a 0 2px 8px;
    border-radius: 1rem;
}
.blog__post:hover .article--items__link {
    text-decoration: none !important;
}
.blog__post .article--items__link:hover  {
    color: rgba(var(--color-button),var(--alpha-button-background));
}

.blog__items--meta__list span {
    font-weight: 700;
    font-size: 13px;
    text-transform: uppercase;
}
.blog__items--meta>ul {
    flex-direction: row-reverse;
    justify-content: flex-end;
}


@media only screen and (max-width: 991px) {
    a.header__logo_link {
        margin-right: 0 !important;
    }
}

.banner__list--item-content-inner .button--primary:hover {
    background-color: rgb(var(--color-foreground));
}
.footer__bottom button.dropdown__open_label:not(.dropdown__open--header) {
    background: transparent!important;
    padding: 7px 15px;
    border-radius: 5px;
}
.article__button a.button.button--extra-small {
    padding: 10px 40px !important;
}

.collection-hero__text-wrapper {
    flex-direction: column-reverse!important;
    gap: 10px;
}
* {
    scrollbar-width: thin;
}

.product__card.product__card--style_1 .product-card-action-buttons.xxv {
    justify-content: center;
    grid-template-columns: unset !important;
}
.product__card.product__card--style_1 .product-card-action-buttons.xxv button.product__quick_view.product__card--action-btn {
    background: rgba(var(--color-button),var(--alpha-button-background));
    color: rgb(var(--color-button-text));
    border-radius: 30px;
}

.product__card.product__card--style_1 .product-card-action-buttons.xxv button.product__quick_view.product__card--action-btn:hover {
    background-color: rgba(var(--secondary-button-hover-background));
    color: rgba(var(--secondary-button-hover-text));
    border-color: rgba(var(--secondary-button-hover-background));
}
span.trustshop-review-summary--average.display_rating--after {
    display: none !important;
}
.bg_p_d_tab .box-img-detail-1 {
    margin: 0 -10px;
    display: flex!important;
    padding-bottom: 10px;
    padding-top: 35px;
}
.bg_p_d_tab   .box-img-detail-1 .image-detail-main {
    width: 68%;
    padding: 0px 10px;
}
.bg_p_d_tab   .box-img-detail-1 .image-detail-sub {
    width: 32.5%;
    padding: 0px 10px;
}
.nrb_p_d_t_content {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
}
.bg_p_d_tab   .box-img-detail-1 .image-detail-main img {
    width: 100%;
    border-radius: 1rem;
}
.bg_p_d_tab {
    background:rgb(255, 255, 255);
    padding: 30px;
    border-radius: 1rem;
    padding-bottom: 52px;
}
.bg_p_d_tab  .image-detail-sub img {
    width: 100%;
    border-radius: 1rem;
}
.bg_p_d_tab .product_tab_list li {
    padding: 0px !important;
    margin: 0;
    border-width: 3px;
    padding-bottom: 12px !important;
    position: relative;
    top: 2px;
}
.bg_p_d_tab .product_tab_list {
    padding: 0;
    border-bottom: 1px solid #ddd;
    padding-bottom: 0px;
}
p.trustshop-header--title.trustshop.trustshop-title {
    font-size: calc(var(--font-heading-size) * 3.2rem);
    font-weight: 700;
    margin-bottom: 30px;
   color: rgb(var(--color-foreground)) !important;
}
.product-recommendations__heading {
    font-weight: 700;
}
.nrb_p_d_t_content table {
    border-radius: 1rem;
}
p.trustshop.trustshop-stars {
    color: rgb(var(--color-foreground)) !important;
    margin-top: 2px;
    line-height: 32px;
}
p.trustshop.trustshop-rating span {
    color: rgb(var(--color-foreground)) !important;
    font-weight: 700;
}
p.trustshop.trustshop-title.trustshop-review-body--title {
    font-weight: 500 !important;
}
button#trustshop-write--review:hover {
    background: #000 !important;
    border-color: #000 !important;
    color: #fff !important;
}
button#trustshop-write--review:hover {
    transition: .3s !important;
}
.share-icons span.social-links span.social__share--text {
   display: none;
}
.product__info-container .social__share_box .social-links a {
display: flex;
margin: 0;
padding: 5px;
font-size: 1.4rem;
align-items: center;
border-radius: 3px;
background: rgba(var(--color-button),var(--alpha-button-background));
margin-right: 10px;
width: 35px;
height: 35px;
text-align: center;
align-items: center;
justify-content: center;
border-radius: 50%;
}
.product__info-container .social__share_box .social-links a:hover ,.product__info-container .social__share_box button.share-button__button:hover{
  background: rgba(var(--primary-button-hover-background));
}

.wishlist__button.wishlist__button--text:hover {
    color: rgba(var(--primary-button-hover-background));
}

.product__info-container .social__share_box .social-links svg {
width: 15px;
fill: #fff;
height: 15px;
}
.product__info-container .social__share_box .social-links svg path {
fill: #fff;
}
.product__info-container .social__share_box share-button.share-button svg {
    width: 15px;
    height: 15px;
    fill: #fff;
    margin: 0;
    padding: 0;
}
.product__info-container .social__share_box button.share-button__button {
    position: relative;
    background: rgba(var(--color-button),var(--alpha-button-background));
    text-align: center;
    align-items: center;
    display: flex;
    justify-content: center;
    top: 20px;
    left: -5px;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    padding-left: 5px;
}
.guarantee__safe__checkout p {
    font-weight: 700;
    color: rgba(var(--color-foreground));
    margin-bottom: 3px;
}
span.shar_lable_x label {
    color: rgba(var(--color-foreground));
}
span.cart--button-text,.shopify-payment-button__button--unbranded, .button__notify--product {
    text-transform: capitalize !important;
}


/* Container for table to make it scrollable */
.table-responsive {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Basic table styling */
.table-responsive table {
  width: 100%;
  border-collapse: collapse;
}

/* Table cell styles */
.table-responsive th,
.table-responsive td {
  padding: 8px;
  text-align: left;
  border: 1px solid #ddd;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
 
}
.nrb_p_d_t_content table {
    border-radius: 1rem;
    box-shadow: none;
}
.nrb_p_d_t_content .table-responsive th,.nrb_p_d_t_content .table-responsive td {
    text-align: center;
}
.cart_notification--footer a.button.button--secondary {
    padding: 1rem 0rem;
}

@keyframes flash {
  50%,
  0%,
  100% {
    opacity: 1;
  }
  25%,
  75% {
    opacity: 0;
  }
}
button.link.rmov_icon {
    position: relative;
    top: 7px;
}
.cart-notification-product__info .quantity {
    border-radius: 5px;
}
.cart-notification-product__info  .cart__item_price,.price__regular,span.price.price--end {
    font-weight: 700;
}
.cart__items .quantity {
    width: 130px;
    border-radius: 5px;
}
.shipping_calculator.nrb .action_drawer_body>div {
    width: 100%;
}
.nrb_cart_f h6 {
    margin: 0;
    padding: 0;
}
.nrb_cart_f .shipping_calculator {
    border: 0;
    padding: 0;
    margin-top: 0;
    position: relative;
}

button.cart__checkout-button.button {
    background: rgba(var(--primary-button-hover-background));
    color: rgba(var(--primary-button-hover-text));
}
button.cart__checkout-button.button:hover {
    background: rgba(var(--color-button),var(--alpha-button-background));
    color: rgba(var(--primary-button-hover-text));
}
.cart__footer--wrapper .text-area {
    min-height: 9rem;
}
.nrb_cart_f p.totals__subtotal-value {
    font-weight: bold;
    font-size: 2.2rem;
}
.nrb_cart_f small.tax-note.caption-large.rte {
    margin: 0;
    padding: 0 0;
    margin-top: 8px;
    margin-bottom: 20px;
}
table.cart-items tr th {
    text-transform: capitalize;
    font-weight: 700;
    letter-spacing: 0;
}
.video-section__media img {
    border-radius: 0rem 1rem 1rem 0rem;
}
.testimonial__chat--icon {
    display: none;
}
.discount__sale__text,.image-with-text__media img, .faq__media.faq__media--medium.rounded--image.media,.article-template__hero-container .media,.team__thumb .media.media--transparent.media--square {
    border-radius: 1rem;
}
.page_header__title.bread_p_t h1.page_header__title_label.h3 {
    text-transform: uppercase;
    padding-top: 10px;
}
.main-blog .title--primary {
    display: none;
}
.article-template__comments-comment {
    padding: 30px;
    border-radius: 1rem;
    background: transparent !important;
    border: 1px solid rgb(var(--color-background));
}
.single-comment__content p.username {
    font-weight: 700;
}

.article-card__image.media {
    border-radius: 1rem 1rem 0rem 0;
}
.blog__items--content {
    border-radius: 0 0 1rem 1rem;
}
.cart_notification--footer a.button.button--secondary {
    padding: 1rem 0rem;
    background: rgba(var(--color-button),var(--alpha-button-background));
}
.cart_notification--footer a.button.button--secondary:hover {
    background: rgba(var(--color-button-text),var(--alpha-button-border));
}
.cart--checkout__button button.button.button--primary {
    background: rgba(var(--color-button),var(--alpha-button-background));
}
.cart--checkout__button button.button.button--primary:hover {
    background: rgba(var(--primary-button-hover-background));
}
.empty__cart__item a.button.button--medium.button-label {
    padding-top: 16px;
    padding-bottom: 16px;
}


.button:hover {
    background: rgba(var(--color-button),var(--alpha-button-background));
}
.mega__menu--collection-list .collection-card--media__wrapper .media {
   border-radius: 1rem; 
}
.mega__menu--collection-list .collection--list-item {
    text-align: center;
}
.mega__menu--collection-list .collection--list-item:hover .product__categories--content__maintitle {
    text-decoration: none;
    text-underline-offset: 0.2rem;
    color: rgba(var(--color-button),var(--alpha-button-background));
}
#predictive__search_overlay {
    background: #fff !important;
}
.product__grid_column_buttons button.gird__column_icon span {
    position: relative;
    top: 2px;
}
.mega__menu--promo .media--adapt.media {
    border-radius: 1rem;
}
li.offcanvas__menu_li svg {
    display: none;
}
.offcanvas__menu_item .header__actions_btn_cart_num {
    display: none;
}
li.offcanvas__menu_li span.offcanvas__menu_text__icon {
    margin-left: 0 !important;
}
cart-remove-button .button {
    background: transparent;
}
.predictive-search__list-item[aria-selected=true]>*, .predictive-search__list-item:hover>* {
    background-color: transparent !important;
}
.predictive-search__image {
    border-radius: 1rem;
}
span.product__media-icon.motion-reduce.quick-add-hidden svg {
    fill: #fff;
}
.announcement-bar--content span.arrow__link--button {
    display: none;
}
.text__with-banner-grid.text-center {
    padding-top: 6rem;
}
a.banner__items--thumbnail.justify-content-center.align-items-center .text__with-banner-grid.text-center {
    padding-top: 3rem;
}
.mega__menu--banner-grid--3 {
    grid-row: 1;
}
.mega__menu--promo .media {
    border-radius: 1rem;
}


/* blnk p css */

.product_blank__area {
    text-align: center;
}
.p_blank_title {
    font-size: 16px;
    font-weight: 600;
    margin-top: 20px;
    margin-bottom: 7px;
}
.blank_p_rating span.trustshop.trustshop-rating-star--container {
    display: flex;
    align-items: center;
    justify-content: center;
}
.p_blank_price {
    font-size: 18px;
    font-weight: 700;
    margin-top: 12px;
    display: inline-block;
    margin-bottom: 0;
}
.product_blank__area .placeholder.placeholder_svg_parent {
    border-radius: 1rem;
}
.banner__items  a.banner__items--thumbnail.placeholder {
    height: 33rem;
}
.lookbook__product--card .p_blank_title {
    margin-top: 0;
    margin-bottom: 7px;
}
.fetch_blog_sec .blog-placeholder svg.placeholder {
    height: 50%;
}
.fetch_blog_sec .blog-placeholder svg.placeholder {
    height: 50%;
    background: #F44B88;
    color: #fff;
    fill: #dbdbdb;
}
.fetch_blog_sec .blog__items--content {
    background-color: #FFF4F8;
}
.product__cart--wrapper .action__btn--svg svg {
    fill: rgb(var(--color-button-text))!important;
}







/*  Responsive css new */

/* large desktop :1366px. */
@media (min-width: 1200px) and (max-width: 1600px) {

}

/* Normal desktop :992px. */
@media (min-width: 992px) and (max-width: 1200px) {
.header__menu_item {
    font-size: 1.6rem !important;
}
.header__menu_li {
    padding: 0 0 !important;
}
h2.slider__content--maintitle {
    font-size: 42px !important;
}

}

/* Tablet desktop :768px. */
@media (min-width: 768px) and (max-width: 991px) {
.featured--collection-card-content {
    padding: 1rem!important;
}


  

}

/* small mobile :320px. */
@media (max-width: 767px) {
.section-heading.text-left {
    text-align: center;
}
.product-card-action-buttons {
    justify-content: center;
}
.product__card--action-btn:not(.product__card--mobile-btn) .cart__buton--label {
    
}

.product__card--action-btn:not(.product__card--mobile-btn) {
    gap: 4px;
    font-size: 14px;
    padding: 7px 10px!important;
}
.swiper-button-next, .swiper-button-prev {
    width: 4rem!important;
    height: 4rem!important;
}
.brand__logo--items a.header__logo_link {
    width: 95%;
    max-width: 90px;
}
p.trustshop-header--title.trustshop.trustshop-title {
    font-size: calc(var(--font-heading-size) * 2.5rem);
}
.nrb_p_d_t_content {
    width: 100%;
}
.nrb_p_d_t .product_tab_list {
    justify-content: center;
}
.bg_p_d_tab .product_tab_list li {
    margin-bottom: 15px;
}
.bg_p_d_tab .image-detail-sub img {
    margin-bottom: 17px;
}
.cart--checkout__button button.button.button--primary.button--full-width {
    padding: 1rem 2rem !important;
}
.article-template__comments-fields, .article-template__comment-fields>* {
    margin-bottom: 0rem!important;
}
.article-template__comment-wrapper h2 {
    margin-bottom: 30px;
}
.article-template__comments {
    margin-top: 0;
}
.sign__author--header {
    justify-content: center;
    flex-direction: column!important;
    gap: 13px!important;
}
.sign--image img {
    max-width: 55% !important;
}
.sign__author--header>div+div {
    margin-left: 0;
}
.single--feature-list--item {
    align-items: flex-start!important;
}
.testimonial__section--inner.testimonial__swiper--activation {
    margin-top: -16px;
}
.contact__info {
    padding: 1.5rem!important;
}
.contact__info--items {
    margin-bottom: 0!important;
    padding: 15px 15px;
}
.contact__info--icon {
    min-height: 70px!important;
    max-width: 70px!important;
}
.map__direcion--content {
    margin-top: 30px;
}

.section-timer-wrapper-sec {
    display: flex;
    gap: 1rem!important;
    flex-direction: column;
}
.section--header-countdown-timer-wrapper.tp01 {
    margin-bottom: 0px!important;
}
.section--header-wrapper {
    text-align: center;
}
.section--header-wrapper .button--wrapper {
    margin-top: 17px;
}
.banner__items--content__title p br {
    display: none;
}
span.announcement-bar__message {
    text-align: center;
}
.tab_sec_title_tab_title.text-space-between_area {
    display: flex;
    justify-content: center;
    margin-bottom: 12px;
    align-items: center;
    flex-direction: column;
    gap: 5px;
}
.product__tab--btn__list:last-child {
    margin: 5px 10px !important;
}







  
  
}

/* Large Mobile :480px. */
@media only screen and (min-width: 480px) and (max-width: 767px) {



}


















