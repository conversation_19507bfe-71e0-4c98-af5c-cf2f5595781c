{% if product != blank %}
  <div class="lookbook__shop--product-wrapper" style="--hotspot-x: {{ hotspot_x }}%; --hotspot-y: {{ hotspot_y }}%;">
  {% if product != blank %}
  {% if quick_shop %} <quick-view-modal> {% endif %}
  {% endif %} 
    {% if quick_shop %}
    <button type="button" class="look__hotspot  {% if product == blank %}no__lookbook{% endif %} {% if hotspot_y > 70 %} lookbook__shop--bottom {% endif %}" data-product-handle="{{ product.handle }}" aria-label="{{ 'products.product.select_options' | t }}" style="--hotspot-background-1: {{ block.settings.background_1 }}; --hotspot-background-2-gradient: {{ block.settings.background_2_gradient | default: "radial-gradient(50% 50% at 50% 50%,#FF9181 0%,#FF9181 100%)" }}" {{ block.shopify_attributes }}>
       <span class="look__hotspot--arrow"></span>
    </button>
    {% else %}
     <a href="{{ product.url | default: '#' }}" class="look__hotspot  {% if product == blank %}no__lookbook{% endif %}" style="--hotspot-background-1: {{ block.settings.background_1 }}; --hotspot-background-2-gradient: {{ block.settings.background_2_gradient | default: "radial-gradient(50% 50% at 50% 50%,#FF9181 0%, #FF9181 100%)" }}" {{ block.shopify_attributes }}>
      {% if product != blank %}  
        <span class="look__hotspot--arrow"></span>
        <span class="visually-hidden">{{ 'products.product.select_options' | t }}</span>
      {% endif %}
    </a>
    {% endif %}
  {% if product != blank %}
 {% if quick_shop %} </quick-view-modal> {% endif %}
   <div class="lookbook__shop--product {% if hotspot_y > 70 %} lookbook__shop--product-bottom {% endif %} {% if hotspot_x > 70 %} lookbook__shop--product-right {% endif %} {% if hotspot_x < 30 %} lookbook__shop--product-left{% endif %}">
      <div class="lookbook__shop--product__title h6">
        {% if quick_shop == false %}<a href="{{ product.url | default: '#' }}"> {% endif %}
          {{ product.title }}
         {% if quick_shop == false %}</a>{% endif %}
      </div>
      {% render 'price', product: product, price_class: "lookbook__shop--product__price" %}
   </div>
  {% else %}
    <div class="product_blank__area lookbook__shop--product {% if hotspot_y > 70 %} lookbook__shop--product-bottom {% endif %} {% if hotspot_x > 70 %} lookbook__shop--product-right {% endif %} {% if hotspot_x < 30 %} lookbook__shop--product-left{% endif %}">
      <div class="lookbook__shop--product__title h6 p_blank_title">Example product title</div>
      <div class="blank_p_rating"> 
        <div class="trustshop-collection-rating-star--wrap"><span class="trustshop trustshop-rating-star--container"><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none"><path d="M7.16604 12.1708C7.48383 11.979 7.88171 11.979 8.19951 12.1708L10.858 13.7754C11.6153 14.2325 12.5495 13.5535 12.3486 12.692L11.6431 9.6678C11.5587 9.30631 11.6816 8.92784 11.9621 8.68479L14.3124 6.64873C14.981 6.06952 14.6236 4.97126 13.7422 4.89648L10.65 4.63414C10.2804 4.60278 9.95851 4.36944 9.81377 4.02794L8.60349 1.17237C8.2589 0.359342 7.10664 0.359342 6.76205 1.17237L5.55177 4.02794C5.40703 4.36944 5.08517 4.60278 4.71559 4.63414L1.62335 4.89648C0.741922 4.97126 0.384515 6.06952 1.05311 6.64873L3.40341 8.68479C3.68397 8.92784 3.80682 9.30631 3.72249 9.6678L3.01697 12.692C2.81601 13.5535 3.75024 14.2325 4.50756 13.7754L7.16604 12.1708Z" fill="url(#rating-8u203vy4q)"></path><defs><linearGradient id="rating-8u203vy4q" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0" id="stop-0gx4e6i2g" stop-color="#F88E0F"></stop>
                <stop offset="0" id="stop-uthvrgyn4" stop-color="#F88E0F"></stop>
                </linearGradient></defs></svg><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none"><path d="M7.16604 12.1708C7.48383 11.979 7.88171 11.979 8.19951 12.1708L10.858 13.7754C11.6153 14.2325 12.5495 13.5535 12.3486 12.692L11.6431 9.6678C11.5587 9.30631 11.6816 8.92784 11.9621 8.68479L14.3124 6.64873C14.981 6.06952 14.6236 4.97126 13.7422 4.89648L10.65 4.63414C10.2804 4.60278 9.95851 4.36944 9.81377 4.02794L8.60349 1.17237C8.2589 0.359342 7.10664 0.359342 6.76205 1.17237L5.55177 4.02794C5.40703 4.36944 5.08517 4.60278 4.71559 4.63414L1.62335 4.89648C0.741922 4.97126 0.384515 6.06952 1.05311 6.64873L3.40341 8.68479C3.68397 8.92784 3.80682 9.30631 3.72249 9.6678L3.01697 12.692C2.81601 13.5535 3.75024 14.2325 4.50756 13.7754L7.16604 12.1708Z" fill="url(#rating-pa1yc3w95)"></path><defs><linearGradient id="rating-pa1yc3w95" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0" id="stop-uiy93vgri" stop-color="#F88E0F"></stop>
                <stop offset="0" id="stop-5cf94q29b" stop-color="#F88E0F"></stop>
                </linearGradient></defs></svg><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none"><path d="M7.16604 12.1708C7.48383 11.979 7.88171 11.979 8.19951 12.1708L10.858 13.7754C11.6153 14.2325 12.5495 13.5535 12.3486 12.692L11.6431 9.6678C11.5587 9.30631 11.6816 8.92784 11.9621 8.68479L14.3124 6.64873C14.981 6.06952 14.6236 4.97126 13.7422 4.89648L10.65 4.63414C10.2804 4.60278 9.95851 4.36944 9.81377 4.02794L8.60349 1.17237C8.2589 0.359342 7.10664 0.359342 6.76205 1.17237L5.55177 4.02794C5.40703 4.36944 5.08517 4.60278 4.71559 4.63414L1.62335 4.89648C0.741922 4.97126 0.384515 6.06952 1.05311 6.64873L3.40341 8.68479C3.68397 8.92784 3.80682 9.30631 3.72249 9.6678L3.01697 12.692C2.81601 13.5535 3.75024 14.2325 4.50756 13.7754L7.16604 12.1708Z" fill="url(#rating-vubamemww)"></path><defs><linearGradient id="rating-vubamemww" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0" id="stop-uwlabx0yz" stop-color="#F88E0F"></stop>
                <stop offset="0" id="stop-gotf3cx0m" stop-color="#F88E0F"></stop>
                </linearGradient></defs></svg><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none"><path d="M7.16604 12.1708C7.48383 11.979 7.88171 11.979 8.19951 12.1708L10.858 13.7754C11.6153 14.2325 12.5495 13.5535 12.3486 12.692L11.6431 9.6678C11.5587 9.30631 11.6816 8.92784 11.9621 8.68479L14.3124 6.64873C14.981 6.06952 14.6236 4.97126 13.7422 4.89648L10.65 4.63414C10.2804 4.60278 9.95851 4.36944 9.81377 4.02794L8.60349 1.17237C8.2589 0.359342 7.10664 0.359342 6.76205 1.17237L5.55177 4.02794C5.40703 4.36944 5.08517 4.60278 4.71559 4.63414L1.62335 4.89648C0.741922 4.97126 0.384515 6.06952 1.05311 6.64873L3.40341 8.68479C3.68397 8.92784 3.80682 9.30631 3.72249 9.6678L3.01697 12.692C2.81601 13.5535 3.75024 14.2325 4.50756 13.7754L7.16604 12.1708Z" fill="url(#rating-z3ih6grlv)"></path><defs><linearGradient id="rating-z3ih6grlv" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0" id="stop-s3vpjv7hr" stop-color="#F88E0F"></stop>
                <stop offset="0" id="stop-2rq7m01u7" stop-color="#F88E0F"></stop>
                </linearGradient></defs></svg><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none"><path d="M7.16604 12.1708C7.48383 11.979 7.88171 11.979 8.19951 12.1708L10.858 13.7754C11.6153 14.2325 12.5495 13.5535 12.3486 12.692L11.6431 9.6678C11.5587 9.30631 11.6816 8.92784 11.9621 8.68479L14.3124 6.64873C14.981 6.06952 14.6236 4.97126 13.7422 4.89648L10.65 4.63414C10.2804 4.60278 9.95851 4.36944 9.81377 4.02794L8.60349 1.17237C8.2589 0.359342 7.10664 0.359342 6.76205 1.17237L5.55177 4.02794C5.40703 4.36944 5.08517 4.60278 4.71559 4.63414L1.62335 4.89648C0.741922 4.97126 0.384515 6.06952 1.05311 6.64873L3.40341 8.68479C3.68397 8.92784 3.80682 9.30631 3.72249 9.6678L3.01697 12.692C2.81601 13.5535 3.75024 14.2325 4.50756 13.7754L7.16604 12.1708Z" fill="url(#rating-to7o1jppg)"></path><defs><linearGradient id="rating-to7o1jppg" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0" id="stop-n09bd7ezw" stop-color="#F88E0F"></stop>
                <stop offset="0" id="stop-di9tete50" stop-color="#F88E0F"></stop>
                </linearGradient></defs></svg></span>
        </div>
      </div>
      <span class="price lookbook__shop--product__price p_blank_price">$99.99</span>
   </div>
   {% endif %}
   </div>  
   {% endif %}