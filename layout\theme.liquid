{% liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
%}
<!doctype html>
<html
  class="no-js"
  lang="{{ request.locale.iso_code }}"
  {% if theme_rtl %}
    dir="rtl"
  {% endif %}
>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="">
    <link rel="canonical" href="{{ canonical_url }}">
    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>

    {%- if settings.favicon != blank -%}
      <link rel="icon" type="image/png" href="{{ settings.favicon | image_url: width: 32, height: 32 }}">
    {%- endif -%}

    {%- if settings.use_custom_body_font == false or settings.use_custom_heading_font == false -%}
      {%- unless settings.type_header_font.system? and settings.type_body_font.system? -%}
        <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
      {%- endunless -%}
    {%- endif -%}

    <title>
      {{ page_title }}
      {%- if current_tags %} &ndash; tagged "{{ current_tags | join: ', ' }}"{% endif -%}
      {%- if current_page != 1 %} &ndash; Page {{ current_page }}{% endif -%}
      {%- unless page_title contains shop.name %} &ndash; {{ shop.name }}{% endunless -%}
    </title>

    {% if page_description %}
      <meta name="description" content="{{ page_description | escape }}">
    {% endif %}

    {% render 'meta-tags' %}

    <script src="{{ 'global.js' | asset_url }}" defer="defer"></script>

    <script src="{{ 'loadMore.js' | asset_url }}" defer="defer"></script>

    <script src="{{ 'swiper-bundle.min.js' | asset_url }}"></script>

    <script src="{{ 'custom.js' | asset_url }}" defer="defer"></script>

    <script src="{{ 'slideshow.js' | asset_url }}" defer="defer"></script>

    <script src="{{ 'tab-active.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'counter-up.js' | asset_url }}" defer="defer"></script>

    {%- unless template.name == 'product' -%}
      <script src="{{ 'product-variant.js' | asset_url }}" defer="defer"></script>
    {%- endunless -%}

    {{ content_for_header }}

    {%- liquid
      assign body_font_bold = settings.type_body_font | font_modify: 'weight', 'bold'
      assign body_font_italic = settings.type_body_font | font_modify: 'style', 'italic'
      assign body_font_bold_italic = body_font_bold | font_modify: 'style', 'italic'
      assign body_fonts = settings.custom_body_font | split: '&'
      assign heading_fonts = settings.custom_heading_font | split: '&'
    %}

    {%- capture body_font_face -%}
      {%- for font in body_fonts -%}
       {%- assign font_URL = font | split: '@' | first  -%}
       {%- assign font_Weight = font | split: '@' | last  -%}
         @font-face {
          font-family: Body-Font;
          font-weight: {{- font_Weight -}};
          src: url({{- font_URL -}});
          font-display: swap;
        }
      {%- endfor -%}
    {%- endcapture -%}

    {% capture heading_font_face %}
      {%- for font in heading_fonts -%}
       {%- assign heading_font_URL = font | split: '@' | first -%}
       {%- assign heading_font_Weight = font | split: '@' | last  -%}
         @font-face {
          font-family: Heading-Font;
          font-weight: {{- heading_font_Weight -}};
          src: url({{- heading_font_URL -}});
          font-display: swap;
        }
      {%- endfor -%}
    {%- endcapture -%}

    {% style %}
         {%- if settings.use_custom_body_font and settings.custom_body_font != blank -%}
        {{ body_font_face }}
        {%- else  -%}
        {{ settings.type_body_font | font_face: font_display: 'swap' }}
        {{ body_font_bold | font_face: font_display: 'swap' }}
        {{ body_font_italic | font_face: font_display: 'swap' }}
        {{ body_font_bold_italic | font_face: font_display: 'swap' }}
        {%- endif -%}

        {%- if settings.use_custom_heading_font and settings.custom_heading_font != blank -%}
         {{ heading_font_face }}
        {%- else  -%}
         {{ settings.type_header_font | font_face: font_display: 'swap' }}
        {%- endif -%}


        {% for scheme in settings.color_schemes -%}
          {% assign scheme_classes = scheme_classes | append: ', .color-' | append: scheme.id %}
          {% if forloop.index == 1 -%}
            :root,
          {%- endif %}
          .color-{{ scheme.id }} {
            --color-background: {{ scheme.settings.background.red }},{{ scheme.settings.background.green }},{{ scheme.settings.background.blue }};
          {% if scheme.settings.background_gradient != empty %}
            --gradient-background: {{ scheme.settings.background_gradient }};
          {% else %}
            --gradient-background: {{ scheme.settings.background }};
          {% endif %}
            --color-foreground: {{ scheme.settings.text.red }},{{ scheme.settings.text.green }},{{ scheme.settings.text.blue }};
            --color-shadow: {{ scheme.settings.shadow.red }},{{ scheme.settings.shadow.green }},{{ scheme.settings.shadow.blue }};
            --color-button: {{ scheme.settings.button.red }},{{ scheme.settings.button.green }},{{ scheme.settings.button.blue }};
            --color-button-text: {{ scheme.settings.button_label.red }},{{ scheme.settings.button_label.green }},{{ scheme.settings.button_label.blue }};
            --color-secondary-button: {{ scheme.settings.background.red }},{{ scheme.settings.background.green }},{{ scheme.settings.background.blue }};
            --color-secondary-button-text: {{ scheme.settings.secondary_button_label.red }},{{ scheme.settings.secondary_button_label.green }},{{ scheme.settings.secondary_button_label.blue }};
            --color-link: {{ scheme.settings.secondary_button_label.red }},{{ scheme.settings.secondary_button_label.green }},{{ scheme.settings.secondary_button_label.blue }};
            --color-badge-foreground: {{ scheme.settings.text.red }},{{ scheme.settings.text.green }},{{ scheme.settings.text.blue }};
            --color-badge-background: {{ scheme.settings.background.red }},{{ scheme.settings.background.green }},{{ scheme.settings.background.blue }};
            --color-badge-border: {{ scheme.settings.text.red }},{{ scheme.settings.text.green }},{{ scheme.settings.text.blue }};
            --payment-terms-background-color: rgb({{ scheme.settings.background.rgb }});
            --primary-button-hover-background: {{ scheme.settings.primary_button_hover_bg.red }},{{ scheme.settings.primary_button_hover_bg.green }},{{ scheme.settings.primary_button_hover_bg.blue }};
            --primary-button-hover-text: {{ scheme.settings.primary_button_hover_text.red }},{{ scheme.settings.primary_button_hover_text.green }},{{ scheme.settings.primary_button_hover_text.blue }};
            --secondary-button-hover-background:  {{ scheme.settings.secondary_button_hover_bg.red }},{{ scheme.settings.secondary_button_hover_bg.green }},{{ scheme.settings.secondary_button_hover_bg.blue }};
            --secondary-button-hover-text:  {{ scheme.settings.secondary_button_hover_text.red }},{{ scheme.settings.secondary_button_hover_text.green }},{{ scheme.settings.secondary_button_hover_text.blue }};
            --text-link-hover-color:  {{ scheme.settings.tex_link_hover_color.red }},{{ scheme.settings.tex_link_hover_color.green }},{{ scheme.settings.tex_link_hover_color.blue }};
            --success-text-color:  {{ scheme.settings.card_success_color.red }},{{ scheme.settings.card_success_color.green }},{{ scheme.settings.card_success_color.blue }};
            --warning-text-color:  {{ scheme.settings.card_warning_color.red }},{{ scheme.settings.card_warning_color.green }},{{ scheme.settings.card_warning_color.blue }};
          }
          {% endfor %}

          {{ scheme_classes | prepend: 'body' }} {
            color: rgba(var(--color-foreground), 0.75);
            background-color: rgb(var(--color-background));
          }

        :root {
          {%- if settings.use_custom_body_font and settings.custom_body_font != blank -%}
           --font-body-family: Body-Font;
           --font-body-weight: {{ settings.body_font_weight | default: 400 }};
          {%- else -%}
          --font-body-family: {{ settings.type_body_font.family }}, {{ settings.type_body_font.fallback_families }};
          --font-body-style: {{ settings.type_body_font.style }};
          --font-body-weight: {{ settings.type_body_font.weight }};
         {%- endif -%}

         {%- if settings.use_custom_heading_font and settings.custom_heading_font != blank -%}
          --font-heading-family: Heading-Font;
          --font-heading-weight: {{ settings.heading_font_weight | default: 600 }};
          {%- else -%}
          --font-heading-family: {{ settings.type_header_font.family }}, {{ settings.type_header_font.fallback_families }};
          --font-heading-style: {{ settings.type_header_font.style }};
          --font-heading-weight: {{ settings.type_header_font.weight }};
          {%- endif -%}

          --placeholder-background: {{ settings.placeholder_background.red }}, {{ settings.placeholder_background.green }}, {{ settings.placeholder_background.blue }};
          --placeholder-foreground: {{ settings.placeholder_foreground.red }}, {{ settings.placeholder_foreground.green }}, {{ settings.placeholder_foreground.blue }};

          --gradient-base-background-1: {% if settings.gradient_background_1 != blank %}{{ settings.gradient_background_1 }}{% else %}{{ settings.colors_background_1 }}{% endif %};
          --gradient-base-background-2: {% if settings.gradient_background_2 != blank %}{{ settings.gradient_background_2 }}{% else %}{{ settings.colors_background_2 }}{% endif %};
          --gradient-base-accent-1: {% if settings.gradient_accent_1 != blank %}{{ settings.gradient_accent_1 }}{% else %}{{ settings.colors_accent_1 }}{% endif %};
          --gradient-base-accent-2: {% if settings.gradient_accent_2 != blank %}{{ settings.gradient_accent_2 }}{% else %}{{ settings.colors_accent_2 }}{% endif %};

      	  --font-body-size: {{ settings.body_font_size | divided_by: 100.0 }};
      	  --font-heading-size: {{ settings.heading_font_size | times: 1.0 | divided_by: settings.body_font_size }};
      	  --heading-letter-spacing: {{ settings.heading_letter_spacing }}px;
      	  --header-text-case: {{ settings.header_text_case }};

      	  --button-border-width: {{ settings.button_border_width }}px;
          --button-border-radius: {{ settings.button_border_radius }}px;
          --button-letter-spacing: {{ settings.button_letter_spacing }}px;
      	  --button-font-size: {{ settings.button_font_size | times: 1.0 | divided_by: settings.body_font_size }};
      	  --button-text-case: {{ settings.button_text_case }};

          --container-lg-width: {{ settings.container_lg_width | divided_by: 10 }}rem;
          --page-width: {{ settings.container_lg_width | divided_by: 10 }}rem;
          --container-fluid-offset: {{ settings.container_fluid_offset | divided_by: 10 }}rem;
          --transition: all 0.3s ease 0s;
          --duration-long: 500ms;
          --grid-desktop-vertical-spacing: 20px;
          --grid-desktop-horizontal-spacing: 20px;
          --grid-mobile-vertical-spacing: 20px;
          --grid-mobile-horizontal-spacing: 20px;
        }

       :root {
        --white-color: #FFFFFF;
      }

        *,
        *::before,
        *::after {
          box-sizing: inherit;
        }

        html {
          box-sizing: border-box;
          height: 100%;
          margin: 0;
          padding: 0;
      	font-size: calc(var(--font-body-size) * 62.5%);
        }

        body {
      	margin: 0;
          min-height: 100%;
          font-size: 1.5rem;
      	letter-spacing: {{ settings.body_letter_spacing }}px;
          line-height: calc(1 + 0.8 / var(--font-body-size));
          font-family: var(--font-body-family);
          font-style: var(--font-body-style);
          font-weight: var(--font-body-weight);
          position: relative;
          visibility: visible;
          overflow-x: hidden;
        }
      @media only screen and (min-width: 992px){
          body {
            font-size: 1.6rem;
          }
      }


      .action__btn--svg svg {
          width: 1.9rem;
          height: 1.9rem;
          fill: rgb(var(--color-button-text));
      }

      .swiper-button-next svg, .swiper-button-prev svg {
          fill: rgb(var(--color-button-text));
      }
      



      
    {% endstyle %}

    {{ 'swiper-bundle.min.css' | asset_url | stylesheet_tag }}
    {{ 'glightbox.min.css' | asset_url | stylesheet_tag }}
    {{ 'base.css' | asset_url | stylesheet_tag }}
    {%- if theme_rtl -%}
      {{ 'rtl.css' | asset_url | stylesheet_tag }}
    {%- endif -%}

    {%- unless settings.type_body_font.system? -%}
      <link rel="preload" as="font" href="{{ settings.type_body_font | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}
    {%- unless settings.type_header_font.system? -%}
      <link rel="preload" as="font" href="{{ settings.type_header_font | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}

    <link
      rel="stylesheet"
      href="{{ 'component-predictive-search.css' | asset_url }}"
      media="print"
      onload="this.media='all'"
    >

    <script>
      document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
    </script>
  
{{ 'custom-styles.css' | asset_url | stylesheet_tag }}
<!-- Swiper CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css" />

<!-- Swiper JS -->
<script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js"></script>

</head>

  <body class="body_class">
    <a class="skip-to-content-link skip--button visually-hidden" href="#MainContent">
      {{ 'accessibility.skip_to_text' | t }}
    </a>

    {%- if settings.show_quick_view_button -%}
      {%- render 'quick_view_modal' -%}
    {%- endif -%}

    {%- sections 'header-group' -%}

    <main id="MainContent" class="content-for-layout focus-none" role="main" tabindex="-1">
      {{ content_for_layout }}
    </main>

    {% sections 'footer-group' %}
    {% section 'color-swatches' %}

    <ul hidden>
      <li id="a11y-refresh-page-message">{{ 'accessibility.refresh_page' | t }}</li>
    </ul>

    <script>
      
      window.routes = {
        cart_add_url: '{{ routes.cart_add_url }}',
        cart_change_url: '{{ routes.cart_change_url }}',
        cart_update_url: '{{ routes.cart_update_url }}',
        predictive_search_url: '{{ routes.predictive_search_url }}'
      };

      window.cartStrings = {
        error: `{{ 'sections.cart.cart_error' | t }}`,
        quantityError: `{{ 'sections.cart.cart_quantity_error_html' | t }}`
      }

      window.variantStrings = {
        addToCart: `{{ 'products.product.add_to_cart' | t }}`,
        soldOut: `{{ 'products.product.sold_out' | t }}`,
        unavailable: `{{ 'products.product.unavailable' | t }}`,
        preorder: `{{ 'products.product.preorder' | t }}`,
        nostock: `{{ 'products.product.out_of_stock' | t }}`,
        discountLabel: `{{ 'products.product.discount_label' | t }}`
      }

      window.accessibilityStrings = {
        imageAvailable: `{{ 'products.product.media.image_available' | t: index: '[index]' }}`,
        shareSuccess: `{{ 'general.share.success_message' | t }}`,
        pauseSlideshow: `{{ 'sections.slideshow.pause_slideshow' | t }}`,
        playSlideshow: `{{ 'sections.slideshow.play_slideshow' | t }}`,
      }
     
      window.countdown = {
        days: `{{ 'products.product.countdown_timer.days' | t }}`,
        Hours: `{{ 'products.product.countdown_timer.hours' | t }}`,
        minutes: `{{ 'products.product.countdown_timer.minutes' | t }}`,
        second: `{{ 'products.product.countdown_timer.seconds' | t }}`
      }
      
      window.shipping = {
      	country_label: `{{ 'general.shipping_calculator.country_label' | t }}`,
        wrong_message: `{{ 'general.shipping_calculator.wrong_message'| t }}`
      }
    </script>

    {%- if settings.cart_type == 'drawer' -%}
      <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}

    <script src="{{ 'faq-collapse.js' | asset_url }}" defer="defer"></script>

    {%- if settings.show_wishlist_button -%}
      <script src="{{ 'wishlist.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}

    {%- if settings.show_compare_view_button -%}
      <script src="{{ 'compare.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}

    {%- if settings.predictive_search_enabled -%}
      <script src="{{ 'predictive-search.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}

    {%- if settings.show_quick_view_button -%}
      <script src="{{ 'quick_view_modal.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}

    {%- if content_for_layout contains 'glightbox' -%}
      <script src="{{ 'glightbox.min.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}

    {%- if content_for_layout contains 'glightbox' -%}
      <script src="{{ 'welcome-video.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}

    <script src="{{ 'color-swatch-variant.js' | asset_url }}" defer="defer"></script>

    <script src="{{ 'lookbook-slider.js' | asset_url }}" defer="defer"></script>

    <script src="{{ 'timeline-slider.js' | asset_url }}" defer="defer"></script>

    {%- if settings.back_top_enable -%}
      {%- render 'back-to-top' -%}
    {%- endif -%}
  </body>
</html>
