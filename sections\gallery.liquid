{%- liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
-%}
{{ 'component-grid.css' | asset_url | stylesheet_tag }}
{{ 'component-mobile-scrolling.css' | asset_url | stylesheet_tag }}
{{ 'gallery.css' | asset_url | stylesheet_tag }}
{{ 'section-title.css' | asset_url | stylesheet_tag }}
{{ 'component-slider-navigation.css' | asset_url | stylesheet_tag }}

<style>
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.mobile_padding_top }}px;
      padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
    }
    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top }}px;
        padding-bottom: {{ section.settings.padding_bottom }}px;
      }
    }
    .gallery__hover--icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    pointer-events: none;
    transition: var(--transition);
    opacity: 0;
    visibility: hidden;
  }

      :root{
      --grid-desktop-vertical-spacing: 20px;
      --grid-desktop-horizontal-spacing: 20px;
      --grid-mobile-vertical-spacing: 20px;
      --grid-mobile-horizontal-spacing: 20px;
    }
</style>
{% if theme_rtl %}
  {{ 'component-slider-navigation-rtl.css' | asset_url | stylesheet_tag }}
{% endif %}

{%- liquid
  assign productShowXl = section.settings.products_show_on_xl
  assign productShowSm = section.settings.products_show_on_sm

  assign content_position = ''
  if section.settings.content_position == 'middle'
    assign content_position = 'align-items-center'
  elsif section.settings.content_position == 'bottom'
    assign content_position = 'align-items-end'
  endif

  assign content_alignment = ''
  assign align_self = 'flex-start'
  if section.settings.content_alignment == 'center'
    assign content_alignment = 'justify-content-center text-center'
    assign align_self = 'center'
  elsif section.settings.content_alignment == 'right'
    assign content_alignment = 'justify-content-end text-right'
    assign align_self = 'flex-end'
  endif

  assign grid = 'grid'

  assign slider_enable = false
  if section.settings.layout == 'slider'
    assign slider_enable = true
  endif

  if slider_enable
    assign productItem = 'swiper-slide'
    assign showPagination = section.settings.show_pagination
    assign showNavigation = section.settings.show_navigation
    assign slideAutoplay = section.settings.auto_rotate
    assign autoplay_time = section.settings.autoplay_time

    if showPagination
      assign pagination_margin = 'mb-50'
    endif
  endif

  assign grid_item = ''
  if section.settings.layout == 'grid'
    assign grid_item = 'grid__item'
  elsif section.settings.layout == 'slider'
    assign grid_item = 'swiper-slide'
  endif

  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif
-%}

{%- capture container_class -%}
{%- if slider_enable -%}
gallery__slider hero__slider--activation swiper
{%- else -%}
{{ grid }} {% unless section.settings.space %}grid--gapless{% endunless %} {% if section.settings.layout == "grid" %} grid--{{ productShowXl }}-col-desktop grid--{{ productShowSm }}-col-tablet-down {% endif %}
{%- endif -%}
{%- endcapture -%}

<div
  class="instagram__section section-{{ section.id }}-padding"
  data-section-id="{{ section.id }}"
  data-section-type="slideShow"
  id="slideshow__{{ section.id }}"
  {%- if slider_enable -%}
    data-slide-autoplay="{{ section.settings.auto_rotate }}"
    data-autoplay-duration="{{ section.settings.change_slides_speed }}000"
    data-slide-loop="{{ section.settings.enable_loop }}"
    data-slideshow="{%- if section.settings.space -%}20{%- else -%}0{%- endif -%}"
    data-slidesperview="{{ productShowXl }}"
    data-samlldesktopview="{{ productShowXl }}"
    data-show-tablet="{{ productShowSm }}"
    data-show-mobile="{{ productShowSm }}"
  {% endif %}
>
  <div class="{{ container }} {% unless section.settings.show_offset %}p-0{% endunless %}">
    {% if section.settings.heading != blank or section.settings.subtitle != blank %}
      <div class="section-heading text-{{ section.settings.text_center }}  {% if section.settings.border_line %} mb-70 {% else %} mb-50{% endif %}">
        {% if section.settings.heading_position == 'bottom' and section.settings.subtitle != blank %}
          <span class="section-heading__sub_title">{{- section.settings.subtitle -}}</span>
        {% endif %}
        <h2 class="section-heading__title {% if section.settings.heading_position == "top" and section.settings.subtitle != blank and section.settings.border_line %}heading__space--bottom{% endif %} ">
          {% if section.settings.border_line %}<span>{% endif %}
          {{- section.settings.heading -}}
          {% if section.settings.border_line %}</span>{% endif %}
        </h2>
        {% if section.settings.heading_position == 'top' and section.settings.subtitle != blank %}
          <span class="section-heading__sub_title">{{- section.settings.subtitle -}}</span>
        {% endif %}
      </div>
    {% endif %}

    {% if section.settings.mobile_stack == false and section.settings.layout != 'slider' %}
      <div class="mobile--scoller">
      <div class="mobile--scoller-inner">
    {% endif %}

    <div class="{{ container_class }}">
      {%- if slider_enable -%}<div class="swiper-wrapper">{% endif %}
      {%- for block in section.blocks -%}
        {% case block.type %}
          {% when 'item' %}
            {% comment %} instagram item {% endcomment %}
            {% liquid
              assign highest_ratio = 0
              for block in section.blocks
                if block.settings.image.aspect_ratio > highest_ratio
                  assign highest_ratio = block.settings.image.aspect_ratio
                endif
              endfor
            %}
            <div
              class="instagram__list--item {{ grid_item }}"
              {{ block.shopify_attributes }}
            >
              {%- if block.settings.link_label == blank and block.settings.link != blank -%}
                <a class="d-block" href="{{ block.settings.link }}" target="_blank">
              {% endif %}
              <div
                class="instagram__list--item-overlay"
                style="
                  --instagram--media-overlay-opacity: {{ section
                  .settings.image_overlay_opacity | divided_by: 100.0 }};
                "
              >
                <div class="instagram__list--media media--{{ section.settings.image_ratio }} {% if block.settings.image != blank or block.settings.video != blank %}media--transparent media{% else %} placeholder {% endif %} {% if section.settings.round_corner %} round--corner-media {% endif %}">
                  {%- if block.settings.video -%}
                    {{ block.settings.video | video_tag: autoplay: true, loop: true, controls: false, muted: true }}
                  {%- elsif block.settings.image != blank -%}
                    {%- capture sizes -%}(min-width: 992px) {% if section.blocks.size <= 2 %} {{ settings.container_lg_width }}px{% else %}{{ settings.container_lg_width | minus: 60 | divided_by: 3 }}px{% endif %}, (min-width: 750px) {% if section.blocks.size == 1 %}calc(100vw - 60px){% else %}550px{% endif %}, calc(100vw - 30px){%- endcapture -%}
                    {{
                      block.settings.image
                      | image_url: width: 1420
                      | image_tag: loading: 'lazy', sizes: sizes, widths: '275, 550, 710, 1420'
                    }}
                  {%- else -%}
                    {{ 'image' | placeholder_svg_tag: 'placeholder-svg-2' }}
                  {%- endif -%}
                </div>
                <span class="gallery__hover--icon">
                  {% render 'icon-gallery', icon: block.settings.icon %}
                </span>
              </div>
              {%- if block.settings.link_label == blank -%}
                </a>
              {% endif %}
            </div>
            {% comment %} Instagram Item .\ {% endcomment %}
        {% endcase %}
      {% endfor %}
      {%- if slider_enable -%}</div>{% endif %}

      {%- if slider_enable and section.settings.show_navigation -%}
        <div class="swiper__nav--btn swiper-button-prev  component--slider--nav color-{{ section.settings.color_scheme_navigation }}">
          {% render 'icon-arrow-left' %}
        </div>
        <div class="swiper__nav--btn swiper-button-next component--slider--nav color-{{ section.settings.color_scheme_navigation }}">
          {% render 'icon-arrow-right' %}
        </div>
      {%- endif -%}
    </div>

    {% if section.settings.mobile_stack == false and section.settings.layout != 'slider' %}
      </div>
      </div>
    {% endif %}
  </div>
</div>
{% schema %}
 {
   "name": "Gallery",
   "settings": [
      {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
    {
       "type": "checkbox",
       "id": "show_offset",
       "label": "Enable Offset (left & right)",
       "default": true
     },
    {
      "type": "header",
      "content": "Section header"
    },
     {
      "type": "inline_richtext",
      "id": "subtitle",
      "default": "Subtitle",
      "label": "Subheading"
    },
    {
      "type": "text",
      "id": "heading",
      "default": "Gallery",
      "label": "t:sections.featured-collection.settings.title.label"
    },
    {
      "type": "select",
      "id": "heading_position",
      "options": [
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        }
      ],
      "default": "bottom",
      "label": "Heading position"
    },
    {
      "type": "select",
      "id": "text_center",
      "options": [
        {
          "value": "right",
          "label": "Right"
        },
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        }
      ],
      "default": "center",
      "label": "Alignment"
    },
    {
       "type": "checkbox",
       "id": "border_line",
       "label": "Enable border line",
       "default": true
     },
     {
        "type": "header",
        "content": "Cards"
      },
      {
        "type": "select",
        "id": "image_ratio",
        "options": [
          {
            "value": "square",
            "label": "t:sections.multicolumn.settings.image_ratio.options__3.label"
          },
          {
            "value": "portrait",
            "label": "t:sections.multicolumn.settings.image_ratio.options__2.label"
          },
          {
            "value": "landscape",
            "label": "Landscape"
          },
          {
            "value": "circle",
            "label": "t:sections.multicolumn.settings.image_ratio.options__4.label"
          },
          {
            "value": "custom_0_5",
            "label": "Custom - 0:5"
          },
          {
            "value": "custom_0_7",
            "label": "Custom - 0:7"
          },
          {
            "value": "custom_1_5",
            "label": "Custom - 1:5"
          },
          {
            "value": "custom_1_7",
            "label": "Custom - 1:7"
          },
          {
            "value": "custom_1_9",
            "label": "Custom - 1:9"
          },
          {
            "value": "custom_2_1",
            "label": "Custom - 2:1"
          }
        ],
        "default": "square",
        "label": "t:sections.multicolumn.settings.image_ratio.label"
      },
     {
        "type": "checkbox",
        "id": "round_corner",
        "label": "Round corner",
        "default": true
      },
     {
        "type": "checkbox",
        "id": "space",
        "label": "Space between",
        "default": true
      },
     {
         "type": "range",
         "id": "image_overlay_opacity",
         "min": 0,
         "max": 100,
         "step": 10,
         "unit": "%",
         "label": "Image overlay opacity",
         "default": 20
       },
     {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "select",
        "id": "layout",
        "options": [
          {
            "value": "grid",
            "label": "Grid"
          },
          {
            "value": "slider",
            "label": "Slider"
          }
        ],
        "default": "slider",
        "label": "Desktop layout"
      },
     {
        "type": "select",
        "id": "products_show_on_xl",
        "label": "Number of columns on desktop",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          },
          {
            "value": "5",
            "label": "5"
          },
          {
            "value": "6",
            "label": "6"
          }
        ],
        "default": "5"
      },
     {
        "type": "select",
        "id": "products_show_on_sm",
        "label": "Number of columns on mobile",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          }
        ],
        "default": "2"
      },
       {
           "type": "checkbox",
           "id": "mobile_stack",
           "label": "Stack on mobile",
           "default": false,
           "info": "This is only applicable to the Grid layout."
       },
     {
      "type": "header",
        "content": "Slider settings"
      },
  	{
        "type": "checkbox",
        "id": "auto_rotate",
        "label": "Auto-rotate slides",
        "default": false
      },
      {
        "type": "range",
        "id": "change_slides_speed",
        "min": 2,
        "max": 9,
        "step": 1,
        "unit": "s",
        "label": "Change slides every",
        "default": 3
      },
      {
        "type": "checkbox",
        "id": "enable_loop",
        "label": "Slider loop",
        "default": true
      },
      {
          "type": "checkbox",
          "id": "show_navigation",
          "label": "Show navigation",
          "default": true
      },
     {
        "type": "color_scheme",
        "id": "color_scheme_navigation",
        "label": "t:sections.all.colors.label",
        "default": "accent-1"
      },
  {
     "type": "header",
     "content": "Section padding"
   },
   {
         "type": "paragraph",
         "content": "Desktop"
       },
       {
         "type": "range",
         "id": "padding_top",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding top",
         "default": 50
       },
       {
         "type": "range",
         "id": "padding_bottom",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding bottom",
         "default": 50
       },
	{
         "type": "paragraph",
         "content": "Mobile"
       },
	{
         "type": "range",
         "id": "mobile_padding_top",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding top",
         "default": 50
       },
       {
         "type": "range",
         "id": "mobile_padding_bottom",
         "min": 0,
         "max": 150,
         "step": 5,
         "unit": "px",
         "label": "Padding bottom",
         "default": 50
       }
],
"blocks": [
    {
      "type": "item",
      "name": "Item",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.multicolumn.blocks.column.settings.image.label"
        },
         {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
            "type": "select",
            "id": "icon",
            "options": [
              {
                "value": "none",
                "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
              },
               {
                "value": "tiktok",
                "label": "Tiktok"
              },
               {
                "value": "instagram",
                "label": "Instagram"
              },
               {
                "value": "facebook",
                "label": "Facebok"
              },
               {
                "value": "twitter",
                "label": "Twitter"
              },
               {
                "value": "snapchat",
                "label": "Snapchat"
              },
               {
                "value": "youtube",
                "label": "YouTube"
              },
               {
                "value": "vimeo",
                "label": "Vimeo"
              },
               {
                "value": "linkedin",
                "label": "Linkedin"
              },
               {
                "value": "whatsapp",
                "label": "Whatsapp"
              },
              {
                "value": "truck",
                "label": "Truck"
              },
		{
                "value": "award",
                "label": "Award"
              },
		{
                "value": "alarm",
                "label": "Alarm"
              },
		{
                "value": "camera",
                "label": "Camera"
              },
		{
                "value": "check",
                "label": "Check"
              },
		{
                "value": "clock",
                "label": "Clock"
              },
		{
                "value": "compass",
                "label": "Compass"
              },
		{
                "value": "card",
                "label": "Credit card"
              },
		{
                "value": "dollar",
                "label": "Dollar icon"
              },
		{
                "value": "gift",
                "label": "Gift"
              },
		{
                "value": "lock",
                "label": "Lock"
              },
		{
                "value": "map",
                "label": "Map"
              },
		{
                "value": "mic",
                "label": "Mic"
              },
		{
                "value": "monitor",
                "label": "Monitor"
              },
		{
                "value": "moon",
                "label": "Moon"
              },
		{
                "value": "music",
                "label": "Music"
              },
		{
                "value": "phone",
                "label": "Phone"
              },
		{
                "value": "printer",
                "label": "Printer"
              },
		{
                "value": "compare",
                "label": "Compare"
              },
		{
                "value": "search",
                "label": "Search"
              },
		{
                "value": "cart",
                "label": "Cart"
              },
		{
                "value": "bag",
                "label": "Bag"
              },
		{
                "value": "smart_phone",
                "label": "Smart phone"
              },
		{
                "value": "smile",
                "label": "Smile"
              },
		{
                "value": "sun",
                "label": "Sun"
              },
		{
                "value": "thumbs_up",
                "label": "Thumbs up"
              },
		{
                "value": "thumbs_down",
                "label": "Thumbs down"
              },
		{
                "value": "trash",
                "label": "Trash"
              },
		{
                "value": "umbrella",
                "label": "Umbrella"
              },
		{
                "value": "user",
                "label": "User"
              },
		{
                "value": "users",
                "label": "Users"
              },
		{
                "value": "watch",
                "label": "Watch"
              }
            ],
            "default": "instagram",
            "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.label"
          },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        }
      ]
    }
  ],
"presets": [
     {
       "name": "Gallery",
       "blocks": [
          {
            "type": "item"
          },
          {
            "type": "item"
          },
          {
            "type": "item"
          },
          {
            "type": "item"
          },
          {
            "type": "item"
          }
        ]
     }
   ],
   "disabled_on": {
    "groups": ["header"]
  }
 }
{% endschema %}
