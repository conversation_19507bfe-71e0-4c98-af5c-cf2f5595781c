.complementary--product-card {
  display: grid;
  grid-template-columns: 8rem auto;
  gap: 1.5rem;
}
.complementary--product-card-content {
  text-align: left;
  display: grid;
  grid-template-columns: auto auto;
  gap: 1.5rem;
}
.product__vendor {
  line-height: 1;
  margin-bottom: 0.3rem;
}
.complementary--product-card-content .product-grid-item__titleh {
  font-size: 1.4rem;
  margin: 0;
}

.complementary--product-card-content .price {
  font-size: 1.4rem;
}
.complementary--product-card + .complementary--product-card {
  margin-top: 1.5rem;
  border-top: 0.1rem solid rgba(var(--color-foreground), 0.1);
  padding-top: 1.5rem;
}
.complementary__product--cart-btn {
  background: transparent;
  padding: 0;
  font-size: 1.3rem;
  border: 0.1rem solid rgba(var(--color-foreground), 0.2);
  padding: 0.6rem 1.2rem;
  color: rgba(var(--color-base-outline-button-labels));
  line-height: revert;
  border-radius: var(--button-border-radius);
  position: relative;
}
.complementary__product--cart-btn-inner > svg {
  width: 1rem;
  display: inline-block;
}
.complementary__product--cart-btn-inner {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}
.complementary__product--cart-btn.loading::after {
  width: 1rem;
  height: 1rem;
  top: 0.8rem;
  left: 50%;
  margin-left: -0.5rem;
}
.complementary-products--slider:not(.complementary--prouduct-accordion) {
  margin-top: 2.5rem;
}
.complementary__product--cart-btn:hover {
  background: rgba(var(--secondary-button-hover-background));
  color: rgba(var(--secondary-button-hover-text));
}
.complementary--prouduct-accordion {
  padding-top: 2rem;
  padding-bottom: 3rem;
}
.complementary--product-content-grid {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.complementary--product__cart--btn {
  text-align: right;
  align-self: center;
  padding-right: 0.2rem;
}
@media only screen and (min-width: 750px) and (max-width: 1199px),
  (max-width: 599px) {
  .complementary--product-card-content {
    grid-template-columns: auto;
  }
  .complementary--product__cart--btn {
    text-align: left;
  }
}
.complementory--slider.swiper {
  padding: 0.5rem 0.5rem 0.5rem 0;
}
.complementary--product__title--link:hover {
  text-decoration: underline;
}

.complementary--product-card .media > img {
  transition: var(--transition);
}

.complementary--product-card .media:hover > img {
  transform: scale(1.07);
}
