/* component-grid */
.grid {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 2rem;
  padding: 0;
  list-style: none;
  column-gap: var(--grid-mobile-horizontal-spacing);
  row-gap: var(--grid-mobile-vertical-spacing);
}

@media screen and (min-width: 750px) {
  .grid {
    column-gap: var(--grid-desktop-horizontal-spacing);
    row-gap: var(--grid-desktop-vertical-spacing);
  }
}
.grid:last-child {
  margin-bottom: 0;
}
.grid__item {
  width: calc(25% - var(--grid-mobile-horizontal-spacing) * 3 / 4);
  max-width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
  flex-grow: 1;
  flex-shrink: 0;
}

@media screen and (min-width: 750px) {
  .grid__item {
    width: calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
  }
}

.grid--gapless.grid {
  column-gap: 0;
  row-gap: 0;
}

.grid--1-col .grid__item {
  max-width: 100%;
  width: 100%;
}

.grid--3-col .grid__item {
  width: calc(33.33% - var(--grid-mobile-horizontal-spacing) * 2 / 3);
}

@media screen and (min-width: 750px) {
  .grid--3-col .grid__item {
    width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
  }
}

.grid--2-col .grid__item {
  width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
}

@media screen and (min-width: 750px) {
  .grid--2-col .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
  }

  .grid--4-col-tablet .grid__item {
    width: calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
  }

  .grid--3-col-tablet .grid__item {
    width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
  }

  .grid--2-col-tablet .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
  }
}
@media screen and (max-width: 991px) {
  .grid--1-col-tablet-down .grid__item {
    width: 100%;
    max-width: 100%;
  }
}
@media screen and (min-width: 990px) {
  .grid--6-col-desktop .grid__item {
    width: calc(16.66% - var(--grid-desktop-horizontal-spacing) * 5 / 6);
    max-width: calc(16.66% - var(--grid-desktop-horizontal-spacing) * 5 / 6);
  }

  .grid--5-col-desktop .grid__item {
    width: calc(20% - var(--grid-desktop-horizontal-spacing) * 4 / 5);
    max-width: calc(20% - var(--grid-desktop-horizontal-spacing) * 4 / 5);
  }

  .grid--4-col-desktop .grid__item {
    width: calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
    max-width: calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
  }

  .grid--3-col-desktop .grid__item {
    width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
    max-width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
  }

  .grid--2-col-desktop .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
  }
}
@media screen and (min-width: 990px) {
  .grid--1-col-desktop {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .grid--1-col-desktop .grid__item {
    width: 100%;
    max-width: 100%;
  }
}
@media screen and (max-width: 749px) {
  .grid--peek .grid__item {
    min-width: 35%;
  }

  /* Fix to show some space at the end of our sliders in all browsers */
  .grid--2-col-tablet-down .grid__item {
    width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
  }
  .grid--peek .grid__item {
    width: calc(50% - var(--grid-mobile-horizontal-spacing) - 3rem);
  }
}

@media screen and (min-width: 750px) and (max-width: 991px) {
  .grid--2-col-tablet-down .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
  }
  .grid--1-col-tablet-down.grid--peek .grid__item {
    width: calc(100% - var(--grid-desktop-horizontal-spacing) - 3rem);
  }
  .grid--3-col-tablet-only .grid__item {
    width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
    max-width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
  }

  .grid--2-col-tablet-only .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
  }
}
