<!-- Mobile Menu Bar -->
<div class="col-auto d-none d-md-only-block">
  <div class="mobile__menu_bar header__actions_btn--menu">
    {% render 'icon-hamburger' %}
  </div>
</div>
<!-- Mobile Menu Bar end -->

<!-- Header Logo Start -->
<div class="header__logo col-auto">
  {%- if request.page_type == 'index' -%}
    <h1 class="header__heading mb-0">
  {%- endif -%}
  {%- render 'header-logo', className: 'header__logo_link' -%}
  {%- if request.page_type == 'index' -%}
    </h1>
  {%- endif -%}
</div>
<!-- Header Logo End -->

<!-- Header Menu Start -->
<nav class="header__menu justify-content-center  col d-md-none">
  {%- render 'menu-nav', container: container -%}
</nav>
<!-- Header <PERSON>u End -->

<!-- Header Actions Start -->
<div class="header__actions col-auto">
  {%- unless search_button == 'hide' -%}
    <button
      class="header__actions_btn header__actions_btn--search d-none"
      aria-label="{{ 'general.search.search' | t }}"
    >
<svg clip-rule="evenodd" fill-rule="evenodd" stroke-linejoin="round" stroke-miterlimit="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" id="fi_15953176"><g id="Icon"><path d="m10 .75c5.105 0 9.25 4.145 9.25 9.25s-4.145 9.25-9.25 9.25-9.25-4.145-9.25-9.25 4.145-9.25 9.25-9.25zm0 1.5c-4.277 0-7.75 3.473-7.75 7.75s3.473 7.75 7.75 7.75 7.75-3.473 7.75-7.75-3.473-7.75-7.75-7.75z"></path><path d="m10 4.75c-.414 0-.75-.336-.75-.75s.336-.75.75-.75c2.346 0 4.415 1.2 5.625 3.019.229.344.135.81-.21 1.04-.344.229-.81.135-1.04-.21-.941-1.415-2.55-2.349-4.375-2.349z"></path><path d="m15.47 16.53c-.293-.292-.293-.768 0-1.06.292-.293.768-.293 1.06 0l6.5 6.5c.293.292.293.768 0 1.06-.292.293-.768.293-1.06 0z"></path></g></svg>
    </button>
  {%- endunless -%}

  {%- if shop.customer_accounts_enabled and account_icon -%}
    <a
      href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}"
      class="header__actions_btn header__actions_btn--user d-md-none"
    >
      {%- if customer -%}
        {% render 'icon-account' %}
      {%- else -%}
        {% render 'user-icon' %}
      {%- endif -%}
      <span class="visually-hidden">
        {%- liquid
          if customer
            echo 'customer.account_fallback' | t
          else
            echo 'customer.log_in' | t
          endif
        -%}
      </span>
    </a>
  {%- endif -%}

  {%- if compare_icon -%}
    <a href="/pages/compare" class="header__actions_btn header__actions_btn--wishlist d-md-none">
      {% render 'compare-icon' %}
      <span class="header__actions_btn_cart_num compare__count"></span>
    </a>
  {%- endif -%}

  {%- if wishlist_icon -%}
    <a href="/pages/wishlist" class="header__actions_btn header__actions_btn--wishlist d-md-none">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
        <path fill="currentColor" d="M462.3 62.7c-54.5-46.4-136-38.7-186.6 13.5L256 96.6l-19.7-20.3C195.5 34.1 113.2 8.7 49.7 62.7c-62.8 53.6-66.1 149.8-9.9 207.8l193.5 199.8c6.2 6.4 14.4 9.7 22.6 9.7 8.2 0 16.4-3.2 22.6-9.7L472 270.5c56.4-58 53.1-154.2-9.7-207.8zm-13.1 185.6L256.4 448.1 62.8 248.3c-38.4-39.6-46.4-115.1 7.7-161.2 54.8-46.8 119.2-12.9 142.8 11.5l42.7 44.1 42.7-44.1c23.2-24 88.2-58 142.8-11.5 54 46 46.1 121.5 7.7 161.2z" />
      </svg>
      <span class="header__actions_btn_cart_num wishlist__count"></span>
    </a>
  {%- endif -%}

  {%- if cart_icon -%}
    {%- unless template contains 'cart' -%}
      {%- if settings.cart_type == 'drawer' -%}
        <open-minicart>
          <a href="{{ routes.cart_url }}" class="header__actions_btn header__actions_btn--cart">
            {% render 'icon-cart', icon: cart_icon_type %}

            <div class="cart-count-bubble header__actions_btn_cart_num">
              {%- if cart.item_count < 100 -%}
                <span id="cart-notification-count" aria-hidden="true">{{ cart.item_count }}</span>
              {%- endif -%}
              <span class="visually-hidden">{{ 'sections.header.cart_count' | t: count: cart.item_count }}</span>
            </div>
          </a>
        </open-minicart>
      {%- else -%}
        <a href="{{ routes.cart_url }}" class="header__actions_btn header__actions_btn--cart">
          {% render 'icon-cart', icon: cart_icon_type %}
          <div class="cart-count-bubble header__actions_btn_cart_num">
            {%- if cart.item_count < 100 -%}
              <span id="cart-notification-count" aria-hidden="true">{{ cart.item_count }}</span>
            {%- endif -%}
            <span class="visually-hidden">{{ 'sections.header.cart_count' | t: count: cart.item_count }}</span>
          </div>
        </a>
      {%- endif -%}
    {%- endunless -%}
  {%- endif -%}
</div>
