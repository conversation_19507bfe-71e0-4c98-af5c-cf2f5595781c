.product__card--list {
  display: flex;
  padding: 2rem;
  align-items: center;
}
.product__card--list__content {
  width: 100%;
  max-width: calc(100% - 8rem);
  padding-left: 2rem;
}
.product__card--list.product--corner-radius-true {
  border-radius: 1rem;
}
.product__card-list__thumbnail {
  width: 100%;
  max-width: 8rem;
  position: relative;
}
.product-card-list-action-buttons {
  display: flex;
  gap: 1rem;
}
.product--card-list-button {
  background: no-repeat;
  border: 0.1rem solid rgba(var(--color-foreground), 0.3);
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 0.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.product--card-list-button svg {
  width: 1.8rem;
}
.product--card-list-button span {
  line-height: 1;
}
.product--card-list-button:hover {
  background: rgba(var(--primary-button-hover-background));
  color: rgba(var(--primary-button-hover-text));
}
.product-card-list--price-cart {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}
.product-card-list--price-cart .price dd {
  margin: 0 8px 0 0;
}
@media only screen and (min-width: 1200px) {
  .product--card-list-button {
    width: 3.8rem;
    height: 3.8rem;
  }
  .product--card-list-button svg {
    width: 2rem;
  }
}
@media only screen and (min-width: 750px) {
  .product__card--list__content {
    max-width: calc(100% - 10rem);
  }
  .product__card-list__thumbnail {
    max-width: 10rem;
  }
}