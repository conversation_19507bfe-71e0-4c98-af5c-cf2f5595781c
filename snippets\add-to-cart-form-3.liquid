{% liquid
  if card_button_type == 'primary'
    assign button_class = 'button button--primary'
  elsif card_button_type == 'link'
    assign button_class = 'link with--icon button--not-underline'
  else
    assign button_class = 'button button--secondary'
  endif
%}
{% if product_card_product.has_only_default_variant %}
  <product-form>
    <form action="/cart/add" method="post" enctype="multipart/form-data" data-type="add-to-cart-form">
      <input type="hidden" name="id" value="{{ variant.id }}">
      <button
        type="submit"
        name="add"
        class="deals--product-cart-button {{ button_class }} {% if tooltip %} product--tooltip{% endif %}"
        {% if product_card_product.selected_or_first_available_variant.available == false %}
          disabled
        {% endif %}
      >
        {%- if product_card_product.selected_or_first_available_variant.available -%}
          {%- if product_card_product.selected_or_first_available_variant.inventory_quantity <= 0
            and product_card_product.selected_or_first_available_variant.inventory_policy == 'continue'
            and show_preorder_button
          -%}
            {% if use_button_icon %}
              <svg id="fi_9454066" enable-background="new 0 0 512 512" height="512" viewBox="0 0 512 512" width="512"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="m182.436 381.615h-24v-114.404h24zm85.125-124.313h-24v134.221h24zm85.096 9.909h-24v114.404h24zm139.343-53.313h-23.645l-38.681 195.749c-5.696 29.01-30.366 49.281-59.99 49.281h-227.368c-29.605 0-54.286-20.265-60.019-49.281l-38.653-195.749h-23.644v-24h111.88l30.479-87.796c10.311-29.781 37.383-49.03 68.965-49.03h49.351c31.548 0 58.618 19.245 68.964 49.028l30.454 87.798h111.907zm-334.715-24h197.406l-27.724-79.928c-6.942-19.983-25.113-32.898-46.292-32.898h-49.351c-21.202 0-39.371 12.911-46.29 32.892zm286.606 24h-375.784l37.735 191.098c3.482 17.623 18.48 29.932 36.474 29.932h227.368c17.998 0 32.984-12.303 36.442-29.919z">
                </path>
              </svg>
            {% endif %}
            <span class="cart__buton--label">{{ 'products.product.pre_order' | t }} </span>
          {%- else -%}
            {% if use_button_icon %}
              <svg id="fi_9454066" enable-background="new 0 0 512 512" height="512" viewBox="0 0 512 512" width="512"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="m182.436 381.615h-24v-114.404h24zm85.125-124.313h-24v134.221h24zm85.096 9.909h-24v114.404h24zm139.343-53.313h-23.645l-38.681 195.749c-5.696 29.01-30.366 49.281-59.99 49.281h-227.368c-29.605 0-54.286-20.265-60.019-49.281l-38.653-195.749h-23.644v-24h111.88l30.479-87.796c10.311-29.781 37.383-49.03 68.965-49.03h49.351c31.548 0 58.618 19.245 68.964 49.028l30.454 87.798h111.907zm-334.715-24h197.406l-27.724-79.928c-6.942-19.983-25.113-32.898-46.292-32.898h-49.351c-21.202 0-39.371 12.911-46.29 32.892zm286.606 24h-375.784l37.735 191.098c3.482 17.623 18.48 29.932 36.474 29.932h227.368c17.998 0 32.984-12.303 36.442-29.919z">
              </path>
            </svg>
            {% endif %}
            <span class="cart__buton--label">{{ 'products.product.add_to_cart' | t }} </span>
          {%- endif -%}
        {%- else -%}
          {% if use_button_icon %}
          <svg id="fi_9454066" enable-background="new 0 0 512 512" height="512" viewBox="0 0 512 512" width="512"
            xmlns="http://www.w3.org/2000/svg">
            <path
              d="m182.436 381.615h-24v-114.404h24zm85.125-124.313h-24v134.221h24zm85.096 9.909h-24v114.404h24zm139.343-53.313h-23.645l-38.681 195.749c-5.696 29.01-30.366 49.281-59.99 49.281h-227.368c-29.605 0-54.286-20.265-60.019-49.281l-38.653-195.749h-23.644v-24h111.88l30.479-87.796c10.311-29.781 37.383-49.03 68.965-49.03h49.351c31.548 0 58.618 19.245 68.964 49.028l30.454 87.798h111.907zm-334.715-24h197.406l-27.724-79.928c-6.942-19.983-25.113-32.898-46.292-32.898h-49.351c-21.202 0-39.371 12.911-46.29 32.892zm286.606 24h-375.784l37.735 191.098c3.482 17.623 18.48 29.932 36.474 29.932h227.368c17.998 0 32.984-12.303 36.442-29.919z">
            </path>
          </svg>
          {% endif %}
          <span class="cart__buton--label">{{ 'products.product.sold_out' | t }} </span>
        {%- endif -%}

        {% if tooltip %}
          <div class="product--tooltip-label {{ tooltip_position }} {% if tooltip_desktop != true %} desktop--tooltip-disable{% endif %}">
            {% if product_card_product.selected_or_first_available_variant.available %}
              {{ 'products.product.add_to_cart' | t }}
            {% else %}
              {{ 'products.product.sold_out' | t }}
            {%- endif -%}
          </div>
        {% endif %}
      </button>
    </form>
  </product-form>
{%- else -%}
  <quick-view-modal>
    <button
      aria-haspopup="dialog"
      type="button"
      class="deals--product-cart-button {{ button_class }}"
      data-product-handle="{{ product_card_product.handle }}"
      aria-label="{{ 'products.product.select_options' | t }}"
    >
      {% if use_button_icon %}
        <svg id="fi_9454066" enable-background="new 0 0 512 512" height="512" viewBox="0 0 512 512" width="512"
          xmlns="http://www.w3.org/2000/svg">
          <path
            d="m182.436 381.615h-24v-114.404h24zm85.125-124.313h-24v134.221h24zm85.096 9.909h-24v114.404h24zm139.343-53.313h-23.645l-38.681 195.749c-5.696 29.01-30.366 49.281-59.99 49.281h-227.368c-29.605 0-54.286-20.265-60.019-49.281l-38.653-195.749h-23.644v-24h111.88l30.479-87.796c10.311-29.781 37.383-49.03 68.965-49.03h49.351c31.548 0 58.618 19.245 68.964 49.028l30.454 87.798h111.907zm-334.715-24h197.406l-27.724-79.928c-6.942-19.983-25.113-32.898-46.292-32.898h-49.351c-21.202 0-39.371 12.911-46.29 32.892zm286.606 24h-375.784l37.735 191.098c3.482 17.623 18.48 29.932 36.474 29.932h227.368c17.998 0 32.984-12.303 36.442-29.919z">
          </path>
        </svg>
      {% endif %}
      <span class="cart__buton--label">{{ 'products.product.select_options' | t }}</span>
    </button>
  </quick-view-modal>
{%- endif -%}
