{% liquid
  assign gift_wrap_product = linklists['gift-wrapping'].links.first.object
  assign gift_wrap_product_first_variant_id = gift_wrap_product.variants.first.id

  assign gift_wraps_in_cart = 0
  for item in cart.items
    if item.id == gift_wrap_product_first_variant_id
      assign gift_wraps_in_cart = item.quantity
    endif
  endfor

  assign gift_card_wrap = false
  if cart.attributes['gift-wrapping'] or gift_wraps_in_cart > 0
    assign gift_card_wrap = true
  endif

  assign gift_message = false
  if linklists['gift-wrapping'].links.size > 0 and linklists['gift-wrapping'].links.first.type == 'product_link'
    if cart.attributes['gift-wrapping'] or gift_wraps_in_cart > 0
        assign gift_message = true
    endif
  endif
%}
<div class="cart-notification-wrapper h-100">
  <div class="cart_action_drawer_overlay"></div>

  <div
    id="cart-notification"
    class="cart-notification focus-inset h-100 d-flex flex-direction-column"
    aria-modal="true"
    aria-label="{{ 'general.cart.item_added' | t }}"
    role="dialog"
    tabindex="-1"
  >
    <div class="cart_notification_topbar">
      <div class="cart-notification__header {% if cart == empty %} empty__cart {% endif %}">
        <h2 class="cart-notification__heading item__empty_message {% if cart == empty %} no-js-inline {% endif %}">
          <div class="item__success_message {% if cart == empty %} no-js-inline {% endif %} h5">
            {{ 'general.cart.item_added' | t }}
          </div>
        </h2>

        <button
          type="button"
          class="cart-notification__close modal__close-button link link--text focus-inset"
          aria-label="{{ 'accessibility.close' | t }}"
        >
          <svg class="icon icon-close" aria-hidden="true" focusable="false">
            <use href="#icon-close">
          </svg>
        </button>
      </div>

      <div class="empty__cart__item {% if cart != empty %} no-js-inline {% endif %}">
        <span class="empty_c_icon">
          <svg width="70" height="70" viewBox="0 0 71 71" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M70.6467 37.8178C70.5702 37.0338 69.9916 36.3901 69.1367 36.1376L62.2007 34.0896L68.9531 27.3346C69.4014 26.8862 69.5889 26.2933 69.4678 25.708C69.3466 25.1227 68.933 24.6244 68.3331 24.341L59.6217 20.2255C59.0524 19.9567 58.3415 20.1409 58.0329 20.6371C57.7246 21.1333 57.936 21.7535 58.5049 22.0223L67.0967 26.0813L59.9009 33.2797L37.1725 22.5419L41.3898 18.3232C41.8752 18.4785 42.3992 18.5632 42.9455 18.5632C44.6416 18.5632 46.1221 17.7453 46.8975 16.5386L53.4099 19.6153C53.5874 19.6991 53.7785 19.7389 53.9671 19.7389C54.3834 19.7389 54.7863 19.5451 54.9987 19.2036C55.307 18.7074 55.0955 18.0872 54.5265 17.8184L47.4696 14.4846C47.39 12.3681 45.392 10.6673 42.9456 10.6673C40.4491 10.6673 38.4179 12.4384 38.4179 14.6153C38.4179 15.5737 38.812 16.4532 39.4658 17.1378L35.3276 21.2775L27.9962 13.9436C27.304 13.2511 26.1738 13.073 25.2471 13.5106L22.3813 14.8645C22.0455 12.549 19.7741 10.7519 17.0289 10.7519C14.0533 10.7519 11.6323 12.8629 11.6323 15.4577C11.6323 16.8923 12.3734 18.1783 13.5383 19.0421L2.32195 24.341C1.72225 24.6244 1.30881 25.1225 1.18741 25.708C1.06631 26.2933 1.25381 26.8862 1.7021 27.3345L8.45433 34.0894L1.5185 36.1373C0.663654 36.3898 0.085057 37.0335 0.00849408 37.8175C-0.0680689 38.6014 0.377246 39.3213 1.17069 39.6962L9.31558 43.544V47.4009C9.31558 47.9652 9.84012 48.4227 10.4875 48.4227C11.1348 48.4227 11.6593 47.9652 11.6593 47.4009V44.6512L23.0544 50.0345C23.5405 50.2642 24.0829 50.381 24.6272 50.381C24.9832 50.381 25.3402 50.331 25.6827 50.2299L34.1559 47.7283V68.0589L12.1587 57.6665C11.8508 57.521 11.6595 57.2408 11.6595 56.9354V52.4453C11.6595 51.881 11.135 51.4235 10.4876 51.4235C9.84027 51.4235 9.31574 51.881 9.31574 52.4453V56.9354C9.31574 57.9915 9.97715 58.9601 11.0422 59.4633L33.757 70.1948C34.2489 70.4272 34.7884 70.5433 35.3279 70.5433C35.8675 70.5433 36.407 70.4271 36.8989 70.1948L59.6137 59.4633C60.6785 58.9601 61.3401 57.9915 61.3401 56.9354V43.5438L69.4848 39.696C70.2778 39.3216 70.7233 38.6018 70.6467 37.8178ZM37.9183 29.9126C42.0205 30.9181 44.8855 34.2166 44.8855 37.934C44.8855 38.9692 44.6722 39.9738 44.2506 40.9258L35.3276 45.1413L26.4046 40.9258C25.9832 39.9738 25.7697 38.9692 25.7697 37.934C25.7697 34.2051 28.6457 30.9038 32.7638 29.9058C33.3873 29.7547 33.7523 29.1913 33.579 28.6477C33.4059 28.104 32.7595 27.7858 32.1362 27.9368C27.0079 29.1798 23.4261 33.2906 23.4261 37.934C23.4261 38.4939 23.4782 39.0467 23.5782 39.5906L12.9475 34.5682L35.3279 23.9949L57.7082 34.5683L47.0775 39.5907C47.1775 39.0468 47.2296 38.4941 47.2296 37.9341C47.2296 33.3049 43.6616 29.1975 38.5528 27.9452C37.9297 27.7924 37.2826 28.1093 37.1076 28.6524C36.932 29.1957 37.295 29.7599 37.9183 29.9126ZM42.9455 12.7113C44.1497 12.7113 45.1295 13.5655 45.1295 14.6155C45.1295 15.6656 44.1497 16.52 42.9455 16.52C41.7412 16.52 40.7616 15.6656 40.7616 14.6155C40.7616 13.5655 41.7412 12.7113 42.9455 12.7113ZM17.0289 12.7959C18.7122 12.7959 20.0819 13.99 20.0819 15.4579C20.0819 16.9257 18.7122 18.12 17.0289 18.12C15.3456 18.12 13.9761 16.9258 13.9761 15.4579C13.9761 13.99 15.3456 12.7959 17.0289 12.7959ZM16.202 20.1084C16.4717 20.1447 16.7478 20.1637 17.0289 20.1637C19.213 20.1637 21.0966 19.0259 21.9452 17.3951L26.2868 15.344L33.4824 22.5422L10.754 33.28L3.5582 26.0816L16.202 20.1084ZM24.9308 48.2945C24.6802 48.3686 24.403 48.3478 24.1708 48.238L11.0509 42.0398C11.0475 42.0382 11.0439 42.0366 11.0404 42.0349L2.50695 38.0035L10.3786 35.6793L32.4084 46.087L24.9308 48.2945ZM58.9959 56.9358C58.9959 57.2412 58.8045 57.5214 58.4967 57.6669L36.4995 68.0593V47.7287L44.9727 50.2303C45.3152 50.3314 45.6722 50.3814 46.0283 50.3814C46.5724 50.3814 47.1149 50.2645 47.601 50.0349L58.996 44.6516V56.9358H58.9959ZM46.4842 48.238C46.2521 48.3478 45.9749 48.3686 45.7242 48.2945L38.2468 46.087L60.2767 35.6793L68.1483 38.0035L46.4842 48.238ZM68.385 38.0734C68.3848 38.0734 68.3847 38.0732 68.3844 38.0732L68.385 38.0734Z" fill="#D7DBE0"></path>
            <path d="M44.0911 58.6479L41.4028 59.9179C40.8338 60.1867 40.6225 60.8069 40.9308 61.3031C41.143 61.6445 41.5461 61.8384 41.9624 61.8384C42.1508 61.8384 42.3422 61.7986 42.5195 61.7148L45.2078 60.4448C45.7769 60.176 45.9881 59.5558 45.6798 59.0596C45.3716 58.5634 44.6606 58.3789 44.0911 58.6479Z" fill="#D7DBE0"></path>
            <path d="M21.5297 9.39234C21.7586 9.5918 22.0586 9.69167 22.3585 9.69167C22.6583 9.69167 22.9583 9.59194 23.1872 9.39234L24.1539 8.5494L25.1207 9.39234C25.3496 9.5918 25.6496 9.69167 25.9494 9.69167C26.2493 9.69167 26.5493 9.59194 26.7782 9.39234C27.2358 8.99328 27.2358 8.34626 26.7782 7.94734L25.8113 7.10427L26.7782 6.2612C27.2358 5.86214 27.2358 5.21512 26.7782 4.8162C26.3203 4.41714 25.5785 4.41714 25.1208 4.8162L24.1541 5.65914L23.1874 4.8162C22.7296 4.41714 21.9877 4.41714 21.53 4.8162C21.0724 5.21526 21.0724 5.86228 21.53 6.2612L22.4969 7.10427L21.53 7.94734C21.0721 8.34626 21.0721 8.99328 21.5297 9.39234Z" fill="#D7DBE0"></path>
            <path d="M56.2241 16.086C56.453 16.2855 56.753 16.3853 57.0528 16.3853C57.3526 16.3853 57.6526 16.2856 57.8816 16.086L58.8483 15.2431L59.815 16.086C60.0439 16.2855 60.3439 16.3853 60.6437 16.3853C60.9436 16.3853 61.2436 16.2856 61.4725 16.086C61.9301 15.6869 61.9301 15.0399 61.4725 14.641L60.5056 13.7979L61.4725 12.9549C61.9301 12.5558 61.9301 11.9088 61.4725 11.5099C61.0147 11.1108 60.2728 11.1108 59.8151 11.5099L58.8484 12.3528L57.8817 11.5099C57.4239 11.1108 56.682 11.1108 56.2244 11.5099C55.7667 11.9089 55.7667 12.5559 56.2244 12.9549L57.1912 13.7979L56.2244 14.641C55.7664 15.0399 55.7664 15.6869 56.2241 16.086Z" fill="#D7DBE0"></path>
            <path d="M49.7541 9.69083C52.5694 9.69083 54.8597 7.69378 54.8597 5.23908C54.8595 2.78425 52.5692 0.787201 49.7541 0.787201C46.9389 0.787201 44.6484 2.78425 44.6484 5.23908C44.6484 7.69378 46.9387 9.69083 49.7541 9.69083ZM49.7541 2.83085C51.2769 2.83085 52.5159 3.91112 52.5159 5.23908C52.5159 6.56691 51.2769 7.64718 49.7541 7.64718C48.2312 7.64718 46.9922 6.56691 46.9922 5.23908C46.9922 3.91112 48.2312 2.83085 49.7541 2.83085Z" fill="#D7DBE0"></path>
            <path d="M35.195 39.0057C34.0659 39.0057 33.0003 39.3321 32.1928 39.9259C31.6981 40.2897 31.6351 40.9344 32.0525 41.3657C32.4695 41.7972 33.2089 41.852 33.7037 41.4882C34.0834 41.209 34.6265 41.0492 35.1951 41.0492H35.2029C35.7744 41.0507 36.3195 41.2135 36.699 41.496C36.9198 41.6603 37.1903 41.7405 37.4592 41.7405C37.7903 41.7405 38.1194 41.6189 38.3511 41.3821C38.7712 40.9529 38.7128 40.3078 38.2206 39.9414C37.4137 39.3409 36.3444 39.0084 35.2097 39.0054C35.2048 39.0057 35.1998 39.0057 35.195 39.0057Z" fill="#D7DBE0"></path>
            <path d="M30.2373 35.9796H30.2263C29.5785 35.9713 29.0565 36.4301 29.0518 36.9943C29.0474 37.5586 29.5738 38.0193 30.221 38.0232H30.2293C30.8726 38.0232 31.3965 37.5703 31.401 37.0085C31.4055 36.4442 30.8846 35.9835 30.2373 35.9796Z" fill="#D7DBE0"></path>
            <path d="M40.1992 38.0023C40.2033 38.0029 40.2075 38.0033 40.2116 38.004C40.2453 38.0097 40.2796 38.0145 40.3142 38.0176C40.3255 38.0186 40.3369 38.0186 40.3481 38.0193C40.3742 38.0209 40.4003 38.0231 40.4271 38.0232H40.4353C40.4756 38.0232 40.5155 38.0215 40.5549 38.0179C41.1466 37.9663 41.6085 37.5309 41.6085 37.0013C41.6085 36.4786 41.1583 36.0485 40.5778 35.9876C40.5725 35.9871 40.5674 35.986 40.5621 35.9856C40.5353 35.9831 40.508 35.9824 40.4807 35.9815C40.4682 35.9811 40.456 35.9797 40.4433 35.9796H40.4372C40.4371 35.9796 40.4367 35.9796 40.4366 35.9796H40.4324H40.4255C40.425 35.9796 40.4246 35.9796 40.4241 35.9796C39.7808 35.9796 39.2624 36.4325 39.2578 36.9943C39.2539 37.4907 39.6608 37.9065 40.1992 38.0023Z" fill="#D7DBE0"></path>
            </svg>
        </span>
        <h3 class="caption-large">{{ 'general.cart.empty_title' | t }}</h3>
        {%- if settings.continue_shopping_enable -%}
          <a class="button button--medium button-label" href="{{ settings.continue_shopping_link }}">
            {{- 'general.continue_shopping' | t -}}
          </a>
        {%- endif -%}
      </div>
    </div>

    <div
      class="cart__items {% if cart == empty %}no-js-inline {% endif %} flex-grow-1 y_scroll"
      id="min-cart-items"
    >
      <form action="{{ routes.cart_url }}" method="post" id="cart" class="w-100">
        <div class="js-contents">
          <div id="cart-notification-product">
            {% if settings.free_shipping_message %}
            {%- render 'cart-free-shipping' -%}
            {% endif %}
            {%- render 'cart-item-notification' -%}
          </div>
        </div>
      </form>
    </div>

    <div class="cart-notification__links {% if cart == empty %} no-js-inline {% endif %}" id="empty__cart__button">
      <div id="gift-wrapping" class="add__gift--wrap-in--mini-cart">
          {% render 'add-gift-wrap' %}
      </div>

    <div class="mini--cart-drawer-footer color-background-2">

      {% if gift_message or settings.cart_note_enable or settings.shipping_calc_enable or settings.coupon_enable %}
        <div class="cart__notification--attribute d-flex  {% if gift_message %}justify-content-between{% else %}justify-content-around{% endif %}">
        {% if gift_message %}
          <details id="details__gift--card-product" class="cart--drawer-details-popup">
            <summary class="details__gift--card-label mini-cart-popup-summary">
              {% render 'gift-wrap-icon' %}
              {{ 'general.gift_wrapping_message.button_title' | t }} 
            </summary>
        
            <div class="gift__card--details">
        
              <gift-wrap-message class="cart__drawer--popup">
                <div class="action_drawer_heading">
                  <h6 class="mb-15 mt-0">{{ 'general.gift_wrapping_message.popup_title' | t }} </h6>
                </div>
                <div class="action_drawer_body mb-20">
                  <textarea
                    class="text-area"
                    id="giftMessage"
                    class="form-control"
                    rows="3"
                    placeholder="{{ 'general.gift_wrapping_message.placeholder_title' | t }}"
                    spellcheck="false"
                    name="attributes[gift-note]"
                  ></textarea>
                </div>
                <div class="action_drawer_footer d-flex flex-direction-column">
                  <button class="button button--primary button__save">{{ 'general.gift_wrapping_message.save_button' | t }}</button>
                  <button
                    onclick="this.closest('details').querySelector('summary').click()"
                    class="link button__cancel"
                  >
                    {{ 'general.gift_wrapping_message.cancel_title' | t }}
                  </button>
                </div>
              </gift-wrap-message>
        
            </div>
          </details>
          {% endif %} 
        

          {% if settings.cart_note_enable %}
          <details id="drawer--cart-note" class="cart--drawer-details-popup">
            <summary class="details__gift--card-label cart_notification_action_button mini-cart-popup-summary">
              <svg width="18" height="19" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10.8642 1.18519V0.691358C10.8642 0.296296 10.5679 0 10.1728 0C9.77778 0 9.48148 0.395062 9.48148 0.691358V1.08642H5.03704V0.691358C5.03704 0.395062 4.74074 0 4.34568 0C3.95062 0 3.65432 0.395062 3.65432 0.691358V1.18519C1.28395 1.38272 0 2.96296 0 5.4321V11.6543C0 14.4198 1.58025 16 4.34568 16H10.1728C12.9383 16 14.5185 14.4198 14.5185 11.6543V5.4321C14.5185 2.96296 13.2346 1.38272 10.8642 1.18519ZM4.34568 3.55556C4.64198 3.55556 5.03704 3.25926 5.03704 2.8642V2.46914H9.48148V2.8642C9.48148 3.25926 9.87654 3.55556 10.1728 3.55556C10.4691 3.55556 10.8642 3.25926 10.8642 2.8642V2.46914C12.4444 2.66667 13.1358 3.65432 13.1358 5.4321V11.6543C13.1358 13.7284 12.1481 14.6173 10.1728 14.6173H4.34568C2.27161 14.6173 1.38272 13.6296 1.38272 11.6543V5.33333C1.38272 3.55556 2.07407 2.5679 3.65432 2.37037V2.76543C3.65432 3.25926 3.95062 3.55556 4.34568 3.55556Z" fill="#111111"></path>
                <path d="M4.34565 8.00019H10.1728C10.4691 8.00019 10.8642 7.7039 10.8642 7.30884C10.8642 6.91377 10.4691 6.61748 10.1728 6.61748H4.34565C3.95059 6.61748 3.6543 6.91377 3.6543 7.30884C3.6543 7.7039 4.04936 8.00019 4.34565 8.00019Z" fill="#111111"></path>
                <path d="M7.30862 10.2715H4.34565C3.95059 10.2715 3.6543 10.5678 3.6543 10.9629C3.6543 11.3579 4.04936 11.6542 4.34565 11.6542H7.30862C7.60491 11.6542 7.99998 11.3579 7.99998 10.9629C7.99998 10.5678 7.60491 10.2715 7.30862 10.2715Z" fill="#111111"></path>
              </svg>
              {{ 'general.add_cart_note.button_title' | t }}
            </summary>
            <div class="gift__card--details">
              <cart-note class="cart__drawer--popup">
                <div class="action_drawer_heading">
                  <h6 class="mb-15 mt-0">{{ 'general.add_cart_note.popup_title' | t }}</h6>
                </div>
                <div class="action_drawer_body mb-20">
                  <textarea
                    class="text-area"
                    name="note"
                    id="cartNote"
                    class="form-control"
                    rows="3"
                    placeholder="Special instructions for seller"
                    spellcheck="false"
                  ></textarea>
                </div>
                <div class="action_drawer_footer d-flex flex-direction-column">
                  <button class="button button--primary button__save">{{ 'general.add_cart_note.save_button' | t }}</button> 
                  <button
                    onclick="this.closest('details').querySelector('summary').click()"
                    class="link button__cancel"
                  >
                    {{ 'general.add_cart_note.cancel_title' | t }}
                  </button>
                </div>
              </cart-note>
            </div>
          </details>
          {% endif %}

          {% if settings.shipping_calc_enable %}
          <details id="drawer--cart-shipping" class="cart--drawer-details-popup">
            <summary class="details__gift--card-label cart_notification_action_button mini-cart-popup-summary">
             <svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M15.6076 3.1571L11.0506 0.516616C9.79747 -0.172205 7.97468 -0.172205 6.83544 0.516616L2.27848 3.1571C1.02532 3.84592 0 5.56798 0 7.06042V11.997C0 13.4894 1.02532 15.0967 2.27848 15.9003L6.83544 18.5408C7.40506 18.8852 8.20253 19 9 19C9.79747 19 10.481 18.8852 11.1646 18.5408L15.7215 15.9003C16.9747 15.2115 18 13.4894 18 11.997V6.94562C17.8861 5.56798 16.8608 3.84592 15.6076 3.1571ZM7.40506 1.77946C7.74684 1.54985 8.31646 1.43505 8.88608 1.43505C9.4557 1.43505 9.91139 1.54985 10.3671 1.77946L14.9241 4.41994C15.2658 4.53474 15.4937 4.87915 15.7215 5.22357L13.2152 6.71601L6.1519 2.46828L7.40506 1.77946ZM2.8481 4.30514L4.78481 3.1571L11.8481 7.29003L8.88608 9.12689L1.93671 5.10876C2.16456 4.76435 2.50633 4.53474 2.8481 4.30514ZM2.8481 14.5227C2.05063 14.0634 1.25316 12.8006 1.25316 11.8822V6.94562C1.25316 6.71601 1.25316 6.48641 1.36709 6.2568L8.20253 10.2749V17.3927C7.86076 17.3927 7.63291 17.2779 7.40506 17.1631L2.8481 14.5227ZM16.519 11.8822C16.519 12.8006 15.8354 14.0634 14.9241 14.5227L10.3671 17.0483C10.1392 17.1631 9.91139 17.2779 9.56962 17.2779V10.2749L12.5316 8.55287V10.5045C12.5316 10.8489 12.8734 11.1934 13.2152 11.1934C13.557 11.1934 13.8987 10.8489 13.8987 10.5045V7.74925L16.4051 6.2568C16.4051 6.60121 16.519 6.83082 16.519 6.94562V11.8822Z" fill="#111111"></path>
      </svg> {{ 'general.shipping_calculator.button_title' | t }}</summary>
            <div class="gift__card--details">
              <shipping-calculator class="cart__drawer--popup">
                <div class="action_drawer_heading">
                  <h6 class="mb-15 mt-0">{{ 'general.shipping_calculator.popup_title' | t }}</h6>
                </div>
                <div class="action_drawer_body">
                  <div class="select__field_form mb-20">
                    <select
                      id="all--countries-shipping"
                      name="address[country]"
                      autocomplete="country"
                    >
                      {{ all_country_option_tags }}
                    </select>
                    <svg aria-hidden="true" focusable="false" viewBox="0 0 10 6">
                      <use href="#icon-caret" />
                    </svg>
                  </div>

                  <div id="AddressProvinceContainerNewShiping" class="select__field_form mb-20" style="display: none">
                    <select
                      id="AddressProvince_shipping"
                      name="address[province]"
                      autocomplete="address-level1"
                    ></select>
                    <svg aria-hidden="true" focusable="false" viewBox="0 0 10 6">
                      <use href="#icon-caret" />
                    </svg>
                  </div>

                  <div class="input__field_form mb-20">
                    <input
                      type="text"
                      class="input__field"
                      id="ShippingAddressZip"
                      autocapitalize="characters"
                      autocomplete="postal-code"
                      placeholder="{{ 'customer.addresses.zip' | t }}"
                    >
                  </div>
                </div>
                <div class="action_drawer_footer d-flex flex-direction-column">
                  <button class="button button--primary button__save">
                    {{ 'general.shipping_calculator.calculate_button' | t }}
                  </button>
                  <button class="link button__cancel" onclick="this.closest('details').querySelector('summary').click()"> {{ 'general.shipping_calculator.cancel_button' | t }}</button>
                </div>
                <div class="shipping_rate_message mt-15 no-js-inline text-center">
                  <p>
                    {{ 'general.shipping_calculator.address_first_label' | t }}
                    <span class="shipping_address_count"></span>
                    {{ 'general.shipping_calculator.address_second_label' | t }}
                  </p>
                </div>
                <div class="shipping_rate_package text-center"></div>
              </shipping-calculator>
            </div>
          </details>
          {% endif %}

          {% if settings.coupon_enable %}
          <details id="drawer--cart-coupon" class="cart--drawer-details-popup">
            <summary class="details__gift--card-label cart_notification_action_button mini-cart-popup-summary">
             <svg id="fi_4015759" height="" viewBox="0 0 32 32" width="" xmlns="http://www.w3.org/2000/svg"><path d="m15.996 0c-.911 0-1.832.36-2.527 1.055l-2.59 2.591h-3.67c-1.962 0-3.574 1.611-3.574 3.575v3.661l-2.59 2.599c-1.389 1.388-1.389 3.667 0 5.055l2.59 2.59v3.669c0 1.963 1.611 3.575 3.574 3.575h3.67l2.59 2.59c1.389 1.388 3.674 1.388 5.063 0l2.59-2.59h3.661c1.964 0 3.583-1.611 3.583-3.575v-3.669l2.589-2.59c1.389-1.388 1.389-3.667 0-5.055l-2.589-2.599v-3.661c0-1.963-1.62-3.575-3.583-3.575h-3.661l-2.59-2.591c-.695-.695-1.623-1.055-2.536-1.055zm0 2.134c.372 0 .736.137 1.024.425l2.905 2.905c.2.201.472.315.756.315h4.102c.819 0 1.449.623 1.449 1.441v4.11c0 .28.11.549.307.748l2.914 2.905c.578.579.578 1.469 0 2.047l-2.914 2.897c-.199.202-.309.474-.307.756v4.11c0 .818-.63 1.441-1.449 1.441h-4.102c-.285 0-.556.114-.756.315l-2.905 2.905c-.579.579-1.46.579-2.039 0l-2.905-2.905c-.2-.201-.472-.314-.756-.315h-4.11c-.818 0-1.44-.623-1.44-1.441v-4.11c0-.284-.113-.556-.314-.756l-2.906-2.897c-.578-.579-.578-1.469 0-2.047l2.906-2.905c.199-.198.313-.467.314-.748v-4.11c0-.818.622-1.441 1.44-1.441h4.11c.285 0 .556-.114.756-.315l2.905-2.905c.288-.289.644-.425 1.015-.425zm-3.495 5.803c-1.754 0-3.197 1.45-3.197 3.204 0 1.755 1.443 3.197 3.197 3.197 1.755 0 3.197-1.442 3.197-3.197-.001-1.754-1.442-3.204-3.197-3.204zm0 2.134c.602 0 1.063.468 1.063 1.07s-.461 1.063-1.063 1.063-1.071-.461-1.071-1.063.469-1.07 1.071-1.07zm8.975 6.841c-1.754 0-3.204 1.442-3.204 3.197 0 1.754 1.45 3.205 3.204 3.205 1.755 0 3.197-1.45 3.197-3.205s-1.441-3.197-3.197-3.197zm0 2.134c.602 0 1.063.461 1.063 1.063s-.461 1.071-1.063 1.071c-.601 0-1.071-.469-1.071-1.071s.471-1.063 1.071-1.063zm.599-9.747c-.277.008-.54.125-.732.323l-11.315 11.314c-.417.417-.417 1.094 0 1.512.417.417 1.095.417 1.512 0l11.314-11.314c.425-.409.438-1.084.029-1.509-.21-.219-.504-.338-.808-.326z"></path></svg>
              {{ 'general.coupon_code.button_title' | t }}
            </summary>
            <div class="gift__card--details">
              <discount-code class="cart__drawer--popup">
                <div class="action_drawer_heading">
                  <h6 class="mb-15 mt-0"> {{ 'general.coupon_code.popup_title' | t }} </h6>
                </div>
                <div class="action_drawer_body mb-20">
                  <input
                    id="coupon_code"
                    placeholder="Enter discount code here"
                    type="text"
                    name="discount"
                    form="cart"
                    value=""
                  >
                </div>
                <div class="action_drawer_footer d-flex flex-direction-column">
                  <button class="button button--primary button__save"">{{ 'general.coupon_code.save_button' | t }}</button>
                  <button class="link button__cancel" onclick="this.closest('details').querySelector('summary').click()"> {{ 'general.coupon_code.cancel_title' | t }}</button>
                </div>

                <div class="coupon_wrong_message error no-js-inline mt-15">
                  {{ 'general.coupon_code.coupon_wrong_message' | t }}
                </div>
              </discount-code
            </div>
          </details>
          {% endif %}
        </div>
        {% endif %}

         <div class="cart_notification_links_inner">
              <div class="totals d-flex justify-content-between align-items-center">
                <h3 class="totals__subtotal">{{ 'sections.cart.subtotal' | t }}</h3>
                <p id="cart-notification-subtotal">{{ cart.total_price | money_with_currency }}</p>
              </div>
      
              <div id="cart-notification-discount">
                {%- if cart.cart_level_discount_applications.size > 0 -%}
                  <div class="cart_notification--discount mb-20">
                    <ul class="discounts list-unstyled" role="list" aria-label="{{ 'customer.order.discount' | t }}">
                      {%- for discount in cart.cart_level_discount_applications -%}
                        <li class="discounts__discount d-flex justify-content-between">
                          <span class="discount__title">
                            {%- render 'icon-discount' -%}
                            {{ discount.title }}
                          </span>
                          <span class="discount__amount"> (-{{ discount.total_allocated_amount | money }})</span>
                        </li>
                      {%- endfor -%}
                    </ul>
                  </div>
                {%- endif -%}
              </div>
      
              <div class="tax-note caption-large rte mb-15">
                {%- if cart.taxes_included and shop.shipping_policy.body != blank -%}
                  {{ 'sections.cart.taxes_included_and_shipping_policy_html' | t: link: shop.shipping_policy.url }}
                {%- elsif cart.taxes_included -%}
                  {{ 'sections.cart.taxes_included_but_shipping_at_checkout' | t }}
                {%- elsif shop.shipping_policy.body != blank -%}
                  {{ 'sections.cart.taxes_and_shipping_policy_at_checkout_html' | t: link: shop.shipping_policy.url }}
                {%- else -%}
                  {{ 'sections.cart.taxes_and_shipping_at_checkout' | t }}
                {%- endif -%}
              </div>
      
              <div class="d-flex justify-content-between cart_notification--footer">
                {%- if settings.cart_btn_enable -%}
                <a class="button button--secondary button--full-width mb-15" href="{{ routes.cart_url }}">
                  {{- 'general.cart.view' | t -}}
                </a>
                {% endif %}
                {%- if settings.checkout_btn_enable -%}
                  <div class="cart--checkout__button">
                    <button type="submit" class="button button--primary button--full-width" name="checkout" form="cart">
                      {{ 'sections.cart.checkout' | t }}
                    </button>
                  </div>
                {%- endif -%}
              </div>
            </div>
                              
                        
      </div>     
    </div>
  </div>
</div>
