{% liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
%}

{{ 'template-collection.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'product-card.css' | asset_url | stylesheet_tag }}
{{ 'component-rating.css' | asset_url | stylesheet_tag }}
{{ 'countdown-timer.css' | asset_url | stylesheet_tag }}
{{ 'stock-countdwon.css' | asset_url | stylesheet_tag }}
{{ 'product-tooltip.css' | asset_url | stylesheet_tag }}

<link rel="stylesheet" href="{{ 'component-search.css' | asset_url }}" media="print" onload="this.media='all'">

<noscript>{{ 'component-search.css' | asset_url | stylesheet_tag }}</noscript>

{%- if section.settings.enable_filtering or section.settings.enable_sorting -%}
  {{ 'component-facets.css' | asset_url | stylesheet_tag }}
  <script src="{{ 'facets.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

<script src="{{ 'menu-drawer.js' | asset_url }}" defer="defer"></script>

<script src="{{ 'offcanvas-filter-active.js' | asset_url }}" defer="defer"></script>

<style>
   .section-{{ section.id }}-padding {
    margin-top: {{ section.settings.mobile_padding_top }}px;
    margin-bottom: {{ section.settings.mobile_padding_bottom }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      margin-top: {{ section.settings.padding_top }}px;
      margin-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
</style>

{%- liquid
  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif

  assign product_columns = section.settings.product_column
  assign product_column_view = ''

  case product_columns
    when '5'
      assign product_column_view = '5'
    when '4'
      assign product_column_view = '4'
    when '3'
      assign product_column_view = '3'
    when '2'
      assign product_column_view = '2'
    else
      assign product_column_view = '1'
  endcase
-%}
{%- if theme_rtl -%}
  {{ 'product-card-rtl.css' | asset_url | stylesheet_tag }}
  {{ 'template-collection-rtl.css' | asset_url | stylesheet_tag }}
{%- endif -%}
<div
  class="template-search  section-{{ section.id }}-padding"
  data-section-id="{{ section.id }}"
  data-section-type="collection-product"
  data-design="{{ request.design_mode }}"
  data-column="{{ section.settings.product_column }}"
>
  {%- if section.settings.enable_sorting and section.settings.filter_type == 'vertical' -%}
    <div class="{{ container }} small-hide mb-30">
      <div class="filter__toolbox d-flex align-items-center color-background-2">
        {%- if section.settings.product_view_switcher -%}
          <div class="product__grid_column_buttons d-sm-none">
            <button
              class="gird__column_icon product_col_two {% if product_column_view == "2" %}active{% endif %}"
              aria-label="Product column button"
            >
              <span>
                <svg fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 5.5 12.5">
                  <defs/><defs><style>.cls-1{fill-rule:evenodd}</style></defs><g id="Layer_2" data-name="Layer 2"><g id="Layer_1-2" data-name="Layer 1"><g id="shop_page" data-name="shop page"><g id="Group-10"><path id="Rectangle" d="M.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 01.75 0z" class="cls-1"/><path id="Rectangle-2" d="M4.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 014.75 0z" class="cls-1" data-name="Rectangle"/></g></g></g></g>
                </svg>
              </span>
            </button>

            <button
              class="gird__column_icon product_col_three {% if product_column_view == "3" %}active{% endif %}"
              aria-label="Product column button"
            >
              <span>
                <svg fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 9.5 12.5">
                  <defs/><defs><style>.cls-1{fill-rule:evenodd}</style></defs><g id="Layer_2" data-name="Layer 2"><g id="Layer_1-2" data-name="Layer 1"><g id="shop_page" data-name="shop page"><g id="Group-16"><path id="Rectangle" d="M.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 01.75 0z" class="cls-1"/><path id="Rectangle-2" d="M4.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 014.75 0z" class="cls-1" data-name="Rectangle"/><path id="Rectangle-3" d="M8.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 018.75 0z" class="cls-1" data-name="Rectangle"/></g></g></g></g>
                </svg>
              </span>
            </button>

            <button
              class="gird__column_icon product_col_four {% if product_column_view == "4" %}active{% endif %}"
              aria-label="Product column button"
            >
              <span>
                <svg fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13.5 12.5">
                  <defs/><defs><style>.cls-1{fill-rule:evenodd}</style></defs><g id="Layer_2" data-name="Layer 2"><g id="Layer_1-2" data-name="Layer 1"><g id="shop_page" data-name="shop page"><g id="_4_col" data-name="4_col"><path id="Rectangle" d="M.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 01.75 0z" class="cls-1"/><path id="Rectangle-2" d="M4.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 014.75 0z" class="cls-1" data-name="Rectangle"/><path id="Rectangle-3" d="M8.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11A.76.76 0 018.75 0z" class="cls-1" data-name="Rectangle"/><path id="Rectangle-4" d="M12.75 0a.76.76 0 01.75.75v11a.76.76 0 01-.75.75.76.76 0 01-.75-.75v-11a.76.76 0 01.75-.75z" class="cls-1" data-name="Rectangle"/></g></g></g></g>
                </svg>
              </span>
            </button>
          </div>
        {%- endif -%}
        <facet-filters-form class="facets-vertical-sort no-js-hidden flex-grow-1">
          <form class="facets-vertical-form" id="FacetSortForm">
            <div class="facet-filters sorting caption">
              <div class="facet-filters__field">
                <h2 class="facet-filters__label ">
                  <sort-select>
                    <button class="sortby__button">
                      {% render 'sort-icon' %}
                      <strong> {{ 'products.facets.sort_by_label' | t }}</strong>
                    </button>
                  </sort-select>
                </h2>
                {%- assign sort_by = collection.sort_by | default: collection.default_sort_by -%}
                <ul class="filter__sort--by-conatiner">
                  {%- for option in collection.sort_options -%}
                    <li class="sortlist__Item">
                      <input
                        type="radio"
                        id="{{ option.value }}_{{ forloop.index }}"
                        class="{% if option.value == sort_by %}checked{% endif %}"
                        name="sort_by"
                        value="{{ option.value | escape }}"
                      >
                      <label for="{{ option.value }}_{{ forloop.index }}"> {{ option.name | escape }}</label>
                    </li>
                  {%- endfor -%}
                </ul>
              </div>

              <noscript>
                <button type="submit" class="facets__button-no-js button button--secondary">
                  {{ 'products.facets.sort_button' | t }}
                </button>
              </noscript>
            </div>
            <div class="product-count-vertical light" role="status">
              <h2 class="product-count__text text-body">
                <span id="ProductCountDesktop">
                  {%- if collection.results_count -%}
                    {{
                      'templates.search.results_with_count'
                      | t: terms: collection.terms, count: collection.results_count
                    }}
                  {%- elsif collection.products_count == collection.all_products_count -%}
                    {{ 'products.facets.product_count_simple' | t: count: collection.products_count }}
                  {%- else -%}
                    {{
                      'products.facets.product_count'
                      | t: product_count: collection.products_count, count: collection.all_products_count
                    }}
                  {%- endif -%}
                </span>
              </h2>
              <div class="loading-overlay__spinner">
                <svg
                  aria-hidden="true"
                  focusable="false"
                  role="presentation"
                  class="spinner"
                  viewBox="0 0 66 66"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                </svg>
              </div>
            </div>
          </form>
        </facet-filters-form>
      </div>
    </div>
  {%- endif -%}

  <div
    {% if section.settings.filter_type == 'vertical' %}
      class="facets-vertical {{ container }}"
    {% endif %}
  >
    {%- if section.settings.enable_filtering or section.settings.enable_sorting -%}
      <aside
        aria-labelledby="verticalTitle"
        class="facets-wrapper{% unless section.settings.enable_filtering %} facets-wrapper--no-filters{% endunless %}{% if section.settings.filter_type != 'vertical' %} {{ container }}{% endif %} {% unless section.settings.filter_type == 'vertical' %} mb-30 {% endunless %}"
        id="main-search-filters"
        data-id="{{ section.id }}"
      >
        {% render 'facets',
          results: collection,
          enable_filtering: section.settings.enable_filtering,
          enable_sorting: section.settings.enable_sorting,
          filter_type: section.settings.filter_type
        %}
      </aside>
    {%- endif -%}

    <div class="product-grid-container">
      <div id="ProductGridContainer">
        {%- if collection.products.size == 0 -%}
          <div
            class="template-search__results collection collection--empty{% if section.settings.filter_type != 'vertical' %} {{ container }}{% endif %}"
            id="product-grid"
            data-id="{{ section.id }}"
          >
            <div class="loading-overlay"></div>
            <div class="title-wrapper center">
              <h2 class="title title--primary">
                {{ 'sections.collection_template.empty' | t -}}
                <br>
                {{
                  'sections.collection_template.use_fewer_filters_html'
                  | t: link: collection.url, class: 'underlined-link link'
                }}
              </h2>
            </div>
          </div>
        {%- else -%}
          {% paginate collection.products by section.settings.products_per_page %}
            <div
              class="template-search__results collection{% if section.settings.filter_type != 'vertical' %} {{ container }}{% endif %}"
              id="product-grid"
              data-id="{{ section.id }}"
            >
              <div class="loading-overlay"></div>
              <div
                class="row row-cols-lg-{{ product_column_view }} row-cols-md-3 {% if section.settings.product_column_on_mobile %} row-cols-1 {% else %} row-cols-2 {% endif %}"
                data-product-column
              >
                {%- for product in collection.products -%}
                  <div class="col">
                    {% render 'product-card',
                      className: 'mb-20',
                      product_card_product: product,
                      media_size: section.settings.image_ratio,
                      show_secondary_image: section.settings.show_secondary_image,
                      show_vendor: section.settings.show_vendor,
                      show_badge: section.settings.show_badges,
                      show_cart_button: section.settings.show_cart_button,
                      show_preorder_button: section.settings.show_preorder_button,
                      show_quick_view: section.settings.show_quick_view_button,
                      show_quick_compare: section.settings.show_compare_view_button,
                      show_wishlist: section.settings.show_wishlist_button,
                      show_countdown: section.settings.show_countdown,
                      show_title: section.settings.show_title,
                      show_price: section.settings.show_price,
                      show_rating: section.settings.show_product_rating,
                      card_style: section.settings.card_style,
                      color_swatches: section.settings.color_swatches,
                      color_scheme: section.settings.product_card_color_scheme,
                      spacing: section.settings.product_card_spacing,
                      corner_radius: section.settings.product_card_radius,
                      inventory_status: section.settings.inventory_status
                    %}
                  </div>
                {%- endfor -%}
              </div>
              {%- if paginate.pages > 1 -%}
                {% render 'pagination', paginate: paginate, anchor: '' %}
              {%- endif -%}
            </div>
          {% endpaginate %}
        {%- endif -%}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.main-collection-product-grid.name",
  "class": "collection-grid-section",
  "tag": "section",
  "settings": [
      {
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
  	  {
        "type": "range",
        "id": "products_per_page",
        "min": 8,
        "max": 100,
        "step": 1,
        "default": 16,
        "label": "t:sections.main-collection-product-grid.settings.products_per_page.label"
      },
      {
        "type": "select",
        "id": "product_column",
        "label": "Number of columns on desktop",
        "options": [
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          }
        ],
        "default": "3"
      },
    {
      "type": "checkbox",
      "id": "product_column_on_mobile",
      "default": false,
      "label": "Display one column on mobile"
    },
    {
      "type": "checkbox",
      "id": "product_view_switcher",
      "default": true,
      "label": "Enable layout switcher"
    },
  	{
      "type": "header",
      "content": "t:sections.main-collection-product-grid.settings.header__1.content"
    },
    {
      "type": "checkbox",
      "id": "enable_filtering",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_filtering.label",
      "info": "t:sections.main-collection-product-grid.settings.enable_filtering.info"
    },
    {
      "type": "select",
      "id": "filter_type",
      "options": [
        {
          "value": "horizontal",
          "label": "Horizontal"
        },
        {
          "value": "vertical",
          "label": "Vertical"
        },
        {
          "value": "drawer",
          "label": "Drawer"
        }
      ],
      "default": "vertical",
      "label": "Desktop filter layout",
      "info": "Drawer is the default mobile layout."
    },
    {
      "type": "checkbox",
      "id": "enable_sorting",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_sorting.label"
    },
    {
      "type": "select",
      "id": "price_filter_type",
      "options": [
        {
          "value": "input",
          "label": "Input field"
        },
        {
          "value": "range",
          "label": "Range slider"
        }
      ],
      "default": "range",
      "label": "Price filter layout"
    },
    {
      "type": "color_scheme",
      "id": "facets_color_scheme",
      "label": "Filters color scheme",
      "default": "background-2"
    },
    {
      "type": "checkbox",
      "id": "filter_box_radius",
      "label": "Vertical filter round corner",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "filter_box_spacing",
      "label": "Vertical filter inner spacing",
      "default": false
    },
     {
     "type": "header",
     "content": "Product card"
   },
    {
        "type": "select",
        "id": "card_style",
        "label": "Card style",
        "options": [
          {
            "value": "style_1",
            "label": "Style 1"
          },
          {
            "value": "style_2",
            "label": "Style 2"
          }
        ],
        "default": "style_1"
      },
    {
       "type": "select",
       "id": "image_ratio",
       "options": [
         {
           "value": "adapt",
           "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__1.label"
         },
         {
           "value": "portrait",
           "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__2.label"
         },
         {
           "value": "square",
           "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__3.label"
         },
          {
           "value": "landscape",
           "label": "Landscape"
         }
       ],
       "default": "adapt",
       "label": "t:sections.main-collection-product-grid.settings.image_ratio.label"
     },
     {
       "type": "checkbox",
       "id": "show_secondary_image",
       "default": false,
       "label": "t:sections.main-collection-product-grid.settings.show_secondary_image.label"
     },
      {
        "type": "checkbox",
        "id": "color_swatches",
        "default": true,
        "label": "Enable color swatches",
        "info": "To display color swatches, you need to enable it. [Learn more](https:\/\/team90degree.com\/suruchi-theme\/theme-settings\/color-swatches)."
      },
     {
       "type": "checkbox",
       "id": "show_badges",
       "default": true,
       "label": "Show badges"
     },
     {
       "type": "checkbox",
       "id": "show_cart_button",
       "default": true,
       "label": "Show cart button"
     },
    {
        "type": "checkbox",
        "id": "show_preorder_button",
        "default": true,
        "label": "Show pre-order button",
        "info": "You may want to allow customers to purchase out-of-stock items. [Learn more](https://team90degree.com/suruchi-theme/general-topics-faq/how-to-enable-the-pre-order-product-to-your-store)"
      },
     {
       "type": "checkbox",
       "id": "show_quick_view_button",
       "default": true,
       "label": "Show quick view"
     },
     {
       "type": "checkbox",
       "id": "show_compare_view_button",
       "default": true,
       "label": "Show compare button"
     },
     {
       "type": "checkbox",
       "id": "show_wishlist_button",
       "default": true,
       "label": "Show wishlist button"
     },
     {
       "type": "checkbox",
       "id": "show_title",
       "default": true,
       "label": "Show title"
     },
     {
       "type": "checkbox",
       "id": "show_price",
       "default": true,
       "label": "Show price"
     },
     {
       "type": "checkbox",
       "id": "show_vendor",
       "default": false,
       "label": "t:sections.main-collection-product-grid.settings.show_vendor.label"
     },
 {
       "type": "checkbox",
       "id": "show_countdown",
       "default": false,
       "label": "Show countdown",
       "info": "To display countdown timer, you need to enable it. [Learn more](https://team90degree.com/suruchi-theme/collections-and-products/products/add-a-countdown-timer-to-the-product)."
   },

   {
     "type": "checkbox",
     "id": "show_product_rating",
     "default": false,
     "label": "Show product rating",
     "info": "To display product rating, you need to enable it. [Learn more](https://team90degree.com/suruchi-theme/general-topics-faq/how-to-add-product-reviews-to-your-store)."
   },
    {
        "type": "checkbox",
        "id": "inventory_status",
        "label": "Show inventory status",
        "default": false
    },
     {
      "type": "checkbox",
      "id": "product_card_radius",
      "label": "Round corner",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "product_card_spacing",
      "label": "Card spacing",
      "default": false
    },
   {
      "type": "color_scheme",
      "id": "product_card_color_scheme",
      "label": "Product card color scheme",
      "default": "background-1"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
          "type": "paragraph",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        },
		{
          "type": "paragraph",
          "content": "Mobile"
        },
		{
          "type": "range",
          "id": "mobile_padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "mobile_padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        }
  ]
}
{% endschema %}
