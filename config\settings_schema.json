[{"name": "theme_info", "theme_name": "MeMini", "theme_version": "1.0.2", "theme_author": "Team90Degree", "theme_documentation_url": "https://team90degree.com/shopify/doc/memini-doc/", "theme_support_url": "https://help.team90degree.com/"}, {"name": "General", "settings": [{"type": "header", "content": "t:settings_schema.favicon.name"}, {"type": "image_picker", "id": "favicon", "label": "t:settings_schema.favicon.settings.favicon.label", "info": "t:settings_schema.favicon.settings.favicon.info"}, {"type": "header", "content": "t:settings_schema.currency_format.name"}, {"type": "paragraph", "content": "t:settings_schema.currency_format.settings.paragraph"}, {"type": "checkbox", "id": "currency_code_enabled", "label": "t:settings_schema.currency_format.settings.currency_code_enabled.label", "default": true}, {"type": "header", "content": "Back to top"}, {"type": "checkbox", "id": "back_top_enable", "label": "Back to top enable", "default": true}, {"type": "header", "content": "Right-to-Left support"}, {"type": "checkbox", "id": "enable_rtl", "label": "Enable RTL", "default": false}, {"type": "textarea", "id": "langauges_rtl", "label": "Languages support RTL", "default": "he,ar", "info": "Enter the ISO language code, separate by comma. Leave blank to enable for all languages. [Check ISO language code](https://www.loc.gov/standards/iso639-2/php/code_list.php)", "placeholder": "he,ar"}]}, {"name": "t:settings_schema.layout.name", "settings": [{"type": "range", "id": "container_lg_width", "label": "Page width", "info": "This width of the page will affect larger devices> 1200px", "default": 1200, "min": 1000, "max": 1800, "step": 10, "unit": "px"}, {"type": "range", "id": "container_fluid_offset", "label": "Offset left & right", "default": 50, "min": 0, "max": 300, "step": 10, "unit": "px"}]}, {"name": "t:settings_schema.colors.name", "settings": [{"type": "color_scheme_group", "id": "color_schemes", "definition": [{"type": "color", "id": "background", "label": "t:settings_schema.colors.settings.background.label", "default": "#FFFFFF"}, {"type": "color_background", "id": "background_gradient", "label": "t:settings_schema.colors.settings.background_gradient.label", "info": "t:settings_schema.colors.settings.background_gradient.info"}, {"type": "color", "id": "text", "label": "t:settings_schema.colors.settings.text.label", "default": "#121212"}, {"type": "header", "content": "Primary/Solid button"}, {"type": "color", "id": "button", "label": "t:settings_schema.colors.settings.button_background.label", "default": "#121212"}, {"type": "color", "id": "button_label", "label": "t:settings_schema.colors.settings.button_label.label", "default": "#FFFFFF"}, {"type": "color", "id": "primary_button_hover_bg", "default": "#121212", "label": "Button background hover"}, {"type": "color", "id": "primary_button_hover_text", "default": "#FFFFFF", "label": "Button text hover"}, {"type": "header", "content": "Secondary/Outline Button"}, {"type": "color", "id": "secondary_button_label", "label": "t:settings_schema.colors.settings.secondary_button_label.label", "default": "#121212"}, {"type": "color", "id": "secondary_button_hover_bg", "default": "#121212", "label": "Button background hover"}, {"type": "color", "id": "secondary_button_hover_text", "default": "#FFFFFF", "label": "Button text hover"}, {"type": "header", "content": "Product card"}, {"type": "color", "id": "card_success_color", "default": "#337239", "label": "Success foreground color"}, {"type": "color", "id": "card_warning_color", "default": "#922C2C", "label": "Warning foreground color"}, {"type": "header", "content": "Text link"}, {"type": "color", "id": "tex_link_hover_color", "label": "Text link hover", "default": "#121212"}], "role": {"text": "text", "background": {"solid": "background"}, "links": "secondary_button_label", "icons": "text", "primary_button": "button", "on_primary_button": "button_label", "primary_button_border": "button", "secondary_button": "background", "on_secondary_button": "secondary_button_label", "secondary_button_border": "secondary_button_label"}}, {"type": "header", "content": "Placeholder svg color"}, {"type": "color", "id": "placeholder_background", "default": "#f5f5f5", "label": "Background color"}, {"type": "color", "id": "placeholder_foreground", "default": "#121212", "label": "Foreground color"}]}, {"name": "t:settings_schema.typography.name", "settings": [{"type": "header", "content": "t:settings_schema.typography.settings.header__2.content"}, {"type": "font_picker", "id": "type_body_font", "default": "assistant_n4", "label": "t:settings_schema.typography.settings.type_body_font.label", "info": "t:settings_schema.typography.settings.type_body_font.info"}, {"type": "checkbox", "id": "use_custom_body_font", "label": "Use custom font", "default": false}, {"type": "header", "content": "Custom font for body text"}, {"id": "custom_body_font", "type": "textarea", "label": "Font URL", "placeholder": "https://cdn.shopify.com/s/files/0/00/000/0000/files/jost.woff2@400&next_url...", "info": "e.g: Font_URL@font_weight&Font_URL@font_weight [Learn more about custom fonts.](https://team90degree.com/suruchi-theme/theme-settings/typography)"}, {"id": "body_font_weight", "type": "select", "label": "Body font weight", "default": "400", "options": [{"value": "100", "label": "Thin 100"}, {"value": "200", "label": "Extra light 200"}, {"value": "300", "label": "Light 300"}, {"value": "400", "label": "Regular 400"}, {"value": "500", "label": "Medium 500"}, {"value": "600", "label": "Semi-bold 600"}, {"value": "700", "label": "Bold 700"}, {"value": "800", "label": "Extra bold 800"}, {"value": "900", "label": "Black 900"}]}, {"type": "range", "id": "body_font_size", "min": 80, "max": 180, "step": 5, "unit": "%", "label": "Body font size", "default": 100}, {"type": "range", "id": "body_letter_spacing", "min": 0, "max": 10, "step": 1, "unit": "px", "label": "Letter spacing", "default": 0}, {"type": "header", "content": "t:settings_schema.typography.settings.header__1.content"}, {"type": "font_picker", "id": "type_header_font", "default": "assistant_n4", "label": "t:settings_schema.typography.settings.type_header_font.label", "info": "t:settings_schema.typography.settings.type_header_font.info"}, {"type": "checkbox", "id": "use_custom_heading_font", "label": "Use custom font", "default": false}, {"type": "header", "content": "Custom font for heading text"}, {"id": "custom_heading_font", "type": "textarea", "label": "Font URL", "placeholder": "https://cdn.shopify.com/s/files/0/00/000/0000/files/jost.woff2@400&next_url...", "info": "e.g: Font_URL@font_weight&Font_URL@font_weigh [Learn more about custom fonts.](https://team90degree.com/suruchi-theme/theme-settings/typography)"}, {"id": "heading_font_weight", "type": "select", "label": "Heading font weight", "default": "600", "options": [{"value": "100", "label": "Thin 100"}, {"value": "200", "label": "Extra light 200"}, {"value": "300", "label": "Light 300"}, {"value": "400", "label": "Regular 400"}, {"value": "500", "label": "Medium 500"}, {"value": "600", "label": "Semi-bold 600"}, {"value": "700", "label": "Bold 700"}, {"value": "800", "label": "Extra bold 800"}, {"value": "900", "label": "Black 900"}]}, {"type": "range", "id": "heading_font_size", "min": 80, "max": 180, "step": 5, "unit": "%", "label": "Heading font size", "default": 100}, {"type": "range", "id": "heading_letter_spacing", "min": 0, "max": 10, "step": 1, "unit": "px", "label": "Letter spacing", "default": 0}, {"type": "select", "id": "header_text_case", "options": [{"value": "none", "label": "<PERSON><PERSON><PERSON>"}, {"value": "uppercase", "label": "Uppercase"}, {"value": "lowercase", "label": "Lowercase"}, {"value": "capitalize", "label": "Capitalize"}], "default": "none", "label": "Text transform"}, {"type": "header", "content": "BUTTONS"}, {"type": "range", "id": "button_font_size", "min": 80, "max": 180, "step": 5, "unit": "%", "label": "Button font size", "default": 100}, {"type": "range", "id": "button_border_width", "min": 1, "max": 10, "step": 1, "unit": "px", "label": "Border Width", "default": 1}, {"type": "range", "id": "button_border_radius", "min": 0, "max": 100, "step": 1, "unit": "px", "label": "Border Radius", "default": 0}, {"type": "range", "id": "button_letter_spacing", "min": 0, "max": 10, "step": 1, "unit": "px", "label": "Letter spacing", "default": 0}, {"type": "select", "id": "button_text_case", "options": [{"value": "none", "label": "<PERSON><PERSON><PERSON>"}, {"value": "uppercase", "label": "Uppercase"}, {"value": "lowercase", "label": "Lowercase"}, {"value": "capitalize", "label": "Capitalize"}], "default": "none", "label": "Text transform"}]}, {"name": "Product card", "settings": [{"type": "header", "content": "Product content"}, {"type": "select", "id": "product_title_size", "options": [{"value": "h6", "label": "Small"}, {"value": "h5", "label": "Medium"}, {"value": "h4", "label": "Large"}], "default": "h6", "label": "Title size"}, {"type": "select", "id": "product_content_alignment", "label": "Alignment", "default": "center", "options": [{"value": "center", "label": "Center"}, {"value": "left", "label": "Left"}, {"value": "right", "label": "Right"}]}, {"type": "select", "id": "p_card_cart_icon_type", "label": "Cart icon type", "default": "cart", "options": [{"value": "none", "label": "None"}, {"value": "bag", "label": "Bag"}, {"value": "cart", "label": "<PERSON><PERSON>"}, {"value": "basket", "label": "Basket"}]}, {"type": "header", "content": "t:settings_schema.styles.settings.header__1.content"}, {"type": "checkbox", "id": "sale_percentage_show", "default": true, "label": "Show sale percentage"}, {"type": "color_scheme", "id": "sale_badge_color_scheme", "label": "Color scheme for sale badge", "default": "accent-2"}, {"type": "color_scheme", "id": "new_badge_color_scheme", "label": "Color scheme for new badge", "default": "accent-1"}, {"type": "color_scheme", "id": "sold_out_badge_color_scheme", "label": "Color scheme for soldout badge", "default": "inverse"}, {"type": "header", "content": "Style 3 Custom Button Settings"}, {"type": "checkbox", "id": "style3_button_enable", "default": true, "label": "Enable Style 3 custom button", "info": "Show the customizable button on Style 3 product cards"}, {"type": "text", "id": "style3_button_text", "default": "Customize", "label": "Button text", "info": "Text displayed on the Style 3 custom button"}, {"type": "header", "content": "Button Styling"}, {"type": "color", "id": "style3_button_bg_color", "label": "Background color", "info": "Leave empty to use theme default"}, {"type": "color", "id": "style3_button_text_color", "label": "Text color", "info": "Leave empty to use theme default"}, {"type": "color", "id": "style3_button_border_color", "label": "Border color", "info": "Leave empty to use theme default"}, {"type": "select", "id": "style3_button_font_size", "label": "Font size", "options": [{"value": "small", "label": "Small"}, {"value": "medium", "label": "Medium"}, {"value": "large", "label": "Large"}], "default": "medium"}, {"type": "select", "id": "style3_button_font_weight", "label": "Font weight", "options": [{"value": "normal", "label": "Normal"}, {"value": "medium", "label": "Medium"}, {"value": "bold", "label": "Bold"}], "default": "medium"}, {"type": "select", "id": "style3_button_width", "label": "Button width", "options": [{"value": "auto", "label": "Auto-fit content"}, {"value": "full", "label": "Full width"}, {"value": "custom", "label": "Custom max-width"}], "default": "auto"}, {"type": "range", "id": "style3_button_max_width", "min": 100, "max": 400, "step": 10, "unit": "px", "label": "Custom max-width", "default": 200, "info": "Only applies when 'Custom max-width' is selected"}, {"type": "header", "content": "Hover States"}, {"type": "color", "id": "style3_button_hover_bg_color", "label": "Hover background color", "info": "Background color when button is hovered. Leave empty to use automatic hover effect"}, {"type": "color", "id": "style3_button_hover_text_color", "label": "Hover text color", "info": "Text color when button is hovered. Leave empty to use automatic hover effect"}, {"type": "select", "id": "style3_button_hover_effect", "label": "Hover effect", "options": [{"value": "none", "label": "None"}, {"value": "lift", "label": "Subtle lift"}, {"value": "shadow", "label": "Drop shadow"}, {"value": "lift_shadow", "label": "Lift + Shadow"}], "default": "lift_shadow"}, {"type": "range", "id": "style3_button_border_radius", "min": 0, "max": 50, "step": 1, "unit": "px", "label": "Border radius", "default": 4, "info": "Roundness of button corners"}, {"type": "range", "id": "style3_button_padding_vertical", "min": 4, "max": 20, "step": 1, "unit": "px", "label": "Vertical padding", "default": 8, "info": "Top and bottom padding inside the button"}, {"type": "range", "id": "style3_button_padding_horizontal", "min": 8, "max": 40, "step": 2, "unit": "px", "label": "Horizontal padding", "default": 16, "info": "Left and right padding inside the button"}, {"type": "header", "content": "Recent viewed product"}, {"type": "select", "id": "recent_viewed_proudct_card", "label": "Card style", "options": [{"value": "style_1", "label": "Style 1"}, {"value": "style_2", "label": "Style 2"}, {"value": "style_3", "label": "Style 3"}], "default": "style_1"}, {"type": "select", "id": "recent_viewed_proudct_media_size", "options": [{"value": "adapt", "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__1.label"}, {"value": "portrait", "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__2.label"}, {"value": "square", "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__3.label"}, {"value": "landscape", "label": "Landscape"}], "default": "adapt", "label": "t:sections.main-collection-product-grid.settings.image_ratio.label"}, {"type": "checkbox", "id": "show_badges", "default": true, "label": "Show badges"}, {"type": "checkbox", "id": "show_cart_button", "default": true, "label": "Show cart button"}, {"type": "checkbox", "id": "show_quick_view_button", "default": true, "label": "Show quick shop button"}, {"type": "checkbox", "id": "show_compare_view_button", "default": true, "label": "Show compare button"}, {"type": "checkbox", "id": "show_wishlist_button", "default": true, "label": "Show wishlist button"}, {"type": "checkbox", "id": "recent_viewd_product_rating", "default": false, "label": "Show product rating"}, {"type": "checkbox", "id": "recent_viewd_inventory_status", "label": "Show inventory status", "default": false}, {"type": "checkbox", "id": "product_card_radius", "label": "Round corner", "default": false}, {"type": "checkbox", "id": "product_card_spacing", "label": "Card spacing", "default": false}, {"type": "color_scheme", "id": "product_card_color_scheme", "label": "Product card color scheme", "default": "background-1"}]}, {"name": "Color swatches", "settings": [{"type": "checkbox", "id": "color_swatches", "default": true, "label": "Enable color swatches", "info": "Color selections are displayed in swatches on the product card, allowing you to swiftly select."}, {"type": "text", "id": "color_swatch_activation", "label": "Color swatch activation", "info": "Define the color option name. Eg. Color,Colour,etc. [Learn more](https://team90degree.com/suruchi-theme/theme-settings/color-swatches)", "default": "Color"}, {"type": "range", "id": "max_swatches_show", "min": 1, "max": 6, "step": 1, "default": 4, "label": "Maximum number of swatches to show"}, {"type": "select", "id": "color_swatch_type", "options": [{"value": "image", "label": "Variant image"}, {"value": "color", "label": "Color swatch"}], "default": "color", "label": "Swatch type"}, {"type": "header", "content": "Recently viewed product"}, {"type": "checkbox", "id": "color_swatches_recent_view_product", "default": false, "label": "Enable color swatches on recently viewed product"}]}, {"name": "Quick shop", "settings": [{"type": "select", "id": "quick_shop_type", "options": [{"value": "drawer", "label": "Drawer"}, {"value": "popup", "label": "Popup"}], "default": "drawer", "label": "Quick shop type"}, {"type": "select", "id": "varian_picker", "options": [{"value": "dropdown", "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__1.label"}, {"value": "button", "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__2.label"}], "default": "button", "label": "Variant picker type"}, {"type": "header", "content": "Color swatches", "info": "Required! The variant picker type must be 'Button'"}, {"type": "checkbox", "id": "show_color_swatch", "default": true, "label": "Enable color swatches"}, {"type": "text", "id": "choose_options_name", "default": "Color", "label": "Option name", "info": "To show the color/image on swatch button, define the color option name. Eg. Color,Colour,etc. [Learn more](https://team90degree.com/suruchi-theme/theme-settings/quick-shop)"}, {"type": "select", "id": "color_option_style", "options": [{"value": "image", "label": "Variant image"}, {"value": "color", "label": "Color swatch"}], "default": "color", "label": "Swatch type"}, {"type": "select", "id": "color_option_design", "options": [{"value": "round", "label": "Round"}, {"value": "square", "label": "Square"}], "default": "round", "label": "Swatch button style"}]}, {"name": "Wishlist page", "settings": [{"type": "header", "content": "Product card"}, {"type": "select", "id": "wishlist_card_style", "label": "Card style", "options": [{"value": "style_1", "label": "Style 1"}, {"value": "style_2", "label": "Style 2"}, {"value": "style_3", "label": "Style 3"}], "default": "style_1"}, {"type": "select", "id": "wishlist_image_ratio", "options": [{"value": "adapt", "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__1.label"}, {"value": "portrait", "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__2.label"}, {"value": "square", "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__3.label"}, {"value": "landscape", "label": "Landscape"}], "default": "adapt", "label": "t:sections.main-collection-product-grid.settings.image_ratio.label"}, {"type": "checkbox", "id": "wishlist_show_secondary_image", "default": false, "label": "t:sections.main-collection-product-grid.settings.show_secondary_image.label"}, {"type": "checkbox", "id": "wishlist_color_swatches", "default": true, "label": "Enable color swatches", "info": "To display color swatches, you need to enable it. [Learn more](https://help.team90degree.com/docs/grocee-meatfields/)."}, {"type": "checkbox", "id": "wishlist_show_badges", "default": true, "label": "Show badges"}, {"type": "checkbox", "id": "wishlist_show_cart_button", "default": true, "label": "Show cart button"}, {"type": "checkbox", "id": "wishlist_show_quick_view_button", "default": true, "label": "Show quick view"}, {"type": "checkbox", "id": "wishlist_show_compare_view_button", "default": true, "label": "Show compare button"}, {"type": "checkbox", "id": "wishlist_show_wishlist_button", "default": true, "label": "Show wishlist button"}, {"type": "checkbox", "id": "wishlist_show_title", "default": true, "label": "Show title"}, {"type": "checkbox", "id": "wishlist_show_price", "default": true, "label": "Show price"}, {"type": "checkbox", "id": "wishlist_show_vendor", "default": false, "label": "t:sections.main-collection-product-grid.settings.show_vendor.label"}, {"type": "checkbox", "id": "wishlist_show_countdown", "default": false, "label": "Show countdown"}, {"type": "checkbox", "id": "wishlist_show_product_rating", "default": false, "label": "Show product rating"}]}, {"name": "t:settings_schema.social-media.name", "settings": [{"type": "header", "content": "t:settings_schema.social-media.settings.header.content"}, {"type": "text", "id": "social_twitter_link", "label": "t:settings_schema.social-media.settings.social_twitter_link.label", "info": "t:settings_schema.social-media.settings.social_twitter_link.info"}, {"type": "text", "id": "social_facebook_link", "label": "t:settings_schema.social-media.settings.social_facebook_link.label", "info": "t:settings_schema.social-media.settings.social_facebook_link.info"}, {"type": "text", "id": "social_pinterest_link", "label": "t:settings_schema.social-media.settings.social_pinterest_link.label", "info": "t:settings_schema.social-media.settings.social_pinterest_link.info"}, {"type": "text", "id": "social_instagram_link", "label": "t:settings_schema.social-media.settings.social_instagram_link.label", "info": "t:settings_schema.social-media.settings.social_instagram_link.info"}, {"type": "text", "id": "social_tiktok_link", "label": "t:settings_schema.social-media.settings.social_tiktok_link.label", "info": "t:settings_schema.social-media.settings.social_tiktok_link.info"}, {"type": "text", "id": "social_tumblr_link", "label": "t:settings_schema.social-media.settings.social_tumblr_link.label", "info": "t:settings_schema.social-media.settings.social_tumblr_link.info"}, {"type": "text", "id": "social_snapchat_link", "label": "t:settings_schema.social-media.settings.social_snapchat_link.label", "info": "t:settings_schema.social-media.settings.social_snapchat_link.info"}, {"type": "text", "id": "social_youtube_link", "label": "t:settings_schema.social-media.settings.social_youtube_link.label", "info": "t:settings_schema.social-media.settings.social_youtube_link.info"}, {"type": "text", "id": "social_vimeo_link", "label": "t:settings_schema.social-media.settings.social_vimeo_link.label", "info": "t:settings_schema.social-media.settings.social_vimeo_link.info"}]}, {"name": "<PERSON><PERSON>", "settings": [{"type": "select", "id": "cart_type", "options": [{"value": "drawer", "label": "Drawer"}, {"value": "page", "label": "Page"}], "default": "drawer", "label": "Cart Type"}, {"type": "header", "content": "<PERSON><PERSON> Drawer"}, {"type": "checkbox", "id": "show_vendor_cart_drawer", "default": false, "label": "Show vendor"}, {"type": "checkbox", "id": "cart_note_enable", "default": true, "label": "Show cart note"}, {"type": "checkbox", "id": "shipping_calc_enable", "default": true, "label": "Show shipping calculator"}, {"type": "checkbox", "id": "coupon_enable", "default": true, "label": "Show coupon"}, {"type": "checkbox", "id": "checkout_btn_enable", "default": true, "label": "Show checkout button"}, {"type": "checkbox", "id": "cart_btn_enable", "default": true, "label": "Show cart button"}, {"type": "checkbox", "id": "continue_shopping_enable", "default": true, "label": "Show continue shopping button"}, {"type": "url", "id": "continue_shopping_link", "label": "Button link", "default": "/collections/all"}, {"type": "header", "content": "Free shipping message", "info": "Make sure that you have properly configured your [shipping rates.](/admin/settings/shipping)"}, {"type": "checkbox", "id": "free_shipping_message", "default": true, "label": "Show free shipping message"}, {"type": "number", "id": "free_shipping_amount", "label": "Free shipping minimum amount", "placeholder": "150", "default": 150}, {"type": "text", "id": "free_shipping_only_amount", "default": "25", "label": "Free shipping only amount", "info": "Minimum amount required to display \"only\" the title on the shipping message"}, {"type": "color", "id": "shipping_rate_low", "default": "#ee2761", "label": "Shipping low color"}, {"type": "color", "id": "shipping_rate_medium", "default": "#e0b252", "label": "Shipping medium color"}, {"type": "color", "id": "shipping_rate_high", "default": "#23b2c7", "label": "Shipping high color"}, {"type": "color", "id": "shipping_rate_sucess", "default": "#428445", "label": "Shipping success color"}]}, {"name": "t:settings_schema.search_input.name", "settings": [{"type": "header", "content": "t:settings_schema.search_input.settings.header.content"}, {"type": "checkbox", "id": "predictive_search_enabled", "default": true, "label": "t:settings_schema.search_input.settings.predictive_search_enabled.label"}, {"type": "select", "id": "search_type", "options": [{"value": "vendor", "label": "<PERSON><PERSON><PERSON>"}, {"value": "product_type", "label": "Product type"}, {"value": "tag", "label": "Tag"}, {"value": "none", "label": "None"}], "default": "vendor", "label": "Search filter type"}, {"type": "textarea", "id": "search_filter_tags", "label": "Search filter tags", "default": "T-<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>", "info": "Separate by \",\" | The tags will be displayed when you select \"Search filter type: Tag\"", "placeholder": "Vintage, T<PERSON><PERSON>rt, <PERSON>, <PERSON><PERSON>"}, {"type": "range", "id": "number_of_search_result", "min": 1, "max": 10, "step": 1, "default": 4, "label": "Number of search results"}, {"type": "checkbox", "id": "predictive_search_show_vendor", "default": false, "label": "t:settings_schema.search_input.settings.predictive_search_show_vendor.label", "info": "t:settings_schema.search_input.settings.predictive_search_show_vendor.info"}, {"type": "checkbox", "id": "predictive_search_show_price", "default": true, "label": "t:settings_schema.search_input.settings.predictive_search_show_price.label", "info": "t:settings_schema.search_input.settings.predictive_search_show_price.info"}, {"type": "select", "id": "show_unavailable_product", "options": [{"value": "show", "label": "Show"}, {"value": "hide", "label": "<PERSON>de"}, {"value": "last", "label": "Show unavailable after available"}], "default": "last", "label": "Show unavailable products"}, {"type": "header", "content": "Result types", "info": "In addition to products, you may display other types of results."}, {"type": "checkbox", "id": "show_collection", "default": true, "label": "Show collection"}, {"type": "checkbox", "id": "show_article", "default": true, "label": "Show articles"}, {"type": "checkbox", "id": "show_page", "default": true, "label": "Show pages"}, {"type": "header", "content": "Most searched products"}, {"type": "text", "id": "most_searched_product_heading", "label": "Heading", "default": "Most searched products:"}, {"type": "collection", "id": "most_searced_products_collection", "label": "Collection recommended", "info": "Select a collection to recommend for customers"}, {"type": "range", "id": "most_searced_products_limit", "min": 2, "max": 50, "step": 1, "default": 8, "label": "Collection recommended product limit"}, {"type": "header", "content": "Popular searches"}, {"type": "text", "id": "popular_search_heading", "label": "Heading", "default": "Popular searches:"}, {"type": "textarea", "id": "popular_search_queries", "label": "Popular searches", "default": "Stuffed Toys, Soft, Toy, Cat", "info": "Separate by \",\"", "placeholder": "Soft, Toy, Cat, Dog"}]}, {"name": "Customer/account", "settings": [{"type": "header", "content": "Page title bar"}, {"type": "checkbox", "id": "account_page_title_bar", "default": true, "label": "Show page title bar", "info": "This will only be displayed on the customer's account and address pages"}, {"type": "checkbox", "id": "account_page_title", "default": true, "label": "Show page heading"}, {"type": "checkbox", "id": "account_page_breadcrumb", "default": true, "label": "Show breadcrumb navigation"}, {"type": "color_scheme", "id": "customer_color_scheme", "label": "t:sections.all.colors.label", "default": "background-2"}, {"type": "header", "content": "Desktop padding"}, {"type": "range", "id": "account_page_title_padding_top", "min": 0, "max": 150, "step": 5, "unit": "px", "label": "Padding top", "default": 30}, {"type": "range", "id": "account_page_title_padding_bottom", "min": 0, "max": 150, "step": 5, "unit": "px", "label": "Padding bottom", "default": 30}, {"type": "header", "content": "Mobile padding"}, {"type": "range", "id": "account_page_title_padding_top_sm", "min": 0, "max": 150, "step": 5, "unit": "px", "label": "Padding top", "default": 30}, {"type": "range", "id": "account_page_title_padding_bottom_sm", "min": 0, "max": 150, "step": 5, "unit": "px", "label": "Padding bottom", "default": 30}]}]