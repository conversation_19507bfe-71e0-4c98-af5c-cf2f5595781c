{%- if cart.cart_level_discount_applications.size > 0 -%}
<div class="cart_notification--discount mb-20">
  <ul class="discounts list-unstyled" role="list" aria-label="{{ 'customer.order.discount' | t }}">
    {%- for discount in cart.cart_level_discount_applications -%}
    <li class="discounts__discount d-flex justify-content-between">

      <span class="discount__title">
        {%- render 'icon-discount' -%}
        {{ discount.title }}
      </span>

      <span class="discount__amount">  (-{{ discount.total_allocated_amount | money }})</span>


    </li>
    {%- endfor -%}
  </ul>
</div>
{%- endif -%}
</div>