{% comment %}
  © Sections Pro. You are free to use this section in your store. You may not redistribute this section in another Shopify app.
{% endcomment %}
<style>

  {% assign short_id = section.id | slice: -3, 3 %}
  
  

  {%- capture sp_content -%} 

  {% if section.settings.override_fonts %}
      {{ section.settings.text_font | font_face }}
      {{ section.settings.headline_font | font_face }}
  {% endif %}

  #spro-{{ section.id }} p {
      {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.text_size }}px;
      {% endif %}
      {% if section.settings.override_fonts %}
      font-family: {{ section.settings.text_font.family }}, {{ section.settings.text_font.fallback_families }};
      font-weight: {{ section.settings.text_font.weight }};
      {% endif %}
      {% if section.settings.text_line_height != 'inherit' %}line-height: {{ section.settings.text_line_height }};{% endif %}
      margin: 0 0 {{ section.settings.element_spacing }}px 0;
      padding: 0;
      {% if section.settings.override_text_colors %}
      color: {{ section.settings.text_color }};
      {% endif %}
  }

  #spro-{{ section.id }} div.spro-richtext {
    margin: 0 0 {{ section.settings.element_spacing }}px 0;
  }

  #spro-{{ section.id }} ul, #spro-{{ section.id }} ol {
    margin: 0 0 {{ section.settings.element_spacing }}px 0;
  }

  #spro-{{ section.id }} li {
    {% if section.settings.override_text_sizes %}
    font-size: {{ section.settings.text_size }}px;
    {% endif %}
    {% if section.settings.override_fonts %}
    font-family: {{ section.settings.text_font.family }}, {{ section.settings.text_font.fallback_families }};
    font-weight: {{ section.settings.text_font.weight }};
    {% endif %}
    {% if section.settings.text_line_height != 'inherit' %}line-height: {{ section.settings.text_line_height }};{% endif %}
    margin: 0 0 5px 0;
    padding: 0;
    {% if section.settings.override_text_colors %}
    color: {{ section.settings.text_color }};
    {% endif %}
  }

  #spro-{{ section.id }} li:last-child {
    margin: 0;
  }

  #spro-{{ section.id }} p a,
  #spro-{{ section.id }} p a:visited
  #spro-{{ section.id }} li a,
  #spro-{{ section.id }} li a:visited {
    {% if section.settings.override_text_colors %}
    color: {{ section.settings.link_color }};
    {% endif %}
  }

  @media (max-width: 767px) {
      #spro-{{ section.id }} p,
      #spro-{{ section.id }} li {
        {% if section.settings.override_text_sizes %}
        font-size: {{ section.settings.mobile_text_size }}px;
        {% endif %}
      }
  }

  #spro-{{ section.id }} h1,
  #spro-{{ section.id }} h2,
  #spro-{{ section.id }} h3,
  #spro-{{ section.id }} h4,
  #spro-{{ section.id }} h5 {
      {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.headline_size }}px;
      {% endif %}
      {% if section.settings.override_fonts %}
      font-family: {{ section.settings.headline_font.family }}, {{ section.settings.headline_font.fallback_families }};
      font-weight: {{ section.settings.headline_font.weight }};
      {% endif %}
      {% if section.settings.headline_line_height != 'inherit' %}line-height: {{ section.settings.headline_line_height }};{% endif %}
      margin: 0 0 {{ section.settings.element_spacing }}px 0;
      padding: 0;
      {% if section.settings.override_text_colors %}
      color: {{ section.settings.text_color }};
      {% endif %}
  }

  @media (max-width: 767px) {
      #spro-{{ section.id }} h1,
      #spro-{{ section.id }} h2,
      #spro-{{ section.id }} h3,
      #spro-{{ section.id }} h4,
      #spro-{{ section.id }} h5 {
        {% if section.settings.override_text_sizes %}
          font-size: {{ section.settings.mobile_headline_size }}px;
        {% endif %}
      }
  }

  #spro-{{ section.id }} h2 {
    {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.headline_size | minus:5  | at_least:13 }}px;
    {% endif %}
  }

  #spro-{{ section.id }} h3 {
    {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.headline_size | minus:10 | at_least:13 }}px;
    {% endif %}
  }

  #spro-{{ section.id }} h4 {
    {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.headline_size | minus:15 | at_least:13 }}px;
    {% endif %}
  }

  #spro-{{ section.id }} h5 {
    {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.headline_size | minus:20 | at_least:13 }}px;
    {% endif %}
  }

  @media (max-width: 767px) {
      #spro-{{ section.id }} h2 {
        {% if section.settings.override_text_sizes %}
          font-size: {{ section.settings.mobile_headline_size | minus:5 | at_least:13 }}px;
        {% endif %}
      }

      #spro-{{ section.id }} h3 {
        {% if section.settings.override_text_sizes %}
          font-size: {{ section.settings.mobile_headline_size | minus:10 | at_least:13 }}px;
        {% endif %}
      }

      #spro-{{ section.id }} h4 {
        {% if section.settings.override_text_sizes %}
        font-size: {{ section.settings.mobile_headline_size | minus:15 | at_least:13 }}px;
      {% endif %}
      }

      #spro-{{ section.id }} h5 {
        {% if section.settings.override_text_sizes %}
          font-size: {{ section.settings.mobile_headline_size | minus:20 | at_least:13 }}px;
        {% endif %}
      }
  }

  #spro-{{ section.id }} {
      background-image: {{ section.settings.section_background_color }};
      {% if section.settings.section_background_image %}
          background: {% if section.settings.section_background_image_color %}{{ section.settings.section_background_image_color }}{%endif%} url({{ section.settings.section_background_image | image_url }});
          background-repeat: no-repeat;
          background-position: center;
          width: 100%;
          background-size: {{ section.settings.section_background_size }};
      {% endif %}
      width: 100%;
      box-sizing: border-box;
      padding: {{ section.settings.section_padding_top_bottom }}px {{ section.settings.section_padding_left_right }}px;
      overflow: hidden;
  }

  {% if section.settings.mobile_section_background_image %}
  @media (max-width: 767px) {
    #spro-{{ section.id }} {
          background: {% if section.settings.mobile_section_background_image_color %}{{ section.settings.mobile_section_background_image_color }}{%endif%} url({{ section.settings.mobile_section_background_image | image_url }});
          background-repeat: no-repeat;
          background-position: center;
          width: 100%;
          background-size: {{ section.settings.mobile_section_background_size }};
    }
  }
  {% endif %}

  @media (max-width: 767px) {
      #spro-{{ section.id }} {
        padding: {{ section.settings.mobile_section_padding_top_bottom }}px {{ section.settings.mobile_section_padding_left_right }}px;
      }
  }

  {% if section.settings.show_on_device == 'mobile' %}
    @media (min-width: 768px) {
        #spro-{{ section.id }} {
            display: none;
        }
    }
  {% endif %}

  {% if section.settings.show_on_device == 'desktop' %}
    @media (max-width: 767px) {
        #spro-{{ section.id }} {
            display: none;
        }
    }
  {% endif %}

  #spro-{{ section.id }} .spro-container {
      position: relative;
      margin: 0 auto;
      background-image: {{ section.settings.container_background_color }};
      border-radius: {{ section.settings.container_radius }}px;
      {% if section.settings.container_shadow %}box-shadow: 0 0 5px 0 rgba(0,0,0,0.20);{% endif %}
      border: {{ section.settings.container_border_size }}px solid {{ section.settings.container_border_color }};
      max-width: {{ section.settings.container_max_width }}px;
      padding: {{ section.settings.container_padding_top_bottom }}px {{ section.settings.container_padding_left_right }}px;
  }

  @media (max-width: 767px) {
      #spro-{{ section.id }} .spro-container {
      padding: {{ section.settings.mobile_container_padding_top_bottom }}px {{ section.settings.mobile_container_padding_left_right }}px;
      }
  }


  #spro-{{ section.id }} .spro-headline {
    margin: 0;
    padding: 0 0 {{ section.settings.headline_spacing }}px 0;
  }

  #spro-{{ section.id }} .spro-headline * {
    text-align: {{ section.settings.text_alignment }};
  }

  @media only screen and (max-width: 800px) {
    #spro-{{ section.id }} .spro-headline * {
      text-align: {{ section.settings.mobile_text_alignment }};
    }
  }

  @keyframes slpProgressBar {
    0% { width: 0; }
    100% { width: 100%; }
  }


  #spro-{{ section.id }} .spro-grid {
    display: grid;
    gap: {{ section.settings.grid_gap}}px;
    position: relative;
    z-index: 2;
  }

  @media only screen and (min-width: 801px) {
    #spro-{{ section.id }} .spro-grid {
      display: grid;
      grid-auto-columns: 1fr;
      grid-auto-flow: column;

    }
  }

  @media only screen and (max-width: 800px) {
    {% if section.settings.mobile_display == 'slideshow' %}
    #spro-{{ section.id }} .spro-grid {
      display: grid;
      grid-auto-columns: 100%;
      grid-auto-flow: column;
      width: 100%;
      overflow-x: scroll;
      scroll-snap-type: x mandatory;
      scrollbar-width: none;
    }

    #spro-{{ section.id }} .spro-grid::-webkit-scrollbar {
      display: none;  /* Safari and Chrome */
    }

    #spro-{{ section.id }} .spro-col {
      scroll-snap-align: start;
    }
    {% else %}
      #spro-{{ section.id }} .spro-grid {
        display: grid;
        grid-template-columns: {{ section.settings.mobile_columns }};
      }
    {% endif %}
  }

  #spro-{{ section.id }} .spro-col {
    position: relative;
    padding: 0;
    overflow: hidden;
  }

  {% assign icon_height =  section.settings.icon_size | plus: section.settings.icon_padding %}
  {% assign icon_offset =  icon_height | minus: section.settings.icon_size | divided_by: 2 %}

  #spro-{{ section.id }} .spro-icon-container {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      {% if section.settings.icon_shape == 'rounded' %}
        border-radius: 5px;
      {% endif %}
      {% if section.settings.icon_shape == 'circle' %}
        border-radius: 50%;
      {% endif %}
      background-image: {{ section.settings.icon_background_color }};
      transition: all 0.5s ease;
      width: {{ icon_height }}px;
      height: {{ icon_height }}px;
      text-align: center;
      margin: 0 0 {{ section.settings.element_spacing }}px 0;
      overflow: hidden;
    }

    #spro-{{ section.id }} .spro-icon-container svg {
      fill: {{ section.settings.icon_color }};
      width: {{ section.settings.icon_size }}px;
      height: {{ section.settings.icon_size }}px;
      display: inline-block;
    }

    #spro-{{ section.id }} .spro-icon-container img {
      margin: 0;
      padding: 0;
      {% if section.settings.media_fit == 'size' %}
        width: {{ section.settings.media_height }}px;
      {% else %}
        width: 100%;
        height: 100%;
        object-fit: {{ section.settings.media_fit }};
      {% endif %}
    }

    #spro-{{ section.id }} .spro-icon-container video {
      margin: 0;
      padding: 0;
      {% if section.settings.media_fit == 'size' %}
        max-width: {{ section.settings.media_height }}px;
      {% else %}
        width: 100%;
        height: 100%;
        object-fit: {{ section.settings.media_fit }};
      {% endif %}
    }

  #spro-{{ section.id }} .spro-col:hover .spro-icon-container {
    transform: scale(1.05);
  }

  #spro-{{ section.id }} .spro-content {
    position: relative;
    z-index: 3;
    padding: 10px 0 0 0;
    text-align: {{ section.settings.icon_text_alignment }};
  }

  #spro-{{ section.id }} .spro-content h4 {
    font-size: {{ section.settings.icon_headline_size }}px;
    margin: 0 0 5px 0;
    padding: 0;
    text-align: {{ section.settings.icon_text_alignment }};
  }

  #spro-{{ section.id }} .spro-content p {
    margin: 0;
    padding: 0;
    {% if section.settings.override_text_colors %}
      color: {{ section.settings.text_color }};
    {% endif %}
    text-align: {{ section.settings.icon_text_alignment }};
  }

  @media only screen and (max-width: 800px) {
    #spro-{{ section.id }} .spro-content, 
    #spro-{{ section.id }} .spro-content h4,
    #spro-{{ section.id }} .spro-content p {
      text-align: {{ section.settings.mobile_icon_text_alignment }};
    }
  }


  {% if section.settings.mobile_display == 'slideshow' %}

  /* snap links */
  #spro-{{ section.id }} .spro-snap-links {
    padding: 0;
  }

  #spro-snap-links-{{ section.id }} a {
      display: inline-block;
      background-color: {{ section.settings.indicator_color }};
      width: 12px;
      height: 12px;
      border-radius: 50%;
      text-indent: -9999px;
      padding: 0;
      margin: 0 2.5px;
      cursor: pointer;
      opacity: 0.25;
  }

  #spro-snap-links-{{ section.id }} a[active] {
      background-color: {{ section.settings.indicator_color }};
      opacity: 1;
  }

  #spro-{{ section.id }}[autoplaying] #spro-snap-links-{{ section.id }} a[active] {
      background: {{ section.settings.progress_background_color }};
      opacity: 1;
      width: 25px;
      transition: all .5s;
      border-radius: 10px;
      overflow: hidden;
  }

  #spro-{{ section.id }}[autoplaying] #spro-snap-links-{{ section.id }} a[active]>span {
    display: block;
    background: {{ section.settings.progress_foreground_color }};
    height: 100%;
    animation: slpProgressBar {{ section.settings.autoplay_time }}ms ease-in-out;
    animation-fill-mode: both;
  }


  #spro-snap-links-{{ section.id }} {
    display: none;
  }

  @media only screen and (max-width: 800px) {

    #spro-snap-links-{{ section.id }} {
      display: block;
      text-align: center;
    }
  }

  {% endif %}
  {%- endcapture -%} 

  {%- liquid
    assign chunks = sp_content | strip_newlines | split: ' ' | join: ' ' | split: '*/'
    for chunk in chunks
      assign mini = chunk | split: '/*' | first | strip | replace: ': ', ':' | replace: '; ', ';' | replace: '} ', '}' | replace: '{ ', '{' | replace: ' {', '{' | replace: ';}', '}'
      echo mini
    endfor
  %}
</style>

<div id="spro-{{ section.id }}" class="spro-section" spro-section>

  <div class="spro-container" spro-container>

    {% if section.settings.headline != '' or section.settings.text != '' %}
      <div class="spro-headline" spro-column>
        {% if section.settings.headline != '' %}<h2>{{ section.settings.headline }}</h2>{% endif %}
        {% if section.settings.text != '' %}<p>{{ section.settings.text }}</p>{% endif %}
      </div>
    {% endif %}

    <div id="spro-carousel-{{ section.id }}" class="spro-grid">
      {% for block in section.blocks %}
        <div class="spro-col" data-index="{{ forloop.index | minus: 1 }}" spro-column>
          {% if block.type == 'icon' %}
            <div class="spro-content">
              {% if block.settings.link %}
                <a href="{{ block.settings.link }}">
              {% endif %}

              <div class="spro-icon-container">
                {% if block.settings.icon == 'cart' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M280-80q-33 0-56.5-23.5T200-160q0-33 23.5-56.5T280-240q33 0 56.5 23.5T360-160q0 33-23.5 56.5T280-80Zm400 0q-33 0-56.5-23.5T600-160q0-33 23.5-56.5T680-240q33 0 56.5 23.5T760-160q0 33-23.5 56.5T680-80ZM246-720l96 200h280l110-200H246Zm-38-80h590q23 0 35 20.5t1 41.5L692-482q-11 20-29.5 31T622-440H324l-44 80h480v80H280q-45 0-68-39.5t-2-78.5l54-98-144-304H40v-80h130l38 80Zm134 280h280-280Z"/>
                  </svg>
                {% elsif block.settings.icon == 'gift' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M160-80v-440H80v-240h208q-5-9-6.5-19t-1.5-21q0-50 35-85t85-35q23 0 43 8.5t37 23.5q17-16 37-24t43-8q50 0 85 35t35 85q0 11-2 20.5t-6 19.5h208v240h-80v440H160Zm400-760q-17 0-28.5 11.5T520-800q0 17 11.5 28.5T560-760q17 0 28.5-11.5T600-800q0-17-11.5-28.5T560-840Zm-200 40q0 17 11.5 28.5T400-760q17 0 28.5-11.5T440-800q0-17-11.5-28.5T400-840q-17 0-28.5 11.5T360-800ZM160-680v80h280v-80H160Zm280 520v-360H240v360h200Zm80 0h200v-360H520v360Zm280-440v-80H520v80h280Z"/>
                  </svg>
                {% elsif block.settings.icon == 'gift-card' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M160-280v80h640v-80H160Zm0-440h88q-5-9-6.5-19t-1.5-21q0-50 35-85t85-35q30 0 55.5 15.5T460-826l20 26 20-26q18-24 44-39t56-15q50 0 85 35t35 85q0 11-1.5 21t-6.5 19h88q33 0 56.5 23.5T880-640v440q0 33-23.5 56.5T800-120H160q-33 0-56.5-23.5T80-200v-440q0-33 23.5-56.5T160-720Zm0 320h640v-240H596l84 114-64 46-136-184-136 184-64-46 82-114H160v240Zm200-320q17 0 28.5-11.5T400-760q0-17-11.5-28.5T360-800q-17 0-28.5 11.5T320-760q0 17 11.5 28.5T360-720Zm240 0q17 0 28.5-11.5T640-760q0-17-11.5-28.5T600-800q-17 0-28.5 11.5T560-760q0 17 11.5 28.5T600-720Z"/>
                  </svg>
                {% elsif block.settings.icon == 'shipping' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M240-160q-50 0-85-35t-35-85H40v-440q0-33 23.5-56.5T120-800h560v160h120l120 160v200h-80q0 50-35 85t-85 35q-50 0-85-35t-35-85H360q0 50-35 85t-85 35Zm0-80q17 0 28.5-11.5T280-280q0-17-11.5-28.5T240-320q-17 0-28.5 11.5T200-280q0 17 11.5 28.5T240-240ZM120-360h32q17-18 39-29t49-11q27 0 49 11t39 29h272v-360H120v360Zm600 120q17 0 28.5-11.5T760-280q0-17-11.5-28.5T720-320q-17 0-28.5 11.5T680-280q0 17 11.5 28.5T720-240Zm-40-200h170l-90-120h-80v120ZM360-540Z"/>
                  </svg>
                {% elsif block.settings.icon == 'payment' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M560-440q-50 0-85-35t-35-85q0-50 35-85t85-35q50 0 85 35t35 85q0 50-35 85t-85 35ZM280-320q-33 0-56.5-23.5T200-400v-320q0-33 23.5-56.5T280-800h560q33 0 56.5 23.5T920-720v320q0 33-23.5 56.5T840-320H280Zm80-80h400q0-33 23.5-56.5T840-480v-160q-33 0-56.5-23.5T760-720H360q0 33-23.5 56.5T280-640v160q33 0 56.5 23.5T360-400Zm440 240H120q-33 0-56.5-23.5T40-240v-440h80v440h680v80ZM280-400v-320 320Z"/>
                  </svg>
                {% elsif block.settings.icon == 'trending' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="m136-240-56-56 296-298 160 160 208-206H640v-80h240v240h-80v-104L536-320 376-480 136-240Z"/>
                  </svg>
                {% elsif block.settings.icon == 'tag' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M570-104q-23 23-57 23t-57-23L104-456q-11-11-17.5-26T80-514v-286q0-33 23.5-56.5T160-880h286q17 0 32 6.5t26 17.5l352 353q23 23 23 56.5T856-390L570-104Zm-57-56 286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640ZM160-800Z"/>
                  </svg>
                {% elsif block.settings.icon == 'bag' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M200-80q-33 0-56.5-23.5T120-160v-480q0-33 23.5-56.5T200-720h80q0-83 58.5-141.5T480-920q83 0 141.5 58.5T680-720h80q33 0 56.5 23.5T840-640v480q0 33-23.5 56.5T760-80H200Zm0-80h560v-480H200v480Zm280-240q83 0 141.5-58.5T680-600h-80q0 50-35 85t-85 35q-50 0-85-35t-35-85h-80q0 83 58.5 141.5T480-400ZM360-720h240q0-50-35-85t-85-35q-50 0-85 35t-35 85ZM200-160v-480 480Z"/>
                  </svg>
                {% elsif block.settings.icon == 'heart' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="m480-120-58-52q-101-91-167-157T150-447.5Q111-500 95.5-544T80-634q0-94 63-157t157-63q52 0 99 22t81 62q34-40 81-62t99-22q94 0 157 63t63 157q0 46-15.5 90T810-447.5Q771-395 705-329T538-172l-58 52Zm0-108q96-86 158-147.5t98-107q36-45.5 50-81t14-70.5q0-60-40-100t-100-40q-47 0-87 26.5T518-680h-76q-15-41-55-67.5T300-774q-60 0-100 40t-40 100q0 35 14 70.5t50 81q36 45.5 98 107T480-228Zm0-273Z"/>
                  </svg>
                {% elsif block.settings.icon == 'star' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="m354-247 126-76 126 77-33-144 111-96-146-13-58-136-58 135-146 13 111 97-33 143ZM233-80l65-281L80-550l288-25 112-265 112 265 288 25-218 189 65 281-247-149L233-80Zm247-350Z"/>
                  </svg>
                {% elsif block.settings.icon == 'leaf' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M216-176q-45-45-70.5-104T120-402q0-63 24-124.5T222-642q35-35 86.5-60t122-39.5Q501-756 591.5-759t202.5 7q8 106 5 195t-16.5 160.5q-13.5 71.5-38 125T684-182q-53 53-112.5 77.5T450-80q-65 0-127-25.5T216-176Zm112-16q29 17 59.5 24.5T450-160q46 0 91-18.5t86-59.5q18-18 36.5-50.5t32-85Q709-426 716-500.5t2-177.5q-49-2-110.5-1.5T485-670q-61 9-116 29t-90 55q-45 45-62 89t-17 85q0 59 22.5 103.5T262-246q42-80 111-153.5T534-520q-72 63-125.5 142.5T328-192Zm0 0Zm0 0Z"/>
                  </svg>
                {% elsif block.settings.icon == 'paw' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M180-475q-42 0-71-29t-29-71q0-42 29-71t71-29q42 0 71 29t29 71q0 42-29 71t-71 29Zm180-160q-42 0-71-29t-29-71q0-42 29-71t71-29q42 0 71 29t29 71q0 42-29 71t-71 29Zm240 0q-42 0-71-29t-29-71q0-42 29-71t71-29q42 0 71 29t29 71q0 42-29 71t-71 29Zm180 160q-42 0-71-29t-29-71q0-42 29-71t71-29q42 0 71 29t29 71q0 42-29 71t-71 29ZM266-75q-45 0-75.5-34.5T160-191q0-52 35.5-91t70.5-77q29-31 50-67.5t50-68.5q22-26 51-43t63-17q34 0 63 16t51 42q28 32 49.5 69t50.5 69q35 38 70.5 77t35.5 91q0 47-30.5 81.5T694-75q-54 0-107-9t-107-9q-54 0-107 9t-107 9Z"/>
                  </svg>
                {% elsif block.settings.icon == 'shield' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="m438-338 226-226-57-57-169 169-84-84-57 57 141 141Zm42 258q-139-35-229.5-159.5T160-516v-244l320-120 320 120v244q0 152-90.5 276.5T480-80Zm0-84q104-33 172-132t68-220v-189l-240-90-240 90v189q0 121 68 220t172 132Zm0-316Z"/>
                  </svg>
                {% elsif block.settings.icon == 'percent' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M300-520q-58 0-99-41t-41-99q0-58 41-99t99-41q58 0 99 41t41 99q0 58-41 99t-99 41Zm0-80q25 0 42.5-17.5T360-660q0-25-17.5-42.5T300-720q-25 0-42.5 17.5T240-660q0 25 17.5 42.5T300-600Zm360 440q-58 0-99-41t-41-99q0-58 41-99t99-41q58 0 99 41t41 99q0 58-41 99t-99 41Zm0-80q25 0 42.5-17.5T720-300q0-25-17.5-42.5T660-360q-25 0-42.5 17.5T600-300q0 25 17.5 42.5T660-240Zm-444 80-56-56 584-584 56 56-584 584Z"/>
                  </svg>
                {% elsif block.settings.icon == 'timer' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M360-840v-80h240v80H360Zm80 440h80v-240h-80v240Zm40 320q-74 0-139.5-28.5T226-186q-49-49-77.5-114.5T120-440q0-74 28.5-139.5T226-694q49-49 114.5-77.5T480-800q62 0 119 20t107 58l56-56 56 56-56 56q38 50 58 107t20 119q0 74-28.5 139.5T734-186q-49 49-114.5 77.5T480-80Zm0-80q116 0 198-82t82-198q0-116-82-198t-198-82q-116 0-198 82t-82 198q0 116 82 198t198 82Zm0-280Z"/>
                  </svg>
                {% elsif block.settings.icon == 'bank' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M640-520q17 0 28.5-11.5T680-560q0-17-11.5-28.5T640-600q-17 0-28.5 11.5T600-560q0 17 11.5 28.5T640-520Zm-320-80h200v-80H320v80ZM180-120q-34-114-67-227.5T80-580q0-92 64-156t156-64h200q29-38 70.5-59t89.5-21q25 0 42.5 17.5T720-820q0 6-1.5 12t-3.5 11q-4 11-7.5 22.5T702-751l91 91h87v279l-113 37-67 224H480v-80h-80v80H180Zm60-80h80v-80h240v80h80l62-206 98-33v-141h-40L620-720q0-20 2.5-38.5T630-796q-29 8-51 27.5T547-720H300q-58 0-99 41t-41 99q0 98 27 191.5T240-200Zm240-298Z"/>
                  </svg>
                {% elsif block.settings.icon == 'box' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M200-640v440h560v-440H640v320l-160-80-160 80v-320H200Zm0 520q-33 0-56.5-23.5T120-200v-499q0-14 4.5-27t13.5-24l50-61q11-14 27.5-21.5T250-840h460q18 0 34.5 7.5T772-811l50 61q9 11 13.5 24t4.5 27v499q0 33-23.5 56.5T760-120H200Zm16-600h528l-34-40H250l-34 40Zm184 80v190l80-40 80 40v-190H400Zm-200 0h560-560Z"/>
                  </svg>
                {% elsif block.settings.icon == 'map' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="m600-120-240-84-186 72q-20 8-37-4.5T120-170v-560q0-13 7.5-23t20.5-15l212-72 240 84 186-72q20-8 37 4.5t17 33.5v560q0 13-7.5 23T812-192l-212 72Zm-40-98v-468l-160-56v468l160 56Zm80 0 120-40v-474l-120 46v468Zm-440-10 120-46v-468l-120 40v474Zm440-458v468-468Zm-320-56v468-468Z"/>
                  </svg>
                {% elsif block.settings.icon == 'package' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M440-183v-274L200-596v274l240 139Zm80 0 240-139v-274L520-457v274Zm-80 92L160-252q-19-11-29.5-29T120-321v-318q0-22 10.5-40t29.5-29l280-161q19-11 40-11t40 11l280 161q19 11 29.5 29t10.5 40v318q0 22-10.5 40T800-252L520-91q-19 11-40 11t-40-11Zm200-528 77-44-237-137-78 45 238 136Zm-160 93 78-45-237-137-78 45 237 137Z"/>
                  </svg>
                {% elsif block.settings.icon == 'return' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M314-115q-104-48-169-145T80-479q0-26 2.5-51t8.5-49l-46 27-40-69 191-110 110 190-70 40-54-94q-11 27-16.5 56t-5.5 60q0 97 53 176.5T354-185l-40 70Zm306-485v-80h109q-46-57-111-88.5T480-800q-55 0-104 17t-90 48l-40-70q50-35 109-55t125-20q79 0 151 29.5T760-765v-55h80v220H620ZM594 0 403-110l110-190 69 40-57 98q118-17 196.5-107T800-480q0-11-.5-20.5T797-520h81q1 10 1.5 19.5t.5 20.5q0 135-80.5 241.5T590-95l44 26-40 69Z"/>
                  </svg>
                {% elsif block.settings.icon == 'chat' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M80-80v-720q0-33 23.5-56.5T160-880h640q33 0 56.5 23.5T880-800v480q0 33-23.5 56.5T800-240H240L80-80Zm126-240h594v-480H160v525l46-45Zm-46 0v-480 480Z"/>
                  </svg>
                {% elsif block.settings.icon == 'email' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm320-280L160-640v400h640v-400L480-440Zm0-80 320-200H160l320 200ZM160-640v-80 480-400Z"/>
                  </svg>
                {% elsif block.settings.icon == 'handshake' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M475-160q4 0 8-2t6-4l328-328q12-12 17.5-27t5.5-30q0-16-5.5-30.5T817-607L647-777q-11-12-25.5-17.5T591-800q-15 0-30 5.5T534-777l-11 11 74 75q15 14 22 32t7 38q0 42-28.5 70.5T527-522q-20 0-38.5-7T456-550l-75-74-175 175q-3 3-4.5 6.5T200-435q0 8 6 14.5t14 6.5q4 0 8-2t6-4l136-136 56 56-135 136q-3 3-4.5 6.5T285-350q0 8 6 14t14 6q4 0 8-2t6-4l136-135 56 56-135 136q-3 2-4.5 6t-1.5 8q0 8 6 14t14 6q4 0 7.5-1.5t6.5-4.5l136-135 56 56-136 136q-3 3-4.5 6.5T454-180q0 8 6.5 14t14.5 6Zm-1 80q-37 0-65.5-24.5T375-166q-34-5-57-28t-28-57q-34-5-56.5-28.5T206-336q-38-5-62-33t-24-66q0-20 7.5-38.5T149-506l232-231 131 131q2 3 6 4.5t8 1.5q9 0 15-5.5t6-14.5q0-4-1.5-8t-4.5-6L398-777q-11-12-25.5-17.5T342-800q-15 0-30 5.5T285-777L144-635q-9 9-15 21t-8 24q-2 12 0 24.5t8 23.5l-58 58q-17-23-25-50.5T40-590q2-28 14-54.5T87-692l141-141q24-23 53.5-35t60.5-12q31 0 60.5 12t52.5 35l11 11 11-11q24-23 53.5-35t60.5-12q31 0 60.5 12t52.5 35l169 169q23 23 35 53t12 61q0 31-12 60.5T873-437L545-110q-14 14-32.5 22T474-80Zm-99-560Z"/>
                  </svg>
                {% elsif block.settings.icon == 'thumbsup' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M720-120H280v-520l280-280 50 50q7 7 11.5 19t4.5 23v14l-44 174h258q32 0 56 24t24 56v80q0 7-2 15t-4 15L794-168q-9 20-30 34t-44 14Zm-360-80h360l120-280v-80H480l54-220-174 174v406Zm0-406v406-406Zm-80-34v80H160v360h120v80H80v-520h200Z"/>
                  </svg>
                {% elsif block.settings.icon == 'gaming' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M189-160q-60 0-102.5-43T42-307q0-9 1-18t3-18l84-336q14-54 57-87.5t98-33.5h390q55 0 98 33.5t57 87.5l84 336q2 9 3.5 18.5T919-306q0 61-43.5 103.5T771-160q-42 0-78-22t-54-60l-28-58q-5-10-15-15t-21-5H385q-11 0-21 5t-15 15l-28 58q-18 38-54 60t-78 22Zm3-80q19 0 34.5-10t23.5-27l28-57q15-31 44-48.5t63-17.5h190q34 0 63 18t45 48l28 57q8 17 23.5 27t34.5 10q28 0 48-18.5t21-46.5q0 1-2-19l-84-335q-7-27-28-44t-49-17H285q-28 0-49.5 17T208-659l-84 335q-2 6-2 18 0 28 20.5 47t49.5 19Zm348-280q17 0 28.5-11.5T580-560q0-17-11.5-28.5T540-600q-17 0-28.5 11.5T500-560q0 17 11.5 28.5T540-520Zm80-80q17 0 28.5-11.5T660-640q0-17-11.5-28.5T620-680q-17 0-28.5 11.5T580-640q0 17 11.5 28.5T620-600Zm0 160q17 0 28.5-11.5T660-480q0-17-11.5-28.5T620-520q-17 0-28.5 11.5T580-480q0 17 11.5 28.5T620-440Zm80-80q17 0 28.5-11.5T740-560q0-17-11.5-28.5T700-600q-17 0-28.5 11.5T660-560q0 17 11.5 28.5T700-520Zm-360 60q13 0 21.5-8.5T370-490v-40h40q13 0 21.5-8.5T440-560q0-13-8.5-21.5T410-590h-40v-40q0-13-8.5-21.5T340-660q-13 0-21.5 8.5T310-630v40h-40q-13 0-21.5 8.5T240-560q0 13 8.5 21.5T270-530h40v40q0 13 8.5 21.5T340-460Zm140-20Z"/>
                  </svg>
                {% elsif block.settings.icon == 'support' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M480-40v-80h280v-40H600v-320h160v-40q0-116-82-198t-198-82q-116 0-198 82t-82 198v40h160v320H200q-33 0-56.5-23.5T120-240v-280q0-74 28.5-139.5T226-774q49-49 114.5-77.5T480-880q74 0 139.5 28.5T734-774q49 49 77.5 114.5T840-520v400q0 33-23.5 56.5T760-40H480ZM200-240h80v-160h-80v160Zm480 0h80v-160h-80v160ZM200-400h80-80Zm480 0h80-80Z"/>
                  </svg>
                {% elsif block.settings.icon == 'phone' %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="spro-svg" viewBox="0 -960 960 960">
                    <path d="M280-40q-33 0-56.5-23.5T200-120v-720q0-33 23.5-56.5T280-920h400q33 0 56.5 23.5T760-840v720q0 33-23.5 56.5T680-40H280Zm0-200v120h400v-120H280Zm200 100q17 0 28.5-11.5T520-180q0-17-11.5-28.5T480-220q-17 0-28.5 11.5T440-180q0 17 11.5 28.5T480-140ZM280-320h400v-400H280v400Zm0-480h400v-40H280v40Zm0 560v120-120Zm0-560v-40 40Z"/>
                  </svg>
                {% endif %}
              </div>

              <h4>{{ block.settings.headline }}</h4>
              <p>{{ block.settings.text }}</p>
              {% if block.settings.link %}
                </a>
              {% endif %}
            </div>
          {% endif %}

          {% if block.type == 'image' %}
            <div class="spro-content">
              {% if block.settings.link %}
                <a href="{{ block.settings.link }}">
              {% endif %}

              <div class="spro-icon-container">
                {% if block.settings.image %}
                  <img
                    src="{{ block.settings.image | image_url: width: 300 }}"
                    loading="lazy"
                    srcset="{{ block.settings.image | image_url: width: 300 }} 600w, {{ block.settings.image | image_url: width: 300 }} 1100w"
                  >
                {% else %}
                  <img src="data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg version='1.1' viewBox='0 0 500 500' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Crect x='2' y='2' width='496' height='496' fill='%23D8D8D8' stroke='%23D8D8D8' stroke-width='4'/%3E%3C/g%3E%3C/svg%3E">
                {% endif %}
              </div>

              <h4>{{ block.settings.headline }}</h4>
              <p>{{ block.settings.text }}</p>
              {% if block.settings.link %}
                </a>
              {% endif %}
            </div>
          {% endif %}

          {% if block.type == 'video' %}
            <div class="spro-content">
              {% if block.settings.link %}
                <a href="{{ block.settings.link }}">
              {% endif %}

              <div class="spro-icon-container">
                {{
                  block.settings.video
                  | video_tag:
                    image_size: '600x',
                    playsinline: block.settings.video_inline,
                    autoplay: block.settings.video_autoplay,
                    loop: block.settings.video_loop,
                    controls: block.settings.video_controls,
                    muted: block.settings.video_muted
                }}
              </div>

              <h4>{{ block.settings.headline }}</h4>
              <p>{{ block.settings.text }}</p>
              {% if block.settings.link %}
                </a>
              {% endif %}
            </div>
          {% endif %}
        </div>
        <!-- /.spro-col -->
      {% endfor %}
    </div>
    <!-- /.spro-grid -->

    {% if section.settings.mobile_display == 'slideshow' %}
      <div id="spro-snap-links-{{ section.id }}" class="spro-snap-links">
        {% for block in section.blocks %}
          <a data-index="{{ forloop.index | minus: 1 }}"><span></span></a>
        {% endfor %}
      </div>
    {% endif %}
  </div>
  <!-- /.spro-container -->
</div>
<!-- /.spro-section -->

{% if section.settings.mobile_display == 'slideshow' %}
  <script>
  
    // mobile only
    if(window.innerWidth <= 800 ) {
  
      // create new Sections Pro slideshow
      class SpSlideShow_{{ short_id }} {
  
        constructor(section, carousel, enable_autoplay, autoplay_time, slides, links) {
  
          console.log(`[spslideshow] setup`);      
  
          this.section = section;
          this.carousel = carousel;
          this.slides = slides;
          this.links = links;
          this.index = 0;
          this.interval = null;
          this.observer = null;
          this.direction = 'up';
          this.autoplay_time = autoplay_time;
          this.autoplay_is_setup = false;
  
          // enable autoplay
          if(enable_autoplay) {
            console.log(`[spslideshow] enable auotplay`);  
            this.setupAutoplayObserver();
            this.setupClearEvents();
          }
  
          // setup links and observer
          this.setupLinks();
          this.setupObserver();
        }
  
        // don't start autoplay until the slideshow is visible
        setupAutoplayObserver() {
          var context = this;
  
          // use intersection observer to render dots
          this.observer = new IntersectionObserver(function(entries, observer) {
  
            // Loop over the entries
            entries.forEach(entry => {
              // If the element is visible
              if (entry.isIntersecting) {
                if(context.autoplay_is_setup == false) {
                  context.setupAutoplay();
                  context.autoplay_is_setup = true;
                }
              }
            });
  
          }, {
            threshold: 0.5
          });
  
          for (var i = 0; i < this.slides.length; i++) {
            this.observer.observe(this.carousel);
          }
        }
  
        // setup autoplay
        setupAutoplay() {
  
          var autoplay_time = this.autoplay_time;
  
          console.log(`[spslideshow] setup autoplay time = ${autoplay_time}`);  
  
          var context = this;
          context.section.setAttribute('autoplaying', '');
  
          this.interval = setInterval(function() {
  
            // handle next
            if(context.direction == 'up') context.index++;
            else context.index--;
  
            // rewind
            if(context.index == context.slides.length) {
              context.direction = 'down';
              context.index-=2;
            }
            else if(context.index < 0) {
              context.direction = 'up';
              context.index+=2;
            }
  
            // get slide
            var slide = context.slides[context.index];
  
            if(slide) {
              context.carousel.scroll({
                behavior: 'smooth',
                left: slide.offsetLeft
              });
            }
            else {
              clearInterval(context.interval);
            }
  
          }, autoplay_time);
        }
  
        // setup events
        setupClearEvents() {
          var context = this;
  
          this.carousel.addEventListener('mousedown', (e) => {
            context.clearInterval();
          });
  
          this.carousel.addEventListener('touchstart', (e) => {
            context.clearInterval();
          });
        }
  
        // cancel the interval timer
        clearInterval() {
          if(this.interval) clearInterval(this.interval);
          this.section.removeAttribute('autoplaying');
        }
  
        // setup navigation links
        setupLinks() {
  
          var context = this;
  
          for(var x=0; x<this.links.length; x++) {
  
            // handle click of links
            this.links[x].addEventListener('click', (e) => {
  
                // clear interval
                context.clearInterval();
  
                var index = parseInt(e.target.getAttribute(`data-index`));
                var slide = null;
  
                for(x=0; x<context.slides.length; x++) {
                  var i = parseInt(context.slides[x].getAttribute('data-index')) || 0;
                  if(i==index) slide = context.slides[x];
                }
  
                if(slide) {
                  context.carousel.scroll({
                    behavior: 'smooth',
                    left: slide.offsetLeft
                  });
                }
  
                e.preventDefault();
            });
          }
  
        }
  
        // set active link
        setActiveLink(index) {
          for(var y=0; y<this.links.length; y++) this.links[y].removeAttribute('active');
  
          // set all active
          for(var y=0; y<this.links.length; y++) {
            if(this.links[y].hasAttribute('data-index')) {
              var i = parseInt(this.links[y].getAttribute('data-index'));
              if(i == index) this.links[y].setAttribute('active', '');
            }
          }
  
        }
  
        // setup observer
        setupObserver() {
  
          var context = this;
  
          // use intersection observer to render dots
          this.observer = new IntersectionObserver(function(entries, observer) {
  
            // Loop over the entries
            entries.forEach(entry => {
              // If the element is visible
              if (entry.isIntersecting) {
                var index = parseInt(entry.target.getAttribute('data-index')) || 0;
                context.setActiveLink(index);
              }
            });
  
          }, {
            root: context.carousel, threshold: 0.5
          });
  
          for (var i = 0; i < this.slides.length; i++) {
            this.observer.observe(this.slides[i]);
          }
  
        }
  
      }
      // end SpSlideShow class
  
      var section_{{ short_id }} = document.querySelector('#spro-{{ section.id }}');
      var carousel_{{ short_id }} = document.querySelector(`#spro-carousel-{{ section.id }}`);
      var slides_{{ short_id }} = section_{{ short_id }}.querySelectorAll('.spro-col');
      var links_{{ short_id }} = section_{{ short_id }}.querySelectorAll('.spro-snap-links a');
      
      new SpSlideShow_{{ short_id }}(section_{{ short_id }}, carousel_{{ short_id }}, {{ section.settings.enable_autoplay }}, {{ section.settings.autoplay_time }}, slides_{{ short_id }}, links_{{ short_id }});
    
    }
  </script>
{% endif %}

{% schema %}
{
  "name": "🚀 Icon Grid",
  "settings": [
    
    {
        "type": "header",
        "content": "Font",
        "info": "Set the fonts for your section. If overriding, the theme fonts will be ignored."
    },
    {
        "type": "checkbox",
        "id": "override_fonts",
        "label": "Override theme fonts",
        "default": false
    },
    {
        "type": "font_picker",
        "id": "headline_font",
        "label": "Headline Font",
        "default": "sans-serif",
        "visible_if": "{{ section.settings.override_fonts == true }}"
    },
    {
        "type": "font_picker",
        "id": "text_font",
        "label": "Text Font",
        "default": "sans-serif",
        "visible_if": "{{ section.settings.override_fonts == true }}"
    },
    {
        "type": "header",
        "content": "Text",
        "info": "Set the text for your section. If overriding, the theme text styles will be ignored."
    },
    {
        "type": "checkbox",
        "id": "override_text_sizes",
        "label": "Override text sizes",
        "default": false
    },
    {
        "type": "range",
        "id": "text_size",
        "min": 10,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Text Size",
        "default": 15,
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "range",
        "id": "mobile_text_size",
        "min": 10,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Mobile Text Size",
        "default": 15,
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "select",
        "id": "text_line_height",
        "label": "Text Line Height",
        "options": [
            {
                "value": "0.75",
                "label": "0.75"
            },
            {
                "value": "1.0",
                "label": "1.0"
            },
            {
                "value": "1.1",
                "label": "1.1"
            },
            {
                "value": "1.2",
                "label": "1.2"
            },
            {
                "value": "1.3",
                "label": "1.3"
            },
            {
                "value": "1.4",
                "label": "1.4"
            },
            {
                "value": "1.5",
                "label": "1.5"
            },
            {
                "value": "1.6",
                "label": "1.6"
            },
            {
                "value": "1.7",
                "label": "1.7"
            },
            {
                "value": "1.8",
                "label": "1.8"
            },
            {
                "value": "1.9",
                "label": "1.9"
            },
            {
                "value": "2.0",
                "label": "2.0"
            },
            {
                "value": "inherit",
                "label": "Inherit"
            }
        ],
        "default": "inherit",
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "range",
        "id": "headline_size",
        "min": 10,
        "max": 75,
        "step": 1,
        "unit": "px",
        "label": "Headline Size",
        "default": 40,
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "range",
        "id": "mobile_headline_size",
        "min": 10,
        "max": 75,
        "step": 1,
        "unit": "px",
        "label": "Mobile Headline Size",
        "default": 40,
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "select",
        "id": "headline_line_height",
        "label": "Headline Line Height",
        "options": [
            {
                "value": "0.75",
                "label": "0.75"
            },
            {
                "value": "1.0",
                "label": "1.0"
            },
            {
                "value": "1.1",
                "label": "1.1"
            },
            {
                "value": "1.2",
                "label": "1.2"
            },
            {
                "value": "1.3",
                "label": "1.3"
            },
            {
                "value": "1.4",
                "label": "1.4"
            },
            {
                "value": "1.5",
                "label": "1.5"
            },
            {
                "value": "1.6",
                "label": "1.6"
            },
            {
                "value": "1.7",
                "label": "1.7"
            },
            {
                "value": "1.8",
                "label": "1.8"
            },
            {
                "value": "1.9",
                "label": "1.9"
            },
            {
                "value": "2.0",
                "label": "2.0"
            },
            {
                "value": "inherit",
                "label": "Inherit"
            }
        ],
        "default": "inherit",
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "checkbox",
        "id": "override_text_colors",
        "label": "Override text colors",
        "default": false
    },
    {
        "type": "color",
        "id": "text_color",
        "default": "#111",
        "label": "Text Color",
        "visible_if": "{{ section.settings.override_text_colors == true }}"
    },
    {
        "type": "color",
        "id": "link_color",
        "default": "#005bd3",
        "label": "Link Color",
        "visible_if": "{{ section.settings.override_text_colors == true }}"
    },
    {
        "type": "header",
        "content": "Section Design",
        "info": "Set the design for the section"
    },
    {
        "type": "select",
        "id": "show_on_device",
        "label": "Show Section",
        "options": [
            {
                "value": "all",
                "label": "All Devices"
            },
            {
                "value": "mobile",
                "label": "Mobile Only"
            },
            {
                "value": "desktop",
                "label": "Desktop Only"
            }
        ],
        "default": "all"
    },
    {
        "type": "color_background",
        "id": "section_background_color",
        "default": "linear-gradient(127deg, rgba(241, 246, 251, 1) 11%, rgba(241, 246, 251, 1) 81%)",
        "label": "Background Color"
    },
    {
        "type": "paragraph",
        "content": "Set the Background Image (optional)"
    },
    {
        "type": "image_picker",
        "id": "section_background_image",
        "label": "Background Image"
    },
    {
        "type": "color",
        "id": "section_background_image_color",
        "label": "Background Image Color"
    },
    {
        "type": "select",
        "id": "section_background_size",
        "default": "cover",
        "label": "Background Image Size",
        "options": [
          {
            "value": "auto",
            "label": "Auto"
          },
          {
            "value": "cover",
            "label": "Cover"
          },
          {
            "value": "contain",
            "label": "Contain"
          }
        ]
    },
    {
        "type": "paragraph",
        "content": "Set the Mobile Background Image (optional)"
    },
    {
        "type": "image_picker",
        "id": "mobile_section_background_image",
        "label": "Mobile Background Image"
    },
    {
        "type": "color",
        "id": "mobile_section_background_image_color",
        "label": "Mobile Background Image Color"
    },
    {
        "type": "select",
        "id": "mobile_section_background_size",
        "default": "cover",
        "label": "Mobile Background Image Size",
        "options": [
          {
            "value": "auto",
            "label": "Auto"
          },
          {
            "value": "cover",
            "label": "Cover"
          },
          {
            "value": "contain",
            "label": "Contain"
          }
        ]
    },
    {
        "type": "paragraph",
        "content": "Set the padding for the section"
    },
    {
        "type": "number",
        "id": "section_padding_top_bottom",
        "default": 25,
        "label": "Padding Top/Bottom"
    },
    {
        "type": "number",
        "id": "mobile_section_padding_top_bottom",
        "default": 25,
        "label": "Mobile Padding Top/Bottom"
    },
    {
        "type": "number",
        "id": "section_padding_left_right",
        "default": 25,
        "label": "Padding Left/Right"
    },
    {
        "type": "number",
        "id": "mobile_section_padding_left_right",
        "default": 25,
        "label": "Mobile Padding Left/Right"
    },
    {
        "type": "header",
        "content": "Container Design",
        "info": "Set the design for your inner container"
    },
    {
        "type": "color_background",
        "id": "container_background_color",
        "label": "Background Color"
    },
    {
        "type": "number",
        "id": "container_max_width",
        "default": 1000,
        "label": "Max Width"
    },
    {
        "type": "number",
        "id": "container_padding_top_bottom",
        "default": 10,
        "label": "Padding Top/Bottom"
    },
    {
        "type": "number",
        "id": "mobile_container_padding_top_bottom",
        "default": 10,
        "label": "Mobile Padding Top/Bottom"
    },
    {
        "type": "number",
        "id": "container_padding_left_right",
        "default": 10,
        "label": "Padding Left/Right"
    },
    {
        "type": "number",
        "id": "mobile_container_padding_left_right",
        "default": 10,
        "label": "Mobile Padding Left/Right"
    },
    {
        "type": "number",
        "id": "element_spacing",
        "default": 15,
        "label": "Spacing Between Elements"
    },
    {
        "type": "range",
        "id": "container_radius",
        "min": 0,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Round Borders on Container",
        "default": 0
    },
    {
        "type": "checkbox",
        "id": "container_shadow",
        "default": false,
        "label": "Subtle Shadow on Container"
    },
    {
        "type": "range",
        "id": "container_border_size",
        "min": 0,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Border Size on Container",
        "default": 0
    },
    {
        "type": "color",
        "id": "container_border_color",
        "default": "#888",
        "label": "Border Color on Container"
    }
,
    {
      "type": "header",
      "content": "Icon Design",
      "info": "Set the display of icon"
    },
    {
      "type": "range",
      "id": "icon_headline_size",
      "min": 10,
      "max": 50,
      "step": 1,
      "unit": "px",
      "label": "Headline Size",
      "default": 18
  },
  {
      "type": "text_alignment",
      "id": "icon_text_alignment",
      "label": "Icon Text Alignment",
      "default": "center"
  },
  {
      "type": "text_alignment",
      "id": "mobile_icon_text_alignment",
      "label": "Mobile Icon Text Alignment",
      "default": "center"
  },
    {
      "type": "select",
      "id": "icon_shape",
      "default": "circle",
      "label": "Icon Shape",
      "options": [
        {
          "value": "square",
          "label": "Square"
        },
        {
          "value": "rounded",
          "label": "Rounded"
        },
        {
          "value": "circle",
          "label": "Circle"
        }
      ]
    },
    {
      "type": "number",
      "id": "icon_padding",
      "default": 25,
      "label": "Icon Padding"
    },
    {
      "type": "range",
      "id": "icon_size",
      "min": 10,
      "max": 200,
      "step": 5,
      "unit": "px",
      "label": "Icon Size",
      "default": 40
    },
    {
      "type": "color_background",
      "id": "icon_background_color",
      "default": "linear-gradient(164deg, #0D8BF4 0%, #0D8BF4 0%)",
      "label": "Background"
    },
    {
      "type": "color",
      "id": "icon_color",
      "default": "#fff",
      "label": "Icon Color"
    },
    {
      "type": "header",
      "content": "Image and Video Icons",
      "info": "Set how image and videos appear in the icons"
    },
    {
      "type": "select",
      "id": "media_fit",
      "label": "Media Fit",
      "options": [
        {
          "value": "contain",
          "label": "Contain"
        },
        {
          "value": "cover",
          "label": "Cover"
        },
        {
          "value": "size",
          "label": "Size"
        }
      ],
      "default": "cover"
    },
    {
      "type": "number",
      "id": "media_height",
      "label": "Media Height (if Size selected)"
    },
    {
      "type": "header",
      "content": "Grid Design",
      "info": "Set the display of the grid"
    },
    {
      "type": "number",
      "id": "grid_gap",
      "default": 20,
      "label": "Gap Between Columns"
    },
    {
      "type": "header",
      "content": "Mobile Display",
      "info": "Set the mobile display"
    },
    {
      "type": "select",
      "id": "mobile_display",
      "label": "Mobile Display",
      "options": [
        {
          "value": "slideshow",
          "label": "Slideshow"
        },
        {
          "value": "stacked",
          "label": "Stacked"
        }
      ],
      "default": "slideshow"
    },
    {
      "type": "select",
      "id": "mobile_columns",
      "label": "Columns (when stacked)",
      "options": [
        {
          "value": "1fr",
          "label": "1 column"
        },
        {
          "value": "1fr 1fr",
          "label": "2 columns"
        },
        {
          "value": "1fr 1fr 1fr",
          "label": "3 columns"
        }
      ],
      "default": "1fr"
    },
    {
      "type": "color_background",
      "id": "progress_background_color",
      "default": "linear-gradient(177deg, rgba(17, 17, 17, .25) 3%, rgba(17, 17, 17, 0.25) 100%)",
      "label": "Slideshow Progress Background Color"
    },
    {
      "type": "color_background",
      "id": "progress_foreground_color",
      "default": "linear-gradient(177deg, rgba(17, 17, 17, 1) 3%, rgba(17, 17, 17, 1) 100%)",
      "label": "Slideshow Progress Foreground Color"
    },
    {
      "type": "color",
      "id": "indicator_color",
      "default": "#111",
      "label": "Slideshow Indicator Color"
    },
    {
      "type": "checkbox",
      "id": "enable_autoplay",
      "default": false,
      "label": "Enable Autoplay?"
    },
    {
      "type": "select",
      "id": "autoplay_time",
      "label": "Autoplay Time",
      "options": [
        {
          "value": "10000",
          "label": "Slowest (10s)"
        },
        {
          "value": "7500",
          "label": "Slower (7.5s)"
        },
        {
          "value": "5000",
          "label": "Normal (5s)"
        },
        {
          "value": "3000",
          "label": "Faster (3s)"
        },
        {
          "value": "2000",
          "label": "Fastest (2s)"
        }
      ],
      "default": "5000"
    },
    {
      "type": "header",
      "content": "Headline",
      "info": "Set text for the headline"
    },
    {
        "type": "text_alignment",
        "id": "text_alignment",
        "label": "Text Alignment",
        "default": "center"
    },
    {
        "type": "text_alignment",
        "id": "mobile_text_alignment",
        "label": "Mobile Text Alignment",
        "default": "center"
    },
    {
      "type": "inline_richtext",
      "id": "headline",
      "label": "Headline",
      "default": "<b>Sample Headline</b>"
    },
    {
      "type": "inline_richtext",
      "id": "text",
      "label": "Text",
      "default": "Use this block to add a description. Leave blank to remove."
    },
    {
      "type": "number",
      "id": "headline_spacing",
      "default": 15,
      "label": "Spacing between Headline and Features"
    }
  ],
  "blocks": [
    {
      "name": "Icon",
      "type": "icon",
      "settings": [
        {
          "type": "select",
          "id": "icon",
          "label": "Icon",
          "options": [
            {
              "value": "none",
              "label": "No Icon"
            },
            {
              "value": "cart",
              "label": "Cart"
            },
            {
              "value": "gift",
              "label": "Gift"
            },
            {
              "value": "gift-card",
              "label": "Gift Card"
            },
            {
              "value": "shipping",
              "label": "Shipping"
            },
            {
              "value": "payment",
              "label": "Payment"
            },
            {
              "value": "trending",
              "label": "Trending"
            },
            {
              "value": "tag",
              "label": "Tag"
            },
            {
              "value": "bag",
              "label": "Bag"
            },
            {
              "value": "heart",
              "label": "Heart"
            },
            {
              "value": "star",
              "label": "Star"
            },
            {
              "value": "paw",
              "label": "Paw"
            },
            {
              "value": "leaf",
              "label": "Leaf"
            },
            {
              "value": "shield",
              "label": "Shield"
            },
            {
              "value": "percent",
              "label": "Percent"
            },
            {
              "value": "timer",
              "label": "Timer"
            },
            {
              "value": "bank",
              "label": "Bank"
            },
            {
              "value": "box",
              "label": "Box"
            },
            {
              "value": "map",
              "label": "Map"
            },
            {
              "value": "package",
              "label": "Package"
            },
            {
              "value": "return",
              "label": "Return"
            },
            {
              "value": "chat",
              "label": "Chat"
            },
            {
              "value": "email",
              "label": "Email"
            },
            {
              "value": "handshake",
              "label": "Handshake"
            },
            {
              "value": "thumbsup",
              "label": "Thumbs Up"
            },
            {
              "value": "gaming",
              "label": "Gaming"
            },
            {
              "value": "support",
              "label": "Support"
            },
            {
              "value": "phone",
              "label": "Phone"
            }
          ],
          "default": "tag"
        },
        {
          "type": "inline_richtext",
          "id": "headline",
          "label": "Headline",
          "default": "<b>Headline</b>"
        },
        {
          "type": "inline_richtext",
          "id": "text",
          "default": "Some followup text to build on the feature",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link (optional)"
        }
      ]
    },
    {
      "name": "Image",
      "type": "image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "inline_richtext",
          "id": "headline",
          "label": "Headline",
          "default": "<b>Headline</b>"
        },
        {
          "type": "inline_richtext",
          "id": "text",
          "default": "Some followup text to build on the feature",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link (optional)"
        }
      ]
    },
    {
      "name": "Video",
      "type": "video",
      "settings": [
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "checkbox",
          "id": "video_inline",
          "label": "Play Inline",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "video_autoplay",
          "label": "Autoplay",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "video_loop",
          "label": "Loop Video",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "video_controls",
          "label": "Show Video Controls",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "video_muted",
          "label": "Mute Video",
          "default": true
        },
        {
          "type": "inline_richtext",
          "id": "headline",
          "label": "Headline",
          "default": "<b>Headline</b>"
        },
        {
          "type": "inline_richtext",
          "id": "text",
          "default": "Some followup text to build on the feature",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link (optional)"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "🚀 Icon Grid",
      "blocks": [
        {
          "type": "icon"
        },
        {
          "type": "icon"
        },
        {
          "type": "icon"
        }
      ]
    }
  ]
}
{% endschema %}
