{%- liquid
  assign theme_rtl = false
  if settings.enable_rtl and settings.langauges_rtl == blank
    assign theme_rtl = true
  endif

  if settings.enable_rtl and settings.langauges_rtl != blank
    assign rtl_languages = settings.langauges_rtl | split: ','
    for language in rtl_languages
      if language == request.locale.iso_code
        assign theme_rtl = true
      endif
    endfor
  endif
-%}
{{ 'section-featured-blog.css' | asset_url | stylesheet_tag }}
{{ 'component-slider-pagination.css' | asset_url | stylesheet_tag }}
{{ 'component-slider-navigation.css' | asset_url | stylesheet_tag }}
{{ 'section-title.css' | asset_url | stylesheet_tag }}

{% if theme_rtl %}
  {{ 'component-slider-navigation-rtl.css' | asset_url | stylesheet_tag }}
  {{ 'section-featured-blog-rtl.css' | asset_url | stylesheet_tag }}
{% endif %}
{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.mobile_padding_top }}px;
    padding-bottom: {{ section.settings.mobile_padding_bottom }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  assign container = ''
  if section.settings.container == 'container'
    assign container = 'container'
  elsif section.settings.container == 'container-fluid'
    assign container = 'container-fluid'
  else
    assign container = 'container-fluid px-0'
  endif

  assign productShowLg = section.settings.products_show_on_desktop
  assign productShowMd = section.settings.products_show_on_tablet
  assign productShowSm = section.settings.products_show_on_mobile

  assign slider_enable = section.settings.slider_enable
  assign productItem = 'col mb-30'

  if slider_enable
    assign productItem = 'swiper-slide'
  endif
-%}

{%- capture sliderWrapper -%}
{%- if slider_enable -%}
blog__slider--wrapper hero__slider--activation component--slider-wrapper swiper
{%- else -%}
row row-cols-lg-{{ productShowLg }} row-cols-md-{{ productShowMd }} row-cols-{{ productShowSm }}
{%- endif -%}
{%- endcapture -%}

<div
  class="blog__section fetch_blog_sec section--padding slideShow section-{{ section.id }}-padding "
  data-section-id="{{ section.id }}"
  data-section-type="slideShow"
  data-slide-autoplay="{{ section.settings.auto_rotate }}"
  data-autoplay-duration="{{ section.settings.change_slides_speed }}000"
  data-slide-loop="{{ section.settings.enable_loop }}"
  data-slideshow="30"
  data-slidesperview="{{ productShowLg }}"
  data-samlldesktopview="{{ productShowLg }}"
  data-show-tablet="{{ productShowMd }}"
  data-show-mobile="{{ productShowSm }}"
>
  <div class="{{ container }}">
    {% if section.settings.heading != blank or section.settings.subtitle != blank %}
      <div class="section-heading text-{{ section.settings.text_center }}  {% if section.settings.border_line or section.settings.show_navigation_topbar %} mb-70 {% else %} mb-50{% endif %}">
        {% if section.settings.heading_position == 'bottom' and section.settings.subtitle != blank %}
          <span class="section-heading__sub_title">{{- section.settings.subtitle -}}</span>
        {% endif %}
        <h2 class="section-heading__title {% if section.settings.heading_position == "top" and section.settings.subtitle != blank and section.settings.border_line %}heading__space--bottom{% endif %} ">
          {% if section.settings.border_line %}<span>{% endif %}
          {{- section.settings.heading -}}
          {% if section.settings.border_line %}</span>{% endif %}
        </h2>
        {% if section.settings.heading_position == 'top' and section.settings.subtitle != blank %}
          <span class="section-heading__sub_title">{{- section.settings.subtitle -}}</span>
        {% endif %}
      </div>
    {% endif %}

    <div class="blog__section--inner {{ sliderWrapper }}">
      {%- if slider_enable -%}<div class="swiper-wrapper">{%- endif -%}

      {%- liquid
        if section.settings.post_limit <= section.settings.blog.articles_count
          assign posts_exceed_limit = true
        endif
      -%}
      {%- if section.settings.blog != blank and section.settings.blog.articles_count > 0 -%}
        {%- for article in section.settings.blog.articles limit: section.settings.post_limit -%}
          <div class="{{ productItem }}">
            <div class="blog__post">
              {% render 'article-card',
                show_content: section.settings.show_content,
                blog: section.settings.blog,
                article: article,
                show_image: section.settings.show_image,
                show_date: section.settings.show_date,
                show_author: section.settings.show_author,
                show_comment: section.settings.show_comment,
                show_content: section.settings.show_content,
                button_type: section.settings.read_more_btn_type,
                color_scheme: section.settings.card_color_scheme,
                corner_radius: section.settings.card_corner_radius
              %}
            </div>
          </div>
        {%- endfor -%}

      {%- else -%}
        {% for i in (1..3) %}
          <div class="{{ productItem }}">
            <div class="blog-placeholder blog--item-{{ forloop.index }}">


              <article class="blog__items   article--card-radius" aria-labelledby="Article-657701535846">
                <div class="blog__items--thumbnail">
                    <div class="article-card__image-wrapper card--client-height">
                      <div class="article-card__image media media--landscape">
                            <div class="placeholder media media--landscape mb-20 card--client-height">
                              {{ 'image' | placeholder_svg_tag: 'placeholder' }}
                            </div>
                      </div>
                    </div>
               
                </div>
                <div class="blog__items--content">
                  <div class="blog__items--meta">
                    <ul class="d-flex">
                      <li class="blog__items--meta__list">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                          class="blog__items--meta__icon">
                          <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                          <line x1="16" y1="2" x2="16" y2="6"></line>
                          <line x1="8" y1="2" x2="8" y2="6"></line>
                          <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
            
                        <span class="blog__items--meta__text"><time datetime="2025-01-12T16:00:09Z">January 2025</time></span>
                      </li>
                      <li class="blog__items--meta__list">
                        <svg class="blog__items--meta__icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                          stroke-linejoin="round">
                          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                          <circle cx="12" cy="7" r="4"></circle>
                        </svg>
                        <span>Store Admin</span>
                      </li>
                    </ul>
                  </div>
                  <h3 class="blog__items--title h4 mb-0" id="Article-657701535846">
                    <a class="article--items__link" href="#">
                      Why Are Plush Cushions Popular Amongst Kids 2025
                    </a>
                  </h3>
                  <div class="article__button">
                    <a class=" button button--extra-small button--primary   button--with-icon"
                      href="#">Read More
                    </a>
                  </div>
                </div>
              </article>



              
            </div>
          </div>
        {% endfor %}
      {%- endif -%}

      {%- if slider_enable -%}
        </div>
      {%- endif -%}
      {%- if slider_enable -%}
        {%- if section.settings.show_navigation -%}
          <div class="swiper-button-prev  component--slider--nav color-{{ section.settings.color_scheme_navigation }}">
            {% render 'icon-arrow-left' %}
          </div>
          <div class="swiper-button-next component--slider--nav  color-{{ section.settings.color_scheme_navigation }}">
            {% render 'icon-arrow-right' %}
          </div>
        {%- endif -%}
      {%- endif -%}
    </div>
    {% liquid
      if section.settings.button_type == 'primary'
        assign button_class = 'button--primary'
      else
        assign button_class = 'button--secondary'
      endif
    %}
    {%- if section.settings.show_view_all and section.settings.post_limit < section.settings.blog.articles_count -%}
      <div class="blog__view-all center mt-40">
        <a
          href="{{ section.settings.blog.url }}"
          class="button button--{{ section.settings.button_size }} {{ button_class }} {% if section.settings.view_button_icon %} button--with-icon{% endif %}"
        >
          {{ 'sections.featured_blog.view_all' | t }}
          {% if section.settings.view_button_icon %}
            <span class="button--icon button--icon-right"> {% render 'icon-arrow-right' %} </span>
          {% endif %}
        </a>
      </div>
    {%- endif -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.featured-blog.name",
  "tag": "section",
  "class": "spaced-section spaced-section--full-width",
  "settings": [
	{
        "type": "select",
        "id": "container",
        "label": "Page width",
        "default": "container-fluid",
        "options": [
          {
            "value": "container",
            "label": "Boxed"
          },
          {
            "value": "container-fluid",
            "label": "Full width"
          }
        ]
      },
   {
      "type": "header",
      "content": "Section header"
    },
     {
      "type": "inline_richtext",
      "id": "subtitle",
      "default": "Subtitle",
      "label": "Subheading"
    },
    {
      "type": "text",
      "id": "heading",
      "default": "Featured Blog",
      "label": "t:sections.featured-collection.settings.title.label"
    },
    {
      "type": "select",
      "id": "heading_position",
      "options": [
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        }
      ],
      "default": "bottom",
      "label": "Heading position"
    },
    {
      "type": "select",
      "id": "text_center",
      "options": [
        {
          "value": "right",
          "label": "Right"
        },
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        }
      ],
      "default": "center",
      "label": "Alignment"
    },
    {
       "type": "checkbox",
       "id": "border_line",
       "label": "Enable border line",
       "default": true
     },
    {
     "type": "header",
     "content": "General"
   },
    {
      "type": "blog",
      "id": "blog",
      "label": "t:sections.featured-blog.settings.blog.label"
    },
    {
      "type": "range",
      "id": "post_limit",
      "min": 1,
      "max": 10,
      "step": 1,
      "default": 3,
      "label": "t:sections.featured-blog.settings.post_limit.label"
    },
    {
     "type": "header",
     "content": "Article card"
   },
    {
      "type": "checkbox",
      "id": "show_image",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_image.label",
      "info": "t:sections.featured-blog.settings.show_image.info"
    },
	{
      "type": "checkbox",
      "id": "show_content",
      "default": false,
      "label": "Show content"
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "default": false,
      "label": "t:sections.featured-blog.settings.show_date.label"
    },
    {
      "type": "checkbox",
      "id": "show_author",
      "default": false,
      "label": "t:sections.featured-blog.settings.show_author.label"
    },
	{
      "type": "checkbox",
      "id": "show_comment",
      "default": false,
      "label": "Show comment"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Button label",
      "default": "Load more"
    },
     {
       "type": "checkbox",
       "id": "button_icon",
       "label": "Use arrow icon",
       "default": true
     },
     {
        "type": "select",
        "id": "read_more_btn_type",
        "label": "Button type",
        "default": "primary",
        "options": [
          {
          "value": "primary",
          "label": "Solid"
          },
          {
          "value": "secondary",
          "label": "Outline"
          },
          {
          "value": "link",
          "label": "Link"
          }
        ]
      },
      {
        "type": "color_scheme",
        "id": "card_color_scheme",
        "label": "Card color scheme",
        "default": "background-1"
      },
      {
      "type": "checkbox",
      "id": "card_corner_radius",
      "label": "Round corner",
      "default": false
    },
     {
      "type": "header",
      "content": "View more button"
    },
     {
      "type": "checkbox",
      "id": "show_view_all",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_view_all.label"
    },
	{
      "type": "text",
      "id": "view_all",
      "default": "View All",
      "label": "Button text"
    },
    {
       "type": "checkbox",
       "id": "view_button_icon",
       "label": "Use arrow icon",
       "default": true
     },
	{
      "type": "select",
      "id": "button_type",
      "label": "Button type",
      "default": "primary",
      "options": [
        {
        "value": "secondary",
        "label": "Secondary"
        },
        {
        "value": "primary",
        "label": "Primary"
        }
      ]
    },
    {
      "type": "select",
      "id": "button_size",
      "label": "Button size",
      "default": "medium",
      "options": [
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "small",
          "label": "Small"
        }
      ]
    },
	{
        "type": "header",
        "content": "Slider Settings"
      },
      {
          "type": "checkbox",
          "id": "slider_enable",
          "label": "Enable slider",
          "default": true
      },
	  {
        "type": "checkbox",
        "id": "show_navigation",
        "label": "Show navigation",
        "default": false
      },
    {
        "type": "color_scheme",
        "id": "color_scheme_navigation",
        "label": "t:sections.all.colors.label",
        "default": "accent-1"
      },

	  {
        "type": "checkbox",
        "id": "enable_loop",
        "label": "Slider loop",
        "default": true
      },
	  {
        "type": "checkbox",
        "id": "auto_rotate",
        "label": "Auto-rotate slides",
        "default": false
      },
      {
        "type": "range",
        "id": "change_slides_speed",
        "min": 3,
        "max": 9,
        "step": 2,
        "unit": "s",
        "label": "Change slides every",
        "default": 5
      },
	  {
        "type": "header",
        "content": "Grid settings"
      },
      {
        "type": "range",
        "id": "products_show_on_desktop",
        "min": 2,
        "max": 8,
        "step": 1,
        "default": 3,
        "label": "Products per row on the desktop"
      },
      {
        "type": "range",
        "id": "products_show_on_tablet",
        "min": 2,
        "max": 4,
        "step": 1,
        "default": 3,
        "label": "Products per row on the tablet"
      },
      {
        "type": "range",
        "id": "products_show_on_mobile",
        "min": 1,
        "max": 3,
        "step": 1,
        "default": 2,
        "label": "Products per row on the mobile"
      },
	  {
      "type": "header",
      "content": "Section padding"
    },
    {
          "type": "paragraph",
          "content": "Desktop"
        },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        },
		{
          "type": "paragraph",
          "content": "Mobile"
        },
		{
          "type": "range",
          "id": "mobile_padding_top",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding top",
          "default": 0
        },
        {
          "type": "range",
          "id": "mobile_padding_bottom",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Padding bottom",
          "default": 0
        }
  ],
  "presets": [
    {
      "name": "t:sections.featured-blog.presets.name",
      "settings": {
        "blog": "News"
      }
    }
  ],
  "disabled_on": {
    "groups": ["header","footer"]
  }
}
{% endschema %}
