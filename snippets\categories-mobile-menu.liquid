<ul class="offcanvas__menu_ul">
  {%- for block in section.blocks -%}
    {%- case block.type -%}
      {%- when 'category_menu' -%}
        <li class="offcanvas__menu_li">
          <a class="mobile__catgory--mneu-link offcanvas__menu_item" href="{{  block.settings.cat_menu_url }}">
            {% if block.settings.icon != 'none' %}
              <span class="categories__menu--icon svg--icon">
                {% render 'categories-menu-icon', icon: block.settings.icon %}
              </span>
            {% endif %}
            {%- if block.settings.image != blank -%}
              <span class="categories__menu--icon image--icon">
                <img
                  src="{{ block.settings.image | img_url: '30x' }}"
                  alt="{{ block.settings.image.alt | escape }}"
                  width="30"
                  height="{{ 30 | divided_by: block.settings.image.aspect_ratio | ceil }}"
                  loading="lazy"
                >
              </span>
            {%- endif -%}
            <span class="categories__menu--label">{{ block.settings.cat_menu_name }}</span>
          </a>
          {%- if block.settings.menu.links != blank -%}
            <ul class="offcanvas__sub_menu">
              {%- for link in block.settings.menu.links -%}
                <li class="offcanvas__sub_menu_li">
                  {%- if link.links != blank -%}
                    <a class="offcanvas__sub_menu_item" href="{{ link.url }}">
                      {{ link.title | escape }}
                    </a>
                    <ul class="offcanvas__sub_menu">
                      {%- for childlink in link.links -%}
                        <li class="offcanvas__sub_menu_li">
                          <a class="offcanvas__sub_menu_item" href="{{ childlink.url }}">
                            {{- childlink.title | escape }}
                          </a>
                        </li>
                      {% endfor %}
                    </ul>
                  {%- else -%}
                    <a class="offcanvas__sub_menu_item" href="{{ link.url }}">
                      {{- link.title | escape -}}
                    </a>
                  {%- endif -%}
                </li>
              {%- endfor -%}
            </ul>
          {% endif %}
        </li>
    {%- endcase -%}
  {%- endfor -%}

  {%- if wishlist_icon -%}
    <li class="offcanvas__menu_li">
      <a href="/pages/wishlist" class="offcanvas__menu_item header__actions_btn">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
          <path fill="currentColor" d="M462.3 62.7c-54.5-46.4-136-38.7-186.6 13.5L256 96.6l-19.7-20.3C195.5 34.1 113.2 8.7 49.7 62.7c-62.8 53.6-66.1 149.8-9.9 207.8l193.5 199.8c6.2 6.4 14.4 9.7 22.6 9.7 8.2 0 16.4-3.2 22.6-9.7L472 270.5c56.4-58 53.1-154.2-9.7-207.8zm-13.1 185.6L256.4 448.1 62.8 248.3c-38.4-39.6-46.4-115.1 7.7-161.2 54.8-46.8 119.2-12.9 142.8 11.5l42.7 44.1 42.7-44.1c23.2-24 88.2-58 142.8-11.5 54 46 46.1 121.5 7.7 161.2z" />
        </svg>
        <span class="offcanvas__menu_text__icon"> {{ 'general.wishlist_page.title' | t }} </span>
        <span class="header__actions_btn_cart_num wishlist__count"></span>
      </a>
    </li>
  {%- endif -%}
</ul>
