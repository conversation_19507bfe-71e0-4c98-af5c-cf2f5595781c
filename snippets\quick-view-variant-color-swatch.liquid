{%- liquid
  assign choose_option = settings.choose_options_name | downcase

  for option in product.options_with_values
    assign option_name = option.name | downcase
    if option_name == choose_option
      assign color_swatch_option = 'option' | append: option.position
    endif
  endfor
-%}

<quick-variant-radios
  class="no-js-hidden"
  data-section="{{ section.id }}"
  data-origin="{{ request.origin }}"
  data-url="{{ product.url }}"
  data-color-swatch="{{ color_swatch_option }}"
  data-color-swatch-style="{{ settings.color_option_style }}"
>
  {%- for option in product.options_with_values -%}
    <fieldset class="js product-form__input radio--swatch variant--swatch-option{{ option.position }}">
      <legend class="form__label">
        <strong>{{ option.name }}:</strong> <span>{{ option.selected_value }}</span>
      </legend>
      {% render 'product-variant-options',
        product: product,
        option: option,
        picker_type: settings.varian_picker,
        choose_option: choose_option,
        swatch_type: settings.color_option_style,
        swatch_button_style: settings.color_option_design
      %}
    </fieldset>
  {%- endfor -%}
  <script type="application/json" data-variant>
    {{ product.variants | json }}
  </script>
  <script type="application/json" data-preorder>
    {%- assign firstBrackets = '{'  -%}
    {%- assign seconrdBrackets = '}'  -%}
    {{ firstBrackets }}
    {%- for variant in product.variants -%}
    "{{variant.id}}": {"qty": {{variant.inventory_quantity}}, "inventory_policy": "{{variant.inventory_policy}}"}{% unless forloop.last == true %},{% endunless %}
      {%- endfor -%}
      {{ seconrdBrackets }}
  </script>
</quick-variant-radios>
