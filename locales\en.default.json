/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "general": {
    "password_page": {
      "login_form_heading": "Enter store using password:",
      "login_password_button": "Enter using password",
      "login_form_password_label": "Password",
      "login_form_password_placeholder": "Your password",
      "login_form_error": "Wrong password!",
      "login_form_submit": "Enter",
      "modal": "Password modal",
      "admin_link_html": "Are you the store owner? <a href=\"/admin\" class=\"link underlined-link\">Log in here</a>",
      "powered_by_shopify_html": "This shop will be powered by {{ shopify }}"
    },
    "wishlist_page": {
      "title": "Wishlist"
    },
    "compare_page": {
      "title": "Compare"
    },
    "social": {
      "alt_text": {
        "share_on_facebook": "Share on Facebook",
        "share_on_twitter": "Tweet on Twitter",
        "share_on_pinterest": "Pin on Pinterest"
      },
      "links": {
        "twitter": "Twitter",
        "facebook": "Facebook",
        "pinterest": "Pinterest",
        "instagram": "Instagram",
        "tumblr": "Tumblr",
        "snapchat": "Snapchat",
        "youtube": "YouTube",
        "vimeo": "Vimeo",
        "tiktok": "TikTok"
      }
    },
    "continue_shopping": "Return to shop",
    "back_to_home_label": "Home",
    "pagination": {
      "label": "Pagination",
      "page": "Page {{ number }}",
      "next": "Next page",
      "previous": "Previous page"
    },
    "search": {
      "search": "Search our store",
      "reset": "reset",
      "search_btn_txt": "Search"
    },
    "cart": {
      "title": "Shopping Cart",
      "empty_title": "Your cart is currently empty.",
      "view": "View cart",
      "item_added": "Item added to your cart",
      "remove": "remove"
    },
    "shipping_calculator": {
      "button_title": "Shipping",
      "popup_title": "Estimate shipping rates",
      "calculate_button": "Calculate",
      "cancel_button": "Cancel",
      "address_first_label": "We found",
      "address_second_label": "shipping rate(s) for your address",
      "country_label": "Please Select a country",
      "wrong_message": "Please put correct information"
    },
    "gift_wrapping_message": {
      "button_title": "Gift Message",
      "popup_title": "Add a personalized message to your order",
      "placeholder_title": "Special instructions for seller",
      "save_button": "Save",
      "cancel_title": "Cancel"
    },
    "free_shipping_message": {
      "free_shipping_only_title": "Only",
      "free_shipping_title": "away from Free Standard Shipping.",
      "free_shipping_success": "Congratulations! You've got free shipping!"
    },
    "add_cart_note": {
      "button_title": "Note",
      "popup_title": "Order special instructions",
      "save_button": "Apply",
      "cancel_title": "Cancel"
    },
    "coupon_code": {
      "button_title": "Coupon",
      "popup_title": "Apply a discount code",
      "save_button": "Save",
      "cancel_title": "Cancel",
      "coupon_wrong_message": "Can't be blank coupon code!"
    },
    "share": {
      "close": "Close share",
      "copy_to_clipboard": "Copy link",
      "share_url": "Link",
      "success_message": "Link copied to clipboard",
      "facebook": "Facebook",
      "twitter": "Twitter",
      "pinterest": "Pin it",
      "facebook_follow": "Follow us on Facebook",
      "twitter_follow": "Follow us on Twitter",
      "pinterest_follow": "Follow us on Pinterest"
    },
    "slider": {
      "of": "of",
      "next_slide": "Slide right",
      "previous_slide": "Slide left"
    }
  },
  "date_formats": {
    "month_year": "%B %Y"
  },
  "newsletter": {
    "label": "Enter your email",
    "success": "Thanks for subscribing",
    "button_label": "Subscribe"
  },
  "accessibility": {
    "skip_to_text": "Skip to content",
    "skip_to_product_info": "Skip to product information",
    "skip_to_filter_info": "Skip to filter information",
    "close": "Close",
    "unit_price_separator": "per",
    "vendor": "Vendor:",
    "error": "Error",
    "refresh_page": "Choosing a selection results in a full page refresh.",
    "link_messages": {
      "new_window": "Opens in a new window.",
      "external": "Opens external website."
    },
    "of": "of",
    "next_slide": "Slide right",
    "previous_slide": "Slide left",
    "loading": "Loading...",
    "total_reviews": "total reviews",
    "star_reviews_info": "{{ rating_value }} out of {{ rating_max }} stars"
  },
  "blogs": {
    "article": {
      "blog": "Blog",
      "read_more_title": "Read more: {{ title }}",
      "comments": {
        "one": "{{ count }} comment",
        "other": "{{ count }} comments"
      },
      "moderated": "Please note, comments need to be approved before they are published.",
      "comment_form_title": "Leave a comment",
      "name": "Name",
      "email": "Email",
      "message": "Comment",
      "post": "Post comment",
      "back_to_blog": "Back to blog",
      "previous_blog": "Previous",
      "next_blog": "Next",
      "share": "Share this article",
      "success": "Your comment was posted successfully! Thank you!",
      "success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated."
    }
  },
  "onboarding": {
    "product_title": "Example product title",
    "collection_title": "Your collection's name"
  },
  "products": {
    "product": {
      "add_to_cart": "Add to cart",
      "pre_order": "Pre Order",
      "select_options": "Add to cart",
      "description": "Description",
      "inventory_in_stock": "left In stock",
      "inventory_in_stock_show_count": "{{ quantity }} left in stock",
      "inventory_low_stock": "Low stock",
      "hurry_up_text":"Hurry up! Only",
      "inventory_low_stock_show_count": "Low stock: {{ quantity }} left",
      "inventory_out_of_stock": "Out of stock",
      "inventory_out_of_stock_continue_selling": "Re-stocking soon",
      "quick_view": "Quick view",
      "on_sale": "Sale",
      "product_variants": "Product variants",
      "stock_bar": "Stock availability",
      "add_to_wishlist": "Add to wishlist",
      "remove_from_wishlist": "Remove from wishlist",
      "add_to_compare": "Add to compare",
      "remove_from_compare": "Remove from compare",
      "media": {
        "gallery_viewer": "Gallery Viewer",
        "load_image": "Load image {{ index }} in gallery view",
        "load_model": "Load 3D Model {{ index }} in gallery view",
        "load_video": "Play video {{ index }} in gallery view",
        "image_available": "Image {{ index }} is now available in gallery view",
        "open_media": "Open media {{ index }} in modal",
        "open_featured_media": "Open featured media in gallery view",
        "play_model": "Play 3D Viewer",
        "play_video": "Play video"
      },
      "quantity": {
        "label": "Quantity",
        "input_label": "Quantity for {{ product }}",
        "increase": "Increase quantity for {{ product }}",
        "decrease": "Decrease quantity for {{ product }}"
      },
      "countdown_timer": {
        "days": "Days",
        "hours": "Hrs",
        "minutes": "Min",
        "seconds": "Sec"
      },
      "pickup_availability": {
        "view_store_info": "View store information",
        "check_other_stores": "Check availability at other stores",
        "pick_up_available": "Pickup available",
        "pick_up_available_at_html": "Pickup available at <span class=\"color-foreground\">{{ location_name }}</span>",
        "pick_up_unavailable_at_html": "Pickup currently unavailable at <span class=\"color-foreground\">{{ location_name }}</span>",
        "unavailable": "Couldn't load pickup availability",
        "refresh": "Refresh"
      },
      "back_in_stock_notify": {
        "button": "Notify Me",
        "Popup_heading": "Notified by email when this product becomes available",
        "email_placeholder": "Enter your email",
        "submit": "Notify when available",
        "Email_Body_First_Title": "Please notify me when",
        "Email_Body_Last_Title": "becomes available"
      },
      "contact_form_popup": {
        "popup_button_text": "Ask a question",
        "popup_heading": "Have a question?",
        "popup_submit_button": "send",
        "name_input_placeholder": "Name *",
        "email_input_placeholder": "Email *",
        "phone_input_placeholder": "Phone *",
        "product_link_placeholder": "Reference URL *",
        "message_textarea_placeholder": "Write Message *",
        "post_success": "Thanks for contacting us. We'll get back to you as soon as possible."
      },
      "price": {
        "from_price_html": "{{ price }}",
        "regular_price": "Regular price",
        "sale_price": "Sale price",
        "unit_price": "Unit price"
      },
      "inventory_status": {
        "track_label": "Not track quantity",
        "stock": "Stock",
        "availability": "Only",
        "in_stock": "items in stock!",
        "out_of_stock": "Out of stock!",
        "soldout_label": "Sold",
        "available_lable": "Available",
        "product_card_in_stock": "In stock",
        "in_stock_label_many": "Items",
        "in_stock_label_single": "Item"
      },
      "share": "Share this product",
      "sold_out": "Sold out",
      "new_badge": "New",
      "unavailable": "Unavailable",
      "value_unavailable": "{{ option_value }} - Unavailable",
      "variant_sold_out_or_unavailable": "Variant sold out or unavailable",
      "preorder": "Preorder",
      "out_of_stock": "Out of stock",
      "vendor": "Vendor",
      "type": "Type",
      "barcode": "Barcode",
      "sku": "Barcode",
      "availability": "Availability",
      "discount_label": "Save -",
      "video_exit_message": "{{ title }} opens full screen video in same window.",
      "view_full_details": "View full details",
      "xr_button": "View in your space",
      "xr_button_label": "View in your space, loads item in augmented reality window"
    },
    "modal": {
      "label": "Media gallery"
    },
    "facets": {
      "apply": "Apply",
      "clear": "Clear",
      "clear_all": "Remove all",
      "from": "From",
      "filter_and_sort": "Filter and sort",
      "filter_by_label": "Filter:",
      "filter_button": "Filter",
      "filters_selected": {
        "one": "{{ count }} selected",
        "other": "{{ count }} selected"
      },
      "filter_selected_accessibility": "{{ type }} ({{ count }} filters selected)",
      "show_more": "Show more",
      "show_less": "Show less",
      "max_price": "The highest price is {{ price }}",
      "product_count": {
        "one": "{{ product_count }} of {{ count }} product",
        "other": "{{ product_count }} of {{ count }} products"
      },
      "product_count_simple": {
        "one": "{{ count }} product",
        "other": "{{ count }} products"
      },
      "reset": "Reset",
      "sort_button": "Sort",
      "sort_by_label": "SORT BY",
      "to": "To",
      "clear_filter": "Remove filter"
    }
  },
  "templates": {
    "search": {
      "no_results": "No results found for “{{ terms }}”. Check the spelling or use a different word or phrase.",
      "page": "Page",
      "products": "Products",
      "results_pages_with_count": {
        "one": "{{ count }} page",
        "other": "{{ count }} pages"
      },
      "results_suggestions_with_count": {
        "one": "{{ count }} suggestion",
        "other": "{{ count }} suggestions"
      },
      "results_products_with_count": {
        "one": "{{ count }} product",
        "other": "{{ count }} products"
      },
      "results_with_count": {
        "one": "{{ count }} result",
        "other": "{{ count }} results"
      },
      "results_with_count_and_term": {
        "one": "{{ count }} result found for “{{ terms }}”",
        "other": "{{ count }} results found for “{{ terms }}”"
      },
      "search_filter": {
        "vendor": "All Vendors",
        "type": "All product types",
        "tag": "All Tags"
      },
      "title": "Search results",
      "search_for": "Search for “{{ terms }}”",
      "suggestions": "Suggestions",
      "pages": "Pages"
    },
    "cart": {
      "cart": "Cart"
    },
    "contact": {
      "form": {
        "name": "Name",
        "email": "Email",
        "phone": "Phone number",
        "comment": "Comment",
        "send": "Send Message",
        "post_success": "Thanks for contacting us. We'll get back to you as soon as possible.",
        "error_heading": "Please adjust the following:"
      }
    },
    "404": {
      "title": "Oops! Page not found",
      "subtext": "The page you requested does not exist."
    }
  },
  "sections": {
    "header": {
      "announcement": "Announcement",
      "menu": "Menu",
      "categories_menu": "Categories",
      "cart_count": {
        "one": "{{ count }} item",
        "other": "{{ count }} items"
      }
    },
    "cart": {
      "title": "Your cart",
      "caption": "Cart items",
      "remove_title": "Remove {{ title }}",
      "subtotal": "Subtotal",
      "new_subtotal": "New subtotal",
      "note": "Order special instructions",
      "checkout": "Check out",
      "empty": "Your cart is empty",
      "cart_error": "There was an error while updating your cart. Please try again.",
      "cart_quantity_error_html": "You can only add [quantity] of this item to your cart.",
      "taxes_and_shipping_policy_at_checkout_html": "Taxes and <a href=\"{{ link }}\">shipping</a> calculated at checkout",
      "taxes_included_but_shipping_at_checkout": "Tax included and shipping calculated at checkout",
      "taxes_included_and_shipping_policy_html": "Tax included. <a href=\"{{ link }}\">Shipping</a> calculated at checkout.",
      "taxes_and_shipping_at_checkout": "Taxes and shipping calculated at checkout",
      "headings": {
        "product": "Product",
        "price": "Price",
        "total": "Total",
        "quantity": "Quantity"
      },
      "update": "Update",
      "login": {
        "title": "Have an account?",
        "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\">Log in</a> to check out faster."
      }
    },
    "footer": {
      "payment": "Payment methods"
    },
    "advanced_search_filter": {
      "submit_button": "Search"
    },
    "slideshow": {
      "load_slide": "Load slide",
      "previous_slideshow": "Previous slide",
      "next_slideshow": "Next slide",
      "pause_slideshow": "Pause slideshow",
      "play_slideshow": "Play slideshow",
      "carousel": "Carousel",
      "slide": "Slide"
    },
    "featured_blog": {
      "view_all": "View all",
      "onboarding_title": "Blog post",
      "onboarding_content": "Give your customers a summary of your blog post"
    },
    "featured_collection": {
      "view_all": "View all",
      "view_all_label": "View all products in the {{ collection_name }} collection"
    },
    "collection_list": {
      "view_all": "View all",
      "items": "Items",
      "default_title": "Collection title"
    },
    "collection_template": {
      "apply": "Apply",
      "clear": "Clear",
      "clear_all": "Clear all",
      "empty": "No products found",
      "from": "From",
      "filter_and_sort": "Filter and sort",
      "filter_by_label": "Filter:",
      "filter_button": "Filter",
      "filters_selected": {
        "one": "{{ count }} selected",
        "other": "{{ count }} selected"
      },
      "max_price": "The highest price is {{ price }}",
      "product_count": {
        "one": "{{ product_count }} of {{ count }} product",
        "other": "{{ product_count }} of {{ count }} products"
      },
      "product_count_simple": {
        "one": "{{ count }} product",
        "other": "{{ count }} products"
      },
      "reset": "Reset",
      "sort_button": "Sort",
      "sort_by_label": "Sort by:",
      "title": "Collection",
      "to": "To",
      "divider": "Price devider",
      "use_fewer_filters_html": "Use fewer filters or <a class=\"{{ class }}\" href=\"{{ link }}\">clear all</a>"
    },
    "video": {
      "load_video": "Load video: {{ description }}"
    }
  },
  "localization": {
    "country_label": "Country/region",
    "language_label": "Language",
    "update_language": "Update language",
    "update_country": "Update country/region"
  },
  "customer": {
    "account": {
      "title": "Account",
      "details": "Account details",
      "view_addresses": "View addresses",
      "return": "Return to Account details",
      "welcome": "Hello, {{ customer_name }} welcome to your dashboard! "
    },
    "account_fallback": "Account",
    "activate_account": {
      "title": "Activate account",
      "subtext": "Create your password to activate your account.",
      "password": "Password",
      "password_confirm": "Confirm password",
      "submit": "Activate account",
      "cancel": "Decline invitation"
    },
    "addresses": {
      "title": "Addresses",
      "default": "Default",
      "add_new": "Add a new address",
      "edit_address": "Edit address",
      "first_name": "First name",
      "last_name": "Last name",
      "company": "Company",
      "address1": "Address 1",
      "address2": "Address 2",
      "city": "City",
      "country": "Country/region",
      "province": "Province",
      "zip": "Postal/ZIP code",
      "phone": "Phone",
      "set_default": "Set as default address",
      "add": "Add address",
      "update": "Update address",
      "cancel": "Cancel",
      "edit": "Edit",
      "delete": "Delete",
      "delete_confirm": "Are you sure you wish to delete this address?"
    },
    "my_account": "My account",
    "log_in": "Log in",
    "log_out": "Log out",
    "dashboard": "Dashboard",
    "go_to_dashboard": "Go To Dashboard",
    "login_page": {
      "cancel": "Cancel",
      "create_account": "New customer? Sign up for an account",
      "email": "Email",
      "forgot_password": "Forgot your password?",
      "guest_continue": "Continue",
      "guest_title": "Continue as a guest",
      "password": "Password",
      "title": "Login",
      "sign_in": "Sign in",
      "submit": "Submit",
      "return_to_store": "Return to store"
    },
    "order": {
      "title": "Order {{ name }}",
      "date_html": "Placed on {{ date }}",
      "cancelled_html": "Order Cancelled on {{ date }}",
      "cancelled_reason": "Reason: {{ reason }}",
      "billing_address": "Billing Address",
      "payment_status": "Payment Status",
      "shipping_address": "Shipping Address",
      "fulfillment_status": "Fulfillment Status",
      "discount": "Discount",
      "shipping": "Shipping",
      "tax": "Tax",
      "product": "Product",
      "sku": "SKU",
      "price": "Price",
      "quantity": "Quantity",
      "total": "Total",
      "fulfilled_at_html": "Fulfilled {{ date }}",
      "track_shipment": "Track shipment",
      "tracking_url": "Tracking link",
      "tracking_company": "Carrier",
      "tracking_number": "Tracking number",
      "subtotal": "Subtotal"
    },
    "orders": {
      "title": "Order history",
      "order_number": "Order",
      "order_number_link": "Order number {{ number }}",
      "date": "Date",
      "payment_status": "Payment status",
      "fulfillment_status": "Fulfillment status",
      "total": "Total",
      "none": "You haven't placed any orders yet."
    },
    "recover_password": {
      "title": "Reset your password",
      "subtext": "We will send you an email to reset your password",
      "success": "We've sent you an email with a link to update your password."
    },
    "register": {
      "name": "Register",
      "title": "Create account",
      "first_name": "First name",
      "last_name": "Last name",
      "email": "Email",
      "password": "Password",
      "submit": "Create"
    },
    "reset_password": {
      "title": "Reset account password",
      "subtext": "Enter a new password for {{ email }}",
      "password": "Password",
      "password_confirm": "Confirm password",
      "submit": "Reset password"
    }
  },
  "gift_cards": {
    "issued": {
      "title": "Here's your {{ value }} gift card for {{ shop }}!",
      "subtext": "Here's your gift card",
      "gift_card_code": "Gift card code",
      "redeem": "Use this code to redeem your gift card at checkout:",
      "qr_code_label": "Or scan this QR code",
      "shop_link": "Continue shopping",
      "remaining_html": "Remaining {{ balance }}",
      "add_to_apple_wallet": "Add to Apple Wallet",
      "qr_image_alt": "QR code — scan to redeem gift card",
      "copy_code": "Copy code",
      "expired": "Expired",
      "copy_code_success": "Code copied successfully",
      "print_gift_card": "Print"
    }
  },
  "recipient": {
    "form": {
      "checkbox": "I want to send this as a gift",
      "expanded": "Gift card recipient form expanded",
      "collapsed": "Gift card recipient form collapsed",
      "email_label": "Recipient email",
      "email_label_optional_for_no_js_behavior": "Recipient email (optional)",
      "email": "Email",
      "name_label": "Recipient name (optional)",
      "name": "Name",
      "message_label": "Message (optional)",
      "message": "Message",
      "max_characters": "{{ max_chars }} characters max",
      "send_on": "YYYY-MM-DD",
      "send_on_label": "Send on (optional)"
    }
  }
}
