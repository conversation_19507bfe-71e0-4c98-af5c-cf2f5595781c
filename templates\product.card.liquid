{% comment %}
  Product recommendation slider
{% endcomment %}
{%- layout none -%}
{%- liquid
  # Use URL parameter card_style if available, otherwise fall back to global setting
  assign card_style_param = request.url | split: 'card_style=' | last | split: '&' | first
  if card_style_param != blank and card_style_param != request.url
    assign card_style_to_use = card_style_param
  else
    assign card_style_to_use = settings.recent_viewed_proudct_card
  endif
-%}
{%- render 'product-card',
  product_card_product: product,
  className: 'swiper-slide',
  media_size: settings.recent_viewed_proudct_media_size,
  show_secondary_image: true,
  show_badge: settings.show_badges,
  show_cart_button: settings.show_cart_button,
  show_quick_view: settings.show_quick_view_button,
  show_quick_compare: settings.show_compare_view_button,
  show_wishlist: settings.show_wishlist_button,
  show_title: true,
  show_price: true,
  card_style: card_style_to_use,
  color_scheme: settings.product_card_color_scheme,
  spacing: settings.product_card_spacing,
  corner_radius: settings.product_card_radius,
  inventory_status: settings.recent_viewd_inventory_status,
  show_rating: settings.recent_viewd_product_rating
-%}
