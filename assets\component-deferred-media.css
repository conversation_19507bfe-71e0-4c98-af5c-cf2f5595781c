.deferred-media__poster {
  background-color: transparent;
  border: none;
  cursor: pointer;
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}

.media > .deferred-media__poster {
  display: flex;
  align-items: center;
  justify-content: center;
}

.deferred-media__poster img {
  width: auto;
  height: 100%;
}

.deferred-media {
  overflow: hidden;
}

.deferred-media:not([loaded]) template {
  z-index: -1;
}

.deferred-media[loaded] > .deferred-media__poster {
  display: none;
}

.deferred-media__poster:focus {
  outline-offset: -0.3rem;
}

.deferred-media__poster-button .icon {
  width: 2rem;
  height: 2rem;
}

.deferred-media__poster-button .icon-play {
  margin-left: 0.2rem;
}

